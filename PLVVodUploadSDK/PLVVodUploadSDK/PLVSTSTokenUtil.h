//
//  PLVSTSTokenUtil.h
//  PLVVodUploadSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/4.
//  Copyright © 2019 polyv. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class PLVSTSToken, PLVUploadParameter;

/**
 后端接口调用工具类
 */
@interface PLVSTSTokenUtil : NSObject

/**
 获取 STS 临时授权 token
 @param userId 用户 id
 @param secretKey 用户 secretKey
 @param error NSError 指针，如果出错，指针指向一个 NSError 对象
 @return PLVSTSToken 对象
 */
+ (nullable PLVSTSToken *)getSTSTokenWithUserId:(NSString *)userId
                                      secretKey:(NSString *)secretKey
                                          error:(NSError **)error;

/**
 初始化上传任务
 @param userId 用户 id
 @param secretKey 用户 secretKey
 @param uploadParameter 上传参数（含待上传视频文件本地存储 URL）
 @param error NSError 指针，如果出错，指针指向一个 NSError 对象
 @return PLVSTSToken 对象
 */
+ (PLVSTSToken *)uploadVideoWithUserId:(NSString *)userId
                             secretKey:(NSString *)secretKey
                       uploadParameter:(PLVUploadParameter *)uploadParameter
                                 error:(NSError **)error;

@end

/**
 后端接口返回数据模型
 */
@interface PLVSTSToken : NSObject

/**
 YES：接口返回 coce 等于 200
 */
@property (nonatomic, assign, getter=isSuccess, readonly) BOOL success;

/**
 上传所需 tAccessKey
 */
@property (nonatomic, copy, readonly) NSString *accessId;

/**
 上传所需 tSecretKey
 */
@property (nonatomic, copy, readonly) NSString *accessKey;

/**
 上传所需 tToken
 */
@property (nonatomic, copy, readonly) NSString *token;

/**
 token 失效时间，如 "2017-10-26T11:18:58Z"
 */
@property (nonatomic, copy, readonly) NSString *Expiration;

/**
 上传域名（后端返回的 domain 字段加上 "https://" 协议头
 */
@property (nonatomic, copy, readonly) NSString *endpoint;

/**
 上传到云空间的 bucketName
 */
@property (nonatomic, copy, readonly) NSString *bucketName;

/**
 上传到云空间的文件夹路径
 */
@property (nonatomic, copy, readonly) NSString *dir;

/**
 暂时没用到
 */
@property (nonatomic, copy, readonly) NSString *host;

/**
 上传成功给服务器的回调
 */
@property (nonatomic, copy, readonly) NSDictionary * __nullable callback;

/**
 上传任务的唯一标识
 */
@property (nonatomic, copy, readonly) NSString * __nullable vid;

/**
 云空间剩余空间（单位为 byte）
 */
@property (nonatomic, assign, readonly) long long remainSpace;

@end

NS_ASSUME_NONNULL_END
