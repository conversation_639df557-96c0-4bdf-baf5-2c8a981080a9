//
//  PLVUploadQueue.h
//  PLVVodUploadSDK
//
//  Created by Miss<PERSON>asi<PERSON> on 2019/4/18.
//  Copyright © 2019 polyv. All rights reserved.
//

#import <Foundation/Foundation.h>

@class PLVUploadVideo;

NS_ASSUME_NONNULL_BEGIN

// 上传任务启动通知
extern NSString *const PLVUploadQueueUploadLaunchNotification;

// 上传任务进入等待队列通知
extern NSString *const PLVUploadQueueUploadWaitingNotification;

/**
 上传队列管理类（任务排队、并发数控制）
 */
@interface PLVUploadQueue : NSObject

/**
 PLVUploadQueue 单例
 @return PLVUploadQueue 单例
 */
+ (instancetype)sharedQueue;

/**
 添加上传任务到队列中
 @param video 添加到队列的 PLVUploadVideo 对象
 */
- (void)addVideo:(PLVUploadVideo *)video;

/**
 从队列中移除某一个上传任务
 @param vid 上传任务对应的 vid
 */
- (void)removeVideoWithVid:(NSString *)vid;

/**
 返回所有上传中或等待上传的任务
 @return 上传任务队列，队列元素为 PLVUploadVideo 对象
 */
- (NSArray <PLVUploadVideo *>*)queueVideos;

/**
 获取上传队列中指定 vid 的上传任务
 @param vid 上传任务对应的 vid
 @return 上传任务（ PLVUploadVideo 对象）
 */
- (PLVUploadVideo *)videoWithVid:(NSString *)vid;

@end

NS_ASSUME_NONNULL_END
