//
//  PLVUploadClient.m
//  PLVVodUploadSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/5.
//  Copyright © 2019 polyv. All rights reserved.
//

#import "PLVUploadClient.h"
#import "PLVUploadQueue.h"
#import "PLVSTSTokenUtil.h"
#import "PLVUploadVideo.h"
#import "PLVUploadParameter.h"
#import "PLVUploadDefine.h"
#import "PLVUploadErrorCodeDefine.h"
#import "PLVResumeUtil.h"
#import <AliyunOSSiOS/AliyunOSSiOS.h>

@interface PLVUploadClientDelegateItem : NSObject

@property (nonatomic, weak) id<PLVUploadClientDelegate> delegate;

- (instancetype)initWithDelegate:(id<PLVUploadClientDelegate>)delegate;

@end

@implementation PLVUploadClientDelegateItem

- (instancetype)initWithDelegate:(id<PLVUploadClientDelegate>)delegate {
    self = [super init];
    if (self) {
        [self setDelegate:delegate];
    }
    return self;
}

@end

@interface PLVUploadClient ()

@property (nonatomic, strong) NSMutableSet *delegateSet;

@property (nonatomic, copy) NSString *userId;

@property (nonatomic, copy) NSString *secretKey;

@property (nonatomic, copy) NSString *endpoint;

@property (nonatomic, strong) OSSClient *ossClient;

@property (nonatomic, strong) NSMutableDictionary <NSString *, OSSResumableUploadRequest *> *requestDictionary;

@property (nonatomic, strong) NSMutableArray <NSString *> *addingUploadTasksRecord;

@property (nonatomic, strong) dispatch_queue_t serialQueue;

@end

@implementation PLVUploadClient

#pragma mark - Life Cycle

- (instancetype)init {
    self = [super init];
    if (self) {
        _requestDictionary = [[NSMutableDictionary alloc] init];
        _serialQueue = dispatch_queue_create("net.polyv.addingUploadTasksRecord", DISPATCH_QUEUE_SERIAL);
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(receiveUploadLaunchNotification:)
                                                     name:PLVUploadQueueUploadLaunchNotification
                                                   object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(receiveUploadWaitingNotification:)
                                                     name:PLVUploadQueueUploadWaitingNotification
                                                   object:nil];
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - Getters & Setters

- (NSMutableSet *)delegateSet {
    if (!_delegateSet) {
        _delegateSet = [NSMutableSet set];
    }
    return _delegateSet;
}

- (NSMutableArray *)addingUploadTasksRecord {
    if (_addingUploadTasksRecord == nil) {
        _addingUploadTasksRecord = [[NSMutableArray alloc] init];
    }
    return _addingUploadTasksRecord;
}

#pragma mark - NSNotification

- (void)receiveUploadLaunchNotification:(NSNotification *)notification {
    PLVUploadVideo *video = (PLVUploadVideo *)notification.object;
    [self startUploadWithVideo:video];
}

- (void)receiveUploadWaitingNotification:(NSNotification *)notification {
    PLVUploadVideo *video = (PLVUploadVideo *)notification.object;
    
    [self invokeDelegateItemsWithAction:@selector(waitingUploadTask:) withObjects:video, nil];
}

#pragma mark - Public

+ (instancetype)sharedClient {
    static dispatch_once_t onceToken;
    static PLVUploadClient *client = nil;
    dispatch_once(&onceToken, ^{
        client = [[PLVUploadClient alloc] init];
    });
    return client;
}

- (void)addDelegate:(id<PLVUploadClientDelegate>)delegate {
    PLVUploadLog(@"新增代理 %@", delegate);
    for (PLVUploadClientDelegateItem *item in self.delegateSet) {
        if (delegate == item.delegate) {
            PLVUploadLog(@"代理 %@ 已存在", delegate);
            return ;
        }
    }
    PLVUploadClientDelegateItem *delegateItem = [[PLVUploadClientDelegateItem alloc] initWithDelegate:delegate];
    [self.delegateSet addObject:delegateItem];
}

- (void)removeDelegate:(id<PLVUploadClientDelegate>)delegate {
    PLVUploadLog(@"移除代理 %@", delegate);
    PLVUploadClientDelegateItem *deleteItem = nil;
    for (PLVUploadClientDelegateItem *item in self.delegateSet) {
        if (delegate == item.delegate) {
            deleteItem = item;
        }
    }
    if (deleteItem) {
        [self.delegateSet removeObject:deleteItem];
    } else {
        PLVUploadLog(@"代理 %@ 不存在", delegate);
    }
}

- (void)loginWithUserId:(NSString *)userId secretKey:(NSString *)secretKey {
    PLVUploadLog(@"登录 SDK: userId=%@, secretKey=%@", userId, secretKey);
    if (userId == nil || userId.length == 0 ||
        secretKey == nil || secretKey.length == 0) {
        NSError *error = [NSError errorWithDomain:PLVClientErrorDomain code:PLVClientErrorCodeLoginFailure userInfo:@{NSLocalizedDescriptionKey:@"参数为空"}];
        [self invokeDelegateItemsWithAction:@selector(uploadClientLoginError:) withObjects:error, nil];
        PLVUploadLog(@"登录 SDK 失败: %@", error);
        return;
    }
    
    if ([userId isEqualToString:@"e97dbe3e64"] || [secretKey isEqualToString:@"zMV29c519P"]) {
        NSError *error = [NSError errorWithDomain:PLVClientErrorDomain code:PLVClientErrorCodeAccountError userInfo:@{NSLocalizedDescriptionKey:@"不允许使用公共账号上传视频"}];
        [self invokeDelegateItemsWithAction:@selector(uploadClientLoginError:) withObjects:error, nil];
        PLVUploadLog(@"登录 SDK 失败: %@", error);
        return;
    }
    
    self.userId = userId;
    self.secretKey = secretKey;
    
    NSError *error;
    PLVSTSToken *token = [PLVSTSTokenUtil getSTSTokenWithUserId:self.userId secretKey:self.secretKey error:&error];
    if (error) {
        [self invokeDelegateItemsWithAction:@selector(uploadClientLoginError:) withObjects:error, nil];
        PLVUploadLog(@"登录 SDK 失败: %@", error);
        return;
    }
    
    self.endpoint = token.endpoint;
    __weak typeof(self) weakSelf = self;
    id<OSSCredentialProvider> credential = [[OSSFederationCredentialProvider alloc] initWithFederationTokenGetter:^OSSFederationToken * _Nullable{
        NSError *error;
        PLVSTSToken *aToken = [PLVSTSTokenUtil getSTSTokenWithUserId:userId secretKey:secretKey error:&error];
        if (error) {
            [weakSelf invokeDelegateItemsWithAction:@selector(uploadClientLoginError:) withObjects:error, nil];
            PLVUploadLog(@"刷新 STS token 失败: %@", error);
            return nil;
        } else {
            PLVUploadLog(@"刷新 STS token 成功");
            OSSFederationToken *ossToken = [[OSSFederationToken alloc] init];
            ossToken.tAccessKey = aToken.accessId;
            ossToken.tSecretKey = aToken.accessKey;
            ossToken.tToken = aToken.token;
            ossToken.expirationTimeInGMTFormat = aToken.Expiration;
            return ossToken;
        }
    }];
    
    self.ossClient = [[OSSClient alloc] initWithEndpoint:self.endpoint credentialProvider:credential];
    PLVUploadLog(@"登录 SDK 完成");
}

- (NSError *)uploadVideoAtFileURL:(NSURL *)fileURL {
    PLVUploadParameter *uploadParameter = [[PLVUploadParameter alloc] init];
    uploadParameter.fileURL = fileURL;
    return [self uploadVideoWithMutipleParameter:uploadParameter];
}

- (NSError *)uploadVideoWithMutipleParameter:(PLVUploadParameter *)uploadParameter {
    
    PLVUploadLog(@"准备初始化上传任务，上传参数为 %@", uploadParameter);
    if (_ossClient == nil ||
        self.userId == nil || self.userId.length == 0 ||
        self.secretKey == nil || self.secretKey.length == 0) {
        NSError *error = [NSError errorWithDomain:PLVClientErrorDomain code:PLVClientErrorCodeHaventLogin userInfo:@{NSLocalizedDescriptionKey:@"登录 SDK 失败或尚未登录 SDK"}];
        [self invokeDelegateItemsWithAction:@selector(prepareUploadError:fileURL:) withObjects:error, uploadParameter.fileURL, nil];
        PLVUploadLog(@"初始化上传任务失败: %@", error);
        return error;
    }
    
    NSURL *fileURL = uploadParameter.fileURL;
    NSString *errorDescription = nil;
    if (uploadParameter == nil || fileURL == nil) {
        errorDescription = @"上传文件 URL 为空";
    }
    
    BOOL isDirectory = NO;
    BOOL fileExist = [[NSFileManager defaultManager] fileExistsAtPath:[fileURL path] isDirectory:&isDirectory];
    if (fileExist == NO) {
        errorDescription = @"上传文件不存在";
    } else if (isDirectory == YES) {
        errorDescription = @"上传文件不可为文件夹";
    }
                           
    NSString *fileName;
    if (uploadParameter.fileName && uploadParameter.fileName.length > 0) {
        fileName = uploadParameter.fileName;
    } else {
        fileName = [PLVResumeUtil fileNameWithURL:fileURL];
    }
    NSInteger fileSize = [PLVResumeUtil fileSizeWithURL:fileURL];
    if (fileName == nil || fileName.length == 0) {
        errorDescription = @"获取上传文件名失败";
    } if (fileSize <= 0) {
        errorDescription = @"获取上传文件大小失败";
    }
    
    if (errorDescription) {
        NSError *error = [NSError errorWithDomain:PLVClientErrorDomain code:PLVClientErrorCodeInitUploadTaskFailure userInfo:@{NSLocalizedDescriptionKey: errorDescription}];
        [self invokeDelegateItemsWithAction:@selector(prepareUploadError:fileURL:) withObjects:error, fileURL, nil];
        PLVUploadLog(@"初始化上传任务失败: %@", error);
        return error;
    }
    
    if ([self isVideoAddingToUploadTask:fileURL]) {
        PLVUploadLog(@"初始化上传任务被中止，文件 %@ 已经在初始化中", fileURL);
        return nil;
    }
    [self videoIsAddingToUploadTask:fileURL];
    
    NSError *error;
    PLVSTSToken *aToken = [PLVSTSTokenUtil uploadVideoWithUserId:self.userId
                                                       secretKey:self.secretKey
                                                 uploadParameter:uploadParameter
                                                           error:&error];
    if (error) { // 创建任务失败
        [self videoFinishAddingToUploadTask:fileURL];
        [self invokeDelegateItemsWithAction:@selector(prepareUploadError:fileURL:) withObjects:error, fileURL, nil];
        PLVUploadLog(@"初始化上传任务失败，接口调用失败 %@", error);
        return error;
    }
    
    if (aToken.remainSpace < [PLVResumeUtil fileSizeWithURL:fileURL]) {
        [self videoFinishAddingToUploadTask:fileURL];
        NSError *error = [NSError errorWithDomain:PLVClientErrorDomain code:PLVClientErrorCodeNoEnoughSpace userInfo:@{@"vid":aToken.vid}];
        [self invokeDelegateItemsWithAction:@selector(prepareUploadError:fileURL:) withObjects:error, fileURL, nil];
        PLVUploadLog(@"初始化上传任务失败，剩余空间不足 %@", error);
        return error;
    }
    
    PLVUploadVideo *video = [self equipVideo:aToken fileURL:fileURL];
    video.fileName = fileName;
    
    NSString *objectKey = [NSString stringWithFormat:@"%@%@.%@", aToken.dir, aToken.vid, [PLVResumeUtil fileSuffixWithURL:fileURL]];
    OSSResumableUploadRequest *resumableUpload = [self formResumableUploadRequestWithBucketName:aToken.bucketName
                                                                                      objectKey:objectKey
                                                                                  callbackParam:aToken.callback
                                                                                  uploadFileURL:fileURL
                                                                                          video:video];
    
    [self.requestDictionary setObject:resumableUpload forKey:aToken.vid];
    [[PLVUploadQueue sharedQueue] addVideo:video];
    
    [self videoFinishAddingToUploadTask:fileURL];
    
    PLVUploadLog(@"初始化上传任务成功");
    return nil;
}

- (void)abortUploadWithVid:(NSString *)vid {
    PLVUploadLog(@"中止任务 vid=%@", vid);
    
    [[PLVUploadQueue sharedQueue] removeVideoWithVid:vid];
    [PLVResumeUtil deleteResumeInformationWithVid:vid];
    
    OSSResumableUploadRequest *request = [self.requestDictionary objectForKey:vid];
    if (request) {
        [request cancel];
        [self.ossClient abortResumableMultipartUpload:request];
        [self.requestDictionary removeObjectForKey:vid];
    } else {
        PLVUploadLog(@"待中止任务已不存在 vid=%@", vid);
    }
}

- (void)retryUploadWithVid:(NSString *)vid fileURL:(NSURL *)fileURL {
    [self retryUploadWithVid:vid fileURL:fileURL fileName:nil];
}

- (void)retryUploadWithVid:(NSString *)vid fileURL:(NSURL *)fileURL fileName:(nullable NSString *)fileName {
    PLVUploadLog(@"续传任务 vid=%@, fileURL=%@", vid, fileURL);
    
    OSSRequest *request = [self.requestDictionary objectForKey:vid];
    if (request == nil) {
        BOOL resumeInfoExist = [PLVResumeUtil resumeInformationExistWithVid:vid];
        if (resumeInfoExist) {
            PLVUploadVideo *video = [self equipResumeVideoWithVid:vid fileURL:fileURL];
            video.fileName = fileName ?: [PLVResumeUtil fileNameWithURL:fileURL];
            
            NSString *bucketName = [PLVResumeUtil bucketNameInResumeInfoWithVid:video.vid];
            NSString *objectKey = [PLVResumeUtil objectKeyInResumeInfoWithVid:video.vid];
            NSDictionary *callbackParam = [PLVResumeUtil callbackParamInResumeInfoWithVid:video.vid];
            
            OSSResumableUploadRequest *resumableUpload = [self formResumableUploadRequestWithBucketName:bucketName
                                                                                              objectKey:objectKey
                                                                                          callbackParam:callbackParam
                                                                                          uploadFileURL:fileURL
                                                                                                  video:video];
            [self.requestDictionary setObject:resumableUpload forKey:vid];
            
            [[PLVUploadQueue sharedQueue] addVideo:video];
            PLVUploadLog(@"成功恢复待续传任务 %@", video);
        } else {
            [self invokeDelegateItemsWithAction:@selector(startUploadTaskFailure:) withObjects:vid, nil];
            [[PLVUploadQueue sharedQueue] removeVideoWithVid:vid];
            PLVUploadLog(@"待续传任务断点信息不存在 vid=%@", vid);
        }
    } else {
        PLVUploadLog(@"待续传任务已在上传中 vid=%@", vid);
    }
}

- (NSArray <PLVUploadVideo *>*)allUploadVideos {
    return [[PLVUploadQueue sharedQueue] queueVideos];
}

- (PLVUploadVideo *)videoWithVid:(NSString *)vid {
    return [[PLVUploadQueue sharedQueue] videoWithVid:vid];
}

#pragma mark - Uploading Request Related

- (void)startUploadWithVideo:(PLVUploadVideo *)video {
    PLVUploadLog(@"准备启动上传任务 %@", video);
    OSSResumableUploadRequest *request = [self.requestDictionary objectForKey:video.vid];
    
    if (request == nil) { // 不存在该上传任务
        [self invokeDelegateItemsWithAction:@selector(startUploadTaskFailure:) withObjects:video.vid, nil];
        [[PLVUploadQueue sharedQueue] removeVideoWithVid:video.vid];
        PLVUploadLog(@"启动上传任务失败，任务不存在，%@", video);
        return;
    }
    
    [PLVResumeUtil saveResumeInformationWithVideo:video ossRequest:request];
    
    [self invokeDelegateItemsWithAction:@selector(startUploadTask:) withObjects:video, nil];
    
    __block PLVUploadVideo *blockVideo = video;
    OSSResumableUploadRequest *ossRequest = (OSSResumableUploadRequest *)request;
    
    OSSTask *osTask = [self.ossClient resumableUpload:ossRequest];
    __weak typeof(self) weakSelf = self;
    
    PLVUploadLog(@"启动上传任务 %@", video);
    
    [osTask continueWithBlock:^id(OSSTask *task) {
        
        if (task.error == nil) {
            blockVideo.status = PLVUploadStatusComplete;
            [weakSelf.requestDictionary removeObjectForKey:video.vid];
            [[PLVUploadQueue sharedQueue] removeVideoWithVid:video.vid];
            [PLVResumeUtil deleteResumeInformationWithVid:video.vid];
            [self invokeDelegateItemsWithAction:@selector(didUploadTask:error:) withObjects:blockVideo, nil, nil];
            
            PLVUploadLog(@"上传成功 vid=%@", video.vid);
        } else {
            
            NSInteger errorCode;
            if ([task.error.domain isEqualToString:OSSClientErrorDomain] && task.error.code == OSSClientErrorCodeNetworkError) {
                blockVideo.status = PLVUploadStatusResumable;
                errorCode = PLVClientErrorCodeOSSErrorCanResumeUpload;
                PLVUploadLog(@"上传失败（可续传） vid=%@, error=%@", video.vid, task.error);
            } else {
                blockVideo.status = PLVUploadStatusFailure;
                errorCode = PLVClientErrorCodeOSSErrorCannotResumeUpload;
                PLVUploadLog(@"上传失败（不可续传） vid=%@, error=%@", video.vid, task.error);
                
                [PLVResumeUtil deleteResumeInformationWithVid:video.vid];
            }
            
            [weakSelf.requestDictionary removeObjectForKey:video.vid];
            [[PLVUploadQueue sharedQueue] removeVideoWithVid:video.vid];
            
            NSError *error = [NSError errorWithDomain:PLVClientErrorDomain code:errorCode userInfo:@{@"ossError":osTask.error}];
            [self invokeDelegateItemsWithAction:@selector(didUploadTask:error:) withObjects:blockVideo, error, nil];
        }
        return nil;
    }];
}

- (PLVUploadVideo *)equipVideo:(PLVSTSToken *)token fileURL:(NSURL *)fileURL {
    PLVUploadVideo *video = [[PLVUploadVideo alloc] init];
    video.vid = token.vid;
    video.fileSize = [PLVResumeUtil fileSizeWithURL:fileURL];
    video.fileURL = fileURL;
    video.status = PLVUploadStatusWaiting;
    return video;
}

- (PLVUploadVideo *)equipResumeVideoWithVid:(NSString *)vid fileURL:(NSURL *)fileURL {
    PLVUploadVideo *video = [[PLVUploadVideo alloc] init];
    video.vid = vid;
    video.fileSize = [PLVResumeUtil fileSizeWithURL:fileURL];
    video.fileURL = fileURL;
    video.status = PLVUploadStatusWaiting;
    return video;
}

- (OSSResumableUploadRequest *)formResumableUploadRequestWithBucketName:(NSString *)bucketName
                                                              objectKey:(NSString *)objectKey
                                                          callbackParam:(NSDictionary *)callbackParam
                                                          uploadFileURL:(NSURL *)fileURL
                                                                  video:(PLVUploadVideo *)video {
    __weak typeof(self) weakSelf = self;
    void (^myUploadProgress)(int64_t bytesSent, int64_t totalByteSent, int64_t totalBytesExpectedToSend) =
    ^(int64_t bytesSent, int64_t totalByteSent, int64_t totalBytesExpectedToSend) {
        float progress = totalByteSent * 1.0 / totalBytesExpectedToSend;
        progress = MAX(MIN(progress, 1), 0);
        video.progress = progress;
        [weakSelf invokeDelegateItemsWithProgressChanged:progress videoId:video.vid];
    };
    
    OSSResumableUploadRequest *resumableUpload = [OSSResumableUploadRequest new];
    resumableUpload.bucketName = bucketName;
    resumableUpload.objectKey = objectKey;
    resumableUpload.callbackParam = callbackParam;
    resumableUpload.uploadingFileURL = fileURL;
    resumableUpload.uploadProgress = myUploadProgress;
    resumableUpload.deleteUploadIdOnCancelling = NO;
    resumableUpload.recordDirectoryPath = [self checkPointCacheDirectory];
    return resumableUpload;
}

- (NSString *)checkPointCacheDirectory {
    NSString *cacheDir = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES)[0];
    NSString *dirString = [NSString stringWithFormat:@"%@/plvCheckPointCaches", cacheDir];
    BOOL isDirectory = NO;
    BOOL exist = [[NSFileManager defaultManager] fileExistsAtPath:dirString isDirectory:&isDirectory];
    if (!exist || isDirectory == NO) {
        [[NSFileManager defaultManager] createDirectoryAtPath:dirString withIntermediateDirectories:YES attributes:nil error:nil];
    }
    return dirString;
}

#pragma mark - Adding Upload Record

- (BOOL)isVideoAddingToUploadTask:(NSURL *)fileURL {
    __block BOOL isAdding = NO;
    dispatch_sync(_serialQueue, ^{
        for (NSString *filePath in self.addingUploadTasksRecord) {
            if ([[fileURL path] isEqualToString:filePath]) {
                isAdding = YES;
                break;
            }
        }
    });
    return isAdding;
}

- (void)videoIsAddingToUploadTask:(NSURL *)fileURL {
    dispatch_sync(_serialQueue, ^{
        [self.addingUploadTasksRecord addObject:[fileURL path]];
    });
}

- (void)videoFinishAddingToUploadTask:(NSURL *)fileURL {
    dispatch_sync(_serialQueue, ^{
        [self.addingUploadTasksRecord removeObject:[fileURL path]];
    });
}

#pragma mark - Mutable Delegate Related

- (void)invokeDelegateItemsWithProgressChanged:(float)progress videoId:(NSString *)vid {
    static BOOL excuting = NO;
    if (excuting == YES) {
        return;
    }
    excuting = YES;
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
        NSMutableArray *deleteItems = [NSMutableArray arrayWithCapacity:0];
        NSSet *copySet = [self.delegateSet copy];
        for (PLVUploadClientDelegateItem *item in copySet) {
            if (item.delegate && [item.delegate respondsToSelector:@selector(uploadTask:progressChange:)]) {
                [item.delegate uploadTask:vid progressChange:progress];
            }
            if (!item.delegate) {
                [deleteItems addObject:item];
            }
        }
        for (id obj in deleteItems) {
            [self.delegateSet removeObject:obj];
        }
    });
    excuting = NO;
}

- (void)invokeDelegateItemsWithAction:(SEL)action withObjects:(id)firstObj, ... NS_REQUIRES_NIL_TERMINATION {
    NSMutableArray *deleteItems = [NSMutableArray arrayWithCapacity:0];
    NSSet *copySet = [self.delegateSet copy];
    for (PLVUploadClientDelegateItem *item in copySet) {
        if (item.delegate && [item.delegate respondsToSelector:action]) {
            NSMethodSignature *sigOfAction = [(id)item.delegate methodSignatureForSelector:action];
            NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:sigOfAction];
            [invocation setTarget:item.delegate];
            [invocation setSelector:action];
            //设置参数
            if (firstObj) {
                NSInteger argsIndex = 2;
                va_list args;
                va_start(args, firstObj);
                
                id argObject = firstObj;
                while (argObject) {
                    //依次取得所有参数
                    if ([argObject isKindOfClass:[NSValue class]] || [argObject isKindOfClass:[NSNumber class]]) {
                        void *value;
                        [argObject getValue:&value];
                        [invocation setArgument:&value atIndex:argsIndex];
                    } else {
                        [invocation setArgument:&argObject atIndex:argsIndex];
                    }
                    argObject = va_arg(args, id);
                    argsIndex++;
                }
                va_end(args);
            }
            //开始执行
            [invocation invoke];
        }
        if (!item.delegate) {
            [deleteItems addObject:item];
        }
    }
    for (id obj in deleteItems) {
        [self.delegateSet removeObject:obj];
    }
}


@end
