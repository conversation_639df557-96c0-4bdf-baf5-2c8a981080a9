//
//  PLVResumeUtil.h
//  PLVVodUploadSDK
//
//  Created by Miss<PERSON>asi<PERSON> on 2019/4/25.
//  Copyright © 2019 polyv. All rights reserved.
//

#import <Foundation/Foundation.h>

@class PLVUploadVideo, OSSResumableUploadRequest;

NS_ASSUME_NONNULL_BEGIN

/**
 断点续传信息管理工具类
 */
@interface PLVResumeUtil : NSObject

/**
 根据上传任务跟上传请求保存断点信息到本地
 每次开始上传前需要调用
 @param video 上传任务
 @param request 上传请求
 */
+ (void)saveResumeInformationWithVideo:(PLVUploadVideo *)video ossRequest:(OSSResumableUploadRequest *)request;

/**
 删除本地指定 vid 的上传任务的断点信息
 上传成功、或上传失败无法续传、或上传被人为取消后需要调用
 @param vid 上传任务的 vid
 */
+ (void)deleteResumeInformationWithVid:(NSString *)vid;

/**
 指定 vid 的上传任务的断点信息是否存在
 @param vid 上传任务的 vid
 @return YES:存在 NO:不存在
 */
+ (BOOL)resumeInformationExistWithVid:(NSString *)vid;

/**
 指定 vid 的可续传任务的 bucketName
 @param vid 上传任务的 vid
 @return bucketName
 */
+ (NSString *)bucketNameInResumeInfoWithVid:(NSString *)vid;

/**
 指定 vid 的可续传任务的 objectKey
 @param vid 上传任务的 vid
 @return objectKey
 */
+ (NSString *)objectKeyInResumeInfoWithVid:(NSString *)vid;

/**
 指定 vid 的可续传任务的服务器回调
 @param vid 上传任务的 vid
 @return 服务器回调 callbackParam
 */
+ (NSDictionary *)callbackParamInResumeInfoWithVid:(NSString *)vid;

#pragma mark - Utils

/**
 根据视频文件本地 URL 获取视频文件名称
 @param fileURL 视频文件本地 URL
 @return 视频文件名称
 */
+ (NSString *)fileNameWithURL:(NSURL *)fileURL;

/**
 根据视频文件本地 URL 获取视频文件后缀
 @param fileURL 视频文件本地 URL
 @return 视频文件后缀
 */
+ (NSString *)fileSuffixWithURL:(NSURL *)fileURL;

/**
 根据视频文件本地 URL 获取视频文件大小
 @param fileURL 视频文件本地 URL
 @return 视频文件大小
 */
+ (NSInteger)fileSizeWithURL:(NSURL *)fileURL;

@end

NS_ASSUME_NONNULL_END
