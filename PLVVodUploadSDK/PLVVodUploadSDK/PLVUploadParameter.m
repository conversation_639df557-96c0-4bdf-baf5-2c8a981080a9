//
//  PLVUploadParameter.m
//  PLVVodUploadSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/16.
//  Copyright © 2019 polyv. All rights reserved.
//

#import "PLVUploadParameter.h"

@implementation PLVUploadParameter

- (instancetype)init {
    self = [super init];
    if (self) {
        _catalogId = 1;
        _screenRecord = NO;
        _keepSource = NO;
        _videoDescription = @"";
        _videoTag = @"";
        _fileName = @"";
    }
    return self;
}

- (NSString *)description {
    return [NSString stringWithFormat:@"<%@:%p> \n%@",[self class],&self, [self propertyDictionary]];
}

- (NSString *)debugDescription {
    return [NSString stringWithFormat:@"<%@:%p> \n%@",[self class],&self, [self propertyDictionary]];
}

- (NSDictionary *)propertyDictionary {
    return @{
             @"fileURL":(self.fileURL ?: @"emptyURL"),
             @"fileName":(self.fileName ?: @"emptyFileName"),
             @"catalogId":@(self.catalogId),
             @"screenRecord":@(self.screenRecord),
             @"keepSource":@(self.keepSource),
             @"videoDescription":(self.videoDescription ?: @"emptyDescrption"),
             @"videoTag":(self.videoTag ?: @"emptyVideoTag")
             };
}

@end
