//
//  PLVUploadVideo.m
//  PLVVodUploadSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/11.
//  Copyright © 2019 polyv. All rights reserved.
//

#import "PLVUploadVideo.h"

@implementation PLVUploadVideo

- (void)setProgress:(float)progress {
    if(progress <= _progress) {
        return;
    }
    _progress = progress;
    if (_uploadProgress) {
        self.uploadProgress(progress);
    }
}

- (NSString *)description {
    return [NSString stringWithFormat:@"<%@:%p> \n%@",[self class],&self, [self propertyDictionary]];
}

- (NSString *)debugDescription {
    return [NSString stringWithFormat:@"<%@:%p> \n%@",[self class],&self, [self propertyDictionary]];
}

- (NSDictionary *)propertyDictionary {
    return @{
             @"vid":(self.vid ?: @"emptyVid"),
             @"status":@(self.status),
             @"fileURL":(self.fileURL ?: @"emptyFileURL"),
             @"fileName":(self.fileName ?: @"emptyFileName"),
             @"fileSize":@(self.fileSize),
             @"progress":@(self.progress)
             };
}

@end
