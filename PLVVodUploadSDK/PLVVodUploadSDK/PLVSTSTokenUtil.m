//
//  PLVSTSTokenUtil.m
//  PLVVodUploadSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/4.
//  Copyright © 2019 polyv. All rights reserved.
//

#import "PLVSTSTokenUtil.h"
#import "PLVUploadParameter.h"
#import "PLVResumeUtil.h"
#import "PLVUploadDefine.h"
#import "PLVUploadErrorCodeDefine.h"
#import <CommonCrypto/CommonDigest.h>

@interface PLVSTSToken ()

@property (nonatomic, assign, getter=isSuccess) BOOL success;

@property (nonatomic, copy) NSString *accessId;

@property (nonatomic, copy) NSString *accessKey;

@property (nonatomic, copy) NSString *token;

@property (nonatomic, copy) NSString *Expiration;

@property (nonatomic, copy) NSString *endpoint;

@property (nonatomic, copy) NSString *bucketName;

@property (nonatomic, copy) NSString *dir;

@property (nonatomic, copy) NSString *host;

@property (nonatomic, copy) NSDictionary *callback;

@property (nonatomic, copy) NSString *vid;

@property (nonatomic, assign) long long remainSpace;

@end

@implementation PLVSTSToken

@end

static NSString *kGetSTSTokeHost = @"api.polyv.net/v2/uploadvideo";
// 获取阿里云上传 token
#define GET_TOKEN_URL(userId)  [NSString stringWithFormat:@"http://%@/%@/token", kGetSTSTokeHost, userId]
// 初始化视频上传信息并且获取阿里云上传 token
#define INIT_VIDEO_URL(userId)   [NSString stringWithFormat:@"http://%@/%@/init", kGetSTSTokeHost, userId]

@implementation PLVSTSTokenUtil

#pragma mark - Public

+ (PLVSTSToken *)getSTSTokenWithUserId:(NSString *)userId
                             secretKey:(NSString *)secretKey
                                 error:(NSError **)error {
    long long timeInterval = [[NSDate date] timeIntervalSince1970] * 1000;
    NSString *ptime = [NSString stringWithFormat:@"%lld", timeInterval];
    
    NSDictionary *param = @{@"ptime":ptime};
    NSString *sign = [self setupSignWithSecretKey:secretKey param:param];
    NSMutableDictionary *newParam = [[NSMutableDictionary alloc] initWithDictionary:param];
    [newParam setObject:sign forKey:@"sign"];
    NSString *paramString = [self stringDictionary:newParam];
    
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    
    __block PLVSTSToken *token = nil;
    __block NSDictionary *errorInfo = nil;
    NSString *url = [NSString stringWithFormat:@"%@?%@", GET_TOKEN_URL(userId), paramString];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.HTTPMethod = @"GET";
    
    PLVUploadLog(@"准备获取 STS token, userId: %@, secretKey: %@, url: %@", userId, secretKey, request.URL);
    
    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData *data, NSURLResponse *response, NSError *aError) {
        if(aError == nil) {
            NSDictionary *dict = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:nil];
            token = [[PLVSTSToken alloc] init];
            NSInteger code = [dict[@"code"] integerValue];
            token.success = code == 200;
            NSDictionary *data = dict[@"data"];
            if (token.success == NO) {
                NSString *description = [NSString stringWithFormat:@"接口返回错误码%zd(%@)", code, dict[@"message"]];
                errorInfo = @{NSLocalizedDescriptionKey:description};
            } else if (data && [data isKindOfClass:[NSDictionary class]]) {
                BOOL valid = [self validateJSON:data withValidator:[self tokenDataValidator]];
                if (valid == NO) {
                    errorInfo = @{NSLocalizedDescriptionKey:@"接口返回数据解析异常"};
                } else {
                    token.accessId = data[@"accessId"];
                    token.accessKey = data[@"accessKey"];
                    token.token = data[@"token"];
                    token.Expiration = data[@"Expiration"];
                    token.endpoint = [NSString stringWithFormat:@"https://%@", data[@"domain"]];
                    token.bucketName = data[@"bucketName"];
                    token.dir = data[@"dir"];
                    token.host = data[@"host"];
                }
            } else {
                errorInfo = @{NSLocalizedDescriptionKey:@"接口返回数据解析异常"};
            }
        } else {
            errorInfo = @{NSLocalizedDescriptionKey:@"网络异常"};
        }
            
        dispatch_semaphore_signal(semaphore);
    }];
    [task resume];
    
    dispatch_time_t timeoutInterval = dispatch_time(DISPATCH_TIME_NOW, 60 * NSEC_PER_SEC);
    dispatch_semaphore_wait(semaphore, timeoutInterval);
    
    if (token == nil) {
        errorInfo = @{NSLocalizedDescriptionKey:@"请求超时"};
    }
    if (error && errorInfo) {
        *error = [NSError errorWithDomain:PLVClientErrorDomain code:PLVClientErrorCodeGetTokenFailure userInfo:errorInfo];
    }
    
    PLVUploadLog(@"结束获取 STS token, 获取%@ %@", *error ? @"失败" : @"成功", *error ?: @"");
    return token;
}

+ (PLVSTSToken *)uploadVideoWithUserId:(NSString *)userId
                             secretKey:(NSString *)secretKey
                       uploadParameter:(PLVUploadParameter *)uploadParameter
                                 error:(NSError **)error{
    
    NSString *fileName = [PLVResumeUtil fileNameWithURL:uploadParameter.fileURL];
    NSInteger fileSize = [PLVResumeUtil fileSizeWithURL:uploadParameter.fileURL];
    
    long long timeInterval = [[NSDate date] timeIntervalSince1970] * 1000;
    NSString *ptime = [NSString stringWithFormat:@"%lld", timeInterval];
    
    NSDictionary *param = @{@"ptime":ptime,
                            @"title":fileName,
                            @"filesize":@(fileSize),
                            @"autoid":@(1),
                            @"uploadType":@"ios_sdk_chunk_v1",
                            @"cataid":@(uploadParameter.catalogId),
                            @"luping":(uploadParameter.screenRecord ? @"1" : @"0"),
                            @"keepsource":(uploadParameter.keepSource ? @"1" : @"0")
                            };
    NSMutableDictionary *mutableDict = [[NSMutableDictionary alloc] initWithDictionary:param];
    if (uploadParameter.videoDescription && uploadParameter.videoDescription.length > 0) {
        [mutableDict setObject:uploadParameter.videoDescription forKey:@"describ"];
    }
    if (uploadParameter.videoTag && uploadParameter.videoTag.length > 0) {
        [mutableDict setObject:uploadParameter.videoTag forKey:@"tag"];
    }
    if (uploadParameter.fileName && uploadParameter.fileName.length > 0) {
        [mutableDict setObject:uploadParameter.fileName forKey:@"title"];
    }
    
    NSString *sign = [self setupSignWithSecretKey:secretKey param:[mutableDict copy]];
    NSMutableDictionary *newParam = [[NSMutableDictionary alloc] initWithDictionary:[mutableDict copy]];
    [newParam setObject:sign forKey:@"sign"];
    NSString *paramString = [self stringDictionary:newParam];
    
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    
    __block PLVSTSToken *token = nil;
    __block NSDictionary *errorInfo = nil;
    NSString *url = INIT_VIDEO_URL(userId);
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.HTTPMethod = @"POST";
    request.HTTPBody = [paramString dataUsingEncoding:NSUTF8StringEncoding];
    
    PLVUploadLog(@"准备初始化视频上传任务, url: %@, HTTPBody: %@", request.URL, paramString);
    
    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData *data, NSURLResponse *response, NSError *aError) {
        if(aError == nil) {
            NSDictionary *dict = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:nil];
            token = [[PLVSTSToken alloc] init];
            NSInteger code = [dict[@"code"] integerValue];
            token.success = code == 200;
            NSDictionary *data = dict[@"data"];
            if (token.success == NO) {
                NSString *description = [NSString stringWithFormat:@"接口返回错误码%zd(%@)", code, dict[@"message"]];
                errorInfo = @{NSLocalizedDescriptionKey:description};
            } else if (data && [data isKindOfClass:[NSDictionary class]]) {
                BOOL valid = [self validateJSON:data withValidator:[self vidoDataValidator]];
                if (valid == NO) {
                    errorInfo = @{NSLocalizedDescriptionKey:@"接口返回数据解析异常"};
                } else {
                    token.remainSpace = [data[@"remainSpace"] longLongValue];
                    token.accessId = data[@"accessId"];
                    token.accessKey = data[@"accessKey"];
                    token.token = data[@"token"];
                    token.Expiration = data[@"Expiration"];
                    token.endpoint = [NSString stringWithFormat:@"https://%@", data[@"domain"]];
                    token.bucketName = data[@"bucketName"];
                    token.dir = data[@"dir"];
                    token.host = data[@"host"];
                    token.vid = data[@"vid"];
                    
                    NSData *jsonData = [data[@"callback"] dataUsingEncoding:NSUTF8StringEncoding];
                    token.callback = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
                }
            } else {
                errorInfo = @{NSLocalizedDescriptionKey:@"接口返回数据解析异常"};
            }
        } else {
            errorInfo = @{NSLocalizedDescriptionKey:@"网络异常"};
        }
        dispatch_semaphore_signal(semaphore);
    }];
    [task resume];
    
    dispatch_time_t timeoutInterval = dispatch_time(DISPATCH_TIME_NOW, 60 * NSEC_PER_SEC);
    dispatch_semaphore_wait(semaphore, timeoutInterval);
    
    if (token == nil) {
        errorInfo = @{NSLocalizedDescriptionKey:@"请求超时"};
    }
    if (error && errorInfo) {
        *error = [NSError errorWithDomain:PLVClientErrorDomain code:PLVClientErrorCodeInitUploadTaskFailure userInfo:errorInfo];
    }
    PLVUploadLog(@"结束初始化视频上传任务, 初始化%@ %@", *error ? @"失败" : @"成功", *error ?: @"");
    return token;
}

#pragma mark - Private

+ (NSString *)setupSignWithSecretKey:(NSString *)secretKey param:(NSDictionary *)param {
    NSString *string = [self stringDictionary:param];
    string = [string stringByAppendingString:secretKey];
    return [[self sha1:string] uppercaseString];
}

+ (NSString *)stringDictionary:(NSDictionary *)param {
    NSMutableString *muString = [NSMutableString new];
    
    NSSortDescriptor *sortDescriptor = [NSSortDescriptor sortDescriptorWithKey:nil ascending:YES];
    NSArray *keyArray = [[param allKeys] sortedArrayUsingDescriptors:@[sortDescriptor]];
    if ([keyArray count] == 0) {
        return @"";
    }
    for (NSString *key in keyArray) {
        [muString appendFormat:@"%@=%@&",key, param[key]];
    }
    return [NSString stringWithFormat:@"%@", [muString substringToIndex:muString.length - 1]];
}

+ (NSString*)sha1:(NSString *)aString {
    NSData *data = [aString dataUsingEncoding:NSUTF8StringEncoding];

    uint8_t digest[CC_SHA1_DIGEST_LENGTH];
    //使用对应的CC_SHA256,CC_SHA384,CC_SHA512
    CC_SHA1(data.bytes, (unsigned int)data.length, digest);
    
    NSMutableString *resultString = [NSMutableString stringWithCapacity:CC_SHA1_DIGEST_LENGTH * 2];
    
    for(int i = 0; i < CC_SHA1_DIGEST_LENGTH; i++) {
        [resultString appendFormat:@"%02x", digest[i]];
    }
    return resultString;
}

#pragma mark - JSON Validator

+ (BOOL)validateJSON:(id)json withValidator:(id)jsonValidator {
    if ([json isKindOfClass:[NSDictionary class]] &&
        [jsonValidator isKindOfClass:[NSDictionary class]]) {
        NSDictionary * dict = json;
        NSDictionary * validator = jsonValidator;
        BOOL result = YES;
        NSEnumerator * enumerator = [validator keyEnumerator];
        NSString * key;
        while ((key = [enumerator nextObject]) != nil) {
            id value = dict[key];
            id format = validator[key];
            if ([value isKindOfClass:[NSDictionary class]]
                || [value isKindOfClass:[NSArray class]]) {
                result = [self validateJSON:value withValidator:format];
                if (!result) {
                    break;
                }
            } else {
                if ([value isKindOfClass:format] == NO &&
                    [value isKindOfClass:[NSNull class]] == NO) {
                    result = NO;
                    break;
                }
            }
        }
        return result;
    } else if ([json isKindOfClass:[NSArray class]] &&
               [jsonValidator isKindOfClass:[NSArray class]]) {
        NSArray * validatorArray = (NSArray *)jsonValidator;
        if (validatorArray.count > 0) {
            NSArray * array = json;
            NSDictionary * validator = jsonValidator[0];
            for (id item in array) {
                BOOL result = [self validateJSON:item withValidator:validator];
                if (!result) {
                    return NO;
                }
            }
        }
        return YES;
    } else if ([json isKindOfClass:jsonValidator]) {
        return YES;
    } else {
        return NO;
    }
}

+ (NSDictionary *)tokenDataValidator {
    return @{
             @"accessId":   [NSString class],
             @"accessKey":  [NSString class],
             @"token":      [NSString class],
             @"Expiration": [NSString class],
             @"endpoint":   [NSString class],
             @"bucketName": [NSString class],
             @"dir":        [NSString class],
             @"host":       [NSString class],
             @"domain":     [NSString class]
             };
}

+ (NSDictionary *)vidoDataValidator {
    return @{
             @"accessId":   [NSString class],
             @"accessKey":  [NSString class],
             @"token":      [NSString class],
             @"Expiration": [NSString class],
             @"endpoint":   [NSString class],
             @"bucketName": [NSString class],
             @"dir":        [NSString class],
             @"host":       [NSString class],
             @"domain":     [NSString class],
             @"vid":        [NSString class],
             @"remainSpace":[NSNumber class],
             @"callback":   [NSString class]
             };
}

@end
