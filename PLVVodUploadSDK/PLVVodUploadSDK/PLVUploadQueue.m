//
//  PLVUploadQueue.m
//  PLVVodUploadSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/18.
//  Copyright © 2019 polyv. All rights reserved.
//

#import "PLVUploadQueue.h"
#import "PLVUploadVideo.h"

NSString *const PLVUploadQueueUploadLaunchNotification = @"PLVUploadQueueUploadLaunchNotification";
NSString *const PLVUploadQueueUploadWaitingNotification = @"PLVUploadQueueUploadWaitingNotification";

@interface PLVUploadQueue ()

@property (nonatomic, assign) NSInteger maxConcurrentRequestCount;
@property (nonatomic, strong) NSMutableArray <PLVUploadVideo *> *videoQueue;
@property (nonatomic, strong) dispatch_queue_t serialQueue;

@end

@implementation PLVUploadQueue

+ (instancetype)sharedQueue {
    static dispatch_once_t onceToken;
    static PLVUploadQueue *queue = nil;
    dispatch_once(&onceToken, ^{
        queue = [[PLVUploadQueue alloc] init];
    });
    return queue;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.maxConcurrentRequestCount = 3;
        self.videoQueue = [[NSMutableArray alloc] init];
        self.serialQueue = dispatch_queue_create("net.polyv.uploadQueue", DISPATCH_QUEUE_SERIAL);
    }
    return self;
}

- (void)addVideo:(PLVUploadVideo *)video {
    dispatch_async(_serialQueue, ^{
        video.status = PLVUploadStatusWaiting;
        [self.videoQueue addObject:video];
        
        NSInteger concurrentCount = [self concurrentCount];
        if (concurrentCount >= self.maxConcurrentRequestCount) {
            dispatch_async(dispatch_get_global_queue(0, 0), ^{
                [[NSNotificationCenter defaultCenter] postNotificationName:PLVUploadQueueUploadWaitingNotification object:video];
            });
        }
        [self queueChange];
    });
}

- (void)removeVideoWithVid:(NSString *)vid {
    dispatch_async(_serialQueue, ^{
        NSPredicate *predicate = [NSPredicate predicateWithFormat:@"%K LIKE %@", @"vid", vid];
        NSArray *filteredVideos = [self.videoQueue filteredArrayUsingPredicate:predicate];
        if (filteredVideos.count > 0) {
            [self.videoQueue removeObjectsInArray:filteredVideos];
            [self queueChange];
        }
    });
}

- (NSArray *)queueVideos {
    __block NSArray *array;
    dispatch_sync(_serialQueue, ^{
        array = [self.videoQueue copy];
    });
    return array;
}

- (PLVUploadVideo *)videoWithVid:(NSString *)vid {
    if ([self.videoQueue count] == 0) {
        return nil;
    }
    
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"%K LIKE %@", @"vid", vid];
    NSArray *filteredVideos = [self.videoQueue filteredArrayUsingPredicate:predicate];
    return (filteredVideos.count > 0) ? filteredVideos[0] : nil;
}

#pragma mark - Private

- (void)queueChange {
    NSInteger concurrentCount = [self concurrentCount];
    if (concurrentCount >= _maxConcurrentRequestCount) {
        return;
    }
    
    for (PLVUploadVideo *video in self.videoQueue) {
        if (video.status == PLVUploadStatusWaiting) {
            concurrentCount++;
            video.status = PLVUploadStatusUploading;
            
            dispatch_async(dispatch_get_global_queue(0, 0), ^{
                [[NSNotificationCenter defaultCenter] postNotificationName:PLVUploadQueueUploadLaunchNotification object:video];
            });
        }
        if (concurrentCount == _maxConcurrentRequestCount) {
            break;
        }
    }
}

- (NSInteger)concurrentCount {
    if ([self.videoQueue count] == 0) {
        return 0;
    }
    
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"%K == %d", @"status", PLVUploadStatusUploading];
    NSArray *filteredVideos = [self.videoQueue filteredArrayUsingPredicate:predicate];
    return filteredVideos.count;
}

@end
