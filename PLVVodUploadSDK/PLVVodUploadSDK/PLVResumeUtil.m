//
//  PLVResumeUtil.m
//  PLVVodUploadSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/25.
//  Copyright © 2019 polyv. All rights reserved.
//

#import "PLVResumeUtil.h"
#import "PLVUploadClient.h"
#import "PLVUploadVideo.h"
#import "PLVUploadDefine.h"
#import <AliyunOSSiOS/AliyunOSSiOS.h>

static NSString *kResumeCacheDirectory = @"plvResumeCaches";
static NSString *kBucketNameKey = @"bucketName";
static NSString *kObjectKeyKey = @"objectKey";
static NSString *kCallbackParamKey = @"callbackParam";

@implementation PLVResumeUtil

#pragma mark - Public

+ (void)saveResumeInformationWithVideo:(PLVUploadVideo *)video ossRequest:(OSSResumableUploadRequest *)request {
    NSString *bucketName = request.bucketName;
    NSString *objectKey = request.objectKey;
    NSDictionary *callbackParam = request.callbackParam;
    
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:callbackParam options:0 error:nil];
    NSString *callbackParamStr = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];

    NSDictionary *writeInfo = @{kBucketNameKey:bucketName, kObjectKeyKey: objectKey, kCallbackParamKey:callbackParamStr};
    [self save:writeInfo withVid:video.vid];
}

+ (void)deleteResumeInformationWithVid:(NSString *)vid {
    NSString *savePath = [NSString stringWithFormat:@"%@/%@", [self resumeCacheDirectory], vid];
    BOOL success = [[NSFileManager defaultManager] removeItemAtPath:savePath error:nil];
    PLVUploadLog(@"删除断点信息%@ vid=%@", success ? @"成功" : @"失败", vid);
}

+ (BOOL)resumeInformationExistWithVid:(NSString *)vid {
    NSString *savePath = [NSString stringWithFormat:@"%@/%@", [self resumeCacheDirectory], vid];
    BOOL exist = [[NSFileManager defaultManager] fileExistsAtPath:savePath];
    if (exist == NO) {
        PLVUploadLog(@"断点信息不存在 vid=%@", vid);
    }
    return exist;
}

+ (NSString *)bucketNameInResumeInfoWithVid:(NSString *)vid {
    NSDictionary *resumeInfo = [self resumeDictWithVid:vid];
    return resumeInfo[kBucketNameKey];
}

+ (NSString *)objectKeyInResumeInfoWithVid:(NSString *)vid {
    NSDictionary *resumeInfo = [self resumeDictWithVid:vid];
    return resumeInfo[kObjectKeyKey];
}

+ (NSDictionary *)callbackParamInResumeInfoWithVid:(NSString *)vid {
    NSDictionary *resumeInfo = [self resumeDictWithVid:vid];
    NSString *callbackParamStr = resumeInfo[kCallbackParamKey];
    NSData *jsonData = [callbackParamStr dataUsingEncoding:NSUTF8StringEncoding];
    return [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
}

#pragma mark - Private

+ (NSString *)resumeCacheDirectory {
    NSString *cacheDir = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES)[0];
    NSString *dirString = [NSString stringWithFormat:@"%@/%@", cacheDir, kResumeCacheDirectory];
    BOOL isDirectory = NO;
    BOOL exist = [[NSFileManager defaultManager] fileExistsAtPath:dirString isDirectory:&isDirectory];
    if (!exist || isDirectory == NO) {
        BOOL success = [[NSFileManager defaultManager] createDirectoryAtPath:dirString withIntermediateDirectories:YES attributes:nil error:nil];
        PLVUploadLog(@"断点信息文件夹创建%@, 路径: %@", success ? @"成功" : @"失败", dirString);
    }
    return dirString;
}

+ (void)save:(NSDictionary *)dict withVid:(NSString *)vid {
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dict options:0 error:nil];
    NSString *dictString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    NSString *savePath = [NSString stringWithFormat:@"%@/%@", [self resumeCacheDirectory], vid];
    BOOL success = [dictString writeToFile:savePath atomically:YES encoding:NSUTF8StringEncoding error:nil];
    PLVUploadLog(@"保存断点信息%@ %@", success ? @"成功" : @"失败", dict);
}

+ (NSDictionary *)resumeDictWithVid:(NSString *)vid  {
    NSString *savePath = [NSString stringWithFormat:@"%@/%@", [self resumeCacheDirectory], vid];
    NSString *dictString = [NSString stringWithContentsOfFile:savePath encoding:NSUTF8StringEncoding error:nil];
    NSData *jsonData = [dictString dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *resultDict = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
    return resultDict;
}

#pragma mark - Utils

+ (NSString *)fileNameWithURL:(NSURL *)fileURL {
    return [fileURL lastPathComponent];
}

+ (NSString *)fileSuffixWithURL:(NSURL *)fileURL {
    return [fileURL pathExtension];
}

+ (NSInteger)fileSizeWithURL:(NSURL *)fileURL {
    NSDictionary *attributes = [self fileAttributesAtPath:[fileURL path]];
    return (NSInteger)attributes.fileSize;
}

+ (NSDictionary *)fileAttributesAtPath:(NSString *)filePath {
    NSError *error;
    NSDictionary *attributes = [[NSFileManager defaultManager] attributesOfItemAtPath:filePath error:&error];
    if (error) {
        return nil;
    } else {
        return attributes;
    }
}

@end
