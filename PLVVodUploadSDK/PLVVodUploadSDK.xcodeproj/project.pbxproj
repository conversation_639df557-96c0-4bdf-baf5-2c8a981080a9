// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		000B412C2271A82200AEFF4D /* PLVResumeUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 000B412A2271A82200AEFF4D /* PLVResumeUtil.h */; };
		000B412D2271A82200AEFF4D /* PLVResumeUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 000B412B2271A82200AEFF4D /* PLVResumeUtil.m */; };
		00154ECF226960E7009E36D7 /* PLVUploadErrorCodeDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 00E98D2A225AEDD400A3DF7E /* PLVUploadErrorCodeDefine.h */; settings = {ATTRIBUTES = (Public, ); }; };
		001694EE2253146A00B4F598 /* PLVVodUploadSDK.h in Headers */ = {isa = PBXBuildFile; fileRef = 001694EC2253146A00B4F598 /* PLVVodUploadSDK.h */; settings = {ATTRIBUTES = (Public, ); }; };
		001694F62253383400B4F598 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 001694F52253383400B4F598 /* libresolv.tbd */; };
		001694F82253384100B4F598 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 001694F72253384100B4F598 /* SystemConfiguration.framework */; };
		001694FA2253384800B4F598 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 001694F92253384800B4F598 /* CoreTelephony.framework */; };
		004E3A372255DB9E007078C7 /* PLVSTSTokenUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 004E3A352255DB9E007078C7 /* PLVSTSTokenUtil.h */; };
		004E3A382255DB9E007078C7 /* PLVSTSTokenUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 004E3A362255DB9E007078C7 /* PLVSTSTokenUtil.m */; };
		006033F422579E4100B1805B /* PLVUploadClient.h in Headers */ = {isa = PBXBuildFile; fileRef = 006033F222579E4100B1805B /* PLVUploadClient.h */; settings = {ATTRIBUTES = (Public, ); }; };
		006033F522579E4100B1805B /* PLVUploadClient.m in Sources */ = {isa = PBXBuildFile; fileRef = 006033F322579E4100B1805B /* PLVUploadClient.m */; };
		00B57EA3228AD339002E56C7 /* PLVUploadDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 00B57EA2228AD307002E56C7 /* PLVUploadDefine.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00D413D522685F3E00202EB9 /* PLVUploadQueue.h in Headers */ = {isa = PBXBuildFile; fileRef = 00D413D322685F3E00202EB9 /* PLVUploadQueue.h */; };
		00D413D622685F3E00202EB9 /* PLVUploadQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = 00D413D422685F3E00202EB9 /* PLVUploadQueue.m */; };
		00E98D4F225EE5E800A3DF7E /* PLVUploadVideo.h in Headers */ = {isa = PBXBuildFile; fileRef = 00E98D4D225EE5E800A3DF7E /* PLVUploadVideo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00E98D50225EE5E800A3DF7E /* PLVUploadVideo.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E98D4E225EE5E800A3DF7E /* PLVUploadVideo.m */; };
		00F4D0B8228D6D9100736B7A /* PLVUploadParameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 00F4D0B6228D6D9100736B7A /* PLVUploadParameter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00F4D0B9228D6D9100736B7A /* PLVUploadParameter.m in Sources */ = {isa = PBXBuildFile; fileRef = 00F4D0B7228D6D9100736B7A /* PLVUploadParameter.m */; };
		2B026F3563760132569112AE /* libPods-vod-PLVVodUploadSDK.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 06C61291965FABBB2F87549B /* libPods-vod-PLVVodUploadSDK.a */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		000B412A2271A82200AEFF4D /* PLVResumeUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVResumeUtil.h; sourceTree = "<group>"; };
		000B412B2271A82200AEFF4D /* PLVResumeUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVResumeUtil.m; sourceTree = "<group>"; };
		001694E92253146A00B4F598 /* PLVVodUploadSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = PLVVodUploadSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		001694EC2253146A00B4F598 /* PLVVodUploadSDK.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodUploadSDK.h; sourceTree = "<group>"; };
		001694ED2253146A00B4F598 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		001694F52253383400B4F598 /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		001694F72253384100B4F598 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		001694F92253384800B4F598 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		004E3A352255DB9E007078C7 /* PLVSTSTokenUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVSTSTokenUtil.h; sourceTree = "<group>"; };
		004E3A362255DB9E007078C7 /* PLVSTSTokenUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVSTSTokenUtil.m; sourceTree = "<group>"; };
		006033F222579E4100B1805B /* PLVUploadClient.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUploadClient.h; sourceTree = "<group>"; };
		006033F322579E4100B1805B /* PLVUploadClient.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVUploadClient.m; sourceTree = "<group>"; };
		00B57EA2228AD307002E56C7 /* PLVUploadDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUploadDefine.h; sourceTree = "<group>"; };
		00D413D322685F3E00202EB9 /* PLVUploadQueue.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUploadQueue.h; sourceTree = "<group>"; };
		00D413D422685F3E00202EB9 /* PLVUploadQueue.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVUploadQueue.m; sourceTree = "<group>"; };
		00E98D2A225AEDD400A3DF7E /* PLVUploadErrorCodeDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUploadErrorCodeDefine.h; sourceTree = "<group>"; };
		00E98D4D225EE5E800A3DF7E /* PLVUploadVideo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUploadVideo.h; sourceTree = "<group>"; };
		00E98D4E225EE5E800A3DF7E /* PLVUploadVideo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVUploadVideo.m; sourceTree = "<group>"; };
		00F4D0B6228D6D9100736B7A /* PLVUploadParameter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUploadParameter.h; sourceTree = "<group>"; };
		00F4D0B7228D6D9100736B7A /* PLVUploadParameter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVUploadParameter.m; sourceTree = "<group>"; };
		06C61291965FABBB2F87549B /* libPods-vod-PLVVodUploadSDK.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-vod-PLVVodUploadSDK.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		0ECE68525B66619A79014AA3 /* Pods-vod-PLVVodUploadSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-vod-PLVVodUploadSDK.debug.xcconfig"; path = "Target Support Files/Pods-vod-PLVVodUploadSDK/Pods-vod-PLVVodUploadSDK.debug.xcconfig"; sourceTree = "<group>"; };
		8319262EC67BEF19E81F6820 /* libPods-PLVVodUploadAPP.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-PLVVodUploadAPP.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		8908C8556B282BBA5438751E /* libPods-PLVVodUploadSDKTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-PLVVodUploadSDKTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		FBC63F76EAABE0024C6FC612 /* Pods-vod-PLVVodUploadSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-vod-PLVVodUploadSDK.release.xcconfig"; path = "Target Support Files/Pods-vod-PLVVodUploadSDK/Pods-vod-PLVVodUploadSDK.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		001694E62253146A00B4F598 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001694FA2253384800B4F598 /* CoreTelephony.framework in Frameworks */,
				001694F82253384100B4F598 /* SystemConfiguration.framework in Frameworks */,
				001694F62253383400B4F598 /* libresolv.tbd in Frameworks */,
				2B026F3563760132569112AE /* libPods-vod-PLVVodUploadSDK.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		001694DF2253146A00B4F598 = {
			isa = PBXGroup;
			children = (
				001694EB2253146A00B4F598 /* PLVVodUploadSDK */,
				001694EA2253146A00B4F598 /* Products */,
				8D8D79CD37373382769779DB /* Frameworks */,
				2EE1C74A8DEDACC7830299E6 /* Pods */,
			);
			sourceTree = "<group>";
		};
		001694EA2253146A00B4F598 /* Products */ = {
			isa = PBXGroup;
			children = (
				001694E92253146A00B4F598 /* PLVVodUploadSDK.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		001694EB2253146A00B4F598 /* PLVVodUploadSDK */ = {
			isa = PBXGroup;
			children = (
				00F4D0B6228D6D9100736B7A /* PLVUploadParameter.h */,
				00F4D0B7228D6D9100736B7A /* PLVUploadParameter.m */,
				004E3A352255DB9E007078C7 /* PLVSTSTokenUtil.h */,
				004E3A362255DB9E007078C7 /* PLVSTSTokenUtil.m */,
				000B412A2271A82200AEFF4D /* PLVResumeUtil.h */,
				000B412B2271A82200AEFF4D /* PLVResumeUtil.m */,
				00D413D322685F3E00202EB9 /* PLVUploadQueue.h */,
				00D413D422685F3E00202EB9 /* PLVUploadQueue.m */,
				00E98D4D225EE5E800A3DF7E /* PLVUploadVideo.h */,
				00E98D4E225EE5E800A3DF7E /* PLVUploadVideo.m */,
				006033F222579E4100B1805B /* PLVUploadClient.h */,
				006033F322579E4100B1805B /* PLVUploadClient.m */,
				001694EC2253146A00B4F598 /* PLVVodUploadSDK.h */,
				00B57EA2228AD307002E56C7 /* PLVUploadDefine.h */,
				00E98D2A225AEDD400A3DF7E /* PLVUploadErrorCodeDefine.h */,
				001694ED2253146A00B4F598 /* Info.plist */,
			);
			path = PLVVodUploadSDK;
			sourceTree = "<group>";
		};
		2EE1C74A8DEDACC7830299E6 /* Pods */ = {
			isa = PBXGroup;
			children = (
				0ECE68525B66619A79014AA3 /* Pods-vod-PLVVodUploadSDK.debug.xcconfig */,
				FBC63F76EAABE0024C6FC612 /* Pods-vod-PLVVodUploadSDK.release.xcconfig */,
			);
			name = Pods;
			path = "../polyv-ios-vod-sdk-demo/Pods";
			sourceTree = "<group>";
		};
		8D8D79CD37373382769779DB /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				001694F92253384800B4F598 /* CoreTelephony.framework */,
				001694F72253384100B4F598 /* SystemConfiguration.framework */,
				001694F52253383400B4F598 /* libresolv.tbd */,
				8908C8556B282BBA5438751E /* libPods-PLVVodUploadSDKTests.a */,
				8319262EC67BEF19E81F6820 /* libPods-PLVVodUploadAPP.a */,
				06C61291965FABBB2F87549B /* libPods-vod-PLVVodUploadSDK.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		001694E42253146A00B4F598 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00E98D4F225EE5E800A3DF7E /* PLVUploadVideo.h in Headers */,
				00F4D0B8228D6D9100736B7A /* PLVUploadParameter.h in Headers */,
				006033F422579E4100B1805B /* PLVUploadClient.h in Headers */,
				004E3A372255DB9E007078C7 /* PLVSTSTokenUtil.h in Headers */,
				00B57EA3228AD339002E56C7 /* PLVUploadDefine.h in Headers */,
				00154ECF226960E7009E36D7 /* PLVUploadErrorCodeDefine.h in Headers */,
				001694EE2253146A00B4F598 /* PLVVodUploadSDK.h in Headers */,
				00D413D522685F3E00202EB9 /* PLVUploadQueue.h in Headers */,
				000B412C2271A82200AEFF4D /* PLVResumeUtil.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		001694E82253146A00B4F598 /* PLVVodUploadSDK */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001694F12253146A00B4F598 /* Build configuration list for PBXNativeTarget "PLVVodUploadSDK" */;
			buildPhases = (
				07574A336F9F3308703E22CF /* [CP] Check Pods Manifest.lock */,
				001694E42253146A00B4F598 /* Headers */,
				001694E52253146A00B4F598 /* Sources */,
				001694E62253146A00B4F598 /* Frameworks */,
				001694E72253146A00B4F598 /* Resources */,
				5E21535591A3422186411D91 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PLVVodUploadSDK;
			productName = PLVVodUploadSDK;
			productReference = 001694E92253146A00B4F598 /* PLVVodUploadSDK.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		001694E02253146A00B4F598 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1020;
				ORGANIZATIONNAME = polyv;
				TargetAttributes = {
					001694E82253146A00B4F598 = {
						CreatedOnToolsVersion = 10.2;
					};
				};
			};
			buildConfigurationList = 001694E32253146A00B4F598 /* Build configuration list for PBXProject "PLVVodUploadSDK" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 001694DF2253146A00B4F598;
			productRefGroup = 001694EA2253146A00B4F598 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				001694E82253146A00B4F598 /* PLVVodUploadSDK */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		001694E72253146A00B4F598 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		07574A336F9F3308703E22CF /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-vod-PLVVodUploadSDK-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		5E21535591A3422186411D91 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-vod-PLVVodUploadSDK/Pods-vod-PLVVodUploadSDK-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-vod-PLVVodUploadSDK/Pods-vod-PLVVodUploadSDK-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-vod-PLVVodUploadSDK/Pods-vod-PLVVodUploadSDK-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		001694E52253146A00B4F598 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				000B412D2271A82200AEFF4D /* PLVResumeUtil.m in Sources */,
				006033F522579E4100B1805B /* PLVUploadClient.m in Sources */,
				004E3A382255DB9E007078C7 /* PLVSTSTokenUtil.m in Sources */,
				00E98D50225EE5E800A3DF7E /* PLVUploadVideo.m in Sources */,
				00D413D622685F3E00202EB9 /* PLVUploadQueue.m in Sources */,
				00F4D0B9228D6D9100736B7A /* PLVUploadParameter.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		001694EF2253146A00B4F598 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		001694F02253146A00B4F598 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		001694F22253146A00B4F598 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0ECE68525B66619A79014AA3 /* Pods-vod-PLVVodUploadSDK.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				DEAD_CODE_STRIPPING = NO;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = PLVVodUploadSDK/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LINK_WITH_STANDARD_LIBRARIES = NO;
				MACH_O_TYPE = staticlib;
				PRODUCT_BUNDLE_IDENTIFIER = net.polyv.PLVVodUploadSDK;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		001694F32253146A00B4F598 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FBC63F76EAABE0024C6FC612 /* Pods-vod-PLVVodUploadSDK.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				DEAD_CODE_STRIPPING = NO;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = PLVVodUploadSDK/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LINK_WITH_STANDARD_LIBRARIES = NO;
				MACH_O_TYPE = staticlib;
				PRODUCT_BUNDLE_IDENTIFIER = net.polyv.PLVVodUploadSDK;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		001694E32253146A00B4F598 /* Build configuration list for PBXProject "PLVVodUploadSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001694EF2253146A00B4F598 /* Debug */,
				001694F02253146A00B4F598 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		001694F12253146A00B4F598 /* Build configuration list for PBXNativeTarget "PLVVodUploadSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001694F22253146A00B4F598 /* Debug */,
				001694F32253146A00B4F598 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 001694E02253146A00B4F598 /* Project object */;
}
