#!/bin/bash
# -------------------------------------------------------------------------------
# Date:     2020/09/08
# Author:   Miss<PERSON><PERSON><PERSON>
# Description: 该脚本用于打包 demo 仓库文件
# -------------------------------------------------------------------------------

# 获取脚本所在路径
SOURCE="$0"
while [ -h "$SOURCE"  ]; do # resolve $SOURCE until the file is no longer a symlink
    DIR="$( cd -P "$( dirname "$SOURCE"  )" && pwd  )"
    SOURCE="$(readlink "$SOURCE")"
    [[ $SOURCE != /*  ]] && SOURCE="$DIR/$SOURCE" # if $SOURCE was a relative symlink, we need to resolve it relative to the path where the symlink file was located
done
CURRENT_PATH="$( cd -P "$( dirname "$SOURCE"  )" && pwd  )"
ROOT_PATH=$(dirname $(dirname ${CURRENT_PATH}))
GITHUB_DEMO_FLODER="${ROOT_PATH}/github/polyv-ios-vod-sdk"

FILE_NAME="vod-demo.tar"

TAR_SOURCE_FILE="${CURRENT_PATH}/${FILE_NAME}"
TAR_DES_FILE="${GITHUB_DEMO_FLODER}/${FILE_NAME}"


cd ${CURRENT_PATH}

tar cvf ${FILE_NAME} ./CHANGELOG.md ./LICENSE ./README.md ./PolyvVodSDKDemo/Podfile ./PolyvVodSDKDemo/PolyvVodSDKDemo ./PolyvVodSDKDemo/PolyvVodSDKDemo.xcodeproj

# 检查打包是否成功
if [ ! -e ${TAR_SOURCE_FILE} ]; then
echo "❌ 打包失败"
exit 1
fi

echo "拷贝tar包到路径 ${GITHUB_DEMO_FLODER}"
cp ${TAR_SOURCE_FILE} ${TAR_DES_FILE}

# 检查拷贝是否成功
if [ ! -e ${TAR_DES_FILE} ]; then
echo "❌ 拷贝tar包失败"
exit 1
fi

rm ${TAR_SOURCE_FILE}
cd ${GITHUB_DEMO_FLODER}
tar -xvf ${FILE_NAME}
rm ${TAR_DES_FILE}

