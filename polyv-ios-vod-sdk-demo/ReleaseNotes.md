## [2.23.0] - 2025-04-21


### Added
- 【DEMO】增加播放线路配置面板  
- 【DEMO、SDK】增加网络状态监控恢复播放机制
- 【SDK】支持动态加密因子 

### Changed
- 【DEMO、SDK】Xcode 16适配
-  【DEMO】优化播放错误弹窗面板
- 【SDK】优化接口请求超时配置
- 【SDK】 优化并启用httpdns优选
- 【SDK】完善播放异常的日志追溯能力
- 【DEMO、SDK】优化异常重试时可按当前进度播放
- 【SDK】PLVIJKPlayer 升级到0.15.0 版本

### 迁移说明

**由 2.18.x 及以下版本升级到 2.19.0 及以上版本时，需要注意视频下载的迁移**

**自 2.19.0 版本开始，本地播放视频鉴权方式进行了调整，为了在覆盖升级时兼容已下载的旧版本视频，初始换sdk时会自动迁移，必须严格测试本地缓存视频是否迁移成功。**