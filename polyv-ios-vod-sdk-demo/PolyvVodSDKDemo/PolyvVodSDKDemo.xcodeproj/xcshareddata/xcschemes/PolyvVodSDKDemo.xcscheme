<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1010"
   version = "1.3">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "03CB8C5A1FB3EB220048D9FC"
               BuildableName = "PolyvVodSDKDemo.app"
               BlueprintName = "PolyvVodSDKDemo"
               ReferencedContainer = "container:PolyvVodSDKDemo.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES">
      <Testables>
         <TestableReference
            skipped = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "03CB8C721FB3EB220048D9FC"
               BuildableName = "PolyvVodSDKDemoTests.xctest"
               BlueprintName = "PolyvVodSDKDemoTests"
               ReferencedContainer = "container:PolyvVodSDKDemo.xcodeproj">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "03CB8C7D1FB3EB220048D9FC"
               BuildableName = "PolyvVodSDKDemoUITests.xctest"
               BlueprintName = "PolyvVodSDKDemoUITests"
               ReferencedContainer = "container:PolyvVodSDKDemo.xcodeproj">
            </BuildableReference>
         </TestableReference>
      </Testables>
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "03CB8C5A1FB3EB220048D9FC"
            BuildableName = "PolyvVodSDKDemo.app"
            BlueprintName = "PolyvVodSDKDemo"
            ReferencedContainer = "container:PolyvVodSDKDemo.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <AdditionalOptions>
      </AdditionalOptions>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "03CB8C5A1FB3EB220048D9FC"
            BuildableName = "PolyvVodSDKDemo.app"
            BlueprintName = "PolyvVodSDKDemo"
            ReferencedContainer = "container:PolyvVodSDKDemo.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <AdditionalOptions>
      </AdditionalOptions>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "03CB8C5A1FB3EB220048D9FC"
            BuildableName = "PolyvVodSDKDemo.app"
            BlueprintName = "PolyvVodSDKDemo"
            ReferencedContainer = "container:PolyvVodSDKDemo.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
