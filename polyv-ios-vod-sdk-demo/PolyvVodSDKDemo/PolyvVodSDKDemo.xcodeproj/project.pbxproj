// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXBuildFile section */
		000935BA22E9AF4500933F66 /* PLVPPTSimpleDetailController.m in Sources */ = {isa = PBXBuildFile; fileRef = 000935B922E9AF4500933F66 /* PLVPPTSimpleDetailController.m */; };
		000935C222E9BD3D00933F66 /* PLVVodPPTViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 000935BD22E9BD3D00933F66 /* PLVVodPPTViewController.m */; };
		000935C322E9BD3D00933F66 /* PLVFloatingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 000935C022E9BD3D00933F66 /* PLVFloatingView.m */; };
		000935D722EA88BB00933F66 /* PLVPPTBaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 000935D622EA88BB00933F66 /* PLVPPTBaseViewController.m */; };
		000935DA22EAA48800933F66 /* PLVPPTVideoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 000935D922EAA48800933F66 /* PLVPPTVideoViewController.m */; };
		00154ED5226979B2009E36D7 /* PLVUploadTableViewModel.mm in Sources */ = {isa = PBXBuildFile; fileRef = 00154ED4226979B2009E36D7 /* PLVUploadTableViewModel.mm */; };
		0019F4592416168A00B3E65D /* PLVVodFastForwardView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0019F4582416168A00B3E65D /* PLVVodFastForwardView.m */; };
		0030335422B101E00087E76A /* PLVDownloadCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0030335322B101E00087E76A /* PLVDownloadCell.m */; };
		0030335722B1047E0087E76A /* PLVUploadCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0030335622B1047E0087E76A /* PLVUploadCell.m */; };
		0030335E22B1E5660087E76A /* PLVVodNetworkUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 0030335D22B1E5660087E76A /* PLVVodNetworkUtil.m */; };
		0046DF4B24E63E6500592F62 /* PLVVodUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 0046DF4A24E63E6500592F62 /* PLVVodUtils.m */; };
		009869BE230BF4FB00AA32E3 /* PLVPPTSkinProgressView.m in Sources */ = {isa = PBXBuildFile; fileRef = 009869BD230BF4FB00AA32E3 /* PLVPPTSkinProgressView.m */; };
		00A582DA22642E8700207B4B /* PLVUploadTableViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 00A582D922642E8700207B4B /* PLVUploadTableViewController.m */; };
		00A582DD2265DB0500207B4B /* PLVUploadingCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 00A582DC2265DB0500207B4B /* PLVUploadingCell.m */; };
		00A582E02265DB1900207B4B /* PLVUploadedCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 00A582DF2265DB1900207B4B /* PLVUploadedCell.m */; };
		00A582E32267031C00207B4B /* PLVUploadModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 00A582E22267031C00207B4B /* PLVUploadModel.m */; };
		00A8166E230BE19D00EB61E5 /* PLVPPTControllerSkinView.m in Sources */ = {isa = PBXBuildFile; fileRef = 00A8166D230BE19D00EB61E5 /* PLVPPTControllerSkinView.m */; };
		00AADCD022F2F1400037A843 /* PLVPPTActionView.m in Sources */ = {isa = PBXBuildFile; fileRef = 00AADCCF22F2F1400037A843 /* PLVPPTActionView.m */; };
		00B0B4962405080D0089495C /* PLVVFloatingPlayerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 00B0B4952405080D0089495C /* PLVVFloatingPlayerViewController.m */; };
		00B2187D2481144700ABDCCD /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 00B218752481144700ABDCCD /* LaunchScreen.storyboard */; };
		00B2187E2481144700ABDCCD /* Settings.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 00B218772481144700ABDCCD /* Settings.bundle */; };
		00B2187F2481144700ABDCCD /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 00B218782481144700ABDCCD /* Assets.xcassets */; };
		00B218802481144700ABDCCD /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 00B218792481144700ABDCCD /* Main.storyboard */; };
		00B218812481144700ABDCCD /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 00B2187A2481144700ABDCCD /* main.m */; };
		00B3AE562484DE95003225F3 /* PLVVodAccount.m in Sources */ = {isa = PBXBuildFile; fileRef = 00B3AE552484DE95003225F3 /* PLVVodAccount.m */; };
		00B57E9E22894361002E56C7 /* PLVUploadCompleteData.mm in Sources */ = {isa = PBXBuildFile; fileRef = 00B57E9722894361002E56C7 /* PLVUploadCompleteData.mm */; };
		00B57E9F22894361002E56C7 /* PLVUploadUncompleteData.mm in Sources */ = {isa = PBXBuildFile; fileRef = 00B57E9822894361002E56C7 /* PLVUploadUncompleteData.mm */; };
		00B57EA022894361002E56C7 /* PLVUploadUtil.mm in Sources */ = {isa = PBXBuildFile; fileRef = 00B57E9922894361002E56C7 /* PLVUploadUtil.mm */; };
		00B57EA122894361002E56C7 /* PLVUploadDataBase.mm in Sources */ = {isa = PBXBuildFile; fileRef = 00B57E9B22894361002E56C7 /* PLVUploadDataBase.mm */; };
		00B57EA7228C0C25002E56C7 /* PLVUploadToast.m in Sources */ = {isa = PBXBuildFile; fileRef = 00B57EA6228C0C25002E56C7 /* PLVUploadToast.m */; };
		00E111EB22F7DC25005A8575 /* PLVPPTTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E111EA22F7DC25005A8575 /* PLVPPTTableViewCell.m */; };
		00E111EE22F82193005A8575 /* PLVPPTActionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E111ED22F82193005A8575 /* PLVPPTActionViewCell.m */; };
		00E111F122F91DA1005A8575 /* PLVEmptyPPTViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E111F022F91DA1005A8575 /* PLVEmptyPPTViewCell.m */; };
		00E111F422F930A4005A8575 /* PLVPPTLoadFailAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E111F322F930A4005A8575 /* PLVPPTLoadFailAlertView.m */; };
		00E111F722F958F9005A8575 /* PLVPPTFailView.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E111F622F958F9005A8575 /* PLVPPTFailView.m */; };
		00EAB3112406760D0083C0E7 /* PLVVFloatingWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = 00EAB3102406760D0083C0E7 /* PLVVFloatingWindow.m */; };
		00EAB3142407737F0083C0E7 /* PLVVFloatingWindowSkin.m in Sources */ = {isa = PBXBuildFile; fileRef = 00EAB3132407737F0083C0E7 /* PLVVFloatingWindowSkin.m */; };
		00EAB3302407B8C80083C0E7 /* PLVVFloatingWindowViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 00EAB32F2407B8C80083C0E7 /* PLVVFloatingWindowViewController.m */; };
		030DF02C1FB590560035D0B0 /* PLVCourseListController.m in Sources */ = {isa = PBXBuildFile; fileRef = 030DF02B1FB590560035D0B0 /* PLVCourseListController.m */; };
		030DF02F1FB590910035D0B0 /* PLVCourseDetailController.m in Sources */ = {isa = PBXBuildFile; fileRef = 030DF02E1FB590910035D0B0 /* PLVCourseDetailController.m */; };
		030DF0361FB5960E0035D0B0 /* PLVCourseNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = 030DF0351FB5960E0035D0B0 /* PLVCourseNetworking.m */; };
		030DF03A1FB5BA9D0035D0B0 /* PLVSchool.m in Sources */ = {isa = PBXBuildFile; fileRef = 030DF0391FB5BA9D0035D0B0 /* PLVSchool.m */; };
		0314085A1FB94DAF00D1424A /* Foundation+Log.m in Sources */ = {isa = PBXBuildFile; fileRef = 031408591FB94DAF00D1424A /* Foundation+Log.m */; };
		0314085D1FB9507A00D1424A /* PLVCourse.m in Sources */ = {isa = PBXBuildFile; fileRef = 0314085C1FB9507A00D1424A /* PLVCourse.m */; };
		031408601FB9806100D1424A /* PLVTeacher.m in Sources */ = {isa = PBXBuildFile; fileRef = 0314085F1FB9806100D1424A /* PLVTeacher.m */; };
		0323727C1FCBC58D0001E904 /* PLVAccountVideoListController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0323727B1FCBC58D0001E904 /* PLVAccountVideoListController.m */; };
		0323727F1FCBE4690001E904 /* PLVVideoCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0323727E1FCBE4690001E904 /* PLVVideoCell.m */; };
		032372821FCC1C570001E904 /* PLVLoadCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 032372811FCC1C570001E904 /* PLVLoadCell.m */; };
		033153041FCD103F00CAE849 /* PLVCourseSection.m in Sources */ = {isa = PBXBuildFile; fileRef = 033153031FCD103F00CAE849 /* PLVCourseSection.m */; };
		033153071FCD105400CAE849 /* PLVCourseVideo.m in Sources */ = {isa = PBXBuildFile; fileRef = 033153061FCD105400CAE849 /* PLVCourseVideo.m */; };
		0334A0052068831E00A49905 /* PLVSimpleDetailController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0334A0042068831E00A49905 /* PLVSimpleDetailController.m */; };
		033AF0881FE9FA530068BC40 /* PLVToolbar.m in Sources */ = {isa = PBXBuildFile; fileRef = 033AF0871FE9FA530068BC40 /* PLVToolbar.m */; };
		034271D020492836002F4475 /* UIControl+PLVVod.m in Sources */ = {isa = PBXBuildFile; fileRef = 034271CF20492836002F4475 /* UIControl+PLVVod.m */; };
		034B55461FC926520031A875 /* PLVTitleHeaderReusableView.m in Sources */ = {isa = PBXBuildFile; fileRef = 034B55451FC926520031A875 /* PLVTitleHeaderReusableView.m */; };
		03540B871FBC408400CCC7E6 /* PLVCourseBannerReusableView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03540B861FBC408400CCC7E6 /* PLVCourseBannerReusableView.m */; };
		0356CD051FCC383700B522A1 /* PLVCourseVideoListController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0356CD041FCC383700B522A1 /* PLVCourseVideoListController.m */; };
		0356CD081FCC387400B522A1 /* PLVCourseIntroductionController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0356CD071FCC387400B522A1 /* PLVCourseIntroductionController.m */; };
		03722E2F1FEB65DC00031180 /* PLVVodExamViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 03722E2D1FEB65DC00031180 /* PLVVodExamViewController.m */; };
		03722E311FEB664700031180 /* PLVVodExamViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 03722E301FEB664700031180 /* PLVVodExamViewController.xib */; };
		03722E3B1FEB90DB00031180 /* PLVVodQuestionView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03722E3A1FEB90DB00031180 /* PLVVodQuestionView.m */; };
		03722E3E1FEB910300031180 /* PLVVodExplanationView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03722E3D1FEB910300031180 /* PLVVodExplanationView.m */; };
		037C21121FECB56D008CD03C /* PLVVodQuestion.m in Sources */ = {isa = PBXBuildFile; fileRef = 037C21111FECB56D008CD03C /* PLVVodQuestion.m */; };
		03856C4C1FE21C8B0038DEB2 /* PLVVodResources.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 03856C4B1FE21C8A0038DEB2 /* PLVVodResources.xcassets */; };
		03856C691FE21C990038DEB2 /* PLVVodPlayerSkin.xib in Resources */ = {isa = PBXBuildFile; fileRef = 03856C501FE21C990038DEB2 /* PLVVodPlayerSkin.xib */; };
		03856C6A1FE21C990038DEB2 /* PLVVodPlayerSkin.m in Sources */ = {isa = PBXBuildFile; fileRef = 03856C511FE21C990038DEB2 /* PLVVodPlayerSkin.m */; };
		03856C6B1FE21C990038DEB2 /* PLVVodDanmuSendView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03856C531FE21C990038DEB2 /* PLVVodDanmuSendView.m */; };
		03856C6C1FE21C990038DEB2 /* PLVVodFullscreenView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03856C561FE21C990038DEB2 /* PLVVodFullscreenView.m */; };
		03856C6D1FE21C990038DEB2 /* PLVVodSettingPanelView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03856C581FE21C990038DEB2 /* PLVVodSettingPanelView.m */; };
		03856C6E1FE21C990038DEB2 /* PLVVodShrinkscreenView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03856C591FE21C990038DEB2 /* PLVVodShrinkscreenView.m */; };
		03856C6F1FE21C990038DEB2 /* PLVVodSkinPlayerController.m in Sources */ = {isa = PBXBuildFile; fileRef = 03856C5B1FE21C990038DEB2 /* PLVVodSkinPlayerController.m */; };
		03856C701FE21C990038DEB2 /* UIColor+PLVVod.m in Sources */ = {isa = PBXBuildFile; fileRef = 03856C631FE21C990038DEB2 /* UIColor+PLVVod.m */; };
		03856C711FE21C990038DEB2 /* UINavigationController+PLVVod.m in Sources */ = {isa = PBXBuildFile; fileRef = 03856C641FE21C990038DEB2 /* UINavigationController+PLVVod.m */; };
		03856C741FE21C990038DEB2 /* PLVVodDanmu+PLVVod.m in Sources */ = {isa = PBXBuildFile; fileRef = 03856C671FE21C990038DEB2 /* PLVVodDanmu+PLVVod.m */; };
		03856CA31FE22A4D0038DEB2 /* PLVVodAccountVideo.m in Sources */ = {isa = PBXBuildFile; fileRef = 03856CA21FE22A4D0038DEB2 /* PLVVodAccountVideo.m */; };
		03856CA61FE22D090038DEB2 /* NSString+PLVVod.m in Sources */ = {isa = PBXBuildFile; fileRef = 03856CA51FE22D090038DEB2 /* NSString+PLVVod.m */; };
		03C9D44F1FF23D4B006F460C /* PLVVodDefinitionPanelView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03C9D44E1FF23D4B006F460C /* PLVVodDefinitionPanelView.m */; };
		03C9D4551FF255B0006F460C /* PLVVodPlaybackRatePanelView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03C9D4541FF255B0006F460C /* PLVVodPlaybackRatePanelView.m */; };
		03CB8C601FB3EB220048D9FC /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 03CB8C5F1FB3EB220048D9FC /* AppDelegate.m */; };
		03CB8C781FB3EB220048D9FC /* PolyvVodSDKDemoTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 03CB8C771FB3EB220048D9FC /* PolyvVodSDKDemoTests.m */; };
		03CB8C831FB3EB220048D9FC /* PolyvVodSDKDemoUITests.m in Sources */ = {isa = PBXBuildFile; fileRef = 03CB8C821FB3EB220048D9FC /* PolyvVodSDKDemoUITests.m */; };
		03DDF4C81FE763BB00F63630 /* DLLRUCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 03DDF4B81FE763BB00F63630 /* DLLRUCache.m */; };
		03DDF4C91FE763BB00F63630 /* DLTabedSlideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03DDF4BE1FE763BB00F63630 /* DLTabedSlideView.m */; };
		03DDF4CA1FE763BB00F63630 /* DLScrollTabbarView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03DDF4C31FE763BB00F63630 /* DLScrollTabbarView.m */; };
		03DDF4CB1FE763BB00F63630 /* DLFixedTabbarView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03DDF4C41FE763BB00F63630 /* DLFixedTabbarView.m */; };
		03DDF4CC1FE763BB00F63630 /* DLUtility.m in Sources */ = {isa = PBXBuildFile; fileRef = 03DDF4C51FE763BB00F63630 /* DLUtility.m */; };
		03DDF4CD1FE763BB00F63630 /* DLCustomSlideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03DDF4C61FE763BB00F63630 /* DLCustomSlideView.m */; };
		03DDF4CE1FE763BB00F63630 /* DLSlideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03DDF4C71FE763BB00F63630 /* DLSlideView.m */; };
		03EA41C91FBD36F500BA68AD /* PLVCourseCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 03EA41C71FBD36F500BA68AD /* PLVCourseCell.m */; };
		03F04E2A204D4A8700CA0A4F /* PLVVodVidTestController.m in Sources */ = {isa = PBXBuildFile; fileRef = 03F04E29204D4A8700CA0A4F /* PLVVodVidTestController.m */; };
		03F899721FF3AA450091AC89 /* PLVVodGestureIndicatorView.m in Sources */ = {isa = PBXBuildFile; fileRef = 03F899711FF3AA450091AC89 /* PLVVodGestureIndicatorView.m */; };
		040FD1A4218ADFA000F6460B /* PLVVodErrorUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 040FD1A3218ADFA000F6460B /* PLVVodErrorUtil.m */; };
		041B5D6D21BE59B000FC7941 /* PLVVodLockScreenView.m in Sources */ = {isa = PBXBuildFile; fileRef = 041B5D6C21BE59B000FC7941 /* PLVVodLockScreenView.m */; };
		0427C524219FD01E00B2C910 /* PLVVodDownloadHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 0427C522219FD01E00B2C910 /* PLVVodDownloadHelper.m */; };
		0427C525219FD01E00B2C910 /* plv_bg_voice.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0427C523219FD01E00B2C910 /* plv_bg_voice.mp3 */; };
		042F08D72D2BD1030059FF85 /* PLVVodHeatMapModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 042F08D62D2BD1030059FF85 /* PLVVodHeatMapModel.m */; };
		042F08DA2D2BE3420059FF85 /* PLVVodMarkerViewData.m in Sources */ = {isa = PBXBuildFile; fileRef = 042F08D92D2BE3420059FF85 /* PLVVodMarkerViewData.m */; };
		0430A41321EC4237001B8942 /* PLVVodPlayTipsView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0430A41221EC4237001B8942 /* PLVVodPlayTipsView.m */; };
		043B5D4C2D365F0C00C300EF /* PLVVodHeatMapContentView.m in Sources */ = {isa = PBXBuildFile; fileRef = 043B5D4B2D365F0C00C300EF /* PLVVodHeatMapContentView.m */; };
		043C804A2DA6544300DF5938 /* PLVVodOptimizeOptionsPanelView.m in Sources */ = {isa = PBXBuildFile; fileRef = 043C80492DA6544300DF5938 /* PLVVodOptimizeOptionsPanelView.m */; };
		043C804D2DA655CC00DF5938 /* PLVVodOptimizeOptionView.m in Sources */ = {isa = PBXBuildFile; fileRef = 043C804C2DA655CC00DF5938 /* PLVVodOptimizeOptionView.m */; };
		043D44712193ECA100C2F776 /* PLVVodServiceUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 043D44702193ECA100C2F776 /* PLVVodServiceUtil.m */; };
		043D44742194098400C2F776 /* PLVUserVideoListResult.m in Sources */ = {isa = PBXBuildFile; fileRef = 043D44732194098400C2F776 /* PLVUserVideoListResult.m */; };
		04422D502CA6A9FA004B35B3 /* PLVSecureView.m in Sources */ = {isa = PBXBuildFile; fileRef = 04422D4E2CA6A9F9004B35B3 /* PLVSecureView.m */; };
		04497D4628D1747D00A17409 /* PLVVodVideoToolBoxPanelView.m in Sources */ = {isa = PBXBuildFile; fileRef = 04497D4528D1747D00A17409 /* PLVVodVideoToolBoxPanelView.m */; };
		0463BAE821A52FBB0010A5EC /* PLVVodDBManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 0463BAE421A52FBB0010A5EC /* PLVVodDBManager.mm */; };
		0463BAE921A52FBB0010A5EC /* PLVVodExtendVideoInfo.mm in Sources */ = {isa = PBXBuildFile; fileRef = 0463BAE621A52FBB0010A5EC /* PLVVodExtendVideoInfo.mm */; };
		046559632892837F00F26E5E /* PLVCastManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 046559622892837F00F26E5E /* PLVCastManager.m */; };
		047497692D23F8D6002781EE /* PLVVodHeatMapView.m in Sources */ = {isa = PBXBuildFile; fileRef = 047497682D23F8D6002781EE /* PLVVodHeatMapView.m */; };
		0474976C2D23F909002781EE /* PLVVodBubbleMarkerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0474976B2D23F909002781EE /* PLVVodBubbleMarkerView.m */; };
		0474976F2D23F927002781EE /* PLVVodProgressMarkerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0474976E2D23F927002781EE /* PLVVodProgressMarkerView.m */; };
		04851978219995A1008058A6 /* PLVVideoPlayTimesResult.m in Sources */ = {isa = PBXBuildFile; fileRef = 04851977219995A1008058A6 /* PLVVideoPlayTimesResult.m */; };
		0489AFDD21077559004A26A4 /* PLVDownloadProcessingCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0489AFDC21077559004A26A4 /* PLVDownloadProcessingCell.m */; };
		0489AFE02107757E004A26A4 /* PLVDownloadComleteCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0489AFDF2107757E004A26A4 /* PLVDownloadComleteCell.m */; };
		04B051CD2106BDD500F6C791 /* PLVDownloadManagerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 04B051CC2106BDD500F6C791 /* PLVDownloadManagerViewController.m */; };
		04B051D02106CE9600F6C791 /* PLVDownloadProcessingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 04B051CF2106CE9600F6C791 /* PLVDownloadProcessingViewController.m */; };
		04B051D32106CEC000F6C791 /* PLVDownloadCompleteViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 04B051D22106CEC000F6C791 /* PLVDownloadCompleteViewController.m */; };
		04D4038D2127BA3C002B16CC /* PLVDownloadCompleteInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 04D4038C2127BA3C002B16CC /* PLVDownloadCompleteInfoModel.m */; };
		04E5BE75221577C600A8BCC9 /* PLVVodRouteLineView.m in Sources */ = {isa = PBXBuildFile; fileRef = 04E5BE73221577C500A8BCC9 /* PLVVodRouteLineView.m */; };
		04F57DB92136827D001AD451 /* PLVPlayQueueBackgroundController.m in Sources */ = {isa = PBXBuildFile; fileRef = 04F57DB82136827D001AD451 /* PLVPlayQueueBackgroundController.m */; };
		04F9F0F020EF51D000D204EA /* UIButton+EnlargeTouchArea.m in Sources */ = {isa = PBXBuildFile; fileRef = 04F9F0EF20EF51D000D204EA /* UIButton+EnlargeTouchArea.m */; };
		04FBDFB22DB0934500D548B7 /* PLVVodNetworkPlayErrorTipsView.m in Sources */ = {isa = PBXBuildFile; fileRef = 04FBDFB12DB0934500D548B7 /* PLVVodNetworkPlayErrorTipsView.m */; };
		1233FED526A56D7D006AAA53 /* PLVVodDefinitionTipsView.m in Sources */ = {isa = PBXBuildFile; fileRef = 1233FED426A56D7D006AAA53 /* PLVVodDefinitionTipsView.m */; };
		125873F025C295030040B87A /* PLVFillBlankQuestionView.m in Sources */ = {isa = PBXBuildFile; fileRef = 125873EF25C295030040B87A /* PLVFillBlankQuestionView.m */; };
		1258745025C2C3DF0040B87A /* PLVFillBlankView.m in Sources */ = {isa = PBXBuildFile; fileRef = 1258744F25C2C3DF0040B87A /* PLVFillBlankView.m */; };
		125AC98225C7B0300043AB55 /* PLVOptionView.m in Sources */ = {isa = PBXBuildFile; fileRef = 125AC98125C7B0300043AB55 /* PLVOptionView.m */; };
		125C716025EF6BAA002E10CF /* PLVVodMarqueeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 125C715F25EF6BAA002E10CF /* PLVVodMarqueeModel.m */; };
		125C716925EF6BBC002E10CF /* PLVVodMarqueeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 125C716825EF6BBC002E10CF /* PLVVodMarqueeView.m */; };
		125C716F25EF6BD1002E10CF /* PLVVodMarqueeAnimationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 125C716E25EF6BD1002E10CF /* PLVVodMarqueeAnimationManager.m */; };
		125D6C812600439F00D7F305 /* PLVVodSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 125D6C802600439F00D7F305 /* PLVVodSDK.framework */; };
		125D6C87260043A400D7F305 /* PLVVodUploadSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 125D6C86260043A400D7F305 /* PLVVodUploadSDK.framework */; };
		1263823C26C0BBAD0009C006 /* PLVKnowledgeListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1263823B26C0BBAD0009C006 /* PLVKnowledgeListViewController.m */; };
		1263824F26C0BF5E0009C006 /* PLVSlideTabbarView.m in Sources */ = {isa = PBXBuildFile; fileRef = 1263824E26C0BF5E0009C006 /* PLVSlideTabbarView.m */; };
		1263829A26C1017D0009C006 /* PLVKnowledgeCategoryTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 1263829926C1017D0009C006 /* PLVKnowledgeCategoryTableViewCell.m */; };
		126382A326C109F40009C006 /* PLVKnowledgePointTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 126382A226C109F40009C006 /* PLVKnowledgePointTableViewCell.m */; };
		126382B226C112280009C006 /* PLVKnowledgeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 126382B126C112280009C006 /* PLVKnowledgeModel.m */; };
		126690CE26244A8100DE371D /* PLVVodToast.m in Sources */ = {isa = PBXBuildFile; fileRef = 126690CD26244A8100DE371D /* PLVVodToast.m */; };
		12671F5C26C2278000A50B95 /* PLVVodMarqueeLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = 12671F5A26C2278000A50B95 /* PLVVodMarqueeLabel.m */; };
		34CEEED9D17D01FD2C68E51B /* libPods-vod-PolyvVodSDKDemo.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 57339ACDF536EE629635D325 /* libPods-vod-PolyvVodSDKDemo.a */; };
		6307224521C79917003B2526 /* PLVCastServiceListView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6307224421C79917003B2526 /* PLVCastServiceListView.m */; };
		6307224821C8DFC8003B2526 /* PLVCastControllView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6307224721C8DFC8003B2526 /* PLVCastControllView.m */; };
		6342327B224DF13A00AB856D /* PLVVodExamTestData.json in Resources */ = {isa = PBXBuildFile; fileRef = 6342327A224DF13A00AB856D /* PLVVodExamTestData.json */; };
		636AAB07223A6E7A00EFC3E4 /* PLVVodNetworkTipsView.m in Sources */ = {isa = PBXBuildFile; fileRef = 636AAB06223A6E7A00EFC3E4 /* PLVVodNetworkTipsView.m */; };
		63745ACA21D1D606007408C1 /* PLVVodCoverView.m in Sources */ = {isa = PBXBuildFile; fileRef = 63745AC921D1D606007408C1 /* PLVVodCoverView.m */; };
		639D17E421E3579B005E00F3 /* PLVCastBusinessManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 639D17E321E3579B005E00F3 /* PLVCastBusinessManager.m */; };
		65A57F472BFC30E30038F788 /* PLVVodSubtitleManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 65A57F3E2BFC30E30038F788 /* PLVVodSubtitleManager.m */; };
		65A57F482BFC30E30038F788 /* PLVVodSubtitleViewModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65A57F402BFC30E30038F788 /* PLVVodSubtitleViewModel.m */; };
		65A57F492BFC30E30038F788 /* PLVVodSubtitleParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 65A57F442BFC30E30038F788 /* PLVVodSubtitleParser.m */; };
		65A57F4A2BFC30E30038F788 /* PLVVodSubtitleItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 65A57F452BFC30E30038F788 /* PLVVodSubtitleItem.m */; };
		DA00893D20BC0F44006CEBBE /* PLVVodAudioCoverPanelView.m in Sources */ = {isa = PBXBuildFile; fileRef = DA00893C20BC0F44006CEBBE /* PLVVodAudioCoverPanelView.m */; };
		************************ /* PLVPictureInPicturePlaceholderView.m in Sources */ = {isa = PBXBuildFile; fileRef = FA11CDFA280E5F960035AB42 /* PLVPictureInPicturePlaceholderView.m */; };
		FAD2A8BB2800149400354955 /* PLVPictureInPictureRestoreManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FAD2A8BA2800149400354955 /* PLVPictureInPictureRestoreManager.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		03CB8C741FB3EB220048D9FC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 03CB8C531FB3EB220048D9FC /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 03CB8C5A1FB3EB220048D9FC;
			remoteInfo = PolyvVodSDKDemo;
		};
		03CB8C7F1FB3EB220048D9FC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 03CB8C531FB3EB220048D9FC /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 03CB8C5A1FB3EB220048D9FC;
			remoteInfo = PolyvVodSDKDemo;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		04655960289280B600F26E5E /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		000935B822E9AF4500933F66 /* PLVPPTSimpleDetailController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPPTSimpleDetailController.h; sourceTree = "<group>"; };
		000935B922E9AF4500933F66 /* PLVPPTSimpleDetailController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPPTSimpleDetailController.m; sourceTree = "<group>"; };
		000935BD22E9BD3D00933F66 /* PLVVodPPTViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodPPTViewController.m; sourceTree = "<group>"; };
		000935BE22E9BD3D00933F66 /* PLVVodPPTViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodPPTViewController.h; sourceTree = "<group>"; };
		000935C022E9BD3D00933F66 /* PLVFloatingView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFloatingView.m; sourceTree = "<group>"; };
		000935C122E9BD3D00933F66 /* PLVFloatingView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFloatingView.h; sourceTree = "<group>"; };
		000935D522EA88BB00933F66 /* PLVPPTBaseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPPTBaseViewController.h; sourceTree = "<group>"; };
		000935D622EA88BB00933F66 /* PLVPPTBaseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPPTBaseViewController.m; sourceTree = "<group>"; };
		000935D822EAA48800933F66 /* PLVPPTVideoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPPTVideoViewController.h; sourceTree = "<group>"; };
		000935D922EAA48800933F66 /* PLVPPTVideoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPPTVideoViewController.m; sourceTree = "<group>"; };
		00154ED3226979B2009E36D7 /* PLVUploadTableViewModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUploadTableViewModel.h; sourceTree = "<group>"; };
		00154ED4226979B2009E36D7 /* PLVUploadTableViewModel.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVUploadTableViewModel.mm; sourceTree = "<group>"; };
		0019F4572416168A00B3E65D /* PLVVodFastForwardView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodFastForwardView.h; sourceTree = "<group>"; };
		0019F4582416168A00B3E65D /* PLVVodFastForwardView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodFastForwardView.m; sourceTree = "<group>"; };
		0030335222B101E00087E76A /* PLVDownloadCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadCell.h; sourceTree = "<group>"; };
		0030335322B101E00087E76A /* PLVDownloadCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVDownloadCell.m; sourceTree = "<group>"; };
		0030335522B1047E0087E76A /* PLVUploadCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUploadCell.h; sourceTree = "<group>"; };
		0030335622B1047E0087E76A /* PLVUploadCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVUploadCell.m; sourceTree = "<group>"; };
		0030335C22B1E5660087E76A /* PLVVodNetworkUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodNetworkUtil.h; sourceTree = "<group>"; };
		0030335D22B1E5660087E76A /* PLVVodNetworkUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodNetworkUtil.m; sourceTree = "<group>"; };
		0046DF4924E63E6500592F62 /* PLVVodUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodUtils.h; sourceTree = "<group>"; };
		0046DF4A24E63E6500592F62 /* PLVVodUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodUtils.m; sourceTree = "<group>"; };
		009869BC230BF4FB00AA32E3 /* PLVPPTSkinProgressView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPPTSkinProgressView.h; sourceTree = "<group>"; };
		009869BD230BF4FB00AA32E3 /* PLVPPTSkinProgressView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPPTSkinProgressView.m; sourceTree = "<group>"; };
		00A582D822642E8700207B4B /* PLVUploadTableViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUploadTableViewController.h; sourceTree = "<group>"; };
		00A582D922642E8700207B4B /* PLVUploadTableViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVUploadTableViewController.m; sourceTree = "<group>"; };
		00A582DB2265DB0500207B4B /* PLVUploadingCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUploadingCell.h; sourceTree = "<group>"; };
		00A582DC2265DB0500207B4B /* PLVUploadingCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVUploadingCell.m; sourceTree = "<group>"; };
		00A582DE2265DB1900207B4B /* PLVUploadedCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUploadedCell.h; sourceTree = "<group>"; };
		00A582DF2265DB1900207B4B /* PLVUploadedCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVUploadedCell.m; sourceTree = "<group>"; };
		00A582E12267031C00207B4B /* PLVUploadModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUploadModel.h; sourceTree = "<group>"; };
		00A582E22267031C00207B4B /* PLVUploadModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVUploadModel.m; sourceTree = "<group>"; };
		00A8166C230BE19D00EB61E5 /* PLVPPTControllerSkinView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPPTControllerSkinView.h; sourceTree = "<group>"; };
		00A8166D230BE19D00EB61E5 /* PLVPPTControllerSkinView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPPTControllerSkinView.m; sourceTree = "<group>"; };
		00AADCCA22F290560037A843 /* PLVPPTBaseViewControllerInternal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPPTBaseViewControllerInternal.h; sourceTree = "<group>"; };
		00AADCCE22F2F1400037A843 /* PLVPPTActionView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPPTActionView.h; sourceTree = "<group>"; };
		00AADCCF22F2F1400037A843 /* PLVPPTActionView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPPTActionView.m; sourceTree = "<group>"; };
		00B0B4942405080D0089495C /* PLVVFloatingPlayerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVFloatingPlayerViewController.h; sourceTree = "<group>"; };
		00B0B4952405080D0089495C /* PLVVFloatingPlayerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVFloatingPlayerViewController.m; sourceTree = "<group>"; };
		00B218752481144700ABDCCD /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		00B218762481144700ABDCCD /* PolyvVodSDKDemo.entitlements */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.entitlements; path = PolyvVodSDKDemo.entitlements; sourceTree = "<group>"; };
		00B218772481144700ABDCCD /* Settings.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = Settings.bundle; sourceTree = "<group>"; };
		00B218782481144700ABDCCD /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		00B218792481144700ABDCCD /* Main.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Main.storyboard; sourceTree = "<group>"; };
		00B2187A2481144700ABDCCD /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		00B2187B2481144700ABDCCD /* PolyvVodSDKDemo-Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "PolyvVodSDKDemo-Prefix.pch"; sourceTree = "<group>"; };
		00B2187C2481144700ABDCCD /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00B3AE542484DE95003225F3 /* PLVVodAccount.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodAccount.h; sourceTree = "<group>"; };
		00B3AE552484DE95003225F3 /* PLVVodAccount.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodAccount.m; sourceTree = "<group>"; };
		00B57E9422894360002E56C7 /* PLVUploadDataBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PLVUploadDataBase.h; path = PolyvVodSDKDemo/Classes/Upload/DataBase/PLVUploadDataBase.h; sourceTree = SOURCE_ROOT; };
		00B57E9522894361002E56C7 /* PLVUploadCompleteData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PLVUploadCompleteData.h; path = PolyvVodSDKDemo/Classes/Upload/DataBase/PLVUploadCompleteData.h; sourceTree = SOURCE_ROOT; };
		00B57E9722894361002E56C7 /* PLVUploadCompleteData.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = PLVUploadCompleteData.mm; path = PolyvVodSDKDemo/Classes/Upload/DataBase/PLVUploadCompleteData.mm; sourceTree = SOURCE_ROOT; };
		00B57E9822894361002E56C7 /* PLVUploadUncompleteData.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = PLVUploadUncompleteData.mm; path = PolyvVodSDKDemo/Classes/Upload/DataBase/PLVUploadUncompleteData.mm; sourceTree = SOURCE_ROOT; };
		00B57E9922894361002E56C7 /* PLVUploadUtil.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = PLVUploadUtil.mm; path = PolyvVodSDKDemo/Classes/Upload/Util/PLVUploadUtil.mm; sourceTree = SOURCE_ROOT; };
		00B57E9B22894361002E56C7 /* PLVUploadDataBase.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = PLVUploadDataBase.mm; path = PolyvVodSDKDemo/Classes/Upload/DataBase/PLVUploadDataBase.mm; sourceTree = SOURCE_ROOT; };
		00B57E9C22894361002E56C7 /* PLVUploadUncompleteData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PLVUploadUncompleteData.h; path = PolyvVodSDKDemo/Classes/Upload/DataBase/PLVUploadUncompleteData.h; sourceTree = SOURCE_ROOT; };
		00B57E9D22894361002E56C7 /* PLVUploadUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PLVUploadUtil.h; path = PolyvVodSDKDemo/Classes/Upload/Util/PLVUploadUtil.h; sourceTree = SOURCE_ROOT; };
		00B57EA5228C0C25002E56C7 /* PLVUploadToast.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUploadToast.h; sourceTree = "<group>"; };
		00B57EA6228C0C25002E56C7 /* PLVUploadToast.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVUploadToast.m; sourceTree = "<group>"; };
		00E111E922F7DC25005A8575 /* PLVPPTTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPPTTableViewCell.h; sourceTree = "<group>"; };
		00E111EA22F7DC25005A8575 /* PLVPPTTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPPTTableViewCell.m; sourceTree = "<group>"; };
		00E111EC22F82193005A8575 /* PLVPPTActionViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPPTActionViewCell.h; sourceTree = "<group>"; };
		00E111ED22F82193005A8575 /* PLVPPTActionViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPPTActionViewCell.m; sourceTree = "<group>"; };
		00E111EF22F91DA1005A8575 /* PLVEmptyPPTViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVEmptyPPTViewCell.h; sourceTree = "<group>"; };
		00E111F022F91DA1005A8575 /* PLVEmptyPPTViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVEmptyPPTViewCell.m; sourceTree = "<group>"; };
		00E111F222F930A4005A8575 /* PLVPPTLoadFailAlertView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPPTLoadFailAlertView.h; sourceTree = "<group>"; };
		00E111F322F930A4005A8575 /* PLVPPTLoadFailAlertView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPPTLoadFailAlertView.m; sourceTree = "<group>"; };
		00E111F522F958F9005A8575 /* PLVPPTFailView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPPTFailView.h; sourceTree = "<group>"; };
		00E111F622F958F9005A8575 /* PLVPPTFailView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPPTFailView.m; sourceTree = "<group>"; };
		00EAB30F2406760D0083C0E7 /* PLVVFloatingWindow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVFloatingWindow.h; sourceTree = "<group>"; };
		00EAB3102406760D0083C0E7 /* PLVVFloatingWindow.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVFloatingWindow.m; sourceTree = "<group>"; };
		00EAB3122407737F0083C0E7 /* PLVVFloatingWindowSkin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVFloatingWindowSkin.h; sourceTree = "<group>"; };
		00EAB3132407737F0083C0E7 /* PLVVFloatingWindowSkin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVFloatingWindowSkin.m; sourceTree = "<group>"; };
		00EAB32E2407B8C80083C0E7 /* PLVVFloatingWindowViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVFloatingWindowViewController.h; sourceTree = "<group>"; };
		00EAB32F2407B8C80083C0E7 /* PLVVFloatingWindowViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVFloatingWindowViewController.m; sourceTree = "<group>"; };
		030DF02A1FB590560035D0B0 /* PLVCourseListController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCourseListController.h; sourceTree = "<group>"; };
		030DF02B1FB590560035D0B0 /* PLVCourseListController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCourseListController.m; sourceTree = "<group>"; };
		030DF02D1FB590910035D0B0 /* PLVCourseDetailController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCourseDetailController.h; sourceTree = "<group>"; };
		030DF02E1FB590910035D0B0 /* PLVCourseDetailController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCourseDetailController.m; sourceTree = "<group>"; };
		030DF0341FB5960E0035D0B0 /* PLVCourseNetworking.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCourseNetworking.h; sourceTree = "<group>"; };
		030DF0351FB5960E0035D0B0 /* PLVCourseNetworking.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCourseNetworking.m; sourceTree = "<group>"; };
		030DF0381FB5BA9D0035D0B0 /* PLVSchool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVSchool.h; sourceTree = "<group>"; };
		030DF0391FB5BA9D0035D0B0 /* PLVSchool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVSchool.m; sourceTree = "<group>"; };
		031408591FB94DAF00D1424A /* Foundation+Log.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "Foundation+Log.m"; sourceTree = "<group>"; };
		0314085B1FB9507A00D1424A /* PLVCourse.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCourse.h; sourceTree = "<group>"; };
		0314085C1FB9507A00D1424A /* PLVCourse.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCourse.m; sourceTree = "<group>"; };
		0314085E1FB9806100D1424A /* PLVTeacher.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVTeacher.h; sourceTree = "<group>"; };
		0314085F1FB9806100D1424A /* PLVTeacher.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVTeacher.m; sourceTree = "<group>"; };
		0323727A1FCBC58C0001E904 /* PLVAccountVideoListController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVAccountVideoListController.h; sourceTree = "<group>"; };
		0323727B1FCBC58D0001E904 /* PLVAccountVideoListController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVAccountVideoListController.m; sourceTree = "<group>"; };
		0323727D1FCBE4690001E904 /* PLVVideoCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVideoCell.h; sourceTree = "<group>"; };
		0323727E1FCBE4690001E904 /* PLVVideoCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVideoCell.m; sourceTree = "<group>"; };
		032372801FCC1C570001E904 /* PLVLoadCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLoadCell.h; sourceTree = "<group>"; };
		032372811FCC1C570001E904 /* PLVLoadCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVLoadCell.m; sourceTree = "<group>"; };
		033153021FCD103F00CAE849 /* PLVCourseSection.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCourseSection.h; sourceTree = "<group>"; };
		033153031FCD103F00CAE849 /* PLVCourseSection.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCourseSection.m; sourceTree = "<group>"; };
		033153051FCD105400CAE849 /* PLVCourseVideo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCourseVideo.h; sourceTree = "<group>"; };
		033153061FCD105400CAE849 /* PLVCourseVideo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCourseVideo.m; sourceTree = "<group>"; };
		0334A0032068831E00A49905 /* PLVSimpleDetailController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVSimpleDetailController.h; sourceTree = "<group>"; };
		0334A0042068831E00A49905 /* PLVSimpleDetailController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVSimpleDetailController.m; sourceTree = "<group>"; };
		033AF0861FE9FA530068BC40 /* PLVToolbar.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVToolbar.h; sourceTree = "<group>"; };
		033AF0871FE9FA530068BC40 /* PLVToolbar.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVToolbar.m; sourceTree = "<group>"; };
		034271CE20492836002F4475 /* UIControl+PLVVod.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIControl+PLVVod.h"; sourceTree = "<group>"; };
		034271CF20492836002F4475 /* UIControl+PLVVod.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIControl+PLVVod.m"; sourceTree = "<group>"; };
		034B55441FC926520031A875 /* PLVTitleHeaderReusableView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVTitleHeaderReusableView.h; sourceTree = "<group>"; };
		034B55451FC926520031A875 /* PLVTitleHeaderReusableView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVTitleHeaderReusableView.m; sourceTree = "<group>"; };
		03540B851FBC408400CCC7E6 /* PLVCourseBannerReusableView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCourseBannerReusableView.h; sourceTree = "<group>"; };
		03540B861FBC408400CCC7E6 /* PLVCourseBannerReusableView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCourseBannerReusableView.m; sourceTree = "<group>"; };
		0356CD031FCC383700B522A1 /* PLVCourseVideoListController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCourseVideoListController.h; sourceTree = "<group>"; };
		0356CD041FCC383700B522A1 /* PLVCourseVideoListController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCourseVideoListController.m; sourceTree = "<group>"; };
		0356CD061FCC387400B522A1 /* PLVCourseIntroductionController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCourseIntroductionController.h; sourceTree = "<group>"; };
		0356CD071FCC387400B522A1 /* PLVCourseIntroductionController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCourseIntroductionController.m; sourceTree = "<group>"; };
		03722E2D1FEB65DC00031180 /* PLVVodExamViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodExamViewController.m; sourceTree = "<group>"; };
		03722E2E1FEB65DC00031180 /* PLVVodExamViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodExamViewController.h; sourceTree = "<group>"; };
		03722E301FEB664700031180 /* PLVVodExamViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = PLVVodExamViewController.xib; sourceTree = "<group>"; };
		03722E391FEB90DB00031180 /* PLVVodQuestionView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodQuestionView.h; sourceTree = "<group>"; };
		03722E3A1FEB90DB00031180 /* PLVVodQuestionView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodQuestionView.m; sourceTree = "<group>"; };
		03722E3C1FEB910300031180 /* PLVVodExplanationView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodExplanationView.h; sourceTree = "<group>"; };
		03722E3D1FEB910300031180 /* PLVVodExplanationView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodExplanationView.m; sourceTree = "<group>"; };
		037C21101FECB56D008CD03C /* PLVVodQuestion.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodQuestion.h; sourceTree = "<group>"; };
		037C21111FECB56D008CD03C /* PLVVodQuestion.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodQuestion.m; sourceTree = "<group>"; };
		03856C4B1FE21C8A0038DEB2 /* PLVVodResources.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = PLVVodResources.xcassets; sourceTree = "<group>"; };
		03856C4E1FE21C990038DEB2 /* PLVVodPlayerSkin.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodPlayerSkin.h; sourceTree = "<group>"; };
		03856C4F1FE21C990038DEB2 /* PLVVodSkinPlayerController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodSkinPlayerController.h; sourceTree = "<group>"; };
		03856C501FE21C990038DEB2 /* PLVVodPlayerSkin.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = PLVVodPlayerSkin.xib; sourceTree = "<group>"; };
		03856C511FE21C990038DEB2 /* PLVVodPlayerSkin.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodPlayerSkin.m; sourceTree = "<group>"; };
		03856C531FE21C990038DEB2 /* PLVVodDanmuSendView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodDanmuSendView.m; sourceTree = "<group>"; };
		03856C541FE21C990038DEB2 /* PLVVodShrinkscreenView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodShrinkscreenView.h; sourceTree = "<group>"; };
		03856C551FE21C990038DEB2 /* PLVVodSettingPanelView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodSettingPanelView.h; sourceTree = "<group>"; };
		03856C561FE21C990038DEB2 /* PLVVodFullscreenView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodFullscreenView.m; sourceTree = "<group>"; };
		03856C571FE21C990038DEB2 /* PLVVodDanmuSendView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodDanmuSendView.h; sourceTree = "<group>"; };
		03856C581FE21C990038DEB2 /* PLVVodSettingPanelView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodSettingPanelView.m; sourceTree = "<group>"; };
		03856C591FE21C990038DEB2 /* PLVVodShrinkscreenView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodShrinkscreenView.m; sourceTree = "<group>"; };
		03856C5A1FE21C990038DEB2 /* PLVVodFullscreenView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodFullscreenView.h; sourceTree = "<group>"; };
		03856C5B1FE21C990038DEB2 /* PLVVodSkinPlayerController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodSkinPlayerController.m; sourceTree = "<group>"; };
		03856C5D1FE21C990038DEB2 /* UIColor+PLVVod.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIColor+PLVVod.h"; sourceTree = "<group>"; };
		03856C5E1FE21C990038DEB2 /* UINavigationController+PLVVod.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UINavigationController+PLVVod.h"; sourceTree = "<group>"; };
		03856C5F1FE21C990038DEB2 /* PLVVodDanmu+PLVVod.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "PLVVodDanmu+PLVVod.h"; sourceTree = "<group>"; };
		03856C631FE21C990038DEB2 /* UIColor+PLVVod.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIColor+PLVVod.m"; sourceTree = "<group>"; };
		03856C641FE21C990038DEB2 /* UINavigationController+PLVVod.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UINavigationController+PLVVod.m"; sourceTree = "<group>"; };
		03856C671FE21C990038DEB2 /* PLVVodDanmu+PLVVod.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "PLVVodDanmu+PLVVod.m"; sourceTree = "<group>"; };
		03856CA11FE22A4D0038DEB2 /* PLVVodAccountVideo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodAccountVideo.h; sourceTree = "<group>"; };
		03856CA21FE22A4D0038DEB2 /* PLVVodAccountVideo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodAccountVideo.m; sourceTree = "<group>"; };
		03856CA41FE22D090038DEB2 /* NSString+PLVVod.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+PLVVod.h"; sourceTree = "<group>"; };
		03856CA51FE22D090038DEB2 /* NSString+PLVVod.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+PLVVod.m"; sourceTree = "<group>"; };
		03C9D44D1FF23D4B006F460C /* PLVVodDefinitionPanelView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodDefinitionPanelView.h; sourceTree = "<group>"; };
		03C9D44E1FF23D4B006F460C /* PLVVodDefinitionPanelView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodDefinitionPanelView.m; sourceTree = "<group>"; };
		03C9D4531FF255B0006F460C /* PLVVodPlaybackRatePanelView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodPlaybackRatePanelView.h; sourceTree = "<group>"; };
		03C9D4541FF255B0006F460C /* PLVVodPlaybackRatePanelView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodPlaybackRatePanelView.m; sourceTree = "<group>"; };
		03CB8C5B1FB3EB220048D9FC /* PolyvVodSDKDemo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PolyvVodSDKDemo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		03CB8C5E1FB3EB220048D9FC /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		03CB8C5F1FB3EB220048D9FC /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		03CB8C731FB3EB220048D9FC /* PolyvVodSDKDemoTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PolyvVodSDKDemoTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		03CB8C771FB3EB220048D9FC /* PolyvVodSDKDemoTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PolyvVodSDKDemoTests.m; sourceTree = "<group>"; };
		03CB8C791FB3EB220048D9FC /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		03CB8C7E1FB3EB220048D9FC /* PolyvVodSDKDemoUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PolyvVodSDKDemoUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		03CB8C821FB3EB220048D9FC /* PolyvVodSDKDemoUITests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PolyvVodSDKDemoUITests.m; sourceTree = "<group>"; };
		03CB8C841FB3EB220048D9FC /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		03CB8C921FB3EDC80048D9FC /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; name = README.md; path = ../README.md; sourceTree = "<group>"; };
		03DDF4B61FE763BB00F63630 /* DLTabedSlideView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DLTabedSlideView.h; sourceTree = "<group>"; };
		03DDF4B81FE763BB00F63630 /* DLLRUCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DLLRUCache.m; sourceTree = "<group>"; };
		03DDF4B91FE763BB00F63630 /* DLCacheProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DLCacheProtocol.h; sourceTree = "<group>"; };
		03DDF4BA1FE763BB00F63630 /* DLLRUCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DLLRUCache.h; sourceTree = "<group>"; };
		03DDF4BB1FE763BB00F63630 /* DLUtility.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DLUtility.h; sourceTree = "<group>"; };
		03DDF4BC1FE763BB00F63630 /* DLSlideView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DLSlideView.h; sourceTree = "<group>"; };
		03DDF4BD1FE763BB00F63630 /* DLCustomSlideView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DLCustomSlideView.h; sourceTree = "<group>"; };
		03DDF4BE1FE763BB00F63630 /* DLTabedSlideView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DLTabedSlideView.m; sourceTree = "<group>"; };
		03DDF4C01FE763BB00F63630 /* DLSlideTabbarProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DLSlideTabbarProtocol.h; sourceTree = "<group>"; };
		03DDF4C11FE763BB00F63630 /* DLScrollTabbarView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DLScrollTabbarView.h; sourceTree = "<group>"; };
		03DDF4C21FE763BB00F63630 /* DLFixedTabbarView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DLFixedTabbarView.h; sourceTree = "<group>"; };
		03DDF4C31FE763BB00F63630 /* DLScrollTabbarView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DLScrollTabbarView.m; sourceTree = "<group>"; };
		03DDF4C41FE763BB00F63630 /* DLFixedTabbarView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DLFixedTabbarView.m; sourceTree = "<group>"; };
		03DDF4C51FE763BB00F63630 /* DLUtility.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DLUtility.m; sourceTree = "<group>"; };
		03DDF4C61FE763BB00F63630 /* DLCustomSlideView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DLCustomSlideView.m; sourceTree = "<group>"; };
		03DDF4C71FE763BB00F63630 /* DLSlideView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DLSlideView.m; sourceTree = "<group>"; };
		03EA41C61FBD36F500BA68AD /* PLVCourseCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCourseCell.h; sourceTree = "<group>"; };
		03EA41C71FBD36F500BA68AD /* PLVCourseCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCourseCell.m; sourceTree = "<group>"; };
		03F04E28204D4A8700CA0A4F /* PLVVodVidTestController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodVidTestController.h; sourceTree = "<group>"; };
		03F04E29204D4A8700CA0A4F /* PLVVodVidTestController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodVidTestController.m; sourceTree = "<group>"; };
		03F899701FF3AA450091AC89 /* PLVVodGestureIndicatorView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodGestureIndicatorView.h; sourceTree = "<group>"; };
		03F899711FF3AA450091AC89 /* PLVVodGestureIndicatorView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodGestureIndicatorView.m; sourceTree = "<group>"; };
		040FD1A2218ADFA000F6460B /* PLVVodErrorUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodErrorUtil.h; sourceTree = "<group>"; };
		040FD1A3218ADFA000F6460B /* PLVVodErrorUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodErrorUtil.m; sourceTree = "<group>"; };
		041B5D6B21BE59B000FC7941 /* PLVVodLockScreenView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodLockScreenView.h; sourceTree = "<group>"; };
		041B5D6C21BE59B000FC7941 /* PLVVodLockScreenView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodLockScreenView.m; sourceTree = "<group>"; };
		0427C521219FD01E00B2C910 /* PLVVodDownloadHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodDownloadHelper.h; sourceTree = "<group>"; };
		0427C522219FD01E00B2C910 /* PLVVodDownloadHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodDownloadHelper.m; sourceTree = "<group>"; };
		0427C523219FD01E00B2C910 /* plv_bg_voice.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = plv_bg_voice.mp3; sourceTree = "<group>"; };
		042F08D52D2BD1030059FF85 /* PLVVodHeatMapModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodHeatMapModel.h; sourceTree = "<group>"; };
		042F08D62D2BD1030059FF85 /* PLVVodHeatMapModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodHeatMapModel.m; sourceTree = "<group>"; };
		042F08D82D2BE3420059FF85 /* PLVVodMarkerViewData.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodMarkerViewData.h; sourceTree = "<group>"; };
		042F08D92D2BE3420059FF85 /* PLVVodMarkerViewData.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodMarkerViewData.m; sourceTree = "<group>"; };
		0430A41121EC4237001B8942 /* PLVVodPlayTipsView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodPlayTipsView.h; sourceTree = "<group>"; };
		0430A41221EC4237001B8942 /* PLVVodPlayTipsView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodPlayTipsView.m; sourceTree = "<group>"; };
		043B5D4A2D365F0C00C300EF /* PLVVodHeatMapContentView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodHeatMapContentView.h; sourceTree = "<group>"; };
		043B5D4B2D365F0C00C300EF /* PLVVodHeatMapContentView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodHeatMapContentView.m; sourceTree = "<group>"; };
		043C80482DA6544300DF5938 /* PLVVodOptimizeOptionsPanelView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodOptimizeOptionsPanelView.h; sourceTree = "<group>"; };
		043C80492DA6544300DF5938 /* PLVVodOptimizeOptionsPanelView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodOptimizeOptionsPanelView.m; sourceTree = "<group>"; };
		043C804B2DA655CC00DF5938 /* PLVVodOptimizeOptionView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodOptimizeOptionView.h; sourceTree = "<group>"; };
		043C804C2DA655CC00DF5938 /* PLVVodOptimizeOptionView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodOptimizeOptionView.m; sourceTree = "<group>"; };
		043D446F2193ECA100C2F776 /* PLVVodServiceUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodServiceUtil.h; sourceTree = "<group>"; };
		043D44702193ECA100C2F776 /* PLVVodServiceUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodServiceUtil.m; sourceTree = "<group>"; };
		043D44722194098400C2F776 /* PLVUserVideoListResult.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVUserVideoListResult.h; sourceTree = "<group>"; };
		043D44732194098400C2F776 /* PLVUserVideoListResult.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVUserVideoListResult.m; sourceTree = "<group>"; };
		04422D4E2CA6A9F9004B35B3 /* PLVSecureView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVSecureView.m; sourceTree = "<group>"; };
		04422D4F2CA6A9F9004B35B3 /* PLVSecureView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVSecureView.h; sourceTree = "<group>"; };
		04497D4428D1747D00A17409 /* PLVVodVideoToolBoxPanelView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodVideoToolBoxPanelView.h; sourceTree = "<group>"; };
		04497D4528D1747D00A17409 /* PLVVodVideoToolBoxPanelView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodVideoToolBoxPanelView.m; sourceTree = "<group>"; };
		0463BAE321A52FBB0010A5EC /* PLVVodExtendVideoInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodExtendVideoInfo.h; sourceTree = "<group>"; };
		0463BAE421A52FBB0010A5EC /* PLVVodDBManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVVodDBManager.mm; sourceTree = "<group>"; };
		0463BAE521A52FBB0010A5EC /* PLVVodDBManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodDBManager.h; sourceTree = "<group>"; };
		0463BAE621A52FBB0010A5EC /* PLVVodExtendVideoInfo.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVVodExtendVideoInfo.mm; sourceTree = "<group>"; };
		046559612892837F00F26E5E /* PLVCastManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCastManager.h; sourceTree = "<group>"; };
		046559622892837F00F26E5E /* PLVCastManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCastManager.m; sourceTree = "<group>"; };
		047497672D23F8D6002781EE /* PLVVodHeatMapView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodHeatMapView.h; sourceTree = "<group>"; };
		047497682D23F8D6002781EE /* PLVVodHeatMapView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodHeatMapView.m; sourceTree = "<group>"; };
		0474976A2D23F909002781EE /* PLVVodBubbleMarkerView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodBubbleMarkerView.h; sourceTree = "<group>"; };
		0474976B2D23F909002781EE /* PLVVodBubbleMarkerView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodBubbleMarkerView.m; sourceTree = "<group>"; };
		0474976D2D23F927002781EE /* PLVVodProgressMarkerView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodProgressMarkerView.h; sourceTree = "<group>"; };
		0474976E2D23F927002781EE /* PLVVodProgressMarkerView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodProgressMarkerView.m; sourceTree = "<group>"; };
		04851976219995A1008058A6 /* PLVVideoPlayTimesResult.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVideoPlayTimesResult.h; sourceTree = "<group>"; };
		04851977219995A1008058A6 /* PLVVideoPlayTimesResult.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVideoPlayTimesResult.m; sourceTree = "<group>"; };
		0489AFDB21077559004A26A4 /* PLVDownloadProcessingCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadProcessingCell.h; sourceTree = "<group>"; };
		0489AFDC21077559004A26A4 /* PLVDownloadProcessingCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVDownloadProcessingCell.m; sourceTree = "<group>"; };
		0489AFDE2107757E004A26A4 /* PLVDownloadComleteCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadComleteCell.h; sourceTree = "<group>"; };
		0489AFDF2107757E004A26A4 /* PLVDownloadComleteCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVDownloadComleteCell.m; sourceTree = "<group>"; };
		04B051CB2106BDD500F6C791 /* PLVDownloadManagerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadManagerViewController.h; sourceTree = "<group>"; };
		04B051CC2106BDD500F6C791 /* PLVDownloadManagerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVDownloadManagerViewController.m; sourceTree = "<group>"; };
		04B051CE2106CE9600F6C791 /* PLVDownloadProcessingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadProcessingViewController.h; sourceTree = "<group>"; };
		04B051CF2106CE9600F6C791 /* PLVDownloadProcessingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVDownloadProcessingViewController.m; sourceTree = "<group>"; };
		04B051D12106CEC000F6C791 /* PLVDownloadCompleteViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadCompleteViewController.h; sourceTree = "<group>"; };
		04B051D22106CEC000F6C791 /* PLVDownloadCompleteViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVDownloadCompleteViewController.m; sourceTree = "<group>"; };
		04B051D82106EC4C00F6C791 /* GLKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GLKit.framework; path = System/Library/Frameworks/GLKit.framework; sourceTree = SDKROOT; };
		04D4038B2127BA3C002B16CC /* PLVDownloadCompleteInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadCompleteInfoModel.h; sourceTree = "<group>"; };
		04D4038C2127BA3C002B16CC /* PLVDownloadCompleteInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVDownloadCompleteInfoModel.m; sourceTree = "<group>"; };
		04E5BE73221577C500A8BCC9 /* PLVVodRouteLineView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodRouteLineView.m; sourceTree = "<group>"; };
		04E5BE74221577C500A8BCC9 /* PLVVodRouteLineView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodRouteLineView.h; sourceTree = "<group>"; };
		04F57DB72136827D001AD451 /* PLVPlayQueueBackgroundController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPlayQueueBackgroundController.h; sourceTree = "<group>"; };
		04F57DB82136827D001AD451 /* PLVPlayQueueBackgroundController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPlayQueueBackgroundController.m; sourceTree = "<group>"; };
		04F9F0EE20EF51D000D204EA /* UIButton+EnlargeTouchArea.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIButton+EnlargeTouchArea.h"; sourceTree = "<group>"; };
		04F9F0EF20EF51D000D204EA /* UIButton+EnlargeTouchArea.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIButton+EnlargeTouchArea.m"; sourceTree = "<group>"; };
		04FBDFB02DB0934500D548B7 /* PLVVodNetworkPlayErrorTipsView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodNetworkPlayErrorTipsView.h; sourceTree = "<group>"; };
		04FBDFB12DB0934500D548B7 /* PLVVodNetworkPlayErrorTipsView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodNetworkPlayErrorTipsView.m; sourceTree = "<group>"; };
		1233FED326A56D7D006AAA53 /* PLVVodDefinitionTipsView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodDefinitionTipsView.h; sourceTree = "<group>"; };
		1233FED426A56D7D006AAA53 /* PLVVodDefinitionTipsView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodDefinitionTipsView.m; sourceTree = "<group>"; };
		125873EE25C295030040B87A /* PLVFillBlankQuestionView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFillBlankQuestionView.h; sourceTree = "<group>"; };
		125873EF25C295030040B87A /* PLVFillBlankQuestionView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFillBlankQuestionView.m; sourceTree = "<group>"; };
		1258744E25C2C3DF0040B87A /* PLVFillBlankView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFillBlankView.h; sourceTree = "<group>"; };
		1258744F25C2C3DF0040B87A /* PLVFillBlankView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFillBlankView.m; sourceTree = "<group>"; };
		125AC98025C7B0300043AB55 /* PLVOptionView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVOptionView.h; sourceTree = "<group>"; };
		125AC98125C7B0300043AB55 /* PLVOptionView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVOptionView.m; sourceTree = "<group>"; };
		125C715E25EF6BAA002E10CF /* PLVVodMarqueeModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodMarqueeModel.h; sourceTree = "<group>"; };
		125C715F25EF6BAA002E10CF /* PLVVodMarqueeModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodMarqueeModel.m; sourceTree = "<group>"; };
		125C716725EF6BBC002E10CF /* PLVVodMarqueeView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodMarqueeView.h; sourceTree = "<group>"; };
		125C716825EF6BBC002E10CF /* PLVVodMarqueeView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodMarqueeView.m; sourceTree = "<group>"; };
		125C716D25EF6BD1002E10CF /* PLVVodMarqueeAnimationManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodMarqueeAnimationManager.h; sourceTree = "<group>"; };
		125C716E25EF6BD1002E10CF /* PLVVodMarqueeAnimationManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodMarqueeAnimationManager.m; sourceTree = "<group>"; };
		125D6C802600439F00D7F305 /* PLVVodSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = PLVVodSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		125D6C86260043A400D7F305 /* PLVVodUploadSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = PLVVodUploadSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1263823A26C0BBAD0009C006 /* PLVKnowledgeListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVKnowledgeListViewController.h; sourceTree = "<group>"; };
		1263823B26C0BBAD0009C006 /* PLVKnowledgeListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVKnowledgeListViewController.m; sourceTree = "<group>"; };
		1263824D26C0BF5E0009C006 /* PLVSlideTabbarView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVSlideTabbarView.h; sourceTree = "<group>"; };
		1263824E26C0BF5E0009C006 /* PLVSlideTabbarView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVSlideTabbarView.m; sourceTree = "<group>"; };
		1263829826C1017D0009C006 /* PLVKnowledgeCategoryTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVKnowledgeCategoryTableViewCell.h; sourceTree = "<group>"; };
		1263829926C1017D0009C006 /* PLVKnowledgeCategoryTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVKnowledgeCategoryTableViewCell.m; sourceTree = "<group>"; };
		126382A126C109F40009C006 /* PLVKnowledgePointTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVKnowledgePointTableViewCell.h; sourceTree = "<group>"; };
		126382A226C109F40009C006 /* PLVKnowledgePointTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVKnowledgePointTableViewCell.m; sourceTree = "<group>"; };
		126382B026C112280009C006 /* PLVKnowledgeModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVKnowledgeModel.h; sourceTree = "<group>"; };
		126382B126C112280009C006 /* PLVKnowledgeModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVKnowledgeModel.m; sourceTree = "<group>"; };
		126690CC26244A8100DE371D /* PLVVodToast.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodToast.h; sourceTree = "<group>"; };
		126690CD26244A8100DE371D /* PLVVodToast.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodToast.m; sourceTree = "<group>"; };
		12671F5A26C2278000A50B95 /* PLVVodMarqueeLabel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodMarqueeLabel.m; sourceTree = "<group>"; };
		12671F5B26C2278000A50B95 /* PLVVodMarqueeLabel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodMarqueeLabel.h; sourceTree = "<group>"; };
		2241869D6E30E88E7DF5E961 /* Pods-PolyvVodSDKDemo.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PolyvVodSDKDemo.release.xcconfig"; path = "Pods/Target Support Files/Pods-PolyvVodSDKDemo/Pods-PolyvVodSDKDemo.release.xcconfig"; sourceTree = "<group>"; };
		57339ACDF536EE629635D325 /* libPods-vod-PolyvVodSDKDemo.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-vod-PolyvVodSDKDemo.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		6307224321C79917003B2526 /* PLVCastServiceListView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCastServiceListView.h; sourceTree = "<group>"; };
		6307224421C79917003B2526 /* PLVCastServiceListView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCastServiceListView.m; sourceTree = "<group>"; };
		6307224621C8DFC7003B2526 /* PLVCastControllView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVCastControllView.h; sourceTree = "<group>"; };
		6307224721C8DFC8003B2526 /* PLVCastControllView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVCastControllView.m; sourceTree = "<group>"; };
		6342327A224DF13A00AB856D /* PLVVodExamTestData.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = PLVVodExamTestData.json; sourceTree = "<group>"; };
		636AAB05223A6E7A00EFC3E4 /* PLVVodNetworkTipsView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodNetworkTipsView.h; sourceTree = "<group>"; };
		636AAB06223A6E7A00EFC3E4 /* PLVVodNetworkTipsView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodNetworkTipsView.m; sourceTree = "<group>"; };
		63745AC821D1D606007408C1 /* PLVVodCoverView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodCoverView.h; sourceTree = "<group>"; };
		63745AC921D1D606007408C1 /* PLVVodCoverView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodCoverView.m; sourceTree = "<group>"; };
		639D17E221E3579B005E00F3 /* PLVCastBusinessManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVCastBusinessManager.h; sourceTree = "<group>"; };
		639D17E321E3579B005E00F3 /* PLVCastBusinessManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVCastBusinessManager.m; sourceTree = "<group>"; };
		65A57F3E2BFC30E30038F788 /* PLVVodSubtitleManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodSubtitleManager.m; sourceTree = "<group>"; };
		65A57F3F2BFC30E30038F788 /* PLVVodSubtitleViewModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodSubtitleViewModel.h; sourceTree = "<group>"; };
		65A57F402BFC30E30038F788 /* PLVVodSubtitleViewModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodSubtitleViewModel.m; sourceTree = "<group>"; };
		65A57F412BFC30E30038F788 /* PLVVodSubtitleManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodSubtitleManager.h; sourceTree = "<group>"; };
		65A57F432BFC30E30038F788 /* PLVVodSubtitleItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodSubtitleItem.h; sourceTree = "<group>"; };
		65A57F442BFC30E30038F788 /* PLVVodSubtitleParser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodSubtitleParser.m; sourceTree = "<group>"; };
		65A57F452BFC30E30038F788 /* PLVVodSubtitleItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodSubtitleItem.m; sourceTree = "<group>"; };
		65A57F462BFC30E30038F788 /* PLVVodSubtitleParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodSubtitleParser.h; sourceTree = "<group>"; };
		69CE5D17BD2E7FDDEF281119 /* Pods-vod-PolyvVodSDKDemo.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-vod-PolyvVodSDKDemo.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-vod-PolyvVodSDKDemo/Pods-vod-PolyvVodSDKDemo.debug.xcconfig"; sourceTree = "<group>"; };
		C9E2B2A6342299C4D21D81D8 /* Pods-vod-PolyvVodSDKDemo.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-vod-PolyvVodSDKDemo.release.xcconfig"; path = "../Pods/Target Support Files/Pods-vod-PolyvVodSDKDemo/Pods-vod-PolyvVodSDKDemo.release.xcconfig"; sourceTree = "<group>"; };
		DA00893B20BC0F44006CEBBE /* PLVVodAudioCoverPanelView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodAudioCoverPanelView.h; sourceTree = "<group>"; };
		DA00893C20BC0F44006CEBBE /* PLVVodAudioCoverPanelView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodAudioCoverPanelView.m; sourceTree = "<group>"; };
		E41F9D5C7BDF3E096A82E953 /* Pods-PolyvVodSDKDemo.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PolyvVodSDKDemo.debug.xcconfig"; path = "Pods/Target Support Files/Pods-PolyvVodSDKDemo/Pods-PolyvVodSDKDemo.debug.xcconfig"; sourceTree = "<group>"; };
		FA11CDF9280E5F960035AB42 /* PLVPictureInPicturePlaceholderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPictureInPicturePlaceholderView.h; sourceTree = "<group>"; };
		FA11CDFA280E5F960035AB42 /* PLVPictureInPicturePlaceholderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPictureInPicturePlaceholderView.m; sourceTree = "<group>"; };
		FAD2A8B92800149400354955 /* PLVPictureInPictureRestoreManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPictureInPictureRestoreManager.h; sourceTree = "<group>"; };
		FAD2A8BA2800149400354955 /* PLVPictureInPictureRestoreManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPictureInPictureRestoreManager.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		03CB8C581FB3EB220048D9FC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				125D6C812600439F00D7F305 /* PLVVodSDK.framework in Frameworks */,
				125D6C87260043A400D7F305 /* PLVVodUploadSDK.framework in Frameworks */,
				34CEEED9D17D01FD2C68E51B /* libPods-vod-PolyvVodSDKDemo.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		03CB8C701FB3EB220048D9FC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		03CB8C7B1FB3EB220048D9FC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		000935BB22E9BD3D00933F66 /* PPT */ = {
			isa = PBXGroup;
			children = (
				000935BC22E9BD3D00933F66 /* Controller */,
				000935BF22E9BD3D00933F66 /* View */,
			);
			path = PPT;
			sourceTree = "<group>";
		};
		000935BC22E9BD3D00933F66 /* Controller */ = {
			isa = PBXGroup;
			children = (
				000935BE22E9BD3D00933F66 /* PLVVodPPTViewController.h */,
				000935BD22E9BD3D00933F66 /* PLVVodPPTViewController.m */,
				000935D822EAA48800933F66 /* PLVPPTVideoViewController.h */,
				000935D922EAA48800933F66 /* PLVPPTVideoViewController.m */,
				000935D522EA88BB00933F66 /* PLVPPTBaseViewController.h */,
				000935D622EA88BB00933F66 /* PLVPPTBaseViewController.m */,
				00AADCCA22F290560037A843 /* PLVPPTBaseViewControllerInternal.h */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		000935BF22E9BD3D00933F66 /* View */ = {
			isa = PBXGroup;
			children = (
				000935C122E9BD3D00933F66 /* PLVFloatingView.h */,
				000935C022E9BD3D00933F66 /* PLVFloatingView.m */,
				00AADCCE22F2F1400037A843 /* PLVPPTActionView.h */,
				00AADCCF22F2F1400037A843 /* PLVPPTActionView.m */,
				00E111EC22F82193005A8575 /* PLVPPTActionViewCell.h */,
				00E111ED22F82193005A8575 /* PLVPPTActionViewCell.m */,
				00E111F222F930A4005A8575 /* PLVPPTLoadFailAlertView.h */,
				00E111F322F930A4005A8575 /* PLVPPTLoadFailAlertView.m */,
				00E111F522F958F9005A8575 /* PLVPPTFailView.h */,
				00E111F622F958F9005A8575 /* PLVPPTFailView.m */,
				00A8166C230BE19D00EB61E5 /* PLVPPTControllerSkinView.h */,
				00A8166D230BE19D00EB61E5 /* PLVPPTControllerSkinView.m */,
				009869BC230BF4FB00AA32E3 /* PLVPPTSkinProgressView.h */,
				009869BD230BF4FB00AA32E3 /* PLVPPTSkinProgressView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		004D0F9D226DB0A50088C076 /* DataBase */ = {
			isa = PBXGroup;
			children = (
				00B57E9422894360002E56C7 /* PLVUploadDataBase.h */,
				00B57E9B22894361002E56C7 /* PLVUploadDataBase.mm */,
				00B57E9522894361002E56C7 /* PLVUploadCompleteData.h */,
				00B57E9722894361002E56C7 /* PLVUploadCompleteData.mm */,
				00B57E9C22894361002E56C7 /* PLVUploadUncompleteData.h */,
				00B57E9822894361002E56C7 /* PLVUploadUncompleteData.mm */,
			);
			name = DataBase;
			path = ../DataBase;
			sourceTree = "<group>";
		};
		00B0B497240516AE0089495C /* Floating */ = {
			isa = PBXGroup;
			children = (
				FA11CDF8280E5F2E0035AB42 /* View */,
				00EAB3312407C3A80083C0E7 /* FloatingWindow */,
				00B0B4942405080D0089495C /* PLVVFloatingPlayerViewController.h */,
				00B0B4952405080D0089495C /* PLVVFloatingPlayerViewController.m */,
				FAD2A8B92800149400354955 /* PLVPictureInPictureRestoreManager.h */,
				FAD2A8BA2800149400354955 /* PLVPictureInPictureRestoreManager.m */,
			);
			path = Floating;
			sourceTree = "<group>";
		};
		00B218742481144700ABDCCD /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00B218752481144700ABDCCD /* LaunchScreen.storyboard */,
				00B218762481144700ABDCCD /* PolyvVodSDKDemo.entitlements */,
				00B218772481144700ABDCCD /* Settings.bundle */,
				00B218782481144700ABDCCD /* Assets.xcassets */,
				00B218792481144700ABDCCD /* Main.storyboard */,
				00B2187A2481144700ABDCCD /* main.m */,
				00B2187B2481144700ABDCCD /* PolyvVodSDKDemo-Prefix.pch */,
				00B2187C2481144700ABDCCD /* Info.plist */,
			);
			path = "Supporting Files";
			sourceTree = "<group>";
		};
		00B57E8F22894244002E56C7 /* Upload */ = {
			isa = PBXGroup;
			children = (
				00B57EA4228BFD7A002E56C7 /* Util */,
				004D0F9D226DB0A50088C076 /* DataBase */,
				00B57E9022894279002E56C7 /* Model */,
				00B57E91228942D7002E56C7 /* View */,
				00B57E92228942FE002E56C7 /* ViewModel */,
				00B57E9322894323002E56C7 /* Controller */,
			);
			path = Upload;
			sourceTree = "<group>";
		};
		00B57E9022894279002E56C7 /* Model */ = {
			isa = PBXGroup;
			children = (
				00A582E12267031C00207B4B /* PLVUploadModel.h */,
				00A582E22267031C00207B4B /* PLVUploadModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		00B57E91228942D7002E56C7 /* View */ = {
			isa = PBXGroup;
			children = (
				0030335522B1047E0087E76A /* PLVUploadCell.h */,
				0030335622B1047E0087E76A /* PLVUploadCell.m */,
				00A582DB2265DB0500207B4B /* PLVUploadingCell.h */,
				00A582DC2265DB0500207B4B /* PLVUploadingCell.m */,
				00A582DE2265DB1900207B4B /* PLVUploadedCell.h */,
				00A582DF2265DB1900207B4B /* PLVUploadedCell.m */,
				00B57EA5228C0C25002E56C7 /* PLVUploadToast.h */,
				00B57EA6228C0C25002E56C7 /* PLVUploadToast.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		00B57E92228942FE002E56C7 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				00154ED3226979B2009E36D7 /* PLVUploadTableViewModel.h */,
				00154ED4226979B2009E36D7 /* PLVUploadTableViewModel.mm */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		00B57E9322894323002E56C7 /* Controller */ = {
			isa = PBXGroup;
			children = (
				00A582D822642E8700207B4B /* PLVUploadTableViewController.h */,
				00A582D922642E8700207B4B /* PLVUploadTableViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		00B57EA4228BFD7A002E56C7 /* Util */ = {
			isa = PBXGroup;
			children = (
				00B57E9D22894361002E56C7 /* PLVUploadUtil.h */,
				00B57E9922894361002E56C7 /* PLVUploadUtil.mm */,
			);
			path = Util;
			sourceTree = "<group>";
		};
		00EAB3312407C3A80083C0E7 /* FloatingWindow */ = {
			isa = PBXGroup;
			children = (
				00EAB30F2406760D0083C0E7 /* PLVVFloatingWindow.h */,
				00EAB3102406760D0083C0E7 /* PLVVFloatingWindow.m */,
				00EAB32E2407B8C80083C0E7 /* PLVVFloatingWindowViewController.h */,
				00EAB32F2407B8C80083C0E7 /* PLVVFloatingWindowViewController.m */,
				00EAB3122407737F0083C0E7 /* PLVVFloatingWindowSkin.h */,
				00EAB3132407737F0083C0E7 /* PLVVFloatingWindowSkin.m */,
			);
			path = FloatingWindow;
			sourceTree = "<group>";
		};
		030DF0371FB5AB730035D0B0 /* Model */ = {
			isa = PBXGroup;
			children = (
				0314085B1FB9507A00D1424A /* PLVCourse.h */,
				0314085C1FB9507A00D1424A /* PLVCourse.m */,
				033153021FCD103F00CAE849 /* PLVCourseSection.h */,
				033153031FCD103F00CAE849 /* PLVCourseSection.m */,
				033153051FCD105400CAE849 /* PLVCourseVideo.h */,
				033153061FCD105400CAE849 /* PLVCourseVideo.m */,
				030DF0381FB5BA9D0035D0B0 /* PLVSchool.h */,
				030DF0391FB5BA9D0035D0B0 /* PLVSchool.m */,
				0314085E1FB9806100D1424A /* PLVTeacher.h */,
				0314085F1FB9806100D1424A /* PLVTeacher.m */,
				03856CA11FE22A4D0038DEB2 /* PLVVodAccountVideo.h */,
				03856CA21FE22A4D0038DEB2 /* PLVVodAccountVideo.m */,
				00B3AE542484DE95003225F3 /* PLVVodAccount.h */,
				00B3AE552484DE95003225F3 /* PLVVodAccount.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		0356CD021FCC369100B522A1 /* Course */ = {
			isa = PBXGroup;
			children = (
				030DF02A1FB590560035D0B0 /* PLVCourseListController.h */,
				030DF02B1FB590560035D0B0 /* PLVCourseListController.m */,
				030DF02D1FB590910035D0B0 /* PLVCourseDetailController.h */,
				030DF02E1FB590910035D0B0 /* PLVCourseDetailController.m */,
				0356CD031FCC383700B522A1 /* PLVCourseVideoListController.h */,
				0356CD041FCC383700B522A1 /* PLVCourseVideoListController.m */,
				0356CD061FCC387400B522A1 /* PLVCourseIntroductionController.h */,
				0356CD071FCC387400B522A1 /* PLVCourseIntroductionController.m */,
			);
			path = Course;
			sourceTree = "<group>";
		};
		03722E2C1FEB65DC00031180 /* Exam */ = {
			isa = PBXGroup;
			children = (
				037C210F1FECB4F9008CD03C /* ExplanationView */,
				03722E381FEB904500031180 /* QuestionView */,
				03722E2E1FEB65DC00031180 /* PLVVodExamViewController.h */,
				03722E2D1FEB65DC00031180 /* PLVVodExamViewController.m */,
				03722E301FEB664700031180 /* PLVVodExamViewController.xib */,
				6342327A224DF13A00AB856D /* PLVVodExamTestData.json */,
			);
			path = Exam;
			sourceTree = "<group>";
		};
		03722E321FEB66CC00031180 /* PolyvOpenSourceModule */ = {
			isa = PBXGroup;
			children = (
				65A57F3C2BFC30E30038F788 /* PLVSubtitle */,
				126690CB26244A2B00DE371D /* Toast */,
				04C85F792473790B00F3493B /* Util */,
				03856C4B1FE21C8A0038DEB2 /* PLVVodResources.xcassets */,
				03856C5C1FE21C990038DEB2 /* Category */,
				03856C4D1FE21C990038DEB2 /* Skin */,
				1263823626C0BB6F0009C006 /* KnowledgeList */,
				03722E2C1FEB65DC00031180 /* Exam */,
				00B0B497240516AE0089495C /* Floating */,
				000935BB22E9BD3D00933F66 /* PPT */,
				125C715C25EF6B6A002E10CF /* PLVMarquee */,
			);
			path = PolyvOpenSourceModule;
			sourceTree = "<group>";
		};
		03722E381FEB904500031180 /* QuestionView */ = {
			isa = PBXGroup;
			children = (
				037C21101FECB56D008CD03C /* PLVVodQuestion.h */,
				037C21111FECB56D008CD03C /* PLVVodQuestion.m */,
				03722E391FEB90DB00031180 /* PLVVodQuestionView.h */,
				03722E3A1FEB90DB00031180 /* PLVVodQuestionView.m */,
				125AC98025C7B0300043AB55 /* PLVOptionView.h */,
				125AC98125C7B0300043AB55 /* PLVOptionView.m */,
				125873EE25C295030040B87A /* PLVFillBlankQuestionView.h */,
				125873EF25C295030040B87A /* PLVFillBlankQuestionView.m */,
				1258744E25C2C3DF0040B87A /* PLVFillBlankView.h */,
				1258744F25C2C3DF0040B87A /* PLVFillBlankView.m */,
			);
			path = QuestionView;
			sourceTree = "<group>";
		};
		037C210F1FECB4F9008CD03C /* ExplanationView */ = {
			isa = PBXGroup;
			children = (
				03722E3C1FEB910300031180 /* PLVVodExplanationView.h */,
				03722E3D1FEB910300031180 /* PLVVodExplanationView.m */,
			);
			path = ExplanationView;
			sourceTree = "<group>";
		};
		03856C4D1FE21C990038DEB2 /* Skin */ = {
			isa = PBXGroup;
			children = (
				03856C4E1FE21C990038DEB2 /* PLVVodPlayerSkin.h */,
				03856C511FE21C990038DEB2 /* PLVVodPlayerSkin.m */,
				03856C501FE21C990038DEB2 /* PLVVodPlayerSkin.xib */,
				03856C4F1FE21C990038DEB2 /* PLVVodSkinPlayerController.h */,
				03856C5B1FE21C990038DEB2 /* PLVVodSkinPlayerController.m */,
				03856C521FE21C990038DEB2 /* View */,
			);
			path = Skin;
			sourceTree = "<group>";
		};
		03856C521FE21C990038DEB2 /* View */ = {
			isa = PBXGroup;
			children = (
				043C80472DA653C700DF5938 /* PlayerOptimizeOptions */,
				047497632D23F809002781EE /* ProgressViewExtend */,
				04E5BE74221577C500A8BCC9 /* PLVVodRouteLineView.h */,
				04E5BE73221577C500A8BCC9 /* PLVVodRouteLineView.m */,
				0430A41021EC3EF6001B8942 /* PlayTips */,
				03F899701FF3AA450091AC89 /* PLVVodGestureIndicatorView.h */,
				03F899711FF3AA450091AC89 /* PLVVodGestureIndicatorView.m */,
				03C9D4531FF255B0006F460C /* PLVVodPlaybackRatePanelView.h */,
				03C9D4541FF255B0006F460C /* PLVVodPlaybackRatePanelView.m */,
				03C9D44D1FF23D4B006F460C /* PLVVodDefinitionPanelView.h */,
				03C9D44E1FF23D4B006F460C /* PLVVodDefinitionPanelView.m */,
				04497D4428D1747D00A17409 /* PLVVodVideoToolBoxPanelView.h */,
				04497D4528D1747D00A17409 /* PLVVodVideoToolBoxPanelView.m */,
				03856C541FE21C990038DEB2 /* PLVVodShrinkscreenView.h */,
				03856C591FE21C990038DEB2 /* PLVVodShrinkscreenView.m */,
				03856C5A1FE21C990038DEB2 /* PLVVodFullscreenView.h */,
				03856C561FE21C990038DEB2 /* PLVVodFullscreenView.m */,
				03856C551FE21C990038DEB2 /* PLVVodSettingPanelView.h */,
				03856C581FE21C990038DEB2 /* PLVVodSettingPanelView.m */,
				03856C571FE21C990038DEB2 /* PLVVodDanmuSendView.h */,
				03856C531FE21C990038DEB2 /* PLVVodDanmuSendView.m */,
				DA00893B20BC0F44006CEBBE /* PLVVodAudioCoverPanelView.h */,
				DA00893C20BC0F44006CEBBE /* PLVVodAudioCoverPanelView.m */,
				041B5D6B21BE59B000FC7941 /* PLVVodLockScreenView.h */,
				041B5D6C21BE59B000FC7941 /* PLVVodLockScreenView.m */,
				63745AC821D1D606007408C1 /* PLVVodCoverView.h */,
				63745AC921D1D606007408C1 /* PLVVodCoverView.m */,
				636AAB05223A6E7A00EFC3E4 /* PLVVodNetworkTipsView.h */,
				636AAB06223A6E7A00EFC3E4 /* PLVVodNetworkTipsView.m */,
				0019F4572416168A00B3E65D /* PLVVodFastForwardView.h */,
				0019F4582416168A00B3E65D /* PLVVodFastForwardView.m */,
				1233FED326A56D7D006AAA53 /* PLVVodDefinitionTipsView.h */,
				1233FED426A56D7D006AAA53 /* PLVVodDefinitionTipsView.m */,
				04FBDFB02DB0934500D548B7 /* PLVVodNetworkPlayErrorTipsView.h */,
				04FBDFB12DB0934500D548B7 /* PLVVodNetworkPlayErrorTipsView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		03856C5C1FE21C990038DEB2 /* Category */ = {
			isa = PBXGroup;
			children = (
				03856C5F1FE21C990038DEB2 /* PLVVodDanmu+PLVVod.h */,
				03856C671FE21C990038DEB2 /* PLVVodDanmu+PLVVod.m */,
				03856C5D1FE21C990038DEB2 /* UIColor+PLVVod.h */,
				03856C631FE21C990038DEB2 /* UIColor+PLVVod.m */,
				03856C5E1FE21C990038DEB2 /* UINavigationController+PLVVod.h */,
				03856C641FE21C990038DEB2 /* UINavigationController+PLVVod.m */,
				034271CE20492836002F4475 /* UIControl+PLVVod.h */,
				034271CF20492836002F4475 /* UIControl+PLVVod.m */,
				03856CA41FE22D090038DEB2 /* NSString+PLVVod.h */,
				03856CA51FE22D090038DEB2 /* NSString+PLVVod.m */,
				04F9F0EE20EF51D000D204EA /* UIButton+EnlargeTouchArea.h */,
				04F9F0EF20EF51D000D204EA /* UIButton+EnlargeTouchArea.m */,
			);
			path = Category;
			sourceTree = "<group>";
		};
		03BB59001FCE465200DE42DC /* Vendor */ = {
			isa = PBXGroup;
			children = (
				04422D4D2CA6A9F9004B35B3 /* PLVSecureView */,
				12671F5926C2276600A50B95 /* PLVMarqueeLabel */,
				03DDF4B51FE763BB00F63630 /* DLSlideView */,
			);
			path = Vendor;
			sourceTree = "<group>";
		};
		03CB8C521FB3EB220048D9FC = {
			isa = PBXGroup;
			children = (
				03CB8C921FB3EDC80048D9FC /* README.md */,
				03CB8C5D1FB3EB220048D9FC /* PolyvVodSDKDemo */,
				03CB8C761FB3EB220048D9FC /* PolyvVodSDKDemoTests */,
				03CB8C811FB3EB220048D9FC /* PolyvVodSDKDemoUITests */,
				03CB8C5C1FB3EB220048D9FC /* Products */,
				94204A1D4CA80F6165C1D2E9 /* Frameworks */,
				91802A2F05429B7AFC2D9AC7 /* Pods */,
			);
			sourceTree = "<group>";
		};
		03CB8C5C1FB3EB220048D9FC /* Products */ = {
			isa = PBXGroup;
			children = (
				03CB8C5B1FB3EB220048D9FC /* PolyvVodSDKDemo.app */,
				03CB8C731FB3EB220048D9FC /* PolyvVodSDKDemoTests.xctest */,
				03CB8C7E1FB3EB220048D9FC /* PolyvVodSDKDemoUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		03CB8C5D1FB3EB220048D9FC /* PolyvVodSDKDemo */ = {
			isa = PBXGroup;
			children = (
				03722E321FEB66CC00031180 /* PolyvOpenSourceModule */,
				03BB59001FCE465200DE42DC /* Vendor */,
				03CB8C901FB3EBD10048D9FC /* Classes */,
				03CB8C5E1FB3EB220048D9FC /* AppDelegate.h */,
				03CB8C5F1FB3EB220048D9FC /* AppDelegate.m */,
				00B218742481144700ABDCCD /* Supporting Files */,
			);
			path = PolyvVodSDKDemo;
			sourceTree = "<group>";
		};
		03CB8C761FB3EB220048D9FC /* PolyvVodSDKDemoTests */ = {
			isa = PBXGroup;
			children = (
				03CB8C771FB3EB220048D9FC /* PolyvVodSDKDemoTests.m */,
				03CB8C791FB3EB220048D9FC /* Info.plist */,
			);
			path = PolyvVodSDKDemoTests;
			sourceTree = "<group>";
		};
		03CB8C811FB3EB220048D9FC /* PolyvVodSDKDemoUITests */ = {
			isa = PBXGroup;
			children = (
				03CB8C821FB3EB220048D9FC /* PolyvVodSDKDemoUITests.m */,
				03CB8C841FB3EB220048D9FC /* Info.plist */,
			);
			path = PolyvVodSDKDemoUITests;
			sourceTree = "<group>";
		};
		03CB8C901FB3EBD10048D9FC /* Classes */ = {
			isa = PBXGroup;
			children = (
				00B57E8F22894244002E56C7 /* Upload */,
				639D17E121E35760005E00F3 /* Cast */,
				040FD1A1218ADF5F00F6460B /* Util */,
				0427F3972105D74500E41084 /* Download */,
				031408591FB94DAF00D1424A /* Foundation+Log.m */,
				030DF0371FB5AB730035D0B0 /* Model */,
				03CB8C911FB3EC730048D9FC /* View */,
				0356CD021FCC369100B522A1 /* Course */,
				0323727A1FCBC58C0001E904 /* PLVAccountVideoListController.h */,
				0323727B1FCBC58D0001E904 /* PLVAccountVideoListController.m */,
				0334A0032068831E00A49905 /* PLVSimpleDetailController.h */,
				0334A0042068831E00A49905 /* PLVSimpleDetailController.m */,
				000935B822E9AF4500933F66 /* PLVPPTSimpleDetailController.h */,
				000935B922E9AF4500933F66 /* PLVPPTSimpleDetailController.m */,
				03F04E28204D4A8700CA0A4F /* PLVVodVidTestController.h */,
				03F04E29204D4A8700CA0A4F /* PLVVodVidTestController.m */,
				04F57DB72136827D001AD451 /* PLVPlayQueueBackgroundController.h */,
				04F57DB82136827D001AD451 /* PLVPlayQueueBackgroundController.m */,
			);
			path = Classes;
			sourceTree = "<group>";
		};
		03CB8C911FB3EC730048D9FC /* View */ = {
			isa = PBXGroup;
			children = (
				03540B851FBC408400CCC7E6 /* PLVCourseBannerReusableView.h */,
				03540B861FBC408400CCC7E6 /* PLVCourseBannerReusableView.m */,
				034B55441FC926520031A875 /* PLVTitleHeaderReusableView.h */,
				034B55451FC926520031A875 /* PLVTitleHeaderReusableView.m */,
				03EA41C61FBD36F500BA68AD /* PLVCourseCell.h */,
				03EA41C71FBD36F500BA68AD /* PLVCourseCell.m */,
				0323727D1FCBE4690001E904 /* PLVVideoCell.h */,
				0323727E1FCBE4690001E904 /* PLVVideoCell.m */,
				032372801FCC1C570001E904 /* PLVLoadCell.h */,
				032372811FCC1C570001E904 /* PLVLoadCell.m */,
				033AF0861FE9FA530068BC40 /* PLVToolbar.h */,
				033AF0871FE9FA530068BC40 /* PLVToolbar.m */,
				00E111E922F7DC25005A8575 /* PLVPPTTableViewCell.h */,
				00E111EA22F7DC25005A8575 /* PLVPPTTableViewCell.m */,
				00E111EF22F91DA1005A8575 /* PLVEmptyPPTViewCell.h */,
				00E111F022F91DA1005A8575 /* PLVEmptyPPTViewCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		03DDF4B51FE763BB00F63630 /* DLSlideView */ = {
			isa = PBXGroup;
			children = (
				03DDF4B71FE763BB00F63630 /* DLCache */,
				03DDF4BD1FE763BB00F63630 /* DLCustomSlideView.h */,
				03DDF4C61FE763BB00F63630 /* DLCustomSlideView.m */,
				03DDF4BC1FE763BB00F63630 /* DLSlideView.h */,
				03DDF4C71FE763BB00F63630 /* DLSlideView.m */,
				03DDF4BF1FE763BB00F63630 /* DLTabbarView */,
				03DDF4B61FE763BB00F63630 /* DLTabedSlideView.h */,
				03DDF4BE1FE763BB00F63630 /* DLTabedSlideView.m */,
				03DDF4BB1FE763BB00F63630 /* DLUtility.h */,
				03DDF4C51FE763BB00F63630 /* DLUtility.m */,
			);
			path = DLSlideView;
			sourceTree = "<group>";
		};
		03DDF4B71FE763BB00F63630 /* DLCache */ = {
			isa = PBXGroup;
			children = (
				03DDF4B81FE763BB00F63630 /* DLLRUCache.m */,
				03DDF4B91FE763BB00F63630 /* DLCacheProtocol.h */,
				03DDF4BA1FE763BB00F63630 /* DLLRUCache.h */,
			);
			path = DLCache;
			sourceTree = "<group>";
		};
		03DDF4BF1FE763BB00F63630 /* DLTabbarView */ = {
			isa = PBXGroup;
			children = (
				03DDF4C01FE763BB00F63630 /* DLSlideTabbarProtocol.h */,
				03DDF4C11FE763BB00F63630 /* DLScrollTabbarView.h */,
				03DDF4C31FE763BB00F63630 /* DLScrollTabbarView.m */,
				03DDF4C21FE763BB00F63630 /* DLFixedTabbarView.h */,
				03DDF4C41FE763BB00F63630 /* DLFixedTabbarView.m */,
			);
			path = DLTabbarView;
			sourceTree = "<group>";
		};
		040FD1A1218ADF5F00F6460B /* Util */ = {
			isa = PBXGroup;
			children = (
				030DF0341FB5960E0035D0B0 /* PLVCourseNetworking.h */,
				030DF0351FB5960E0035D0B0 /* PLVCourseNetworking.m */,
				043D446F2193ECA100C2F776 /* PLVVodServiceUtil.h */,
				043D44702193ECA100C2F776 /* PLVVodServiceUtil.m */,
				043D44722194098400C2F776 /* PLVUserVideoListResult.h */,
				043D44732194098400C2F776 /* PLVUserVideoListResult.m */,
				04851976219995A1008058A6 /* PLVVideoPlayTimesResult.h */,
				04851977219995A1008058A6 /* PLVVideoPlayTimesResult.m */,
			);
			path = Util;
			sourceTree = "<group>";
		};
		0427C520219FD00A00B2C910 /* Helper */ = {
			isa = PBXGroup;
			children = (
				0427C523219FD01E00B2C910 /* plv_bg_voice.mp3 */,
				0427C521219FD01E00B2C910 /* PLVVodDownloadHelper.h */,
				0427C522219FD01E00B2C910 /* PLVVodDownloadHelper.m */,
			);
			path = Helper;
			sourceTree = "<group>";
		};
		0427F3972105D74500E41084 /* Download */ = {
			isa = PBXGroup;
			children = (
				0463BAE221A52F7F0010A5EC /* PolyvOpenDatabase */,
				0427C520219FD00A00B2C910 /* Helper */,
				04D4038A2127B964002B16CC /* Model */,
				0489AFDA21077529004A26A4 /* View */,
				04B051CB2106BDD500F6C791 /* PLVDownloadManagerViewController.h */,
				04B051CC2106BDD500F6C791 /* PLVDownloadManagerViewController.m */,
				04B051CE2106CE9600F6C791 /* PLVDownloadProcessingViewController.h */,
				04B051CF2106CE9600F6C791 /* PLVDownloadProcessingViewController.m */,
				04B051D12106CEC000F6C791 /* PLVDownloadCompleteViewController.h */,
				04B051D22106CEC000F6C791 /* PLVDownloadCompleteViewController.m */,
			);
			path = Download;
			sourceTree = "<group>";
		};
		042F08D42D2BD02D0059FF85 /* Model */ = {
			isa = PBXGroup;
			children = (
				042F08D52D2BD1030059FF85 /* PLVVodHeatMapModel.h */,
				042F08D62D2BD1030059FF85 /* PLVVodHeatMapModel.m */,
				042F08D82D2BE3420059FF85 /* PLVVodMarkerViewData.h */,
				042F08D92D2BE3420059FF85 /* PLVVodMarkerViewData.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		0430A41021EC3EF6001B8942 /* PlayTips */ = {
			isa = PBXGroup;
			children = (
				0430A41121EC4237001B8942 /* PLVVodPlayTipsView.h */,
				0430A41221EC4237001B8942 /* PLVVodPlayTipsView.m */,
			);
			path = PlayTips;
			sourceTree = "<group>";
		};
		043C80472DA653C700DF5938 /* PlayerOptimizeOptions */ = {
			isa = PBXGroup;
			children = (
				043C80482DA6544300DF5938 /* PLVVodOptimizeOptionsPanelView.h */,
				043C80492DA6544300DF5938 /* PLVVodOptimizeOptionsPanelView.m */,
				043C804B2DA655CC00DF5938 /* PLVVodOptimizeOptionView.h */,
				043C804C2DA655CC00DF5938 /* PLVVodOptimizeOptionView.m */,
			);
			path = PlayerOptimizeOptions;
			sourceTree = "<group>";
		};
		04422D4D2CA6A9F9004B35B3 /* PLVSecureView */ = {
			isa = PBXGroup;
			children = (
				04422D4E2CA6A9F9004B35B3 /* PLVSecureView.m */,
				04422D4F2CA6A9F9004B35B3 /* PLVSecureView.h */,
			);
			path = PLVSecureView;
			sourceTree = "<group>";
		};
		0463BAE221A52F7F0010A5EC /* PolyvOpenDatabase */ = {
			isa = PBXGroup;
			children = (
				0463BAE521A52FBB0010A5EC /* PLVVodDBManager.h */,
				0463BAE421A52FBB0010A5EC /* PLVVodDBManager.mm */,
				0463BAE321A52FBB0010A5EC /* PLVVodExtendVideoInfo.h */,
				0463BAE621A52FBB0010A5EC /* PLVVodExtendVideoInfo.mm */,
			);
			path = PolyvOpenDatabase;
			sourceTree = "<group>";
		};
		047497632D23F809002781EE /* ProgressViewExtend */ = {
			isa = PBXGroup;
			children = (
				042F08D42D2BD02D0059FF85 /* Model */,
				047497672D23F8D6002781EE /* PLVVodHeatMapView.h */,
				047497682D23F8D6002781EE /* PLVVodHeatMapView.m */,
				0474976A2D23F909002781EE /* PLVVodBubbleMarkerView.h */,
				0474976B2D23F909002781EE /* PLVVodBubbleMarkerView.m */,
				0474976D2D23F927002781EE /* PLVVodProgressMarkerView.h */,
				0474976E2D23F927002781EE /* PLVVodProgressMarkerView.m */,
				043B5D4A2D365F0C00C300EF /* PLVVodHeatMapContentView.h */,
				043B5D4B2D365F0C00C300EF /* PLVVodHeatMapContentView.m */,
			);
			path = ProgressViewExtend;
			sourceTree = "<group>";
		};
		0489AFDA21077529004A26A4 /* View */ = {
			isa = PBXGroup;
			children = (
				0489AFDB21077559004A26A4 /* PLVDownloadProcessingCell.h */,
				0489AFDC21077559004A26A4 /* PLVDownloadProcessingCell.m */,
				0489AFDE2107757E004A26A4 /* PLVDownloadComleteCell.h */,
				0489AFDF2107757E004A26A4 /* PLVDownloadComleteCell.m */,
				0030335222B101E00087E76A /* PLVDownloadCell.h */,
				0030335322B101E00087E76A /* PLVDownloadCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		04C85F792473790B00F3493B /* Util */ = {
			isa = PBXGroup;
			children = (
				0030335C22B1E5660087E76A /* PLVVodNetworkUtil.h */,
				0030335D22B1E5660087E76A /* PLVVodNetworkUtil.m */,
				040FD1A2218ADFA000F6460B /* PLVVodErrorUtil.h */,
				040FD1A3218ADFA000F6460B /* PLVVodErrorUtil.m */,
				0046DF4924E63E6500592F62 /* PLVVodUtils.h */,
				0046DF4A24E63E6500592F62 /* PLVVodUtils.m */,
			);
			path = Util;
			sourceTree = "<group>";
		};
		04D4038A2127B964002B16CC /* Model */ = {
			isa = PBXGroup;
			children = (
				04D4038B2127BA3C002B16CC /* PLVDownloadCompleteInfoModel.h */,
				04D4038C2127BA3C002B16CC /* PLVDownloadCompleteInfoModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		125C715C25EF6B6A002E10CF /* PLVMarquee */ = {
			isa = PBXGroup;
			children = (
				125C716725EF6BBC002E10CF /* PLVVodMarqueeView.h */,
				125C716825EF6BBC002E10CF /* PLVVodMarqueeView.m */,
				125C715E25EF6BAA002E10CF /* PLVVodMarqueeModel.h */,
				125C715F25EF6BAA002E10CF /* PLVVodMarqueeModel.m */,
				125C716D25EF6BD1002E10CF /* PLVVodMarqueeAnimationManager.h */,
				125C716E25EF6BD1002E10CF /* PLVVodMarqueeAnimationManager.m */,
			);
			path = PLVMarquee;
			sourceTree = "<group>";
		};
		1263823626C0BB6F0009C006 /* KnowledgeList */ = {
			isa = PBXGroup;
			children = (
				1263823A26C0BBAD0009C006 /* PLVKnowledgeListViewController.h */,
				1263823B26C0BBAD0009C006 /* PLVKnowledgeListViewController.m */,
				1263824026C0BD780009C006 /* PLVSlideTabbarView */,
				1263829726C1012F0009C006 /* View */,
				1263826226C0CADD0009C006 /* Model */,
			);
			path = KnowledgeList;
			sourceTree = "<group>";
		};
		1263824026C0BD780009C006 /* PLVSlideTabbarView */ = {
			isa = PBXGroup;
			children = (
				1263824D26C0BF5E0009C006 /* PLVSlideTabbarView.h */,
				1263824E26C0BF5E0009C006 /* PLVSlideTabbarView.m */,
			);
			path = PLVSlideTabbarView;
			sourceTree = "<group>";
		};
		1263826226C0CADD0009C006 /* Model */ = {
			isa = PBXGroup;
			children = (
				126382B026C112280009C006 /* PLVKnowledgeModel.h */,
				126382B126C112280009C006 /* PLVKnowledgeModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		1263829726C1012F0009C006 /* View */ = {
			isa = PBXGroup;
			children = (
				1263829826C1017D0009C006 /* PLVKnowledgeCategoryTableViewCell.h */,
				1263829926C1017D0009C006 /* PLVKnowledgeCategoryTableViewCell.m */,
				126382A126C109F40009C006 /* PLVKnowledgePointTableViewCell.h */,
				126382A226C109F40009C006 /* PLVKnowledgePointTableViewCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		126690CB26244A2B00DE371D /* Toast */ = {
			isa = PBXGroup;
			children = (
				126690CC26244A8100DE371D /* PLVVodToast.h */,
				126690CD26244A8100DE371D /* PLVVodToast.m */,
			);
			path = Toast;
			sourceTree = "<group>";
		};
		12671F5926C2276600A50B95 /* PLVMarqueeLabel */ = {
			isa = PBXGroup;
			children = (
				12671F5B26C2278000A50B95 /* PLVVodMarqueeLabel.h */,
				12671F5A26C2278000A50B95 /* PLVVodMarqueeLabel.m */,
			);
			path = PLVMarqueeLabel;
			sourceTree = "<group>";
		};
		639D17E121E35760005E00F3 /* Cast */ = {
			isa = PBXGroup;
			children = (
				639D17E221E3579B005E00F3 /* PLVCastBusinessManager.h */,
				639D17E321E3579B005E00F3 /* PLVCastBusinessManager.m */,
				046559612892837F00F26E5E /* PLVCastManager.h */,
				046559622892837F00F26E5E /* PLVCastManager.m */,
				6307224621C8DFC7003B2526 /* PLVCastControllView.h */,
				6307224721C8DFC8003B2526 /* PLVCastControllView.m */,
				6307224321C79917003B2526 /* PLVCastServiceListView.h */,
				6307224421C79917003B2526 /* PLVCastServiceListView.m */,
			);
			path = Cast;
			sourceTree = "<group>";
		};
		65A57F3C2BFC30E30038F788 /* PLVSubtitle */ = {
			isa = PBXGroup;
			children = (
				65A57F3D2BFC30E30038F788 /* UI */,
				65A57F422BFC30E30038F788 /* Parser */,
			);
			path = PLVSubtitle;
			sourceTree = "<group>";
		};
		65A57F3D2BFC30E30038F788 /* UI */ = {
			isa = PBXGroup;
			children = (
				65A57F3E2BFC30E30038F788 /* PLVVodSubtitleManager.m */,
				65A57F3F2BFC30E30038F788 /* PLVVodSubtitleViewModel.h */,
				65A57F402BFC30E30038F788 /* PLVVodSubtitleViewModel.m */,
				65A57F412BFC30E30038F788 /* PLVVodSubtitleManager.h */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		65A57F422BFC30E30038F788 /* Parser */ = {
			isa = PBXGroup;
			children = (
				65A57F432BFC30E30038F788 /* PLVVodSubtitleItem.h */,
				65A57F442BFC30E30038F788 /* PLVVodSubtitleParser.m */,
				65A57F452BFC30E30038F788 /* PLVVodSubtitleItem.m */,
				65A57F462BFC30E30038F788 /* PLVVodSubtitleParser.h */,
			);
			path = Parser;
			sourceTree = "<group>";
		};
		91802A2F05429B7AFC2D9AC7 /* Pods */ = {
			isa = PBXGroup;
			children = (
				69CE5D17BD2E7FDDEF281119 /* Pods-vod-PolyvVodSDKDemo.debug.xcconfig */,
				C9E2B2A6342299C4D21D81D8 /* Pods-vod-PolyvVodSDKDemo.release.xcconfig */,
				E41F9D5C7BDF3E096A82E953 /* Pods-PolyvVodSDKDemo.debug.xcconfig */,
				2241869D6E30E88E7DF5E961 /* Pods-PolyvVodSDKDemo.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		94204A1D4CA80F6165C1D2E9 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				125D6C86260043A400D7F305 /* PLVVodUploadSDK.framework */,
				125D6C802600439F00D7F305 /* PLVVodSDK.framework */,
				04B051D82106EC4C00F6C791 /* GLKit.framework */,
				57339ACDF536EE629635D325 /* libPods-vod-PolyvVodSDKDemo.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		FA11CDF8280E5F2E0035AB42 /* View */ = {
			isa = PBXGroup;
			children = (
				FA11CDF9280E5F960035AB42 /* PLVPictureInPicturePlaceholderView.h */,
				FA11CDFA280E5F960035AB42 /* PLVPictureInPicturePlaceholderView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		03CB8C5A1FB3EB220048D9FC /* PolyvVodSDKDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 03CB8C871FB3EB220048D9FC /* Build configuration list for PBXNativeTarget "PolyvVodSDKDemo" */;
			buildPhases = (
				2441B99200F1DDB0AA6A8AA9 /* [CP] Check Pods Manifest.lock */,
				03CB8C571FB3EB220048D9FC /* Sources */,
				03CB8C581FB3EB220048D9FC /* Frameworks */,
				03CB8C591FB3EB220048D9FC /* Resources */,
				0A537BCCD71804471CFBCD1F /* [CP] Copy Pods Resources */,
				61A065C21C556BCAC83A2A10 /* [CP] Embed Pods Frameworks */,
				04655960289280B600F26E5E /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PolyvVodSDKDemo;
			productName = PolyvVodSDKDemo;
			productReference = 03CB8C5B1FB3EB220048D9FC /* PolyvVodSDKDemo.app */;
			productType = "com.apple.product-type.application";
		};
		03CB8C721FB3EB220048D9FC /* PolyvVodSDKDemoTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 03CB8C8A1FB3EB220048D9FC /* Build configuration list for PBXNativeTarget "PolyvVodSDKDemoTests" */;
			buildPhases = (
				03CB8C6F1FB3EB220048D9FC /* Sources */,
				03CB8C701FB3EB220048D9FC /* Frameworks */,
				03CB8C711FB3EB220048D9FC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				03CB8C751FB3EB220048D9FC /* PBXTargetDependency */,
			);
			name = PolyvVodSDKDemoTests;
			productName = PolyvVodSDKDemoTests;
			productReference = 03CB8C731FB3EB220048D9FC /* PolyvVodSDKDemoTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		03CB8C7D1FB3EB220048D9FC /* PolyvVodSDKDemoUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 03CB8C8D1FB3EB220048D9FC /* Build configuration list for PBXNativeTarget "PolyvVodSDKDemoUITests" */;
			buildPhases = (
				03CB8C7A1FB3EB220048D9FC /* Sources */,
				03CB8C7B1FB3EB220048D9FC /* Frameworks */,
				03CB8C7C1FB3EB220048D9FC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				03CB8C801FB3EB220048D9FC /* PBXTargetDependency */,
			);
			name = PolyvVodSDKDemoUITests;
			productName = PolyvVodSDKDemoUITests;
			productReference = 03CB8C7E1FB3EB220048D9FC /* PolyvVodSDKDemoUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		03CB8C531FB3EB220048D9FC /* Project object */ = {
			isa = PBXProject;
			attributes = {
				CLASSPREFIX = PLV;
				LastUpgradeCheck = 1010;
				ORGANIZATIONNAME = POLYV;
				TargetAttributes = {
					03CB8C5A1FB3EB220048D9FC = {
						CreatedOnToolsVersion = 9.1;
						ProvisioningStyle = Automatic;
						SystemCapabilities = {
							com.apple.AccessWiFi = {
								enabled = 1;
							};
							com.apple.BackgroundModes = {
								enabled = 1;
							};
						};
					};
					03CB8C721FB3EB220048D9FC = {
						CreatedOnToolsVersion = 9.1;
						ProvisioningStyle = Automatic;
						TestTargetID = 03CB8C5A1FB3EB220048D9FC;
					};
					03CB8C7D1FB3EB220048D9FC = {
						CreatedOnToolsVersion = 9.1;
						ProvisioningStyle = Automatic;
						TestTargetID = 03CB8C5A1FB3EB220048D9FC;
					};
				};
			};
			buildConfigurationList = 03CB8C561FB3EB220048D9FC /* Build configuration list for PBXProject "PolyvVodSDKDemo" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 03CB8C521FB3EB220048D9FC;
			productRefGroup = 03CB8C5C1FB3EB220048D9FC /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				03CB8C5A1FB3EB220048D9FC /* PolyvVodSDKDemo */,
				03CB8C721FB3EB220048D9FC /* PolyvVodSDKDemoTests */,
				03CB8C7D1FB3EB220048D9FC /* PolyvVodSDKDemoUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		03CB8C591FB3EB220048D9FC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				03856C4C1FE21C8B0038DEB2 /* PLVVodResources.xcassets in Resources */,
				00B2187F2481144700ABDCCD /* Assets.xcassets in Resources */,
				00B218802481144700ABDCCD /* Main.storyboard in Resources */,
				00B2187D2481144700ABDCCD /* LaunchScreen.storyboard in Resources */,
				0427C525219FD01E00B2C910 /* plv_bg_voice.mp3 in Resources */,
				00B2187E2481144700ABDCCD /* Settings.bundle in Resources */,
				03722E311FEB664700031180 /* PLVVodExamViewController.xib in Resources */,
				03856C691FE21C990038DEB2 /* PLVVodPlayerSkin.xib in Resources */,
				6342327B224DF13A00AB856D /* PLVVodExamTestData.json in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		03CB8C711FB3EB220048D9FC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		03CB8C7C1FB3EB220048D9FC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0A537BCCD71804471CFBCD1F /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-vod-PolyvVodSDKDemo/Pods-vod-PolyvVodSDKDemo-resources.sh",
				"${PODS_ROOT}/TZImagePickerController/TZImagePickerController/TZImagePickerController/TZImagePickerController.bundle",
				"${PODS_ROOT}/XRCarouselView/XRCarouselView/XRPlaceholder.png",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TZImagePickerController.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/XRPlaceholder.png",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-vod-PolyvVodSDKDemo/Pods-vod-PolyvVodSDKDemo-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		2441B99200F1DDB0AA6A8AA9 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-vod-PolyvVodSDKDemo-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		61A065C21C556BCAC83A2A10 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-vod-PolyvVodSDKDemo/Pods-vod-PolyvVodSDKDemo-frameworks.sh",
				"${PODS_ROOT}/PLVDLNASender/GCDWebServers.framework",
				"${PODS_ROOT}/PLVDLNASender/WXDLNASender.framework",
				"${PODS_ROOT}/PLVDLNASender/WXMirrorCore.framework",
				"${PODS_ROOT}/PLVIJKPlayer/PLVIJKPlayer.framework",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GCDWebServers.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/WXDLNASender.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/WXMirrorCore.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/PLVIJKPlayer.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-vod-PolyvVodSDKDemo/Pods-vod-PolyvVodSDKDemo-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		03CB8C571FB3EB220048D9FC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				1263823C26C0BBAD0009C006 /* PLVKnowledgeListViewController.m in Sources */,
				00A582DD2265DB0500207B4B /* PLVUploadingCell.m in Sources */,
				1233FED526A56D7D006AAA53 /* PLVVodDefinitionTipsView.m in Sources */,
				04B051D02106CE9600F6C791 /* PLVDownloadProcessingViewController.m in Sources */,
				125C716F25EF6BD1002E10CF /* PLVVodMarqueeAnimationManager.m in Sources */,
				03856C6E1FE21C990038DEB2 /* PLVVodShrinkscreenView.m in Sources */,
				636AAB07223A6E7A00EFC3E4 /* PLVVodNetworkTipsView.m in Sources */,
				0030335422B101E00087E76A /* PLVDownloadCell.m in Sources */,
				043D44742194098400C2F776 /* PLVUserVideoListResult.m in Sources */,
				03DDF4CA1FE763BB00F63630 /* DLScrollTabbarView.m in Sources */,
				00B57E9F22894361002E56C7 /* PLVUploadUncompleteData.mm in Sources */,
				03DDF4C91FE763BB00F63630 /* DLTabedSlideView.m in Sources */,
				000935D722EA88BB00933F66 /* PLVPPTBaseViewController.m in Sources */,
				04497D4628D1747D00A17409 /* PLVVodVideoToolBoxPanelView.m in Sources */,
				04B051CD2106BDD500F6C791 /* PLVDownloadManagerViewController.m in Sources */,
				000935C222E9BD3D00933F66 /* PLVVodPPTViewController.m in Sources */,
				0019F4592416168A00B3E65D /* PLVVodFastForwardView.m in Sources */,
				DA00893D20BC0F44006CEBBE /* PLVVodAudioCoverPanelView.m in Sources */,
				03C9D4551FF255B0006F460C /* PLVVodPlaybackRatePanelView.m in Sources */,
				041B5D6D21BE59B000FC7941 /* PLVVodLockScreenView.m in Sources */,
				034B55461FC926520031A875 /* PLVTitleHeaderReusableView.m in Sources */,
				04F57DB92136827D001AD451 /* PLVPlayQueueBackgroundController.m in Sources */,
				0427C524219FD01E00B2C910 /* PLVVodDownloadHelper.m in Sources */,
				03DDF4C81FE763BB00F63630 /* DLLRUCache.m in Sources */,
				63745ACA21D1D606007408C1 /* PLVVodCoverView.m in Sources */,
				009869BE230BF4FB00AA32E3 /* PLVPPTSkinProgressView.m in Sources */,
				03722E3B1FEB90DB00031180 /* PLVVodQuestionView.m in Sources */,
				03C9D44F1FF23D4B006F460C /* PLVVodDefinitionPanelView.m in Sources */,
				033153071FCD105400CAE849 /* PLVCourseVideo.m in Sources */,
				030DF0361FB5960E0035D0B0 /* PLVCourseNetworking.m in Sources */,
				04422D502CA6A9FA004B35B3 /* PLVSecureView.m in Sources */,
				000935C322E9BD3D00933F66 /* PLVFloatingView.m in Sources */,
				00B57E9E22894361002E56C7 /* PLVUploadCompleteData.mm in Sources */,
				65A57F472BFC30E30038F788 /* PLVVodSubtitleManager.m in Sources */,
				0474976C2D23F909002781EE /* PLVVodBubbleMarkerView.m in Sources */,
				00EAB3142407737F0083C0E7 /* PLVVFloatingWindowSkin.m in Sources */,
				03722E2F1FEB65DC00031180 /* PLVVodExamViewController.m in Sources */,
				00B57EA022894361002E56C7 /* PLVUploadUtil.mm in Sources */,
				0356CD081FCC387400B522A1 /* PLVCourseIntroductionController.m in Sources */,
				00A582DA22642E8700207B4B /* PLVUploadTableViewController.m in Sources */,
				00EAB3112406760D0083C0E7 /* PLVVFloatingWindow.m in Sources */,
				046559632892837F00F26E5E /* PLVCastManager.m in Sources */,
				03856C711FE21C990038DEB2 /* UINavigationController+PLVVod.m in Sources */,
				037C21121FECB56D008CD03C /* PLVVodQuestion.m in Sources */,
				639D17E421E3579B005E00F3 /* PLVCastBusinessManager.m in Sources */,
				0334A0052068831E00A49905 /* PLVSimpleDetailController.m in Sources */,
				03856CA61FE22D090038DEB2 /* NSString+PLVVod.m in Sources */,
				0463BAE921A52FBB0010A5EC /* PLVVodExtendVideoInfo.mm in Sources */,
				FAD2A8BB2800149400354955 /* PLVPictureInPictureRestoreManager.m in Sources */,
				00B218812481144700ABDCCD /* main.m in Sources */,
				0463BAE821A52FBB0010A5EC /* PLVVodDBManager.mm in Sources */,
				03F04E2A204D4A8700CA0A4F /* PLVVodVidTestController.m in Sources */,
				047497692D23F8D6002781EE /* PLVVodHeatMapView.m in Sources */,
				00A8166E230BE19D00EB61E5 /* PLVPPTControllerSkinView.m in Sources */,
				00B57EA7228C0C25002E56C7 /* PLVUploadToast.m in Sources */,
				0356CD051FCC383700B522A1 /* PLVCourseVideoListController.m in Sources */,
				04B051D32106CEC000F6C791 /* PLVDownloadCompleteViewController.m in Sources */,
				0314085D1FB9507A00D1424A /* PLVCourse.m in Sources */,
				1263824F26C0BF5E0009C006 /* PLVSlideTabbarView.m in Sources */,
				033AF0881FE9FA530068BC40 /* PLVToolbar.m in Sources */,
				6307224521C79917003B2526 /* PLVCastServiceListView.m in Sources */,
				6307224821C8DFC8003B2526 /* PLVCastControllView.m in Sources */,
				04E5BE75221577C600A8BCC9 /* PLVVodRouteLineView.m in Sources */,
				031408601FB9806100D1424A /* PLVTeacher.m in Sources */,
				126382B226C112280009C006 /* PLVKnowledgeModel.m in Sources */,
				0489AFDD21077559004A26A4 /* PLVDownloadProcessingCell.m in Sources */,
				03722E3E1FEB910300031180 /* PLVVodExplanationView.m in Sources */,
				126382A326C109F40009C006 /* PLVKnowledgePointTableViewCell.m in Sources */,
				1258745025C2C3DF0040B87A /* PLVFillBlankView.m in Sources */,
				0030335722B1047E0087E76A /* PLVUploadCell.m in Sources */,
				125AC98225C7B0300043AB55 /* PLVOptionView.m in Sources */,
				040FD1A4218ADFA000F6460B /* PLVVodErrorUtil.m in Sources */,
				042F08DA2D2BE3420059FF85 /* PLVVodMarkerViewData.m in Sources */,
				000935DA22EAA48800933F66 /* PLVPPTVideoViewController.m in Sources */,
				033153041FCD103F00CAE849 /* PLVCourseSection.m in Sources */,
				00A582E02265DB1900207B4B /* PLVUploadedCell.m in Sources */,
				03856C6D1FE21C990038DEB2 /* PLVVodSettingPanelView.m in Sources */,
				03856C6C1FE21C990038DEB2 /* PLVVodFullscreenView.m in Sources */,
				032372821FCC1C570001E904 /* PLVLoadCell.m in Sources */,
				00E111EB22F7DC25005A8575 /* PLVPPTTableViewCell.m in Sources */,
				00EAB3302407B8C80083C0E7 /* PLVVFloatingWindowViewController.m in Sources */,
				00B3AE562484DE95003225F3 /* PLVVodAccount.m in Sources */,
				030DF02F1FB590910035D0B0 /* PLVCourseDetailController.m in Sources */,
				03540B871FBC408400CCC7E6 /* PLVCourseBannerReusableView.m in Sources */,
				0323727F1FCBE4690001E904 /* PLVVideoCell.m in Sources */,
				043D44712193ECA100C2F776 /* PLVVodServiceUtil.m in Sources */,
				125C716025EF6BAA002E10CF /* PLVVodMarqueeModel.m in Sources */,
				04D4038D2127BA3C002B16CC /* PLVDownloadCompleteInfoModel.m in Sources */,
				042F08D72D2BD1030059FF85 /* PLVVodHeatMapModel.m in Sources */,
				03856C6B1FE21C990038DEB2 /* PLVVodDanmuSendView.m in Sources */,
				04FBDFB22DB0934500D548B7 /* PLVVodNetworkPlayErrorTipsView.m in Sources */,
				03F899721FF3AA450091AC89 /* PLVVodGestureIndicatorView.m in Sources */,
				03856CA31FE22A4D0038DEB2 /* PLVVodAccountVideo.m in Sources */,
				03DDF4CD1FE763BB00F63630 /* DLCustomSlideView.m in Sources */,
				043C804A2DA6544300DF5938 /* PLVVodOptimizeOptionsPanelView.m in Sources */,
				0489AFE02107757E004A26A4 /* PLVDownloadComleteCell.m in Sources */,
				03DDF4CC1FE763BB00F63630 /* DLUtility.m in Sources */,
				000935BA22E9AF4500933F66 /* PLVPPTSimpleDetailController.m in Sources */,
				03CB8C601FB3EB220048D9FC /* AppDelegate.m in Sources */,
				030DF02C1FB590560035D0B0 /* PLVCourseListController.m in Sources */,
				043B5D4C2D365F0C00C300EF /* PLVVodHeatMapContentView.m in Sources */,
				65A57F482BFC30E30038F788 /* PLVVodSubtitleViewModel.m in Sources */,
				043C804D2DA655CC00DF5938 /* PLVVodOptimizeOptionView.m in Sources */,
				03DDF4CB1FE763BB00F63630 /* DLFixedTabbarView.m in Sources */,
				1263829A26C1017D0009C006 /* PLVKnowledgeCategoryTableViewCell.m in Sources */,
				125C716925EF6BBC002E10CF /* PLVVodMarqueeView.m in Sources */,
				0474976F2D23F927002781EE /* PLVVodProgressMarkerView.m in Sources */,
				65A57F492BFC30E30038F788 /* PLVVodSubtitleParser.m in Sources */,
				04851978219995A1008058A6 /* PLVVideoPlayTimesResult.m in Sources */,
				00B57EA122894361002E56C7 /* PLVUploadDataBase.mm in Sources */,
				************************ /* PLVPictureInPicturePlaceholderView.m in Sources */,
				00A582E32267031C00207B4B /* PLVUploadModel.m in Sources */,
				00E111EE22F82193005A8575 /* PLVPPTActionViewCell.m in Sources */,
				03856C6F1FE21C990038DEB2 /* PLVVodSkinPlayerController.m in Sources */,
				0323727C1FCBC58D0001E904 /* PLVAccountVideoListController.m in Sources */,
				034271D020492836002F4475 /* UIControl+PLVVod.m in Sources */,
				00AADCD022F2F1400037A843 /* PLVPPTActionView.m in Sources */,
				03856C701FE21C990038DEB2 /* UIColor+PLVVod.m in Sources */,
				65A57F4A2BFC30E30038F788 /* PLVVodSubtitleItem.m in Sources */,
				030DF03A1FB5BA9D0035D0B0 /* PLVSchool.m in Sources */,
				126690CE26244A8100DE371D /* PLVVodToast.m in Sources */,
				0030335E22B1E5660087E76A /* PLVVodNetworkUtil.m in Sources */,
				0430A41321EC4237001B8942 /* PLVVodPlayTipsView.m in Sources */,
				00154ED5226979B2009E36D7 /* PLVUploadTableViewModel.mm in Sources */,
				00E111F722F958F9005A8575 /* PLVPPTFailView.m in Sources */,
				0314085A1FB94DAF00D1424A /* Foundation+Log.m in Sources */,
				00E111F422F930A4005A8575 /* PLVPPTLoadFailAlertView.m in Sources */,
				03856C741FE21C990038DEB2 /* PLVVodDanmu+PLVVod.m in Sources */,
				03DDF4CE1FE763BB00F63630 /* DLSlideView.m in Sources */,
				03EA41C91FBD36F500BA68AD /* PLVCourseCell.m in Sources */,
				125873F025C295030040B87A /* PLVFillBlankQuestionView.m in Sources */,
				0046DF4B24E63E6500592F62 /* PLVVodUtils.m in Sources */,
				00B0B4962405080D0089495C /* PLVVFloatingPlayerViewController.m in Sources */,
				12671F5C26C2278000A50B95 /* PLVVodMarqueeLabel.m in Sources */,
				00E111F122F91DA1005A8575 /* PLVEmptyPPTViewCell.m in Sources */,
				03856C6A1FE21C990038DEB2 /* PLVVodPlayerSkin.m in Sources */,
				04F9F0F020EF51D000D204EA /* UIButton+EnlargeTouchArea.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		03CB8C6F1FB3EB220048D9FC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				03CB8C781FB3EB220048D9FC /* PolyvVodSDKDemoTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		03CB8C7A1FB3EB220048D9FC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				03CB8C831FB3EB220048D9FC /* PolyvVodSDKDemoUITests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		03CB8C751FB3EB220048D9FC /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 03CB8C5A1FB3EB220048D9FC /* PolyvVodSDKDemo */;
			targetProxy = 03CB8C741FB3EB220048D9FC /* PBXContainerItemProxy */;
		};
		03CB8C801FB3EB220048D9FC /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 03CB8C5A1FB3EB220048D9FC /* PolyvVodSDKDemo */;
			targetProxy = 03CB8C7F1FB3EB220048D9FC /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		03CB8C851FB3EB220048D9FC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.1;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		03CB8C861FB3EB220048D9FC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.1;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		03CB8C881FB3EB220048D9FC /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 69CE5D17BD2E7FDDEF281119 /* Pods-vod-PolyvVodSDKDemo.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "PolyvVodSDKDemo/Supporting Files/PolyvVodSDKDemo.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/PolyvVodSDKDemo/Vendor",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/PolyvVodSDKDemo",
					"$(PROJECT_DIR)/PolyvVodSDKDemo/Classes/Cast",
				);
				GCC_INCREASE_PRECOMPILED_HEADER_SHARING = NO;
				GCC_PREFIX_HEADER = "$(SRCROOT)/PolyvVodSDkDemo/Supporting Files/PolyvVodSDKDemo-Prefix.pch";
				INFOPLIST_FILE = "$(SRCROOT)/PolyvVodSDKDemo/Supporting Files/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 2.24.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.plv.vod.sdk.demo.intest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		03CB8C891FB3EB220048D9FC /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C9E2B2A6342299C4D21D81D8 /* Pods-vod-PolyvVodSDKDemo.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "PolyvVodSDKDemo/Supporting Files/PolyvVodSDKDemo.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/PolyvVodSDKDemo/Vendor",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/PolyvVodSDKDemo",
					"$(PROJECT_DIR)/PolyvVodSDKDemo/Classes/Cast",
				);
				GCC_INCREASE_PRECOMPILED_HEADER_SHARING = NO;
				GCC_PREFIX_HEADER = "$(SRCROOT)/PolyvVodSDkDemo/Supporting Files/PolyvVodSDKDemo-Prefix.pch";
				INFOPLIST_FILE = "$(SRCROOT)/PolyvVodSDKDemo/Supporting Files/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 2.24.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.plv.vod.sdk.demo.intest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		03CB8C8B1FB3EB220048D9FC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				INFOPLIST_FILE = PolyvVodSDKDemoTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = POLYV.PolyvVodSDKDemoTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PolyvVodSDKDemo.app/PolyvVodSDKDemo";
			};
			name = Debug;
		};
		03CB8C8C1FB3EB220048D9FC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				INFOPLIST_FILE = PolyvVodSDKDemoTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = POLYV.PolyvVodSDKDemoTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PolyvVodSDKDemo.app/PolyvVodSDKDemo";
			};
			name = Release;
		};
		03CB8C8E1FB3EB220048D9FC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				INFOPLIST_FILE = PolyvVodSDKDemoUITests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = POLYV.PolyvVodSDKDemoUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PolyvVodSDKDemo;
			};
			name = Debug;
		};
		03CB8C8F1FB3EB220048D9FC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				INFOPLIST_FILE = PolyvVodSDKDemoUITests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = POLYV.PolyvVodSDKDemoUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PolyvVodSDKDemo;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		03CB8C561FB3EB220048D9FC /* Build configuration list for PBXProject "PolyvVodSDKDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				03CB8C851FB3EB220048D9FC /* Debug */,
				03CB8C861FB3EB220048D9FC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		03CB8C871FB3EB220048D9FC /* Build configuration list for PBXNativeTarget "PolyvVodSDKDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				03CB8C881FB3EB220048D9FC /* Debug */,
				03CB8C891FB3EB220048D9FC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		03CB8C8A1FB3EB220048D9FC /* Build configuration list for PBXNativeTarget "PolyvVodSDKDemoTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				03CB8C8B1FB3EB220048D9FC /* Debug */,
				03CB8C8C1FB3EB220048D9FC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		03CB8C8D1FB3EB220048D9FC /* Build configuration list for PBXNativeTarget "PolyvVodSDKDemoUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				03CB8C8E1FB3EB220048D9FC /* Debug */,
				03CB8C8F1FB3EB220048D9FC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 03CB8C531FB3EB220048D9FC /* Project object */;
}
