<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="landscape" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="PLVVodPlayerSkin">
            <connections>
                <outlet property="audioCoverPanelView" destination="fBS-tL-F6W" id="K5z-ek-SZM"/>
                <outlet property="castButton" destination="obs-qM-9Wh" id="JvU-Ok-1qt"/>
                <outlet property="castButtonInFullScreen" destination="bTw-Qu-swT" id="eb7-TH-HQU"/>
                <outlet property="controlContainerView" destination="mwx-JJ-GUb" id="PrV-1N-MrC"/>
                <outlet property="coverView" destination="fRj-tD-e9G" id="pJn-Ai-wnh"/>
                <outlet property="danmuSendView" destination="qa0-Rh-gAf" id="vaM-3e-5Nc"/>
                <outlet property="definitionPanelView" destination="inz-cI-pDt" id="Mrp-NU-aGp"/>
                <outlet property="fullscreenView" destination="w04-rs-iMM" id="Zit-LI-3dA"/>
                <outlet property="gestureIndicatorView" destination="ggj-qD-OqT" id="ZBR-Ix-1sn"/>
                <outlet property="loadSpeed" destination="xH1-D5-nUE" id="JFX-Zf-KAZ"/>
                <outlet property="loadingContainerView" destination="Kk8-EG-hyF" id="2zT-Is-rc5"/>
                <outlet property="loadingIndicator" destination="jiE-j7-hFB" id="pdv-nr-uFJ"/>
                <outlet property="lockScreenView" destination="tL0-Ye-Q1a" id="6rg-AO-2VO"/>
                <outlet property="panelTap" destination="OPa-9b-dc4" id="hcb-K5-8Xt"/>
                <outlet property="playbackRatePanelView" destination="MZT-mj-7ks" id="E39-EU-mVM"/>
                <outlet property="routeLineView" destination="yEK-9l-tK8" id="Q2G-fg-OQu"/>
                <outlet property="settingsPanelView" destination="WML-5j-ft2" id="x46-bt-2Xh"/>
                <outlet property="sharePanelView" destination="BZZ-yq-7JA" id="09G-4B-nEF"/>
                <outlet property="shrinkscreenView" destination="xdj-6z-3JE" id="mgQ-OP-YLd"/>
                <outlet property="subtitleLabel" destination="37T-Vn-sBr" id="QUx-jr-7n8"/>
                <outlet property="subtitleLabel2" destination="EpL-f0-tTa" id="4rs-6e-g8Y"/>
                <outlet property="subtitleTopLabel" destination="Gkg-Yo-h8S" id="sVo-fJ-3Ks"/>
                <outlet property="subtitleTopLabel2" destination="cB7-dO-pgJ" id="FlG-X3-f2S"/>
                <outlet property="videoToolBoxPanelView" destination="Y0G-ge-dTU" id="xIL-0U-kKE"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="字幕" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="37T-Vn-sBr">
                    <rect key="frame" x="316" y="346.5" width="35" height="20.5"/>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="字幕2" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EpL-f0-tTa" userLabel="字幕2">
                    <rect key="frame" x="310" y="317.5" width="47" height="21"/>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Kk8-EG-hyF" userLabel="LoadingContainer">
                    <rect key="frame" x="298.5" y="152.5" width="70" height="70"/>
                    <subviews>
                        <activityIndicatorView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" hidesWhenStopped="YES" animating="YES" style="whiteLarge" translatesAutoresizingMaskIntoConstraints="NO" id="jiE-j7-hFB">
                            <rect key="frame" x="16.5" y="1.5" width="37" height="37"/>
                        </activityIndicatorView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0 KB/s" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xH1-D5-nUE" userLabel="LoadSpeed">
                            <rect key="frame" x="0.0" y="53.5" width="70" height="15"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="15" id="DtU-iQ-rdG"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="jiE-j7-hFB" firstAttribute="centerX" secondItem="Kk8-EG-hyF" secondAttribute="centerX" id="2pS-9E-ifc"/>
                        <constraint firstAttribute="trailing" secondItem="xH1-D5-nUE" secondAttribute="trailing" id="EN2-LJ-jDE"/>
                        <constraint firstAttribute="height" constant="70" id="JsO-oD-I61"/>
                        <constraint firstItem="xH1-D5-nUE" firstAttribute="leading" secondItem="Kk8-EG-hyF" secondAttribute="leading" id="SDA-vn-lc1"/>
                        <constraint firstAttribute="width" constant="70" id="gLd-Cb-AxF"/>
                        <constraint firstItem="jiE-j7-hFB" firstAttribute="centerY" secondItem="Kk8-EG-hyF" secondAttribute="centerY" constant="-15" id="juC-8X-QLg"/>
                        <constraint firstItem="xH1-D5-nUE" firstAttribute="top" secondItem="jiE-j7-hFB" secondAttribute="bottom" constant="15" id="uxH-T6-DsP"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mwx-JJ-GUb" userLabel="控件容器">
                    <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="顶部字幕" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Gkg-Yo-h8S">
                    <rect key="frame" x="299" y="28" width="69.5" height="20.5"/>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="顶部字幕2" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cB7-dO-pgJ" userLabel="顶部字幕2">
                    <rect key="frame" x="293" y="56.5" width="81.5" height="21"/>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <viewLayoutGuide key="safeArea" id="wTJ-iI-t4B"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="Gkg-Yo-h8S" secondAttribute="trailing" constant="8" id="3Da-Rd-IRz"/>
                <constraint firstItem="EpL-f0-tTa" firstAttribute="centerX" secondItem="wTJ-iI-t4B" secondAttribute="centerX" id="4gp-WR-SQH"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="EpL-f0-tTa" secondAttribute="trailing" constant="8" id="5mj-EB-TNl"/>
                <constraint firstItem="cB7-dO-pgJ" firstAttribute="centerX" secondItem="wTJ-iI-t4B" secondAttribute="centerX" id="8t6-7A-EG8"/>
                <constraint firstAttribute="bottom" secondItem="mwx-JJ-GUb" secondAttribute="bottom" id="BoN-xq-kb5"/>
                <constraint firstItem="37T-Vn-sBr" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="i5M-Pr-FkT" secondAttribute="leading" constant="8" id="CBw-mo-mjC"/>
                <constraint firstItem="37T-Vn-sBr" firstAttribute="top" secondItem="EpL-f0-tTa" secondAttribute="bottom" constant="8" id="Ef0-MY-tzy"/>
                <constraint firstAttribute="trailing" secondItem="mwx-JJ-GUb" secondAttribute="trailing" id="G8o-a7-R4H"/>
                <constraint firstItem="EpL-f0-tTa" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="i5M-Pr-FkT" secondAttribute="leading" constant="8" id="Gr8-zf-wcI"/>
                <constraint firstItem="Gkg-Yo-h8S" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" constant="28" id="Hlb-Vd-JEw"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="cB7-dO-pgJ" secondAttribute="trailing" constant="8" id="NFR-gN-OyG"/>
                <constraint firstItem="37T-Vn-sBr" firstAttribute="centerX" secondItem="wTJ-iI-t4B" secondAttribute="centerX" id="QY3-Q6-9rN"/>
                <constraint firstAttribute="bottom" secondItem="37T-Vn-sBr" secondAttribute="bottom" constant="8" id="RIn-O4-cqe"/>
                <constraint firstItem="Gkg-Yo-h8S" firstAttribute="centerX" secondItem="wTJ-iI-t4B" secondAttribute="centerX" id="SuG-ag-mR3"/>
                <constraint firstItem="mwx-JJ-GUb" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="VYs-W5-qU0"/>
                <constraint firstItem="Gkg-Yo-h8S" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="i5M-Pr-FkT" secondAttribute="leading" constant="8" id="Wyg-Cj-IHt"/>
                <constraint firstItem="cB7-dO-pgJ" firstAttribute="top" secondItem="Gkg-Yo-h8S" secondAttribute="bottom" constant="8" id="Xgu-7m-VpX"/>
                <constraint firstItem="mwx-JJ-GUb" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="XlQ-nD-30A"/>
                <constraint firstItem="37T-Vn-sBr" firstAttribute="centerX" secondItem="wTJ-iI-t4B" secondAttribute="centerX" id="e90-Bc-LRo"/>
                <constraint firstItem="Kk8-EG-hyF" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="fKB-Ay-T7U"/>
                <constraint firstItem="Kk8-EG-hyF" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" id="lXW-ic-N6n"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="37T-Vn-sBr" secondAttribute="trailing" constant="8" id="nC1-3R-oy1"/>
                <constraint firstItem="cB7-dO-pgJ" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="i5M-Pr-FkT" secondAttribute="leading" constant="8" id="pXh-H2-ATZ"/>
                <constraint firstItem="mwx-JJ-GUb" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="sg0-EM-7Dz"/>
            </constraints>
            <point key="canvasLocation" x="25.637181409295355" y="58.399999999999999"/>
        </view>
        <view contentMode="scaleToFill" id="xdj-6z-3JE" userLabel="半屏皮肤" customClass="PLVVodShrinkscreenView">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LZm-tb-AsH" userLabel="左侧音视频切换容器">
                    <rect key="frame" x="16" y="148.5" width="75" height="78"/>
                    <subviews>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" image="plv_vod_av_swtich_bg" translatesAutoresizingMaskIntoConstraints="NO" id="sSx-mN-NYB" userLabel="音视频切换按钮组背景">
                            <rect key="frame" x="0.0" y="0.0" width="36" height="78"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        </imageView>
                        <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="PjV-Vs-YcC" userLabel="视频组件容器">
                            <rect key="frame" x="2" y="2" width="75" height="32"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <subviews>
                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" image="plv_vod_av_swtich_selected" translatesAutoresizingMaskIntoConstraints="NO" id="Rns-jI-0FT" userLabel="视频播放状态图">
                                    <rect key="frame" x="0.0" y="0.0" width="32" height="32"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                </imageView>
                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" image="plv_vod_av_swtich_video" translatesAutoresizingMaskIntoConstraints="NO" id="jOF-a2-xz1" userLabel="视频标志图">
                                    <rect key="frame" x="8" y="10" width="16" height="12"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="视频" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" highlighted="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="70e-Os-z6W" userLabel="视频文字">
                                    <rect key="frame" x="40" y="8" width="25" height="15"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <color key="highlightedColor" red="0.011764705882352941" green="0.66274509803921566" blue="0.95686274509803915" alpha="1" colorSpace="calibratedRGB"/>
                                </label>
                                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fch-CF-8Ah" userLabel="视频切换按钮">
                                    <rect key="frame" x="0.0" y="0.0" width="75" height="32"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <connections>
                                        <action selector="videoPlaybackModeAction:" destination="-1" eventType="touchUpInside" id="3g8-BF-TiG"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </view>
                        <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="mz0-gh-IZ4" userLabel="音频组件容器">
                            <rect key="frame" x="2" y="44" width="75" height="32"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <subviews>
                                <imageView hidden="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" image="plv_vod_av_swtich_selected" translatesAutoresizingMaskIntoConstraints="NO" id="GGq-HH-1qx" userLabel="音频播放状态图">
                                    <rect key="frame" x="0.0" y="0.0" width="32" height="32"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                </imageView>
                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" image="plv_vod_av_swtich_audio" translatesAutoresizingMaskIntoConstraints="NO" id="scH-8B-vl9" userLabel="音频标志图">
                                    <rect key="frame" x="10" y="8" width="12" height="16"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="音频" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IcR-MM-Uhx" userLabel="音频文字">
                                    <rect key="frame" x="40" y="8" width="25" height="15"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <color key="highlightedColor" red="0.01176470588" green="0.66274509800000003" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                </label>
                                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="RWg-wh-7nG" userLabel="音频切换按钮">
                                    <rect key="frame" x="0.0" y="0.0" width="75" height="32"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <connections>
                                        <action selector="audioPlaybackModeAction:" destination="-1" eventType="touchUpInside" id="Kmd-jd-7y9"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="78" id="2iB-mK-aN2"/>
                        <constraint firstAttribute="width" constant="75" id="vkh-D1-O7N"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TOF-OZ-kbk" userLabel="底部工具栏">
                    <rect key="frame" x="0.0" y="321" width="667" height="54"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" spacing="15" translatesAutoresizingMaskIntoConstraints="NO" id="8uy-pg-t24" userLabel="右侧控件容器">
                            <rect key="frame" x="493" y="13.5" width="154" height="27"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="c1e-Fs-ZW8" userLabel="清晰度">
                                    <rect key="frame" x="0.0" y="0.0" width="30" height="27"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <state key="normal" title="高清">
                                        <color key="titleColor" red="0.0" green="0.70196078429999997" blue="0.96862745100000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="definitionAction:" destination="-1" eventType="touchUpInside" id="9ca-n2-TbJ"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="IPq-UF-Gw2" userLabel="倍速">
                                    <rect key="frame" x="45" y="0.0" width="30" height="27"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <state key="normal" title="1x">
                                        <color key="titleColor" red="0.0" green="0.70196078429999997" blue="0.96862745100000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="playbackRateAction:" destination="-1" eventType="touchUpInside" id="YI4-vK-cd1"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4b7-v8-6dn" userLabel="线路">
                                    <rect key="frame" x="90" y="0.0" width="30" height="27"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <state key="normal" title="线路">
                                        <color key="titleColor" red="0.0" green="0.70196078429999997" blue="0.96862745100000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="routeLineAction:" destination="-1" eventType="touchUpInside" id="h99-1f-At6"/>
                                    </connections>
                                </button>
                                <button hidden="YES" opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ZcV-KY-7rP" userLabel="小窗播放">
                                    <rect key="frame" x="127.5" y="0.0" width="19" height="27"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="19" id="JJW-4m-vgE"/>
                                    </constraints>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" image="plv_vod_btn_floating"/>
                                    <connections>
                                        <action selector="floatingButtonAction:" destination="-1" eventType="touchUpInside" id="Uzp-vx-RYZ"/>
                                    </connections>
                                </button>
                                <button hidden="YES" opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="saM-Oi-XEL" userLabel="关闭副屏">
                                    <rect key="frame" x="127.5" y="0.0" width="48" height="27"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="48" id="7Hj-lV-DRY"/>
                                    </constraints>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" image="plv_vod_btn-exitFullScreen"/>
                                    <connections>
                                        <action selector="subScreenButtonAction:" destination="-1" eventType="touchUpInside" id="6qo-Vp-X16"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6FA-3b-cVY" userLabel="全屏/半屏">
                                    <rect key="frame" x="135" y="0.0" width="19" height="27"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="19" id="WfJ-LE-Hq5"/>
                                    </constraints>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" image="plv_vod_btn_fullscreen"/>
                                    <state key="selected" image="plv_vod_btn_exfull"/>
                                    <connections>
                                        <action selector="switchScreenAction:" destination="-1" eventType="touchUpInside" id="N0Q-Pe-dT6"/>
                                    </connections>
                                </button>
                            </subviews>
                        </stackView>
                        <stackView opaque="NO" contentMode="scaleToFill" spacing="17" translatesAutoresizingMaskIntoConstraints="NO" id="9IC-aY-C7s" userLabel="左侧控件容器">
                            <rect key="frame" x="20" y="16" width="135.5" height="22"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xHS-iO-d00" userLabel="播放/暂停">
                                    <rect key="frame" x="0.0" y="0.0" width="14" height="22"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="14" id="ciA-3R-Fr5"/>
                                    </constraints>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" image="plv_vod_btn_play"/>
                                    <state key="selected" image="plv_vod_btn_pause"/>
                                </button>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="00:00 / 20:03" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4r8-7b-Uty" userLabel="时间标签">
                                    <rect key="frame" x="31" y="0.0" width="104.5" height="22"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" red="0.0" green="0.062745098040000002" blue="0.1058823529" alpha="0.69999999999999996" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstItem="9IC-aY-C7s" firstAttribute="leading" secondItem="TOF-OZ-kbk" secondAttribute="leading" constant="20" id="Ckw-oZ-rrc"/>
                        <constraint firstItem="8uy-pg-t24" firstAttribute="centerY" secondItem="TOF-OZ-kbk" secondAttribute="centerY" id="fs7-EU-dpm"/>
                        <constraint firstAttribute="height" constant="54" id="hck-wj-wgh"/>
                        <constraint firstItem="9IC-aY-C7s" firstAttribute="centerY" secondItem="TOF-OZ-kbk" secondAttribute="centerY" id="ovH-jw-VVV"/>
                        <constraint firstAttribute="trailing" secondItem="8uy-pg-t24" secondAttribute="trailing" constant="20" id="vgE-Ia-XRc"/>
                    </constraints>
                </view>
                <progressView opaque="NO" contentMode="scaleToFill" verticalHuggingPriority="750" progress="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="H0g-LX-qZk" userLabel="缓冲进度">
                    <rect key="frame" x="0.0" y="319" width="667" height="2"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <color key="tintColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="0.69587435787671237" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="2" id="yU3-yh-Gci"/>
                    </constraints>
                    <color key="trackTintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </progressView>
                <slider opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="0.29999999999999999" minValue="0.0" maxValue="1" translatesAutoresizingMaskIntoConstraints="NO" id="TzY-zP-vqR" userLabel="播放滑杆">
                    <rect key="frame" x="-2" y="304" width="671" height="31"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <color key="tintColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                    <color key="maximumTrackTintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </slider>
                <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="obs-qM-9Wh">
                    <rect key="frame" x="621" y="10" width="36" height="36"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="36" id="Kgb-gZ-IJ4"/>
                        <constraint firstAttribute="width" constant="36" id="LiO-au-72x"/>
                    </constraints>
                    <state key="normal" image="btn-proj"/>
                    <state key="selected" image="btn-proj-sel"/>
                    <connections>
                        <action selector="castAction:" destination="-1" eventType="touchUpInside" id="5Q8-Hk-g18"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="CXC-Hk-0EM"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="obs-qM-9Wh" secondAttribute="trailing" constant="10" id="0c6-A6-Rq2"/>
                <constraint firstItem="LZm-tb-AsH" firstAttribute="leading" secondItem="xdj-6z-3JE" secondAttribute="leading" constant="16" id="5hd-jr-Vtu"/>
                <constraint firstItem="TzY-zP-vqR" firstAttribute="leading" secondItem="xdj-6z-3JE" secondAttribute="leading" id="IU5-Sg-RJD"/>
                <constraint firstItem="obs-qM-9Wh" firstAttribute="top" secondItem="xdj-6z-3JE" secondAttribute="top" constant="10" id="JL1-pQ-O9l"/>
                <constraint firstAttribute="bottom" secondItem="TOF-OZ-kbk" secondAttribute="bottom" id="KSX-Hu-UNT"/>
                <constraint firstItem="LZm-tb-AsH" firstAttribute="centerY" secondItem="xdj-6z-3JE" secondAttribute="centerY" id="NVm-12-J9g"/>
                <constraint firstAttribute="trailing" secondItem="TOF-OZ-kbk" secondAttribute="trailing" id="axv-6t-hwg"/>
                <constraint firstAttribute="trailing" secondItem="TzY-zP-vqR" secondAttribute="trailing" id="iL2-tT-TdE"/>
                <constraint firstAttribute="trailing" secondItem="H0g-LX-qZk" secondAttribute="trailing" id="jjk-UZ-uor"/>
                <constraint firstItem="TOF-OZ-kbk" firstAttribute="top" secondItem="H0g-LX-qZk" secondAttribute="bottom" id="mpV-ON-yCu"/>
                <constraint firstItem="H0g-LX-qZk" firstAttribute="leading" secondItem="xdj-6z-3JE" secondAttribute="leading" id="oIg-i2-fmd"/>
                <constraint firstItem="TzY-zP-vqR" firstAttribute="centerY" secondItem="H0g-LX-qZk" secondAttribute="centerY" constant="-1" id="pbZ-WM-oZc"/>
                <constraint firstItem="TOF-OZ-kbk" firstAttribute="leading" secondItem="xdj-6z-3JE" secondAttribute="leading" id="yfg-vh-HXO"/>
            </constraints>
            <connections>
                <outlet property="audioModeLabel" destination="IcR-MM-Uhx" id="I5C-aU-VMu"/>
                <outlet property="audioModeSelectedImageView" destination="GGq-HH-1qx" id="6cq-zi-1kk"/>
                <outlet property="audioPlayModeButton" destination="RWg-wh-7nG" id="X3x-QS-6xq"/>
                <outlet property="bufferProgressView" destination="H0g-LX-qZk" id="Ixl-No-6h9"/>
                <outlet property="definitionButton" destination="c1e-Fs-ZW8" id="7YC-qo-vvo"/>
                <outlet property="floatingButton" destination="ZcV-KY-7rP" id="588-m4-9JQ"/>
                <outlet property="playModeContainerView" destination="LZm-tb-AsH" id="sKY-ds-g6n"/>
                <outlet property="playPauseButton" destination="xHS-iO-d00" id="bTa-ie-c6U"/>
                <outlet property="playbackRateButton" destination="IPq-UF-Gw2" id="B1W-C9-qs8"/>
                <outlet property="playbackSlider" destination="TzY-zP-vqR" id="XEZ-xw-Gci"/>
                <outlet property="rightToolStackView" destination="8uy-pg-t24" id="2Eu-km-QWt"/>
                <outlet property="routeButton" destination="4b7-v8-6dn" id="XEe-0P-czR"/>
                <outlet property="subScreenButton" destination="saM-Oi-XEL" id="llm-xp-7Ef"/>
                <outlet property="switchScreenButton" destination="6FA-3b-cVY" id="jFb-tt-cnI"/>
                <outlet property="timeLabel" destination="4r8-7b-Uty" id="Kez-U9-4ew"/>
                <outlet property="videoModeLabel" destination="70e-Os-z6W" id="nzM-3A-22r"/>
                <outlet property="videoModeSelectedImageView" destination="Rns-jI-0FT" id="Oak-Ac-L2M"/>
                <outlet property="videoPlayModeButton" destination="fch-CF-8Ah" id="Lfv-AT-ft2"/>
            </connections>
            <point key="canvasLocation" x="729" y="58"/>
        </view>
        <view contentMode="scaleToFill" id="w04-rs-iMM" userLabel="全屏皮肤" customClass="PLVVodFullscreenView">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pEp-Xe-LQj" userLabel="标题工具栏">
                    <rect key="frame" x="0.0" y="0.0" width="667" height="44"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" spacing="35" translatesAutoresizingMaskIntoConstraints="NO" id="Fh2-rt-Zgw" userLabel="右侧控件容器">
                            <rect key="frame" x="568" y="11" width="79" height="22"/>
                            <subviews>
                                <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bTw-Qu-swT" userLabel="投屏">
                                    <rect key="frame" x="-22" y="0.0" width="22" height="22"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="22" id="afF-T0-n8D"/>
                                        <constraint firstAttribute="height" constant="22" id="ndl-Ds-lrX"/>
                                    </constraints>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" image="btn_proj_q"/>
                                    <state key="selected" image="btn-proj-sel"/>
                                    <connections>
                                        <action selector="castAction:" destination="-1" eventType="touchUpInside" id="wP9-G2-ZEU"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="rem-Kt-gSH" userLabel="分享">
                                    <rect key="frame" x="0.0" y="0.0" width="22" height="22"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="22" id="RSD-VP-nld"/>
                                    </constraints>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" image="plv_vod_btn_share"/>
                                    <connections>
                                        <action selector="shareAction:" destination="-1" eventType="touchUpInside" id="XDa-yU-6A6"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pqT-93-2RH" userLabel="设置">
                                    <rect key="frame" x="57" y="0.0" width="22" height="22"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="22" id="hE9-Ac-oVC"/>
                                    </constraints>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" image="plv_vod_btn_settings"/>
                                    <connections>
                                        <action selector="settingAction:" destination="-1" eventType="touchUpInside" id="eXv-Qs-YKp"/>
                                    </connections>
                                </button>
                            </subviews>
                        </stackView>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BYi-5F-3u5" userLabel="返回">
                            <rect key="frame" x="20" y="11.5" width="510" height="21"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <inset key="contentEdgeInsets" minX="0.0" minY="0.0" maxX="60" maxY="0.0"/>
                            <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                            <state key="normal" title="我是标题" image="plv_vod_btn_back"/>
                            <connections>
                                <action selector="backAction:" destination="-1" eventType="touchUpInside" id="FxY-8M-gb2"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" red="0.0" green="0.062745098040000002" blue="0.1058823529" alpha="0.69999999999999996" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstItem="Fh2-rt-Zgw" firstAttribute="leading" secondItem="BYi-5F-3u5" secondAttribute="trailing" constant="38" id="26A-V6-e9F"/>
                        <constraint firstAttribute="height" constant="44" id="2EI-ON-Tm5"/>
                        <constraint firstAttribute="trailing" secondItem="Fh2-rt-Zgw" secondAttribute="trailing" constant="20" id="VnF-th-5tN"/>
                        <constraint firstItem="Fh2-rt-Zgw" firstAttribute="centerY" secondItem="pEp-Xe-LQj" secondAttribute="centerY" id="Z32-dg-Xbc"/>
                        <constraint firstItem="BYi-5F-3u5" firstAttribute="centerY" secondItem="pEp-Xe-LQj" secondAttribute="centerY" id="r2U-FV-Lyb"/>
                        <constraint firstItem="BYi-5F-3u5" firstAttribute="leading" secondItem="pEp-Xe-LQj" secondAttribute="leading" constant="20" id="shy-je-J36"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Yl1-Wc-fv6" userLabel="底部工具栏">
                    <rect key="frame" x="0.0" y="321" width="667" height="54"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" spacing="17" translatesAutoresizingMaskIntoConstraints="NO" id="2hd-na-E3E" userLabel="左侧控件容器">
                            <rect key="frame" x="20" y="16" width="372" height="22"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="s1w-XP-Fw3" userLabel="播放/暂停">
                                    <rect key="frame" x="0.0" y="0.0" width="14" height="22"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="14" id="5kk-CX-Fbi"/>
                                    </constraints>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" image="plv_vod_btn_play"/>
                                    <state key="selected" image="plv_vod_btn_pause"/>
                                </button>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="00:00 / 20:03" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="14" translatesAutoresizingMaskIntoConstraints="NO" id="mlF-RW-YrS" userLabel="时间标签">
                                    <rect key="frame" x="31" y="0.0" width="341" height="22"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                        </stackView>
                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" spacing="15" translatesAutoresizingMaskIntoConstraints="NO" id="Wvi-ry-FS3" userLabel="右侧控件容器">
                            <rect key="frame" x="392" y="13.5" width="255" height="27"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="FGz-CD-Tfj" userLabel="弹幕开关">
                                    <rect key="frame" x="0.0" y="0.0" width="33" height="27"/>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" image="plv_vod_btn_dm_off"/>
                                    <state key="selected" image="plv_vod_btn_dm_on"/>
                                    <connections>
                                        <action selector="danmuButtonAction:" destination="-1" eventType="touchUpInside" id="1RH-gv-Oc3"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" selected="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GhR-7D-owl" userLabel="软硬解">
                                    <rect key="frame" x="48" y="0.0" width="32" height="27"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="32" id="UUd-x9-AQy"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" title="硬解">
                                        <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <state key="selected">
                                        <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="videotoolboxAction:" destination="-1" eventType="touchUpInside" id="4sf-cA-xyD"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" selected="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="vun-eN-a4o" userLabel="清晰度">
                                    <rect key="frame" x="95" y="0.0" width="32" height="27"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="32" id="Nfb-Gd-QHt"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" title="高清">
                                        <color key="titleColor" red="0.078431372549019607" green="0.63529411764705879" blue="0.95686274509803915" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <state key="selected">
                                        <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="definitionAction:" destination="-1" eventType="touchUpInside" id="33a-yM-aT1"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" selected="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="F67-0j-oen" userLabel="速率">
                                    <rect key="frame" x="142" y="0.0" width="32" height="27"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="32" id="pSE-Mn-V3W"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" title="1x"/>
                                    <state key="selected">
                                        <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="playbackRateAction:" destination="-1" eventType="touchUpInside" id="kau-mP-SKQ"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="XtI-Mj-Vii" userLabel="线路">
                                    <rect key="frame" x="189" y="0.0" width="32" height="27"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="32" id="aGd-Wu-5Qt"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <state key="normal" title="线路">
                                        <color key="titleColor" red="0.0" green="0.70196078429999997" blue="0.96862745100000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="routeLineAction:" destination="-1" eventType="touchUpInside" id="K6i-9i-3Th"/>
                                    </connections>
                                </button>
                                <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="MKf-s6-nIj" userLabel="知识点">
                                    <rect key="frame" x="228.5" y="0.0" width="10" height="27"/>
                                    <constraints>
                                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="10" id="UGc-uC-Mz4"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <state key="normal" title="知识点">
                                        <color key="titleColor" red="0.13725490200000001" green="0.66274509800000003" blue="0.96470588239999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="knowledgeButtonAction:" destination="-1" eventType="touchUpInside" id="UJ8-CP-zEK"/>
                                    </connections>
                                </button>
                                <button hidden="YES" opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="EpI-TO-oqU" userLabel="小窗播放">
                                    <rect key="frame" x="228.5" y="0.0" width="19" height="27"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="19" id="1af-Xr-lPI"/>
                                    </constraints>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" image="plv_vod_btn_floating"/>
                                    <connections>
                                        <action selector="floatingButtonAction:" destination="-1" eventType="touchUpInside" id="Yhs-Mc-UoQ"/>
                                    </connections>
                                </button>
                                <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="MeN-Dc-0Hd" userLabel="目录">
                                    <rect key="frame" x="228.5" y="0.0" width="32" height="27"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="32" id="xXJ-bW-hB2"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <state key="normal" title="目录">
                                        <color key="titleColor" red="0.13725490200000001" green="0.66274509800000003" blue="0.96470588239999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="pptCatalogButtonnAction:" destination="-1" eventType="touchUpInside" id="Xdx-GP-J6D"/>
                                    </connections>
                                </button>
                                <button hidden="YES" opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Dny-xo-50l" userLabel="关闭副屏">
                                    <rect key="frame" x="228.5" y="0.0" width="19" height="27"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="19" id="CPJ-dz-RUg"/>
                                    </constraints>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" image="plv_vod_btn-exitFullScreen"/>
                                    <state key="selected" image="plv_vod_btn_exfull"/>
                                    <connections>
                                        <action selector="subScreenButtonAction:" destination="-1" eventType="touchUpInside" id="DK9-eZ-tII"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleAspectFit" selected="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="PhT-m4-gZI" userLabel="全屏/半屏">
                                    <rect key="frame" x="236" y="0.0" width="19" height="27"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="19" id="pXt-4d-FHM"/>
                                    </constraints>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" image="plv_vod_btn_exfull"/>
                                    <state key="selected" image="plv_vod_btn_exfull"/>
                                    <connections>
                                        <action selector="switchScreenAction:" destination="-1" eventType="touchUpInside" id="IXQ-iD-Abt"/>
                                    </connections>
                                </button>
                            </subviews>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" red="0.0" green="0.062745098040000002" blue="0.1058823529" alpha="0.69999999999999996" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstItem="2hd-na-E3E" firstAttribute="leading" secondItem="Yl1-Wc-fv6" secondAttribute="leading" constant="20" id="3BS-md-Hxg"/>
                        <constraint firstItem="Wvi-ry-FS3" firstAttribute="leading" secondItem="2hd-na-E3E" secondAttribute="trailing" id="Kz6-lJ-cuU"/>
                        <constraint firstAttribute="trailing" secondItem="Wvi-ry-FS3" secondAttribute="trailing" constant="20" id="NcR-ki-HKH"/>
                        <constraint firstAttribute="height" constant="54" id="ciY-mU-Ezd"/>
                        <constraint firstItem="Wvi-ry-FS3" firstAttribute="centerY" secondItem="Yl1-Wc-fv6" secondAttribute="centerY" id="sMQ-qF-nrG"/>
                        <constraint firstItem="2hd-na-E3E" firstAttribute="centerY" secondItem="Yl1-Wc-fv6" secondAttribute="centerY" id="uda-Bc-lCi"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BuY-ou-2r2" userLabel="状态栏">
                    <rect key="frame" x="0.0" y="0.0" width="667" height="0.0"/>
                    <color key="backgroundColor" red="0.0" green="0.062745098040000002" blue="0.1058823529" alpha="0.69999999999999996" colorSpace="calibratedRGB"/>
                </view>
                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="rSx-dm-mU2" userLabel="右侧控件容器">
                    <rect key="frame" x="597" y="157.5" width="60" height="60"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="T4D-xH-rSz" userLabel="截图">
                            <rect key="frame" x="0.0" y="0.0" width="60" height="60"/>
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal" image="plv_vod_btn_screenshot"/>
                            <connections>
                                <action selector="snapshotAction:" destination="-1" eventType="touchUpInside" id="JMQ-mL-JaF"/>
                            </connections>
                        </button>
                        <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="h89-Es-caU" userLabel="弹幕">
                            <rect key="frame" x="0.0" y="0.0" width="60" height="0.0"/>
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal" image="plv_vod_btn_send_dm"/>
                            <connections>
                                <action selector="danmuAction:" destination="-1" eventType="touchUpInside" id="0Up-iU-eKa"/>
                            </connections>
                        </button>
                    </subviews>
                </stackView>
                <progressView opaque="NO" contentMode="scaleToFill" verticalHuggingPriority="750" progress="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="F6C-pN-1tf" userLabel="缓冲进度">
                    <rect key="frame" x="0.0" y="319" width="667" height="2"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <color key="tintColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="0.69587435789999996" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="2" id="kLk-QF-kSW"/>
                    </constraints>
                    <color key="trackTintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </progressView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="egy-dp-xa1" userLabel="播放滑杆背景视图">
                    <rect key="frame" x="0.0" y="304" width="667" height="30"/>
                    <subviews>
                        <slider opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" minValue="0.0" maxValue="1" translatesAutoresizingMaskIntoConstraints="NO" id="suR-o2-ESP" userLabel="播放滑杆">
                            <rect key="frame" x="-2" y="0.0" width="671" height="31"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <color key="tintColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                            <color key="maximumTrackTintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </slider>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="suR-o2-ESP" secondAttribute="trailing" id="Vkw-R1-B7b"/>
                        <constraint firstItem="suR-o2-ESP" firstAttribute="centerY" secondItem="egy-dp-xa1" secondAttribute="centerY" id="Vna-mW-fU1"/>
                        <constraint firstAttribute="height" constant="30" id="gYY-JP-X4I"/>
                        <constraint firstItem="suR-o2-ESP" firstAttribute="leading" secondItem="egy-dp-xa1" secondAttribute="leading" id="kFK-7M-P8a"/>
                    </constraints>
                </view>
                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="top" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="Eay-Sz-PzG">
                    <rect key="frame" x="10" y="127.5" width="75" height="120"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" adjustsImageWhenHighlighted="NO" adjustsImageWhenDisabled="NO" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="FVm-eL-fOM">
                            <rect key="frame" x="0.0" y="0.0" width="32" height="32"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="32" id="ZZ9-Hi-86n"/>
                                <constraint firstAttribute="width" constant="32" id="qRc-IV-VVh"/>
                            </constraints>
                            <state key="normal" backgroundImage="plv_btn_unlockscreen"/>
                            <connections>
                                <action selector="lockScreenAction:" destination="-1" eventType="touchUpInside" id="jJC-0W-1eT"/>
                            </connections>
                        </button>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hTL-qF-dKU" userLabel="左侧音视频切换容器">
                            <rect key="frame" x="0.0" y="42" width="75" height="78"/>
                            <subviews>
                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" image="plv_vod_av_swtich_bg" translatesAutoresizingMaskIntoConstraints="NO" id="J9R-zW-sSy" userLabel="音视频切换按钮组背景">
                                    <rect key="frame" x="0.0" y="0.0" width="36" height="78"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                </imageView>
                                <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="S3W-5y-g2i" userLabel="视频组件容器">
                                    <rect key="frame" x="2" y="2" width="75" height="32"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <subviews>
                                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" image="plv_vod_av_swtich_selected" translatesAutoresizingMaskIntoConstraints="NO" id="WVH-Bi-tNJ" userLabel="视频播放状态图">
                                            <rect key="frame" x="0.0" y="0.0" width="32" height="32"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        </imageView>
                                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" image="plv_vod_av_swtich_video" translatesAutoresizingMaskIntoConstraints="NO" id="6Ky-Yj-Bc3" userLabel="视频标志图">
                                            <rect key="frame" x="8" y="10" width="16" height="12"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="视频" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" highlighted="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QbR-Nf-sVG" userLabel="视频文字">
                                            <rect key="frame" x="40" y="8" width="25" height="15"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <color key="highlightedColor" red="0.01176470588" green="0.66274509800000003" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                        </label>
                                        <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="IV7-JI-VVF" userLabel="视频切换按钮">
                                            <rect key="frame" x="0.0" y="0.0" width="75" height="32"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                            <connections>
                                                <action selector="videoPlaybackModeAction:" destination="-1" eventType="touchUpInside" id="8wD-Pz-aLn"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </view>
                                <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="gwD-Sy-vxV" userLabel="音频组件容器">
                                    <rect key="frame" x="2" y="44" width="75" height="32"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <subviews>
                                        <imageView hidden="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" image="plv_vod_av_swtich_selected" translatesAutoresizingMaskIntoConstraints="NO" id="yFk-Eh-S1F" userLabel="音频播放状态图">
                                            <rect key="frame" x="0.0" y="0.0" width="32" height="32"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        </imageView>
                                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" image="plv_vod_av_swtich_audio" translatesAutoresizingMaskIntoConstraints="NO" id="FTa-1z-Pdz" userLabel="音频标志图">
                                            <rect key="frame" x="10" y="8" width="12" height="16"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="音频" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Qe2-sb-peP" userLabel="音频文字">
                                            <rect key="frame" x="40" y="8" width="25" height="15"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <color key="highlightedColor" red="0.01176470588" green="0.66274509800000003" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                        </label>
                                        <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bg9-DH-N1x" userLabel="音频切换按钮">
                                            <rect key="frame" x="0.0" y="0.0" width="75" height="32"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                            <connections>
                                                <action selector="audioPlaybackModeAction:" destination="-1" eventType="touchUpInside" id="Dk1-vG-Ngq"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </view>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="75" id="GXY-RF-vmW"/>
                                <constraint firstAttribute="height" constant="78" id="qmE-gF-X6Y"/>
                            </constraints>
                        </view>
                    </subviews>
                    <constraints>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="75" id="KAF-mA-5YX"/>
                    </constraints>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="8mb-VS-b1r"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="pEp-Xe-LQj" firstAttribute="top" secondItem="BuY-ou-2r2" secondAttribute="bottom" id="05a-ZY-Aon"/>
                <constraint firstAttribute="trailing" secondItem="BuY-ou-2r2" secondAttribute="trailing" id="2cx-7t-vbb"/>
                <constraint firstItem="pEp-Xe-LQj" firstAttribute="top" secondItem="BuY-ou-2r2" secondAttribute="bottom" id="5Mf-fe-d92"/>
                <constraint firstAttribute="trailing" secondItem="F6C-pN-1tf" secondAttribute="trailing" id="8XU-9M-OLy"/>
                <constraint firstAttribute="trailing" secondItem="egy-dp-xa1" secondAttribute="trailing" id="A0n-pR-428"/>
                <constraint firstAttribute="bottom" secondItem="Yl1-Wc-fv6" secondAttribute="bottom" id="AMN-Hj-lD8"/>
                <constraint firstItem="Yl1-Wc-fv6" firstAttribute="top" secondItem="F6C-pN-1tf" secondAttribute="bottom" id="Bn2-le-m3n"/>
                <constraint firstItem="8mb-VS-b1r" firstAttribute="trailing" secondItem="rSx-dm-mU2" secondAttribute="trailing" constant="10" id="Lgt-B4-lo9"/>
                <constraint firstItem="Yl1-Wc-fv6" firstAttribute="leading" secondItem="w04-rs-iMM" secondAttribute="leading" id="N8Q-ZJ-sYg"/>
                <constraint firstItem="BuY-ou-2r2" firstAttribute="top" secondItem="w04-rs-iMM" secondAttribute="top" id="Nbs-rL-q02"/>
                <constraint firstItem="pEp-Xe-LQj" firstAttribute="leading" secondItem="w04-rs-iMM" secondAttribute="leading" id="P1z-ht-qbH"/>
                <constraint firstItem="Eay-Sz-PzG" firstAttribute="leading" secondItem="w04-rs-iMM" secondAttribute="leading" constant="10" id="PhZ-OI-njl"/>
                <constraint firstItem="F6C-pN-1tf" firstAttribute="centerY" secondItem="egy-dp-xa1" secondAttribute="centerY" constant="1" id="Y5h-FJ-s6e"/>
                <constraint firstItem="BuY-ou-2r2" firstAttribute="leading" secondItem="w04-rs-iMM" secondAttribute="leading" id="bui-Kl-w8g"/>
                <constraint firstItem="F6C-pN-1tf" firstAttribute="leading" secondItem="w04-rs-iMM" secondAttribute="leading" id="cwR-0w-clH"/>
                <constraint firstItem="pEp-Xe-LQj" firstAttribute="top" secondItem="BuY-ou-2r2" secondAttribute="bottom" id="fdZ-t5-uyk"/>
                <constraint firstItem="Eay-Sz-PzG" firstAttribute="centerY" secondItem="w04-rs-iMM" secondAttribute="centerY" id="hGC-TY-xo0"/>
                <constraint firstAttribute="trailing" secondItem="pEp-Xe-LQj" secondAttribute="trailing" id="lXn-Qm-a0T"/>
                <constraint firstItem="egy-dp-xa1" firstAttribute="leading" secondItem="w04-rs-iMM" secondAttribute="leading" id="rgl-mr-wIk"/>
                <constraint firstAttribute="trailing" secondItem="Yl1-Wc-fv6" secondAttribute="trailing" id="t1P-a6-5Mz"/>
                <constraint firstItem="BuY-ou-2r2" firstAttribute="bottom" secondItem="w04-rs-iMM" secondAttribute="topMargin" priority="750" constant="-8" id="v2b-Vv-ggE"/>
                <constraint firstItem="rSx-dm-mU2" firstAttribute="centerY" secondItem="w04-rs-iMM" secondAttribute="centerY" id="ywk-6J-sYF"/>
            </constraints>
            <connections>
                <outlet property="audioModeLabel" destination="Qe2-sb-peP" id="UhC-0H-uJG"/>
                <outlet property="audioModeSelectedImageView" destination="yFk-Eh-S1F" id="knz-dh-Bwb"/>
                <outlet property="audioPlayModeButton" destination="bg9-DH-N1x" id="lBq-Ah-ZGC"/>
                <outlet property="backButton" destination="BYi-5F-3u5" id="lRK-DM-pCa"/>
                <outlet property="bufferProgressView" destination="F6C-pN-1tf" id="LMx-xv-KL0"/>
                <outlet property="danmuButton" destination="FGz-CD-Tfj" id="UuZ-qh-AQ8"/>
                <outlet property="danmuSendButton" destination="h89-Es-caU" id="LW8-1T-Sxr"/>
                <outlet property="definitionButton" destination="vun-eN-a4o" id="o8H-XJ-3Nq"/>
                <outlet property="floatingButton" destination="EpI-TO-oqU" id="DKO-r7-V0n"/>
                <outlet property="knowledgeButton" destination="MKf-s6-nIj" id="AeV-Ay-6hT"/>
                <outlet property="lockButtonLeading" destination="PhZ-OI-njl" id="nwm-XQ-IM3"/>
                <outlet property="lockScreenButton" destination="FVm-eL-fOM" id="kqR-YQ-VCc"/>
                <outlet property="playModeContainerView" destination="hTL-qF-dKU" id="bgm-L0-XxR"/>
                <outlet property="playPauseButton" destination="s1w-XP-Fw3" id="ZZv-xl-UcC"/>
                <outlet property="playbackRateButton" destination="F67-0j-oen" id="Z7Z-Cw-nnm"/>
                <outlet property="playbackSlider" destination="suR-o2-ESP" id="bU8-Zt-jmK"/>
                <outlet property="pptCatalogButton" destination="MeN-Dc-0Hd" id="ydK-o1-Chv"/>
                <outlet property="routeButton" destination="XtI-Mj-Vii" id="MBG-aQ-iW8"/>
                <outlet property="settingButton" destination="pqT-93-2RH" id="G67-xX-s81"/>
                <outlet property="shareButton" destination="rem-Kt-gSH" id="I1B-vu-XnK"/>
                <outlet property="sliderBackView" destination="egy-dp-xa1" id="inP-uV-z3m"/>
                <outlet property="snapshotButton" destination="T4D-xH-rSz" id="gCl-FT-IwT"/>
                <outlet property="statusBarHeight" destination="v2b-Vv-ggE" id="Gmm-cb-Zjb"/>
                <outlet property="subScreenButton" destination="Dny-xo-50l" id="fUi-d4-2Be"/>
                <outlet property="switchScreenButton" destination="PhT-m4-gZI" id="0mM-zZ-QFQ"/>
                <outlet property="timeLabel" destination="mlF-RW-YrS" id="blc-4p-10C"/>
                <outlet property="videoModeLabel" destination="QbR-Nf-sVG" id="n8Z-c0-toN"/>
                <outlet property="videoModeSelectedImageView" destination="WVH-Bi-tNJ" id="ySh-Dd-z8g"/>
                <outlet property="videoPlayModeButton" destination="IV7-JI-VVF" id="tk3-Gd-XA3"/>
                <outlet property="videoToolBoxButton" destination="GhR-7D-owl" id="USA-wf-HVf"/>
            </connections>
            <point key="canvasLocation" x="25" y="881.25"/>
        </view>
        <view contentMode="scaleToFill" id="WML-5j-ft2" userLabel="综合设置面板" customClass="PLVVodSettingPanelView">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="top" axis="vertical" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="UCW-u5-Hg9" userLabel="字幕设置">
                    <rect key="frame" x="309" y="267" width="49" height="96"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4ql-Pe-EdN">
                            <rect key="frame" x="0.0" y="0.0" width="49" height="32"/>
                            <fontDescription key="fontDescription" type="system" pointSize="16"/>
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal" title="字幕一"/>
                            <state key="selected">
                                <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                            <connections>
                                <action selector="subtitleButtonAction:" destination="WML-5j-ft2" eventType="touchUpInside" id="FIf-wi-xzG"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="wHX-GC-5cy">
                            <rect key="frame" x="0.0" y="32" width="49" height="32"/>
                            <fontDescription key="fontDescription" type="system" pointSize="16"/>
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal" title="字幕二"/>
                            <state key="selected">
                                <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                            <connections>
                                <action selector="subtitleButtonAction:" destination="WML-5j-ft2" eventType="touchUpInside" id="tSF-3P-iP4"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="VBB-Ir-xLr">
                            <rect key="frame" x="0.0" y="64" width="49" height="32"/>
                            <fontDescription key="fontDescription" type="system" pointSize="16"/>
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal" title="不显示"/>
                            <state key="selected">
                                <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                            <connections>
                                <action selector="subtitleButtonAction:" destination="WML-5j-ft2" eventType="touchUpInside" id="qFX-Ib-KMd"/>
                            </connections>
                        </button>
                    </subviews>
                    <constraints>
                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="21" id="aFV-4e-KYl"/>
                    </constraints>
                </stackView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rVR-D6-8jg" userLabel="控件容器" customClass="UIStackView">
                    <rect key="frame" x="188.5" y="44" width="290" height="211"/>
                    <subviews>
                        <slider opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="0.5" minValue="0.0" maxValue="1" minimumValueImage="plv_vod_ic_bri_s" maximumValueImage="plv_vod_ic_bri_l" translatesAutoresizingMaskIntoConstraints="NO" id="OVG-eh-nAB" userLabel="亮度滑杆">
                            <rect key="frame" x="-2" y="0.0" width="294" height="14"/>
                            <color key="tintColor" red="0.078431372549019607" green="0.63529411764705879" blue="0.95686274509803915" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="290" id="3Yn-S9-HP5"/>
                                <constraint firstAttribute="height" relation="lessThanOrEqual" constant="13" id="oTi-p5-dw5"/>
                            </constraints>
                        </slider>
                        <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" spacing="50" translatesAutoresizingMaskIntoConstraints="NO" id="F9r-qq-une" userLabel="视频填充设置">
                            <rect key="frame" x="4" y="103" width="282" height="32"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="uuT-gD-j1r">
                                    <rect key="frame" x="0.0" y="0.0" width="33" height="32"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" title="居中"/>
                                    <state key="selected">
                                        <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="scaleModeButtonAction:" destination="WML-5j-ft2" eventType="touchUpInside" id="KyB-dz-mEW"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" horizontalCompressionResistancePriority="751" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="85l-BV-nNQ">
                                    <rect key="frame" x="83" y="0.0" width="33" height="32"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" title="适应"/>
                                    <state key="selected">
                                        <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="scaleModeButtonAction:" destination="WML-5j-ft2" eventType="touchUpInside" id="Nf6-14-JGo"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" horizontalCompressionResistancePriority="752" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="W7r-bD-Nj4">
                                    <rect key="frame" x="166" y="0.0" width="33" height="32"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" title="填充"/>
                                    <state key="selected">
                                        <color key="titleColor" red="0.0" green="0.70090252161026001" blue="0.96674615144729614" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="scaleModeButtonAction:" destination="WML-5j-ft2" eventType="touchUpInside" id="6Eq-mI-Pmw"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" horizontalCompressionResistancePriority="753" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kEB-lT-H0C">
                                    <rect key="frame" x="249" y="0.0" width="33" height="32"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <state key="normal" title="拉伸"/>
                                    <state key="selected">
                                        <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="scaleModeButtonAction:" destination="WML-5j-ft2" eventType="touchUpInside" id="r3N-ku-Nfd"/>
                                    </connections>
                                </button>
                            </subviews>
                        </stackView>
                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="11" translatesAutoresizingMaskIntoConstraints="NO" id="K0j-5l-bjM" userLabel="字幕">
                            <rect key="frame" x="0.0" y="179" width="290" height="32"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1oI-qq-v5j" userLabel="分割线L">
                                    <rect key="frame" x="0.0" y="15.5" width="116.5" height="1"/>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="1" id="tQT-nb-d9R"/>
                                    </constraints>
                                </view>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="字幕" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qTY-El-M9v" userLabel="标签">
                                    <rect key="frame" x="127.5" y="6" width="35" height="20.5"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="35" id="tce-Ig-QYN"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NMv-ef-oyr" userLabel="分割线R">
                                    <rect key="frame" x="173.5" y="15.5" width="116.5" height="1"/>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="1" id="PNg-mg-s5j"/>
                                    </constraints>
                                </view>
                            </subviews>
                            <constraints>
                                <constraint firstItem="NMv-ef-oyr" firstAttribute="width" secondItem="1oI-qq-v5j" secondAttribute="width" id="QIO-hY-LNo"/>
                            </constraints>
                        </stackView>
                        <slider opaque="NO" contentMode="scaleToFill" verticalCompressionResistancePriority="749" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="0.5" minValue="0.0" maxValue="1" minimumValueImage="plv_vod_ic_vol_s" maximumValueImage="plv_vod_ic_vol_l" translatesAutoresizingMaskIntoConstraints="NO" id="Ffk-ye-6WX" userLabel="音量滑杆">
                            <rect key="frame" x="0.0" y="57" width="290" height="3"/>
                            <color key="tintColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" relation="lessThanOrEqual" constant="13" id="HQF-1d-BeF"/>
                            </constraints>
                        </slider>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="Ffk-ye-6WX" firstAttribute="top" secondItem="OVG-eh-nAB" secondAttribute="bottom" constant="44" id="2gA-mo-aan"/>
                        <constraint firstItem="K0j-5l-bjM" firstAttribute="centerX" secondItem="rVR-D6-8jg" secondAttribute="centerX" id="6sF-hH-yDY"/>
                        <constraint firstItem="K0j-5l-bjM" firstAttribute="width" secondItem="OVG-eh-nAB" secondAttribute="width" id="8Jh-hd-kJq"/>
                        <constraint firstItem="K0j-5l-bjM" firstAttribute="height" secondItem="F9r-qq-une" secondAttribute="height" id="Ics-yh-PfO"/>
                        <constraint firstAttribute="trailing" secondItem="OVG-eh-nAB" secondAttribute="trailing" id="Ods-aX-mJw"/>
                        <constraint firstAttribute="bottom" secondItem="K0j-5l-bjM" secondAttribute="bottom" id="R4P-Us-A5j"/>
                        <constraint firstItem="Ffk-ye-6WX" firstAttribute="width" secondItem="OVG-eh-nAB" secondAttribute="width" multiplier="0.986207" id="Sa1-u5-QYX"/>
                        <constraint firstItem="F9r-qq-une" firstAttribute="centerX" secondItem="rVR-D6-8jg" secondAttribute="centerX" id="UwL-Tu-DVf"/>
                        <constraint firstItem="OVG-eh-nAB" firstAttribute="centerX" secondItem="rVR-D6-8jg" secondAttribute="centerX" id="VOQ-EW-aPD"/>
                        <constraint firstItem="F9r-qq-une" firstAttribute="top" secondItem="Ffk-ye-6WX" secondAttribute="bottom" constant="44" id="Vse-ru-pLG"/>
                        <constraint firstItem="Ffk-ye-6WX" firstAttribute="centerX" secondItem="rVR-D6-8jg" secondAttribute="centerX" id="l6e-4T-S3s"/>
                        <constraint firstItem="K0j-5l-bjM" firstAttribute="top" secondItem="F9r-qq-une" secondAttribute="bottom" constant="44" id="nJf-Z3-Lih"/>
                        <constraint firstItem="F9r-qq-une" firstAttribute="width" secondItem="OVG-eh-nAB" secondAttribute="width" multiplier="0.972414" id="rhW-XP-vTc"/>
                        <constraint firstItem="OVG-eh-nAB" firstAttribute="top" secondItem="rVR-D6-8jg" secondAttribute="top" id="ufg-zj-pGc"/>
                        <constraint firstItem="OVG-eh-nAB" firstAttribute="leading" secondItem="rVR-D6-8jg" secondAttribute="leading" id="xxb-yU-gGZ"/>
                    </constraints>
                    <connections>
                        <outletCollection property="gestureRecognizers" destination="1bO-LR-CK4" appends="YES" id="BkJ-qw-BM1"/>
                    </connections>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="3cu-fT-JeW"/>
            <color key="backgroundColor" red="0.0" green="0.062745098040000002" blue="0.1058823529" alpha="0.69999999999999996" colorSpace="calibratedRGB"/>
            <gestureRecognizers/>
            <constraints>
                <constraint firstItem="rVR-D6-8jg" firstAttribute="top" secondItem="3cu-fT-JeW" secondAttribute="top" constant="44" id="0KY-x6-P9d"/>
                <constraint firstItem="UCW-u5-Hg9" firstAttribute="bottom" relation="lessThanOrEqual" secondItem="3cu-fT-JeW" secondAttribute="bottom" constant="-12" id="Ltx-8f-WIN"/>
                <constraint firstItem="UCW-u5-Hg9" firstAttribute="centerX" secondItem="WML-5j-ft2" secondAttribute="centerX" id="lUc-Gc-7Lx"/>
                <constraint firstItem="rVR-D6-8jg" firstAttribute="centerX" secondItem="WML-5j-ft2" secondAttribute="centerX" id="mo8-gK-92M"/>
                <constraint firstItem="UCW-u5-Hg9" firstAttribute="top" secondItem="K0j-5l-bjM" secondAttribute="bottom" constant="12" id="n2V-ES-TzF"/>
            </constraints>
            <connections>
                <outlet property="brightnessSlider" destination="OVG-eh-nAB" id="fba-jl-3pA"/>
                <outlet property="containerStackView" destination="rVR-D6-8jg" id="aPY-WN-xiR"/>
                <outlet property="scalingModeStackView" destination="F9r-qq-une" id="aR5-l8-lQX"/>
                <outlet property="subtitleSeparateStackView" destination="K0j-5l-bjM" id="yS2-zo-Azx"/>
                <outlet property="subtitleStackView" destination="UCW-u5-Hg9" id="2Le-rv-Rhy"/>
                <outlet property="volumeSlider" destination="Ffk-ye-6WX" id="1PQ-Xk-ZaC"/>
            </connections>
            <point key="canvasLocation" x="729" y="893"/>
        </view>
        <view contentMode="scaleToFill" id="inz-cI-pDt" userLabel="清晰度选择" customClass="PLVVodDefinitionPanelView">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" alignment="center" spacing="100" translatesAutoresizingMaskIntoConstraints="NO" id="kjX-nj-aOb">
                    <rect key="frame" x="297" y="167" width="73" height="41"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="p1i-MR-j8y">
                            <rect key="frame" x="0.0" y="0.0" width="73" height="41"/>
                            <fontDescription key="fontDescription" type="system" pointSize="24"/>
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal" title="不可用"/>
                            <state key="selected">
                                <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                        </button>
                    </subviews>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="t1I-gZ-RQi"/>
            <color key="backgroundColor" red="0.0" green="0.062745098040000002" blue="0.1058823529" alpha="0.69999999999999996" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="kjX-nj-aOb" firstAttribute="centerX" secondItem="inz-cI-pDt" secondAttribute="centerX" id="Ddx-64-QuU"/>
                <constraint firstItem="kjX-nj-aOb" firstAttribute="centerY" secondItem="inz-cI-pDt" secondAttribute="centerY" id="SAk-du-6nu"/>
            </constraints>
            <connections>
                <outlet property="qualityStackView" destination="kjX-nj-aOb" id="Skm-up-Ncx"/>
            </connections>
            <point key="canvasLocation" x="26" y="1733"/>
        </view>
        <view contentMode="scaleToFill" id="Y0G-ge-dTU" userLabel="软硬解选择" customClass="PLVVodVideoToolBoxPanelView">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" alignment="center" spacing="100" translatesAutoresizingMaskIntoConstraints="NO" id="wMI-qO-j0V">
                    <rect key="frame" x="297" y="167" width="73" height="41"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DuN-vF-6Ob">
                            <rect key="frame" x="0.0" y="0.0" width="73" height="41"/>
                            <fontDescription key="fontDescription" type="system" pointSize="24"/>
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal" title="不可用"/>
                            <state key="selected">
                                <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                        </button>
                    </subviews>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="x0A-mS-E4u"/>
            <color key="backgroundColor" red="0.0" green="0.062745098040000002" blue="0.1058823529" alpha="0.69999999999999996" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="wMI-qO-j0V" firstAttribute="centerY" secondItem="Y0G-ge-dTU" secondAttribute="centerY" id="j6i-xH-Twt"/>
                <constraint firstItem="wMI-qO-j0V" firstAttribute="centerX" secondItem="Y0G-ge-dTU" secondAttribute="centerX" id="ywt-34-pr5"/>
            </constraints>
            <connections>
                <outlet property="videoToolBoxStackView" destination="wMI-qO-j0V" id="Tmy-RO-gUd"/>
            </connections>
            <point key="canvasLocation" x="26" y="1733"/>
        </view>
        <view contentMode="scaleToFill" id="MZT-mj-7ks" userLabel="速率选择" customClass="PLVVodPlaybackRatePanelView">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" translatesAutoresizingMaskIntoConstraints="NO" id="CrX-Ob-dsy">
                    <rect key="frame" x="20" y="167" width="627" height="41"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="m7X-YV-H58">
                            <rect key="frame" x="0.0" y="0.0" width="627" height="41"/>
                            <fontDescription key="fontDescription" type="system" pointSize="24"/>
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal" title="不可用"/>
                            <state key="selected">
                                <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                        </button>
                    </subviews>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="lxo-sE-N4i"/>
            <color key="backgroundColor" red="0.0" green="0.062745098040000002" blue="0.1058823529" alpha="0.69999999999999996" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="CrX-Ob-dsy" firstAttribute="centerX" secondItem="MZT-mj-7ks" secondAttribute="centerX" id="Cda-Cm-S4L"/>
                <constraint firstItem="CrX-Ob-dsy" firstAttribute="leading" secondItem="MZT-mj-7ks" secondAttribute="leading" constant="20" id="NYp-ro-RZc"/>
                <constraint firstItem="CrX-Ob-dsy" firstAttribute="centerY" secondItem="MZT-mj-7ks" secondAttribute="centerY" id="X9c-yv-J8o"/>
                <constraint firstAttribute="trailing" secondItem="CrX-Ob-dsy" secondAttribute="trailing" constant="20" id="eVZ-2O-5iM"/>
            </constraints>
            <connections>
                <outlet property="playbackRateStackView" destination="CrX-Ob-dsy" id="gRR-aa-msc"/>
            </connections>
            <point key="canvasLocation" x="729" y="1733"/>
        </view>
        <tapGestureRecognizer id="OPa-9b-dc4" userLabel="点击面板">
            <connections>
                <action selector="backMainControl:" destination="-1" id="rtP-vh-Pba"/>
            </connections>
        </tapGestureRecognizer>
        <view contentMode="scaleToFill" id="BZZ-yq-7JA" userLabel="分享选择">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" spacing="57" translatesAutoresizingMaskIntoConstraints="NO" id="SMa-Vu-mCr">
                    <rect key="frame" x="184" y="144" width="299.5" height="87.5"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="isO-vR-aZJ">
                            <rect key="frame" x="0.0" y="0.0" width="60" height="87.5"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="KGS-sV-JHQ" userLabel="QQ">
                                    <rect key="frame" x="0.0" y="0.0" width="60" height="60"/>
                                    <state key="normal" image="plv_vod_btn_qq"/>
                                </button>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="QQ" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="AyD-EV-znf">
                                    <rect key="frame" x="17.5" y="68" width="24.5" height="19.5"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                        </stackView>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="ttD-J9-lay">
                            <rect key="frame" x="117" y="0.0" width="60" height="87.5"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ear-va-Jra" userLabel="微信">
                                    <rect key="frame" x="0.0" y="0.0" width="60" height="60"/>
                                    <state key="normal" image="plv_vod_btn_wechat"/>
                                </button>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="微信" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="O90-fd-aTm">
                                    <rect key="frame" x="13.5" y="68" width="33" height="19.5"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                        </stackView>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="s33-QQ-i8S">
                            <rect key="frame" x="234" y="0.0" width="65.5" height="87.5"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3rk-y9-CFg" userLabel="新浪微博">
                                    <rect key="frame" x="2.5" y="0.0" width="60" height="60"/>
                                    <state key="normal" image="plv_vod_btn_weibo"/>
                                </button>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="新浪微博" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4Qm-WC-7eD">
                                    <rect key="frame" x="0.0" y="68" width="65.5" height="19.5"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                        </stackView>
                    </subviews>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="aKB-eW-Z1c"/>
            <color key="backgroundColor" red="0.0" green="0.062745098040000002" blue="0.1058823529" alpha="0.69999999999999996" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="SMa-Vu-mCr" firstAttribute="centerY" secondItem="BZZ-yq-7JA" secondAttribute="centerY" id="BgF-We-zhs"/>
                <constraint firstItem="SMa-Vu-mCr" firstAttribute="centerX" secondItem="BZZ-yq-7JA" secondAttribute="centerX" id="Rmx-zH-s1u"/>
            </constraints>
            <point key="canvasLocation" x="26" y="2581"/>
        </view>
        <view contentMode="scaleToFill" id="ggj-qD-OqT" userLabel="手势指示器" customClass="PLVVodGestureIndicatorView">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="plv_vod_bg_s" translatesAutoresizingMaskIntoConstraints="NO" id="BTQ-Du-8T5">
                    <rect key="frame" x="268.5" y="122.5" width="130" height="130"/>
                </imageView>
                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="e6b-OP-RwO">
                    <rect key="frame" x="305" y="147.5" width="57" height="80.5"/>
                    <subviews>
                        <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="plv_vod_ic_brightness" translatesAutoresizingMaskIntoConstraints="NO" id="hEZ-Qz-XpK">
                            <rect key="frame" x="0.0" y="0.0" width="57" height="57"/>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="100%" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="5pg-cX-Fpy">
                            <rect key="frame" x="8" y="61" width="41.5" height="19.5"/>
                            <fontDescription key="fontDescription" type="system" pointSize="16"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <constraints>
                        <constraint firstAttribute="width" relation="lessThanOrEqual" constant="110" id="6qM-fd-vex"/>
                    </constraints>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="2YL-y2-NMY"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="e6b-OP-RwO" firstAttribute="centerX" secondItem="ggj-qD-OqT" secondAttribute="centerX" id="5aR-98-PXC"/>
                <constraint firstItem="BTQ-Du-8T5" firstAttribute="centerY" secondItem="ggj-qD-OqT" secondAttribute="centerY" id="74m-ia-Kje"/>
                <constraint firstItem="e6b-OP-RwO" firstAttribute="centerY" secondItem="ggj-qD-OqT" secondAttribute="centerY" id="M85-dk-PgC"/>
                <constraint firstItem="BTQ-Du-8T5" firstAttribute="centerX" secondItem="ggj-qD-OqT" secondAttribute="centerX" id="nie-ll-EGh"/>
            </constraints>
            <connections>
                <outlet property="indicatorImageView" destination="hEZ-Qz-XpK" id="ZzY-Ae-nhT"/>
                <outlet property="indicatorLabel" destination="5pg-cX-Fpy" id="lP1-BR-QeK"/>
            </connections>
            <point key="canvasLocation" x="729" y="2580"/>
        </view>
        <view contentMode="scaleToFill" id="qa0-Rh-gAf" userLabel="弹幕发送" customClass="PLVVodDanmuSendView">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bkd-fA-fQD" userLabel="底部工具栏">
                    <rect key="frame" x="0.0" y="166" width="667" height="209"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UXE-RF-qSh" userLabel="输入栏">
                            <rect key="frame" x="0.0" y="0.0" width="667" height="44"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" spacing="18" translatesAutoresizingMaskIntoConstraints="NO" id="HxO-Il-rGr" userLabel="输入栏组">
                                    <rect key="frame" x="12" y="8" width="643" height="28"/>
                                    <subviews>
                                        <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="首长，请指示.." textAlignment="natural" minimumFontSize="17" background="plv_vod_input_dm_click" disabledBackground="plv_vod_input_dm_def" translatesAutoresizingMaskIntoConstraints="NO" id="GPc-ot-JIj" userLabel="输入弹幕">
                                            <rect key="frame" x="0.0" y="0.0" width="599" height="28"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                            <textInputTraits key="textInputTraits" spellCheckingType="no" keyboardAppearance="alert" returnKeyType="send" enablesReturnKeyAutomatically="YES"/>
                                            <connections>
                                                <outlet property="delegate" destination="-1" id="fZd-A1-Zcm"/>
                                            </connections>
                                        </textField>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="O9u-wQ-4eV" userLabel="弹幕设置">
                                            <rect key="frame" x="617" y="0.0" width="26" height="28"/>
                                            <state key="normal" image="plv_vod_btn_dm_settings"/>
                                            <state key="selected" image="plv_vod_btn_keyboard"/>
                                            <connections>
                                                <action selector="settingButtonAction:" destination="qa0-Rh-gAf" eventType="touchUpInside" id="GY2-dH-Cq8"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="28" id="5kD-ih-sC7"/>
                                    </constraints>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="HxO-Il-rGr" firstAttribute="leading" secondItem="UXE-RF-qSh" secondAttribute="leadingMargin" constant="4" id="1Ld-CK-biK"/>
                                <constraint firstAttribute="trailingMargin" secondItem="HxO-Il-rGr" secondAttribute="trailing" constant="4" id="3cG-Ed-Oy8"/>
                                <constraint firstItem="HxO-Il-rGr" firstAttribute="centerY" secondItem="UXE-RF-qSh" secondAttribute="centerY" id="VBv-4L-QV1"/>
                                <constraint firstAttribute="height" constant="44" id="sdP-uO-FYS"/>
                            </constraints>
                        </view>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="top" spacing="23" translatesAutoresizingMaskIntoConstraints="NO" id="pdp-XN-RPy" userLabel="弹幕设置组">
                            <rect key="frame" x="120" y="64" width="427.5" height="121"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="fFK-KL-STC" userLabel="弹幕颜色组">
                                    <rect key="frame" x="0.0" y="0.0" width="427.5" height="25"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="弹幕颜色" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fFA-e5-49a">
                                            <rect key="frame" x="0.0" y="0.0" width="57.5" height="25"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="40" translatesAutoresizingMaskIntoConstraints="NO" id="hxu-mA-h92">
                                            <rect key="frame" x="77.5" y="0.0" width="350" height="25"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleAspectFit" selected="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GwE-yk-3Z7" userLabel="颜色">
                                                    <rect key="frame" x="0.0" y="0.0" width="25" height="25"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="25" id="5g1-OU-I74"/>
                                                        <constraint firstAttribute="width" constant="25" id="jWI-zA-ddk"/>
                                                    </constraints>
                                                    <state key="normal" image="plv_vod_btn_white"/>
                                                    <state key="selected" image="plv_vod_btn_white_sel"/>
                                                </button>
                                                <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ItU-d5-icP" userLabel="颜色">
                                                    <rect key="frame" x="65" y="0.0" width="25" height="25"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="25" id="O3U-jh-dXU"/>
                                                        <constraint firstAttribute="height" constant="25" id="fWL-Rg-LXh"/>
                                                    </constraints>
                                                    <state key="normal" image="plv_vod_btn_white"/>
                                                    <state key="selected" image="plv_vod_btn_white_sel"/>
                                                </button>
                                                <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cSr-1P-zjh" userLabel="颜色">
                                                    <rect key="frame" x="130" y="0.0" width="25" height="25"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="25" id="2VD-yP-VNr"/>
                                                        <constraint firstAttribute="height" constant="25" id="N8u-X1-lgQ"/>
                                                    </constraints>
                                                    <state key="normal" image="plv_vod_btn_white"/>
                                                    <state key="selected" image="plv_vod_btn_white_sel"/>
                                                </button>
                                                <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="O8Y-Fx-eau" userLabel="颜色">
                                                    <rect key="frame" x="195" y="0.0" width="25" height="25"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="25" id="QWL-Il-Ky4"/>
                                                        <constraint firstAttribute="height" constant="25" id="ibF-Jv-OCW"/>
                                                    </constraints>
                                                    <state key="normal" image="plv_vod_btn_white"/>
                                                    <state key="selected" image="plv_vod_btn_white_sel"/>
                                                </button>
                                                <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="k5v-3K-psa" userLabel="颜色">
                                                    <rect key="frame" x="260" y="0.0" width="25" height="25"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="25" id="AXR-KD-dPJ"/>
                                                        <constraint firstAttribute="height" constant="25" id="Igi-G7-kA6"/>
                                                    </constraints>
                                                    <state key="normal" image="plv_vod_btn_white"/>
                                                    <state key="selected" image="plv_vod_btn_white_sel"/>
                                                </button>
                                                <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="q7Q-AF-RAc" userLabel="颜色">
                                                    <rect key="frame" x="325" y="0.0" width="25" height="25"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="25" id="31A-9P-cVR"/>
                                                        <constraint firstAttribute="width" constant="25" id="t7n-Sy-by1"/>
                                                    </constraints>
                                                    <state key="normal" image="plv_vod_btn_white"/>
                                                    <state key="selected" image="plv_vod_btn_white_sel"/>
                                                </button>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                </stackView>
                                <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="Vp0-kN-utJ" userLabel="弹幕类型组">
                                    <rect key="frame" x="0.0" y="48" width="235.5" height="25"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="弹幕类型" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FeT-w4-r7G">
                                            <rect key="frame" x="0.0" y="0.0" width="57.5" height="25"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" spacing="34" translatesAutoresizingMaskIntoConstraints="NO" id="fM7-iR-VaA">
                                            <rect key="frame" x="77.5" y="0.0" width="158" height="25"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" horizontalCompressionResistancePriority="751" selected="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="05r-fd-MaV">
                                                    <rect key="frame" x="0.0" y="0.0" width="30" height="25"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <state key="normal" title="滚动">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                    <state key="selected">
                                                        <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                                    </state>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" horizontalCompressionResistancePriority="751" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Rvm-Jd-JXd">
                                                    <rect key="frame" x="64" y="0.0" width="30" height="25"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <state key="normal" title="顶部">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                    <state key="selected">
                                                        <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                                    </state>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mv8-1O-V7m">
                                                    <rect key="frame" x="128" y="0.0" width="30" height="25"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <state key="normal" title="底部">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                    <state key="selected">
                                                        <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                                    </state>
                                                </button>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                </stackView>
                                <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="yXB-ea-kt4" userLabel="弹幕字体大小组">
                                    <rect key="frame" x="0.0" y="96" width="235.5" height="25"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="字体大小" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="edB-rr-pCA">
                                            <rect key="frame" x="0.0" y="0.0" width="57.5" height="25"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" spacing="34" translatesAutoresizingMaskIntoConstraints="NO" id="zlT-Hh-st7">
                                            <rect key="frame" x="77.5" y="0.0" width="158" height="25"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" selected="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xfT-yf-vvp">
                                                    <rect key="frame" x="0.0" y="0.0" width="30" height="25"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <state key="normal" title="16">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                    <state key="selected">
                                                        <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                                    </state>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" horizontalCompressionResistancePriority="751" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="C9j-wH-9gj">
                                                    <rect key="frame" x="64" y="0.0" width="30" height="25"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <state key="normal" title="18">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                    <state key="selected">
                                                        <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                                    </state>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" horizontalCompressionResistancePriority="752" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="llg-hd-3Xb">
                                                    <rect key="frame" x="128" y="0.0" width="30" height="25"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <state key="normal" title="24">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                    <state key="selected">
                                                        <color key="titleColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                                    </state>
                                                </button>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                </stackView>
                            </subviews>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" red="0.0" green="0.062745098040000002" blue="0.1058823529" alpha="0.69999999999999996" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="UXE-RF-qSh" secondAttribute="bottom" constant="165" id="4NF-dT-gN3"/>
                        <constraint firstItem="pdp-XN-RPy" firstAttribute="centerX" secondItem="bkd-fA-fQD" secondAttribute="centerX" id="7sL-Fz-f6k"/>
                        <constraint firstItem="UXE-RF-qSh" firstAttribute="leading" secondItem="bkd-fA-fQD" secondAttribute="leading" id="B1a-hM-VyX"/>
                        <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="pdp-XN-RPy" secondAttribute="bottom" constant="20" id="DZR-mm-ilY"/>
                        <constraint firstItem="pdp-XN-RPy" firstAttribute="centerY" secondItem="bkd-fA-fQD" secondAttribute="centerY" constant="20" id="Il0-Nm-OYi"/>
                        <constraint firstItem="UXE-RF-qSh" firstAttribute="top" secondItem="bkd-fA-fQD" secondAttribute="top" id="LRN-Xn-4bV"/>
                        <constraint firstItem="pdp-XN-RPy" firstAttribute="top" relation="greaterThanOrEqual" secondItem="UXE-RF-qSh" secondAttribute="bottom" constant="20" id="P9f-xv-8w0"/>
                        <constraint firstAttribute="trailing" secondItem="UXE-RF-qSh" secondAttribute="trailing" id="jPe-6n-Jnh"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="nXJ-zi-4GQ"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <gestureRecognizers/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="bkd-fA-fQD" secondAttribute="trailing" id="Hc2-LX-ZqY"/>
                <constraint firstAttribute="bottom" secondItem="bkd-fA-fQD" secondAttribute="bottom" id="NJ5-6J-H0W"/>
                <constraint firstItem="bkd-fA-fQD" firstAttribute="leading" secondItem="qa0-Rh-gAf" secondAttribute="leading" id="wka-BP-uim"/>
            </constraints>
            <connections>
                <outlet property="colorButtonStackView" destination="hxu-mA-h92" id="aAZ-pb-04f"/>
                <outlet property="danmuTextField" destination="GPc-ot-JIj" id="REV-pI-xOn"/>
                <outlet property="fontSizeButtonStackView" destination="zlT-Hh-st7" id="GiP-IJ-Up6"/>
                <outlet property="inputHeightLayout" destination="4NF-dT-gN3" id="1VT-Ra-uvo"/>
                <outlet property="settingButton" destination="O9u-wQ-4eV" id="Raq-jk-3ar"/>
                <outlet property="typeButtonStackView" destination="fM7-iR-VaA" id="KtX-Nj-lJd"/>
            </connections>
            <point key="canvasLocation" x="26" y="3402"/>
        </view>
        <tapGestureRecognizer id="1bO-LR-CK4" userLabel="占位手势"/>
        <view contentMode="scaleToFill" id="fBS-tL-F6W" userLabel="音频封面" customClass="PLVVodAudioCoverPanelView">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="weX-TX-Ddq" userLabel="音频封面外层容器">
                    <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
                    <subviews>
                        <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="77I-9W-afn" userLabel="音频封面外层容器背景图">
                            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
                        </imageView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mnb-JY-Zco" userLabel="音频封面内层容器">
                            <rect key="frame" x="253.5" y="107.5" width="160" height="160"/>
                            <subviews>
                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" image="plv_void_audio_cover" translatesAutoresizingMaskIntoConstraints="NO" id="v2p-5q-8wI" userLabel="音频封面底图">
                                    <rect key="frame" x="0.0" y="0.0" width="160" height="160"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                </imageView>
                                <imageView userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="HQu-A1-0TR" userLabel="音频封面图">
                                    <rect key="frame" x="20" y="20" width="120" height="120"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </imageView>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="160" id="uKn-lH-8kz"/>
                                <constraint firstAttribute="height" constant="160" id="vhv-Xd-F5t"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="77I-9W-afn" secondAttribute="bottom" id="1zs-gN-YTD"/>
                        <constraint firstItem="mnb-JY-Zco" firstAttribute="centerY" secondItem="weX-TX-Ddq" secondAttribute="centerY" id="HYa-Rt-MBQ"/>
                        <constraint firstItem="77I-9W-afn" firstAttribute="leading" secondItem="weX-TX-Ddq" secondAttribute="leading" id="MQ9-iP-Kkf"/>
                        <constraint firstAttribute="trailing" secondItem="77I-9W-afn" secondAttribute="trailing" id="ZFw-O5-hyH"/>
                        <constraint firstItem="mnb-JY-Zco" firstAttribute="centerX" secondItem="weX-TX-Ddq" secondAttribute="centerX" id="byK-iu-e7R"/>
                        <constraint firstItem="77I-9W-afn" firstAttribute="top" secondItem="weX-TX-Ddq" secondAttribute="top" id="u1e-7J-0Wt"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="Lf0-Ad-Mcr"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="weX-TX-Ddq" firstAttribute="leading" secondItem="fBS-tL-F6W" secondAttribute="leading" id="Awb-JZ-eAl"/>
                <constraint firstAttribute="bottom" secondItem="weX-TX-Ddq" secondAttribute="bottom" id="WuP-K5-c56"/>
                <constraint firstAttribute="trailing" secondItem="weX-TX-Ddq" secondAttribute="trailing" id="m1w-sN-sge"/>
                <constraint firstItem="weX-TX-Ddq" firstAttribute="top" secondItem="fBS-tL-F6W" secondAttribute="top" id="nFQ-pB-3pe"/>
            </constraints>
            <connections>
                <outlet property="audioCoverBackImg" destination="v2p-5q-8wI" id="832-Kd-GfB"/>
                <outlet property="audioCoverContainerBackgroundImageView" destination="77I-9W-afn" id="voN-I9-Tbk"/>
                <outlet property="audioCoverContainerView" destination="weX-TX-Ddq" id="vSZ-Cj-Yfc"/>
                <outlet property="audioCoverImage" destination="HQu-A1-0TR" id="Voa-0y-NK4"/>
                <outlet property="audioCoverImgContainer" destination="mnb-JY-Zco" id="Vlh-ca-WLf"/>
            </connections>
            <point key="canvasLocation" x="729" y="1753"/>
        </view>
        <view contentMode="scaleToFill" id="tL0-Ye-Q1a" userLabel="锁屏状态" customClass="PLVVodLockScreenView">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="tcY-Bk-qtT">
                    <rect key="frame" x="17" y="171.5" width="32" height="32"/>
                    <state key="normal" backgroundImage="plv_btn_lockscreen"/>
                    <connections>
                        <action selector="unlockScreenAction:" destination="-1" eventType="touchUpInside" id="9wt-9a-662"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="7Lb-h4-q6F"/>
            <color key="backgroundColor" red="0.0" green="0.062745098040000002" blue="0.1058823529" alpha="0.69999999999999996" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="tcY-Bk-qtT" firstAttribute="leading" secondItem="tL0-Ye-Q1a" secondAttribute="leading" constant="17" id="dEH-jA-MIQ"/>
                <constraint firstItem="tcY-Bk-qtT" firstAttribute="centerY" secondItem="tL0-Ye-Q1a" secondAttribute="centerY" id="uBu-lF-OHS"/>
            </constraints>
            <connections>
                <outlet property="unlockScreenBtn" destination="tcY-Bk-qtT" id="oGd-k4-G5K"/>
            </connections>
            <point key="canvasLocation" x="25" y="4177.5"/>
        </view>
        <view contentMode="scaleToFill" id="fRj-tD-e9G" userLabel="音视频播放封面" customClass="PLVVodCoverView">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="4Jb-Mo-ShD">
                    <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="320" id="Y2P-jN-Rs1"/>
                        <constraint firstAttribute="height" constant="320" id="Zmr-yy-MBG"/>
                        <constraint firstAttribute="width" secondItem="4Jb-Mo-ShD" secondAttribute="height" multiplier="1:1" id="mCQ-PV-tFC"/>
                    </constraints>
                    <variation key="default">
                        <mask key="constraints">
                            <exclude reference="mCQ-PV-tFC"/>
                            <exclude reference="Y2P-jN-Rs1"/>
                            <exclude reference="Zmr-yy-MBG"/>
                        </mask>
                    </variation>
                </imageView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="soF-Wy-7RC"/>
            <color key="backgroundColor" systemColor="darkTextColor"/>
            <constraints>
                <constraint firstItem="4Jb-Mo-ShD" firstAttribute="centerX" secondItem="fRj-tD-e9G" secondAttribute="centerX" id="9gd-HE-YYq"/>
                <constraint firstItem="4Jb-Mo-ShD" firstAttribute="width" secondItem="fRj-tD-e9G" secondAttribute="width" id="B26-RS-Nc1"/>
                <constraint firstAttribute="bottomMargin" secondItem="4Jb-Mo-ShD" secondAttribute="bottom" id="Fhk-7c-a4P"/>
                <constraint firstItem="4Jb-Mo-ShD" firstAttribute="top" secondItem="fRj-tD-e9G" secondAttribute="topMargin" id="HaV-PP-dDe"/>
                <constraint firstItem="4Jb-Mo-ShD" firstAttribute="centerY" secondItem="fRj-tD-e9G" secondAttribute="centerY" id="eF4-5y-VTQ"/>
                <constraint firstItem="4Jb-Mo-ShD" firstAttribute="height" secondItem="fRj-tD-e9G" secondAttribute="height" id="jcN-Lr-MFP"/>
                <constraint firstItem="4Jb-Mo-ShD" firstAttribute="height" secondItem="fRj-tD-e9G" secondAttribute="height" id="vpQ-Kt-dP4"/>
            </constraints>
            <variation key="default">
                <mask key="constraints">
                    <exclude reference="Fhk-7c-a4P"/>
                    <exclude reference="HaV-PP-dDe"/>
                    <exclude reference="vpQ-Kt-dP4"/>
                </mask>
            </variation>
            <connections>
                <outlet property="coverImgV" destination="4Jb-Mo-ShD" id="w7a-dv-Lrn"/>
            </connections>
            <point key="canvasLocation" x="311" y="4071"/>
        </view>
        <view contentMode="scaleToFill" id="yEK-9l-tK8" userLabel="线路选择" customClass="PLVVodRouteLineView">
            <rect key="frame" x="0.0" y="0.0" width="667" height="375"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="scaleToFill" spacing="50" translatesAutoresizingMaskIntoConstraints="NO" id="SMI-bl-C7x">
                    <rect key="frame" x="298.5" y="167.5" width="70" height="40"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="CEp-1s-nPx">
                            <rect key="frame" x="0.0" y="0.0" width="70" height="40"/>
                            <fontDescription key="fontDescription" type="system" pointSize="23"/>
                            <state key="normal" title="不可用">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                        </button>
                    </subviews>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="AzZ-Uo-zlJ"/>
            <color key="backgroundColor" red="0.0" green="0.062745098040000002" blue="0.1058823529" alpha="0.69999999999999996" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="SMI-bl-C7x" firstAttribute="centerX" secondItem="yEK-9l-tK8" secondAttribute="centerX" id="IyS-GT-f87"/>
                <constraint firstItem="SMI-bl-C7x" firstAttribute="centerY" secondItem="yEK-9l-tK8" secondAttribute="centerY" id="ic8-Fy-ODZ"/>
            </constraints>
            <connections>
                <outlet property="routeStackView" destination="SMI-bl-C7x" id="Ylq-ZY-MMk"/>
            </connections>
            <point key="canvasLocation" x="25" y="2582"/>
        </view>
    </objects>
    <resources>
        <image name="btn-proj" width="36" height="36"/>
        <image name="btn-proj-sel" width="36" height="36"/>
        <image name="btn_proj_q" width="22" height="19"/>
        <image name="plv_btn_lockscreen" width="32" height="32"/>
        <image name="plv_btn_unlockscreen" width="32" height="32"/>
        <image name="plv_vod_av_swtich_audio" width="12" height="16"/>
        <image name="plv_vod_av_swtich_bg" width="36" height="78"/>
        <image name="plv_vod_av_swtich_selected" width="32" height="32"/>
        <image name="plv_vod_av_swtich_video" width="16" height="12"/>
        <image name="plv_vod_bg_s" width="130" height="130"/>
        <image name="plv_vod_btn-exitFullScreen" width="19" height="19"/>
        <image name="plv_vod_btn_back" width="9" height="17"/>
        <image name="plv_vod_btn_dm_off" width="31" height="19"/>
        <image name="plv_vod_btn_dm_on" width="31" height="19"/>
        <image name="plv_vod_btn_dm_settings" width="26" height="20"/>
        <image name="plv_vod_btn_exfull" width="15" height="15"/>
        <image name="plv_vod_btn_floating" width="19" height="19"/>
        <image name="plv_vod_btn_fullscreen" width="19" height="19"/>
        <image name="plv_vod_btn_keyboard" width="27" height="20"/>
        <image name="plv_vod_btn_pause" width="14" height="19"/>
        <image name="plv_vod_btn_play" width="14" height="19"/>
        <image name="plv_vod_btn_qq" width="60" height="60"/>
        <image name="plv_vod_btn_screenshot" width="60" height="60"/>
        <image name="plv_vod_btn_send_dm" width="60" height="60"/>
        <image name="plv_vod_btn_settings" width="20" height="20"/>
        <image name="plv_vod_btn_share" width="22" height="18"/>
        <image name="plv_vod_btn_wechat" width="60" height="60"/>
        <image name="plv_vod_btn_weibo" width="60" height="60"/>
        <image name="plv_vod_btn_white" width="16" height="16"/>
        <image name="plv_vod_btn_white_sel" width="25" height="25"/>
        <image name="plv_vod_ic_bri_l" width="17" height="17"/>
        <image name="plv_vod_ic_bri_s" width="12" height="12"/>
        <image name="plv_vod_ic_brightness" width="57" height="57"/>
        <image name="plv_vod_ic_vol_l" width="17" height="17"/>
        <image name="plv_vod_ic_vol_s" width="6" height="11"/>
        <image name="plv_vod_input_dm_click" width="28" height="28"/>
        <image name="plv_vod_input_dm_def" width="28" height="28"/>
        <image name="plv_void_audio_cover" width="160" height="160"/>
        <systemColor name="darkTextColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
