<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="vau-sc-JAe">
    <device id="retina3_5" orientation="portrait" appearance="dark"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--POLYV云点播-->
        <scene sceneID="2OQ-Kd-UXE">
            <objects>
                <collectionViewController automaticallyAdjustsScrollViewInsets="NO" id="pBy-Vf-xVx" customClass="PLVCourseListController" sceneMemberID="viewController">
                    <collectionView key="view" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="prototypes" id="rtI-FC-Hon">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="480"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                        <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="15" minimumInteritemSpacing="10" sectionInsetReference="safeArea" id="G9i-Hg-NUG">
                            <size key="itemSize" width="140" height="142"/>
                            <size key="headerReferenceSize" width="0.0" height="0.0"/>
                            <size key="footerReferenceSize" width="0.0" height="0.0"/>
                            <inset key="sectionInset" minX="15" minY="15" maxX="15" maxY="15"/>
                        </collectionViewFlowLayout>
                        <cells>
                            <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="PLVCourseCell" id="Jh8-lt-ESH" customClass="PLVCourseCell">
                                <rect key="frame" x="15" y="15" width="140" height="142"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO">
                                    <rect key="frame" x="0.0" y="0.0" width="140" height="142"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="plv_ph_courseCover" translatesAutoresizingMaskIntoConstraints="NO" id="cJW-gT-eYp">
                                            <rect key="frame" x="0.0" y="0.0" width="140" height="79"/>
                                            <color key="backgroundColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="width" secondItem="cJW-gT-eYp" secondAttribute="height" multiplier="16:9" id="yB9-Wa-0VB"/>
                                            </constraints>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="标题" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OSX-HO-gsI">
                                            <rect key="frame" x="0.0" y="89" width="140" height="16"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                            <nil key="textColor"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="h1X-W3-vn3">
                                            <rect key="frame" x="0.0" y="55" width="140" height="24"/>
                                            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.45000000000000001" colorSpace="custom" customColorSpace="sRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="24" id="U1t-FG-Ob8"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                            <inset key="contentEdgeInsets" minX="8" minY="0.0" maxX="0.0" maxY="0.0"/>
                                            <inset key="titleEdgeInsets" minX="8" minY="0.0" maxX="0.0" maxY="0.0"/>
                                            <state key="normal" title="老师" image="plv_icon_teacher"/>
                                        </button>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="在学人数" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tIG-pW-fbh">
                                            <rect key="frame" x="81" y="111.5" width="41" height="12"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="10"/>
                                            <color key="textColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="价格" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Aou-nc-wDi">
                                            <rect key="frame" x="0.0" y="109" width="29" height="17"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <color key="textColor" red="1" green="0.14913141730000001" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                </view>
                                <constraints>
                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="Aou-nc-wDi" secondAttribute="trailing" symbolic="YES" id="50R-TL-aX2"/>
                                    <constraint firstAttribute="trailing" secondItem="OSX-HO-gsI" secondAttribute="trailing" id="56U-NE-AJo"/>
                                    <constraint firstItem="cJW-gT-eYp" firstAttribute="top" secondItem="Jh8-lt-ESH" secondAttribute="top" id="5tO-uV-Gh4"/>
                                    <constraint firstItem="Aou-nc-wDi" firstAttribute="leading" secondItem="Jh8-lt-ESH" secondAttribute="leading" id="CLC-Vf-ZdB"/>
                                    <constraint firstAttribute="trailingMargin" secondItem="tIG-pW-fbh" secondAttribute="trailing" constant="10" id="DEJ-2N-NMH"/>
                                    <constraint firstItem="h1X-W3-vn3" firstAttribute="leading" secondItem="cJW-gT-eYp" secondAttribute="leading" id="EEc-dN-jTj"/>
                                    <constraint firstAttribute="trailing" secondItem="cJW-gT-eYp" secondAttribute="trailing" id="KNT-wf-LGi"/>
                                    <constraint firstItem="cJW-gT-eYp" firstAttribute="leading" secondItem="Jh8-lt-ESH" secondAttribute="leading" id="OqD-j4-gq2"/>
                                    <constraint firstItem="h1X-W3-vn3" firstAttribute="trailing" secondItem="cJW-gT-eYp" secondAttribute="trailing" id="YBw-A8-aqx"/>
                                    <constraint firstItem="tIG-pW-fbh" firstAttribute="centerY" secondItem="Aou-nc-wDi" secondAttribute="centerY" id="gca-fs-6RG"/>
                                    <constraint firstItem="OSX-HO-gsI" firstAttribute="leading" secondItem="Jh8-lt-ESH" secondAttribute="leading" id="j1h-qx-7tb"/>
                                    <constraint firstItem="OSX-HO-gsI" firstAttribute="top" secondItem="cJW-gT-eYp" secondAttribute="bottom" constant="10" id="mor-3b-sY0"/>
                                    <constraint firstItem="h1X-W3-vn3" firstAttribute="bottom" secondItem="cJW-gT-eYp" secondAttribute="bottom" id="wvt-wU-p1k"/>
                                    <constraint firstItem="Aou-nc-wDi" firstAttribute="top" secondItem="OSX-HO-gsI" secondAttribute="bottom" constant="4" id="yga-HH-cSw"/>
                                    <constraint firstItem="tIG-pW-fbh" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="Jh8-lt-ESH" secondAttribute="leadingMargin" id="zOy-4F-NDQ"/>
                                </constraints>
                                <connections>
                                    <outlet property="coverImageView" destination="cJW-gT-eYp" id="EH1-cg-YVH"/>
                                    <outlet property="priceLabel" destination="Aou-nc-wDi" id="BkP-GA-UiS"/>
                                    <outlet property="studentCountLabel" destination="tIG-pW-fbh" id="eXn-b0-h6m"/>
                                    <outlet property="teacherButton" destination="h1X-W3-vn3" id="CBs-21-urE"/>
                                    <outlet property="titleLabel" destination="OSX-HO-gsI" id="QW5-0N-8jY"/>
                                </connections>
                            </collectionViewCell>
                            <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="sample1" id="wWB-l9-Q5V">
                                <rect key="frame" x="165" y="15" width="140" height="142"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO">
                                    <rect key="frame" x="0.0" y="0.0" width="140" height="142"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="plv_ph_courseCover" translatesAutoresizingMaskIntoConstraints="NO" id="dgL-QP-IXc">
                                            <rect key="frame" x="0.0" y="0.0" width="140" height="79"/>
                                            <color key="backgroundColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="width" secondItem="dgL-QP-IXc" secondAttribute="height" multiplier="16:9" id="uhZ-al-vox"/>
                                            </constraints>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="标题" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="khV-fh-F8k">
                                            <rect key="frame" x="0.0" y="89" width="140" height="16"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                            <nil key="textColor"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="958-tN-ACV">
                                            <rect key="frame" x="0.0" y="55" width="140" height="24"/>
                                            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.45000000000000001" colorSpace="custom" customColorSpace="sRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="24" id="ZJx-ea-S2p"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                            <inset key="contentEdgeInsets" minX="8" minY="0.0" maxX="0.0" maxY="0.0"/>
                                            <inset key="titleEdgeInsets" minX="8" minY="0.0" maxX="0.0" maxY="0.0"/>
                                            <state key="normal" title="老师" image="plv_icon_teacher"/>
                                        </button>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="在学人数" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2ZM-yf-Ubz">
                                            <rect key="frame" x="81" y="111.5" width="41" height="12"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="10"/>
                                            <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="价格" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yBl-61-vnJ">
                                            <rect key="frame" x="0.0" y="109" width="29" height="17"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <color key="textColor" red="1" green="0.14913141730000001" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                </view>
                                <constraints>
                                    <constraint firstItem="2ZM-yf-Ubz" firstAttribute="centerY" secondItem="yBl-61-vnJ" secondAttribute="centerY" id="5cM-sR-XKu"/>
                                    <constraint firstItem="958-tN-ACV" firstAttribute="trailing" secondItem="dgL-QP-IXc" secondAttribute="trailing" id="JrE-Og-gNG"/>
                                    <constraint firstItem="2ZM-yf-Ubz" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="wWB-l9-Q5V" secondAttribute="leadingMargin" id="La6-qP-6mM"/>
                                    <constraint firstItem="958-tN-ACV" firstAttribute="bottom" secondItem="dgL-QP-IXc" secondAttribute="bottom" id="ROv-2u-A01"/>
                                    <constraint firstItem="dgL-QP-IXc" firstAttribute="top" secondItem="wWB-l9-Q5V" secondAttribute="top" id="Ste-y1-Os2"/>
                                    <constraint firstItem="khV-fh-F8k" firstAttribute="top" secondItem="dgL-QP-IXc" secondAttribute="bottom" constant="10" id="XQQ-55-yUw"/>
                                    <constraint firstItem="khV-fh-F8k" firstAttribute="leading" secondItem="wWB-l9-Q5V" secondAttribute="leading" id="aKN-nc-I3N"/>
                                    <constraint firstItem="yBl-61-vnJ" firstAttribute="leading" secondItem="wWB-l9-Q5V" secondAttribute="leading" id="b8r-vK-XSs"/>
                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="yBl-61-vnJ" secondAttribute="trailing" symbolic="YES" id="bQm-0v-GIb"/>
                                    <constraint firstItem="yBl-61-vnJ" firstAttribute="top" secondItem="khV-fh-F8k" secondAttribute="bottom" constant="4" id="i7f-1M-w2g"/>
                                    <constraint firstAttribute="trailingMargin" secondItem="2ZM-yf-Ubz" secondAttribute="trailing" constant="10" id="lz8-uW-zey"/>
                                    <constraint firstAttribute="trailing" secondItem="dgL-QP-IXc" secondAttribute="trailing" id="mha-KE-1Gz"/>
                                    <constraint firstItem="958-tN-ACV" firstAttribute="leading" secondItem="dgL-QP-IXc" secondAttribute="leading" id="pJ6-g4-jIe"/>
                                    <constraint firstItem="dgL-QP-IXc" firstAttribute="leading" secondItem="wWB-l9-Q5V" secondAttribute="leading" id="qSc-9w-JLE"/>
                                    <constraint firstAttribute="trailing" secondItem="khV-fh-F8k" secondAttribute="trailing" id="zx6-RL-MQi"/>
                                </constraints>
                            </collectionViewCell>
                            <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="sample2" id="9Bg-nA-jp3">
                                <rect key="frame" x="15" y="172" width="140" height="142"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO">
                                    <rect key="frame" x="0.0" y="0.0" width="140" height="142"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="plv_ph_courseCover" translatesAutoresizingMaskIntoConstraints="NO" id="Z34-2h-8aO">
                                            <rect key="frame" x="0.0" y="0.0" width="140" height="79"/>
                                            <color key="backgroundColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="width" secondItem="Z34-2h-8aO" secondAttribute="height" multiplier="16:9" id="nre-yi-JUT"/>
                                            </constraints>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="标题" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gbt-9d-7tj">
                                            <rect key="frame" x="0.0" y="89" width="140" height="16"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                            <nil key="textColor"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9gN-To-SVy">
                                            <rect key="frame" x="0.0" y="55" width="140" height="24"/>
                                            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.45000000000000001" colorSpace="custom" customColorSpace="sRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="24" id="ACF-uj-bXi"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                            <inset key="contentEdgeInsets" minX="8" minY="0.0" maxX="0.0" maxY="0.0"/>
                                            <inset key="titleEdgeInsets" minX="8" minY="0.0" maxX="0.0" maxY="0.0"/>
                                            <state key="normal" title="老师" image="plv_icon_teacher"/>
                                        </button>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="在学人数" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="E3L-rw-MUE">
                                            <rect key="frame" x="81" y="111.5" width="41" height="12"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="10"/>
                                            <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="价格" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ItX-BG-zPo">
                                            <rect key="frame" x="0.0" y="109" width="29" height="17"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <color key="textColor" red="1" green="0.14913141730000001" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                </view>
                                <constraints>
                                    <constraint firstAttribute="trailingMargin" secondItem="E3L-rw-MUE" secondAttribute="trailing" constant="10" id="5Tu-v5-Wlc"/>
                                    <constraint firstItem="Z34-2h-8aO" firstAttribute="top" secondItem="9Bg-nA-jp3" secondAttribute="top" id="5dV-UM-HYm"/>
                                    <constraint firstItem="9gN-To-SVy" firstAttribute="trailing" secondItem="Z34-2h-8aO" secondAttribute="trailing" id="Aog-ZS-JJV"/>
                                    <constraint firstAttribute="trailing" secondItem="Z34-2h-8aO" secondAttribute="trailing" id="Fdb-zU-YFB"/>
                                    <constraint firstItem="gbt-9d-7tj" firstAttribute="top" secondItem="Z34-2h-8aO" secondAttribute="bottom" constant="10" id="Igu-tf-s71"/>
                                    <constraint firstItem="9gN-To-SVy" firstAttribute="bottom" secondItem="Z34-2h-8aO" secondAttribute="bottom" id="JxS-RU-Gqd"/>
                                    <constraint firstItem="9gN-To-SVy" firstAttribute="leading" secondItem="Z34-2h-8aO" secondAttribute="leading" id="L8i-s8-Rv2"/>
                                    <constraint firstItem="E3L-rw-MUE" firstAttribute="centerY" secondItem="ItX-BG-zPo" secondAttribute="centerY" id="Riy-zi-l78"/>
                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="ItX-BG-zPo" secondAttribute="trailing" symbolic="YES" id="V7M-J3-aVG"/>
                                    <constraint firstItem="Z34-2h-8aO" firstAttribute="leading" secondItem="9Bg-nA-jp3" secondAttribute="leading" id="acK-60-CMf"/>
                                    <constraint firstAttribute="trailing" secondItem="gbt-9d-7tj" secondAttribute="trailing" id="deA-ki-x6T"/>
                                    <constraint firstItem="ItX-BG-zPo" firstAttribute="top" secondItem="gbt-9d-7tj" secondAttribute="bottom" constant="4" id="g3L-14-stq"/>
                                    <constraint firstItem="E3L-rw-MUE" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="9Bg-nA-jp3" secondAttribute="leadingMargin" id="pMK-q1-5I5"/>
                                    <constraint firstItem="ItX-BG-zPo" firstAttribute="leading" secondItem="9Bg-nA-jp3" secondAttribute="leading" id="tX5-Cv-Po1"/>
                                    <constraint firstItem="gbt-9d-7tj" firstAttribute="leading" secondItem="9Bg-nA-jp3" secondAttribute="leading" id="xBk-IF-9bk"/>
                                </constraints>
                            </collectionViewCell>
                            <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="sample0" id="x7f-zo-UFJ">
                                <rect key="frame" x="165" y="172" width="140" height="142"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO">
                                    <rect key="frame" x="0.0" y="0.0" width="140" height="142"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="plv_ph_courseCover" translatesAutoresizingMaskIntoConstraints="NO" id="QqA-IY-doI">
                                            <rect key="frame" x="0.0" y="0.0" width="140" height="79"/>
                                            <color key="backgroundColor" red="0.078431372550000003" green="0.63529411759999999" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="width" secondItem="QqA-IY-doI" secondAttribute="height" multiplier="16:9" id="P3j-6Z-50G"/>
                                            </constraints>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="标题" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zYV-7u-wEp">
                                            <rect key="frame" x="0.0" y="89" width="140" height="16"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                            <nil key="textColor"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="InF-bR-noM">
                                            <rect key="frame" x="0.0" y="55" width="140" height="24"/>
                                            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.45000000000000001" colorSpace="custom" customColorSpace="sRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="24" id="dSK-oC-4YO"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                            <inset key="contentEdgeInsets" minX="8" minY="0.0" maxX="0.0" maxY="0.0"/>
                                            <inset key="titleEdgeInsets" minX="8" minY="0.0" maxX="0.0" maxY="0.0"/>
                                            <state key="normal" title="老师" image="plv_icon_teacher"/>
                                        </button>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="在学人数" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Kyw-Dp-ca8">
                                            <rect key="frame" x="81" y="111.5" width="41" height="12"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="10"/>
                                            <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="价格" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OaN-y6-e31">
                                            <rect key="frame" x="0.0" y="109" width="29" height="17"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <color key="textColor" red="1" green="0.14913141730000001" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                </view>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="zYV-7u-wEp" secondAttribute="trailing" id="2Zn-jR-6qQ"/>
                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="OaN-y6-e31" secondAttribute="trailing" symbolic="YES" id="5Qw-mq-lDf"/>
                                    <constraint firstAttribute="trailing" secondItem="QqA-IY-doI" secondAttribute="trailing" id="5f4-Sn-PEw"/>
                                    <constraint firstItem="zYV-7u-wEp" firstAttribute="top" secondItem="QqA-IY-doI" secondAttribute="bottom" constant="10" id="JJw-Uf-Q1Z"/>
                                    <constraint firstItem="InF-bR-noM" firstAttribute="bottom" secondItem="QqA-IY-doI" secondAttribute="bottom" id="LJz-ZX-EuB"/>
                                    <constraint firstItem="Kyw-Dp-ca8" firstAttribute="centerY" secondItem="OaN-y6-e31" secondAttribute="centerY" id="MQU-f9-Zfm"/>
                                    <constraint firstAttribute="trailingMargin" secondItem="Kyw-Dp-ca8" secondAttribute="trailing" constant="10" id="N3T-Wy-SaA"/>
                                    <constraint firstItem="zYV-7u-wEp" firstAttribute="leading" secondItem="x7f-zo-UFJ" secondAttribute="leading" id="bQx-DO-7eN"/>
                                    <constraint firstItem="QqA-IY-doI" firstAttribute="top" secondItem="x7f-zo-UFJ" secondAttribute="top" id="d38-u0-NX0"/>
                                    <constraint firstItem="InF-bR-noM" firstAttribute="leading" secondItem="QqA-IY-doI" secondAttribute="leading" id="ehg-64-KZF"/>
                                    <constraint firstItem="OaN-y6-e31" firstAttribute="leading" secondItem="x7f-zo-UFJ" secondAttribute="leading" id="kx2-Za-QxG"/>
                                    <constraint firstItem="QqA-IY-doI" firstAttribute="leading" secondItem="x7f-zo-UFJ" secondAttribute="leading" id="uAt-Ha-SRR"/>
                                    <constraint firstItem="Kyw-Dp-ca8" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="x7f-zo-UFJ" secondAttribute="leadingMargin" id="wih-su-P7S"/>
                                    <constraint firstItem="InF-bR-noM" firstAttribute="trailing" secondItem="QqA-IY-doI" secondAttribute="trailing" id="xf5-r9-oRI"/>
                                    <constraint firstItem="OaN-y6-e31" firstAttribute="top" secondItem="zYV-7u-wEp" secondAttribute="bottom" constant="4" id="yZ0-ZC-4qj"/>
                                </constraints>
                            </collectionViewCell>
                        </cells>
                        <connections>
                            <outlet property="dataSource" destination="pBy-Vf-xVx" id="eiR-BL-J4h"/>
                            <outlet property="delegate" destination="pBy-Vf-xVx" id="kNX-cy-tDj"/>
                        </connections>
                    </collectionView>
                    <navigationItem key="navigationItem" title="POLYV云点播" id="bct-d2-yqb">
                        <barButtonItem key="backBarButtonItem" title=" " id="9ZA-jk-xgA"/>
                        <barButtonItem key="leftBarButtonItem" image="plv_btn_onlineVideo" id="2Tr-lc-4mD">
                            <connections>
                                <segue destination="tbc-pX-4Ut" kind="show" id="3oT-Jv-7Fb"/>
                            </connections>
                        </barButtonItem>
                        <rightBarButtonItems>
                            <barButtonItem image="plv_btn_cache" id="0E1-bX-1Rz">
                                <connections>
                                    <segue destination="h0G-qx-d1C" kind="show" id="LeM-ci-3qM"/>
                                </connections>
                            </barButtonItem>
                            <barButtonItem image="plv_btn_upload" id="gWX-5z-9nU">
                                <connections>
                                    <segue destination="icg-fT-kZJ" kind="show" id="2VG-YT-fzi"/>
                                </connections>
                            </barButtonItem>
                        </rightBarButtonItems>
                    </navigationItem>
                    <connections>
                        <segue destination="qAb-Tx-rFj" kind="show" identifier="course_detail" id="0Xc-QS-eIY"/>
                    </connections>
                </collectionViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="vtg-w7-xe7" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1389.375" y="660"/>
        </scene>
        <!--Course Detail Controller-->
        <scene sceneID="6cJ-fy-9W2">
            <objects>
                <viewController id="qAb-Tx-rFj" customClass="PLVCourseDetailController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="gEo-UI-ljb"/>
                        <viewControllerLayoutGuide type="bottom" id="Z5d-BT-he5"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="s2h-85-Do1">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="480"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <containerView opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="g2T-Lb-o5k">
                                <rect key="frame" x="0.0" y="44" width="320" height="180"/>
                                <color key="backgroundColor" red="0.0" green="0.99143940210000003" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="g2T-Lb-o5k" secondAttribute="height" multiplier="16:9" id="bFN-NG-wgM"/>
                                </constraints>
                            </containerView>
                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qID-Zu-w79" customClass="DLTabedSlideView">
                                <rect key="frame" x="0.0" y="224" width="320" height="256"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                        </subviews>
                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="qID-Zu-w79" secondAttribute="trailing" id="2My-2R-cHa"/>
                            <constraint firstAttribute="trailing" secondItem="g2T-Lb-o5k" secondAttribute="trailing" id="6M9-9C-VUi"/>
                            <constraint firstItem="Z5d-BT-he5" firstAttribute="top" secondItem="qID-Zu-w79" secondAttribute="bottom" id="9ZF-SY-aBO"/>
                            <constraint firstItem="qID-Zu-w79" firstAttribute="top" secondItem="g2T-Lb-o5k" secondAttribute="bottom" id="Fvg-fQ-PMY"/>
                            <constraint firstItem="g2T-Lb-o5k" firstAttribute="top" secondItem="gEo-UI-ljb" secondAttribute="bottom" id="Hqr-2G-vvr"/>
                            <constraint firstItem="qID-Zu-w79" firstAttribute="leading" secondItem="s2h-85-Do1" secondAttribute="leading" id="f9p-WS-XS1"/>
                            <constraint firstItem="g2T-Lb-o5k" firstAttribute="leading" secondItem="s2h-85-Do1" secondAttribute="leading" id="psc-Ft-KS4"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="playerPlaceholder" destination="g2T-Lb-o5k" id="S3W-dB-eLl"/>
                        <outlet property="tabedSlideView" destination="qID-Zu-w79" id="ROk-LG-1if"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="RkR-Ra-EdC" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1389.375" y="1430"/>
        </scene>
        <!--在线视频-->
        <scene sceneID="fAV-Xy-aeZ">
            <objects>
                <tableViewController id="tbc-pX-4Ut" customClass="PLVAccountVideoListController" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" allowsSelection="NO" rowHeight="130" sectionHeaderHeight="28" sectionFooterHeight="28" id="14Q-L5-41Y">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="480"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" systemColor="linkColor"/>
                        <prototypes>
                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="PLVVideoCell" rowHeight="130" id="wor-5K-9oj" customClass="PLVVideoCell">
                                <rect key="frame" x="0.0" y="28" width="320" height="130"/>
                                <autoresizingMask key="autoresizingMask"/>
                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="wor-5K-9oj" id="5xA-f9-6wQ">
                                    <rect key="frame" x="0.0" y="0.0" width="320" height="130"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="plv_ph_courseCover" translatesAutoresizingMaskIntoConstraints="NO" id="8HW-80-zw2">
                                            <rect key="frame" x="16" y="20" width="144" height="90"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="90" id="K7P-sJ-fqG"/>
                                                <constraint firstAttribute="width" constant="144" id="V6w-L6-feX"/>
                                            </constraints>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="标题" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="xYV-wP-fEc">
                                            <rect key="frame" x="168" y="20" width="136" height="19.5"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" alignment="center" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="wdL-TJ-v6E" userLabel="信息组">
                                            <rect key="frame" x="168" y="47.5" width="45" height="14.5"/>
                                            <subviews>
                                                <imageView userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="plv_icon_time" translatesAutoresizingMaskIntoConstraints="NO" id="WPG-OX-vxI">
                                                    <rect key="frame" x="0.0" y="0.5" width="14" height="14"/>
                                                </imageView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="时长" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dUE-r3-jd3">
                                                    <rect key="frame" x="20" y="0.0" width="25" height="14.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <color key="textColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                        </stackView>
                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="Mdb-CE-z3D" userLabel="按钮组">
                                            <rect key="frame" x="168" y="80" width="140" height="30"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Tvw-Uk-Ak9">
                                                    <rect key="frame" x="0.0" y="0.0" width="60" height="30"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="30" id="HMH-rP-CM6"/>
                                                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="60" id="eP5-2l-MMq"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                                    <color key="tintColor" red="0.12941176470588234" green="0.58823529411764708" blue="0.95294117647058818" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <state key="normal" title="下载" backgroundImage="plv_bg_round4">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gZ9-7B-NpY">
                                                    <rect key="frame" x="80" y="0.0" width="60" height="30"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                                    <color key="tintColor" red="0.54509803921568623" green="0.76470588235294112" blue="0.29019607843137252" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <state key="normal" title="播放" backgroundImage="plv_bg_round4">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                </button>
                                            </subviews>
                                        </stackView>
                                        <button hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BxO-vL-Qlg">
                                            <rect key="frame" x="21" y="25" width="37.5" height="21"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="21" id="VAm-Zh-FjR"/>
                                                <constraint firstAttribute="width" secondItem="BxO-vL-Qlg" secondAttribute="height" multiplier="16:9" id="oWY-zv-Rf5"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" name="PingFangTC-Regular" family="PingFang TC" pointSize="10"/>
                                            <state key="normal" title="备注" backgroundImage="plv_bg_round4">
                                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </state>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                                    <constraints>
                                        <constraint firstItem="Mdb-CE-z3D" firstAttribute="bottom" secondItem="8HW-80-zw2" secondAttribute="bottom" id="1ea-SJ-IBS"/>
                                        <constraint firstItem="8HW-80-zw2" firstAttribute="centerY" secondItem="5xA-f9-6wQ" secondAttribute="centerY" id="4BF-XQ-05H"/>
                                        <constraint firstAttribute="trailingMargin" secondItem="xYV-wP-fEc" secondAttribute="trailing" id="4Yb-uL-bah"/>
                                        <constraint firstItem="xYV-wP-fEc" firstAttribute="leading" secondItem="8HW-80-zw2" secondAttribute="trailing" constant="8" id="75Y-Ye-D3u"/>
                                        <constraint firstItem="BxO-vL-Qlg" firstAttribute="top" secondItem="8HW-80-zw2" secondAttribute="top" constant="5" id="Bzo-fb-Wy9"/>
                                        <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="Mdb-CE-z3D" secondAttribute="trailing" id="DNe-gm-xSE"/>
                                        <constraint firstItem="BxO-vL-Qlg" firstAttribute="trailing" relation="lessThanOrEqual" secondItem="8HW-80-zw2" secondAttribute="trailing" id="E6Y-GT-WdG"/>
                                        <constraint firstItem="Mdb-CE-z3D" firstAttribute="leading" secondItem="xYV-wP-fEc" secondAttribute="leading" id="Kno-Hn-Lun"/>
                                        <constraint firstItem="wdL-TJ-v6E" firstAttribute="leading" secondItem="xYV-wP-fEc" secondAttribute="leading" id="QhP-ru-mUH"/>
                                        <constraint firstItem="xYV-wP-fEc" firstAttribute="top" secondItem="8HW-80-zw2" secondAttribute="top" id="WyA-E7-H5t"/>
                                        <constraint firstItem="Mdb-CE-z3D" firstAttribute="top" relation="greaterThanOrEqual" secondItem="wdL-TJ-v6E" secondAttribute="bottom" constant="8" id="X25-aK-u2X"/>
                                        <constraint firstItem="wdL-TJ-v6E" firstAttribute="top" secondItem="xYV-wP-fEc" secondAttribute="bottom" constant="8" id="bAO-y2-ZGZ"/>
                                        <constraint firstItem="8HW-80-zw2" firstAttribute="leading" secondItem="5xA-f9-6wQ" secondAttribute="leadingMargin" id="jjz-9W-MRy"/>
                                        <constraint firstItem="BxO-vL-Qlg" firstAttribute="leading" secondItem="8HW-80-zw2" secondAttribute="leading" constant="5" id="yeH-Hb-OAJ"/>
                                    </constraints>
                                </tableViewCellContentView>
                                <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                                <connections>
                                    <outlet property="coverImageView" destination="8HW-80-zw2" id="JpG-3H-LeV"/>
                                    <outlet property="downloadButton" destination="Tvw-Uk-Ak9" id="ry6-Hc-XbK"/>
                                    <outlet property="durationLabel" destination="dUE-r3-jd3" id="cNH-VJ-j3c"/>
                                    <outlet property="playButton" destination="gZ9-7B-NpY" id="U5p-dI-bij"/>
                                    <outlet property="titleLabel" destination="xYV-wP-fEc" id="yTX-De-j5G"/>
                                    <outlet property="typeButton" destination="BxO-vL-Qlg" id="zTr-A4-vvh"/>
                                </connections>
                            </tableViewCell>
                        </prototypes>
                        <connections>
                            <outlet property="dataSource" destination="tbc-pX-4Ut" id="4RU-bP-fnB"/>
                            <outlet property="delegate" destination="tbc-pX-4Ut" id="eQm-wr-3Eb"/>
                        </connections>
                    </tableView>
                    <toolbarItems/>
                    <navigationItem key="navigationItem" title="在线视频" id="hJB-kF-G29">
                        <rightBarButtonItems>
                            <barButtonItem image="plv_vod_btn_settings" id="yf6-Fv-tjb">
                                <connections>
                                    <action selector="settingsAction:" destination="tbc-pX-4Ut" id="UyO-WX-5GM"/>
                                </connections>
                            </barButtonItem>
                            <barButtonItem title="vid" id="67S-5q-hsh">
                                <connections>
                                    <segue destination="TUW-h1-lx4" kind="presentation" modalPresentationStyle="fullScreen" id="ACa-Dr-ia8"/>
                                </connections>
                            </barButtonItem>
                        </rightBarButtonItems>
                    </navigationItem>
                    <connections>
                        <segue destination="fJC-la-rR2" kind="show" identifier="PLVSimplePlaySegue" id="4pJ-LG-6hU"/>
                    </connections>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="mAd-JW-GeE" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-579" y="-110"/>
        </scene>
        <!--vid 测试-->
        <scene sceneID="5bZ-hw-Ay6">
            <objects>
                <viewController id="u8u-iQ-7yV" customClass="PLVVodVidTestController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="lcv-bh-LvV"/>
                        <viewControllerLayoutGuide type="bottom" id="ZEo-fH-F76"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="B0Z-Xg-nMn">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="480"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FNz-Uy-qMW">
                                <rect key="frame" x="0.0" y="44" width="320" height="180"/>
                                <color key="backgroundColor" red="0.12941176469999999" green="0.58823529409999997" blue="0.95294117649999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="FNz-Uy-qMW" secondAttribute="height" multiplier="16:9" id="Dvr-jO-uCl"/>
                                </constraints>
                            </view>
                            <toolbar opaque="NO" clearsContextBeforeDrawing="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="p4o-ZL-DgP">
                                <rect key="frame" x="0.0" y="134" width="320" height="44"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="ejQ-Ws-gb5"/>
                                </constraints>
                                <items>
                                    <barButtonItem style="done" systemItem="save" id="r2g-u5-AAx">
                                        <connections>
                                            <action selector="saveAction:" destination="u8u-iQ-7yV" id="V9A-xr-UjS"/>
                                        </connections>
                                    </barButtonItem>
                                </items>
                            </toolbar>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="kxf-ZY-F0G">
                                <rect key="frame" x="16" y="186" width="288" height="294"/>
                                <subviews>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="输入vid并回车" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="cnj-Xq-eeg">
                                        <rect key="frame" x="0.0" y="0.0" width="288" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="Wut-cO-MuM"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits" autocorrectionType="no" keyboardType="alphabet" returnKeyType="go"/>
                                        <connections>
                                            <outlet property="delegate" destination="u8u-iQ-7yV" id="92V-Mm-gAo"/>
                                        </connections>
                                    </textField>
                                    <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" editable="NO" text="视频信息" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="JNy-ih-hsD">
                                        <rect key="frame" x="0.0" y="31" width="288" height="263"/>
                                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                                        <color key="textColor" systemColor="labelColor"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                        <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                    </textView>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" constant="294" id="btk-jb-0JV"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="p4o-ZL-DgP" firstAttribute="leading" secondItem="B0Z-Xg-nMn" secondAttribute="leading" id="3m6-Yw-D9g"/>
                            <constraint firstItem="kxf-ZY-F0G" firstAttribute="leading" secondItem="B0Z-Xg-nMn" secondAttribute="leadingMargin" id="8vN-i8-ea7"/>
                            <constraint firstItem="kxf-ZY-F0G" firstAttribute="trailing" secondItem="B0Z-Xg-nMn" secondAttribute="trailingMargin" id="AQ7-SL-rqZ"/>
                            <constraint firstItem="FNz-Uy-qMW" firstAttribute="top" secondItem="lcv-bh-LvV" secondAttribute="bottom" id="AhV-aW-obu"/>
                            <constraint firstAttribute="trailing" secondItem="FNz-Uy-qMW" secondAttribute="trailing" id="EF5-uw-mlE"/>
                            <constraint firstItem="kxf-ZY-F0G" firstAttribute="top" secondItem="p4o-ZL-DgP" secondAttribute="bottom" constant="8" id="LVm-Y1-9ff"/>
                            <constraint firstItem="FNz-Uy-qMW" firstAttribute="leading" secondItem="B0Z-Xg-nMn" secondAttribute="leading" id="NwS-Xg-6rJ"/>
                            <constraint firstAttribute="trailing" secondItem="p4o-ZL-DgP" secondAttribute="trailing" id="P76-Hi-iRD"/>
                            <constraint firstItem="ZEo-fH-F76" firstAttribute="top" secondItem="kxf-ZY-F0G" secondAttribute="bottom" priority="750" id="UTV-hJ-UkO"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="vid 测试" id="MK3-Qe-fX4">
                        <barButtonItem key="rightBarButtonItem" style="done" systemItem="done" id="RQO-lp-kDc">
                            <connections>
                                <action selector="doneAction:" destination="u8u-iQ-7yV" id="Ity-6g-7AH"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="playerPlaceholder" destination="FNz-Uy-qMW" id="jBy-1j-XQS"/>
                        <outlet property="videoInfoTextView" destination="JNy-ih-hsD" id="QWI-Bb-ikD"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="YMp-O9-sdD" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1459" y="-794"/>
        </scene>
        <!--缓存已完成-->
        <scene sceneID="SiK-oQ-mSb">
            <objects>
                <viewController storyboardIdentifier="PLVDownloadCompleteViewController" title="缓存已完成" id="ZBp-37-RsR" customClass="PLVDownloadCompleteViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="EV2-sm-RcI"/>
                        <viewControllerLayoutGuide type="bottom" id="t3K-nx-q1J"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="PmI-7J-K0J">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="200"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="TbS-H3-YJC">
                                <rect key="frame" x="0.0" y="0.0" width="320" height="200"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="PLVDownloadComleteCell" rowHeight="92" id="2kw-9C-BBv" customClass="PLVDownloadComleteCell">
                                        <rect key="frame" x="0.0" y="28" width="320" height="92"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxX="YES"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="2kw-9C-BBv" id="K46-Sh-fJl">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="92"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="POLYV保利威如何通PlaySafe实现视频版权 …" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="59x-al-QQy">
                                                    <rect key="frame" x="144" y="10" width="166" height="33.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <nil key="textColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="vS3-c3-gqY">
                                                    <rect key="frame" x="144" y="68" width="14" height="14"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="14" id="Gh0-4G-Bq9"/>
                                                        <constraint firstAttribute="height" constant="14" id="yVc-nI-7F0"/>
                                                    </constraints>
                                                </imageView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="01:02:50" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lmM-6a-ZOY">
                                                    <rect key="frame" x="166" y="67.5" width="50.5" height="14.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <nil key="textColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1000 M" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cQm-xv-caK">
                                                    <rect key="frame" x="224.5" y="67.5" width="42.5" height="14.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <nil key="textColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="h7z-Hm-ToD">
                                                    <rect key="frame" x="8" y="10" width="128" height="72"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="128" id="YwZ-Hk-S27"/>
                                                        <constraint firstAttribute="height" constant="72" id="fD9-pe-NAK"/>
                                                    </constraints>
                                                </imageView>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="59x-al-QQy" firstAttribute="leading" secondItem="h7z-Hm-ToD" secondAttribute="trailing" constant="8" id="53n-io-Nb3"/>
                                                <constraint firstAttribute="trailing" secondItem="59x-al-QQy" secondAttribute="trailing" constant="10" id="8CJ-bu-SgJ"/>
                                                <constraint firstItem="h7z-Hm-ToD" firstAttribute="leading" secondItem="K46-Sh-fJl" secondAttribute="leading" constant="8" id="GQr-N9-d68"/>
                                                <constraint firstItem="cQm-xv-caK" firstAttribute="leading" secondItem="lmM-6a-ZOY" secondAttribute="trailing" constant="8" id="JSN-nN-s4r"/>
                                                <constraint firstItem="h7z-Hm-ToD" firstAttribute="top" secondItem="K46-Sh-fJl" secondAttribute="top" constant="10" id="N5C-vF-RUC"/>
                                                <constraint firstItem="vS3-c3-gqY" firstAttribute="bottom" secondItem="h7z-Hm-ToD" secondAttribute="bottom" id="Vaj-jx-8jk"/>
                                                <constraint firstItem="59x-al-QQy" firstAttribute="top" secondItem="K46-Sh-fJl" secondAttribute="top" constant="10" id="Z5C-Mg-JSA"/>
                                                <constraint firstItem="lmM-6a-ZOY" firstAttribute="bottom" secondItem="vS3-c3-gqY" secondAttribute="bottom" id="dTk-g4-mwv"/>
                                                <constraint firstItem="lmM-6a-ZOY" firstAttribute="leading" secondItem="vS3-c3-gqY" secondAttribute="trailing" constant="8" id="o7U-cw-e5G"/>
                                                <constraint firstAttribute="bottom" secondItem="h7z-Hm-ToD" secondAttribute="bottom" priority="250" constant="10" id="o7g-Ja-0hL"/>
                                                <constraint firstItem="cQm-xv-caK" firstAttribute="bottom" secondItem="lmM-6a-ZOY" secondAttribute="bottom" id="pbJ-8u-lGV"/>
                                                <constraint firstItem="vS3-c3-gqY" firstAttribute="leading" secondItem="h7z-Hm-ToD" secondAttribute="trailing" constant="8" id="ugb-N8-fTw"/>
                                                <constraint firstItem="59x-al-QQy" firstAttribute="leading" secondItem="h7z-Hm-ToD" secondAttribute="trailing" constant="8" id="yz4-JM-T0z"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <connections>
                                            <outlet property="downloadStateImgView" destination="vS3-c3-gqY" id="EFw-9i-vtk"/>
                                            <outlet property="thumbnailView" destination="h7z-Hm-ToD" id="tOg-LI-3D1"/>
                                            <outlet property="titleLabel" destination="59x-al-QQy" id="V6R-i7-NXN"/>
                                            <outlet property="videoDurationTime" destination="lmM-6a-ZOY" id="fbT-9B-XUs"/>
                                            <outlet property="videoSizeLabel" destination="cQm-xv-caK" id="XlP-0m-Gfx"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                            </tableView>
                        </subviews>
                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="TbS-H3-YJC" secondAttribute="trailing" id="7NZ-dg-B8D"/>
                            <constraint firstItem="TbS-H3-YJC" firstAttribute="top" secondItem="EV2-sm-RcI" secondAttribute="bottom" id="DWW-Lx-Gqo"/>
                            <constraint firstItem="TbS-H3-YJC" firstAttribute="bottom" secondItem="t3K-nx-q1J" secondAttribute="top" id="GR3-8j-T9J"/>
                            <constraint firstItem="TbS-H3-YJC" firstAttribute="leading" secondItem="PmI-7J-K0J" secondAttribute="leading" id="mnn-1T-PZV"/>
                        </constraints>
                    </view>
                    <size key="freeformSize" width="320" height="200"/>
                    <connections>
                        <outlet property="tableView" destination="TbS-H3-YJC" id="jae-kg-CuT"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="NmI-bf-0Mr" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="97.5" y="485"/>
        </scene>
        <!--缓存中-->
        <scene sceneID="jgE-HM-IKD">
            <objects>
                <viewController storyboardIdentifier="PLVDownloadProcessingViewController" title="缓存中" id="gXm-Hx-rqC" customClass="PLVDownloadProcessingViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="b1h-z0-xp8"/>
                        <viewControllerLayoutGuide type="bottom" id="iZp-zI-a6d"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="6fS-ZN-1RF">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="200"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="hEt-MO-idc">
                                <rect key="frame" x="0.0" y="0.0" width="320" height="155"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="PLVDownloadProcessingCell" rowHeight="92" id="PPi-fV-gxw" customClass="PLVDownloadProcessingCell">
                                        <rect key="frame" x="0.0" y="28" width="320" height="92"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="PPi-fV-gxw" id="MJX-IC-Cfs">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="92"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="DVq-ud-nRB">
                                                    <rect key="frame" x="8" y="11" width="128" height="72"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="128" id="1Cn-8y-HCf"/>
                                                        <constraint firstAttribute="height" constant="72" id="h6N-Zc-QCR"/>
                                                    </constraints>
                                                </imageView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="POLYV保利威如何通PlaySafe实现视频版权 …" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ryd-AT-6FY">
                                                    <rect key="frame" x="144" y="10" width="166" height="33.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <nil key="textColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="cjl-z4-ep3">
                                                    <rect key="frame" x="144" y="69" width="14" height="14"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="14" id="Jjb-vj-UeY"/>
                                                        <constraint firstAttribute="height" constant="14" id="g5C-iJ-4If"/>
                                                    </constraints>
                                                </imageView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="16M / 1000 M" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7aB-Lm-7uH">
                                                    <rect key="frame" x="223" y="68" width="77" height="15"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <nil key="textColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="bWx-jM-jAu">
                                                    <rect key="frame" x="61" y="37" width="28" height="27"/>
                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                                </imageView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="下载中" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wed-3W-B3s">
                                                    <rect key="frame" x="166" y="68" width="37" height="15"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <nil key="textColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="Ryd-AT-6FY" secondAttribute="trailing" constant="10" id="2MD-rw-sZO"/>
                                                <constraint firstItem="wed-3W-B3s" firstAttribute="bottom" secondItem="cjl-z4-ep3" secondAttribute="bottom" id="5y1-hm-lLk"/>
                                                <constraint firstItem="Ryd-AT-6FY" firstAttribute="leading" secondItem="DVq-ud-nRB" secondAttribute="trailing" constant="8" id="Fac-FB-eAe"/>
                                                <constraint firstItem="7aB-Lm-7uH" firstAttribute="leading" secondItem="wed-3W-B3s" secondAttribute="trailing" constant="20" id="GIQ-y6-LL6"/>
                                                <constraint firstItem="wed-3W-B3s" firstAttribute="leading" secondItem="cjl-z4-ep3" secondAttribute="trailing" constant="8" id="XHE-0I-Fax"/>
                                                <constraint firstItem="cjl-z4-ep3" firstAttribute="leading" secondItem="DVq-ud-nRB" secondAttribute="trailing" constant="8" id="dE7-61-b2M"/>
                                                <constraint firstItem="DVq-ud-nRB" firstAttribute="top" secondItem="MJX-IC-Cfs" secondAttribute="top" constant="11" id="eEV-rm-WVv"/>
                                                <constraint firstItem="cjl-z4-ep3" firstAttribute="bottom" secondItem="DVq-ud-nRB" secondAttribute="bottom" id="fTa-9q-NU9"/>
                                                <constraint firstItem="Ryd-AT-6FY" firstAttribute="top" secondItem="MJX-IC-Cfs" secondAttribute="top" constant="10" id="naQ-p7-pF5"/>
                                                <constraint firstItem="7aB-Lm-7uH" firstAttribute="bottom" secondItem="wed-3W-B3s" secondAttribute="bottom" id="qez-AW-kZ9"/>
                                                <constraint firstItem="DVq-ud-nRB" firstAttribute="leading" secondItem="MJX-IC-Cfs" secondAttribute="leading" constant="8" id="sCK-ta-37q"/>
                                                <constraint firstAttribute="bottom" secondItem="DVq-ud-nRB" secondAttribute="bottom" priority="250" constant="11" id="tFE-SF-32M"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <connections>
                                            <outlet property="downloadStateImgView" destination="cjl-z4-ep3" id="JgS-1Y-cfS"/>
                                            <outlet property="thumbnailView" destination="DVq-ud-nRB" id="MsM-3l-qGY"/>
                                            <outlet property="titleLabel" destination="Ryd-AT-6FY" id="niG-NL-qOT"/>
                                            <outlet property="videoSizeLabel" destination="7aB-Lm-7uH" id="tZu-p6-MhZ"/>
                                            <outlet property="videoStateLable" destination="wed-3W-B3s" id="34P-Ec-2f6"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                            </tableView>
                            <toolbar opaque="NO" clearsContextBeforeDrawing="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Nyr-Xb-PgL" customClass="PLVToolbar">
                                <rect key="frame" x="0.0" y="156" width="320" height="44"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="JdR-ym-fI1"/>
                                </constraints>
                                <items/>
                            </toolbar>
                        </subviews>
                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="hEt-MO-idc" firstAttribute="leading" secondItem="6fS-ZN-1RF" secondAttribute="leading" id="8pg-Ch-o7z"/>
                            <constraint firstItem="hEt-MO-idc" firstAttribute="top" secondItem="b1h-z0-xp8" secondAttribute="bottom" id="BEf-Xk-EER"/>
                            <constraint firstItem="Nyr-Xb-PgL" firstAttribute="top" secondItem="hEt-MO-idc" secondAttribute="bottom" constant="1" id="T1H-oU-4bf"/>
                            <constraint firstAttribute="trailing" secondItem="Nyr-Xb-PgL" secondAttribute="trailing" id="WWW-YT-GfG"/>
                            <constraint firstItem="iZp-zI-a6d" firstAttribute="top" secondItem="Nyr-Xb-PgL" secondAttribute="bottom" id="WqA-HD-cv2"/>
                            <constraint firstAttribute="trailing" secondItem="hEt-MO-idc" secondAttribute="trailing" id="uA6-ug-dJk"/>
                            <constraint firstItem="iZp-zI-a6d" firstAttribute="top" secondItem="Nyr-Xb-PgL" secondAttribute="bottom" id="vGG-ol-sN6"/>
                            <constraint firstItem="Nyr-Xb-PgL" firstAttribute="leading" secondItem="6fS-ZN-1RF" secondAttribute="leading" id="vnT-TH-ahO"/>
                        </constraints>
                    </view>
                    <size key="freeformSize" width="320" height="200"/>
                    <connections>
                        <outlet property="tableView" destination="hEt-MO-idc" id="FHb-TO-37G"/>
                        <outlet property="toolbar" destination="Nyr-Xb-PgL" id="0Jp-3L-yAR"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="wy6-dh-qZb" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="97.5" y="835"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="5EA-3F-PVg">
            <objects>
                <navigationController id="vau-sc-JAe" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="ndN-v3-YKb">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="50"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <textAttributes key="largeTitleTextAttributes">
                            <color key="textColor" systemColor="labelColor"/>
                        </textAttributes>
                    </navigationBar>
                    <connections>
                        <segue destination="pBy-Vf-xVx" kind="relationship" relationship="rootViewController" id="h07-Qr-AKx"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="z8R-SJ-iAU" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1389" y="-110"/>
        </scene>
        <!--课时目录-->
        <scene sceneID="BwO-Dc-YoC">
            <objects>
                <viewController storyboardIdentifier="PLVCourseVideoListController" title="课时目录" id="UgH-bu-XMa" customClass="PLVCourseVideoListController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="sZe-e9-mBT"/>
                        <viewControllerLayoutGuide type="bottom" id="aNi-HX-ZUl"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="dij-n3-1qA">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="240"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="130" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="qP0-T7-4Mu">
                                <rect key="frame" x="0.0" y="0.0" width="320" height="240"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <inset key="scrollIndicatorInsets" minX="0.0" minY="0.0" maxX="0.0" maxY="50"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="PLVVideoCell" rowHeight="130" id="Y4y-DJ-wqC" customClass="PLVVideoCell">
                                        <rect key="frame" x="0.0" y="28" width="320" height="130"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Y4y-DJ-wqC" id="utZ-wY-37e">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="130"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="plv_ph_courseCover" translatesAutoresizingMaskIntoConstraints="NO" id="HNk-n0-6Gd">
                                                    <rect key="frame" x="16" y="20" width="144" height="90"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="144" id="HWH-dG-HxI"/>
                                                        <constraint firstAttribute="height" constant="90" id="iPe-8D-ASU"/>
                                                    </constraints>
                                                </imageView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="标题" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="zxJ-pq-q58">
                                                    <rect key="frame" x="168" y="20" width="136" height="19.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                    <nil key="textColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" alignment="center" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="EKl-oa-14s" userLabel="信息组">
                                                    <rect key="frame" x="168" y="47.5" width="45" height="14.5"/>
                                                    <subviews>
                                                        <imageView userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="plv_icon_time" translatesAutoresizingMaskIntoConstraints="NO" id="VfC-Ee-5p1">
                                                            <rect key="frame" x="0.0" y="0.5" width="14" height="14"/>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="时长" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Bu3-FL-m40">
                                                            <rect key="frame" x="20" y="0.0" width="25" height="14.5"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                            <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                                            <constraints>
                                                <constraint firstItem="EKl-oa-14s" firstAttribute="leading" secondItem="zxJ-pq-q58" secondAttribute="leading" id="283-SN-24Y"/>
                                                <constraint firstItem="HNk-n0-6Gd" firstAttribute="leading" secondItem="utZ-wY-37e" secondAttribute="leadingMargin" id="6No-kc-LI4"/>
                                                <constraint firstItem="EKl-oa-14s" firstAttribute="top" secondItem="zxJ-pq-q58" secondAttribute="bottom" constant="8" id="9IE-Ey-U33"/>
                                                <constraint firstItem="zxJ-pq-q58" firstAttribute="top" secondItem="HNk-n0-6Gd" secondAttribute="top" id="MgX-Kj-lJQ"/>
                                                <constraint firstItem="zxJ-pq-q58" firstAttribute="leading" secondItem="HNk-n0-6Gd" secondAttribute="trailing" constant="8" id="ce6-VB-UqT"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="zxJ-pq-q58" secondAttribute="trailing" id="nLE-1B-ouU"/>
                                                <constraint firstItem="HNk-n0-6Gd" firstAttribute="centerY" secondItem="utZ-wY-37e" secondAttribute="centerY" id="yhd-Mc-fM0"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <connections>
                                            <outlet property="coverImageView" destination="HNk-n0-6Gd" id="de1-dI-YNZ"/>
                                            <outlet property="durationLabel" destination="Bu3-FL-m40" id="Zdm-dT-3cs"/>
                                            <outlet property="titleLabel" destination="zxJ-pq-q58" id="ug2-vV-F5o"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="dataSource" destination="UgH-bu-XMa" id="sZY-pU-USv"/>
                                    <outlet property="delegate" destination="UgH-bu-XMa" id="VrL-pf-ots"/>
                                </connections>
                            </tableView>
                            <toolbar opaque="NO" clearsContextBeforeDrawing="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="i8o-ka-GaD" customClass="PLVToolbar">
                                <rect key="frame" x="0.0" y="190" width="320" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="ByD-7a-4c0"/>
                                </constraints>
                                <items/>
                            </toolbar>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="qP0-T7-4Mu" firstAttribute="leading" secondItem="dij-n3-1qA" secondAttribute="leading" id="8R0-dQ-hvM"/>
                            <constraint firstItem="aNi-HX-ZUl" firstAttribute="top" secondItem="qP0-T7-4Mu" secondAttribute="bottom" id="IGa-gk-2wX"/>
                            <constraint firstItem="qP0-T7-4Mu" firstAttribute="top" secondItem="dij-n3-1qA" secondAttribute="top" id="RBG-Pg-1gU"/>
                            <constraint firstAttribute="trailing" secondItem="i8o-ka-GaD" secondAttribute="trailing" id="SIg-EZ-iKr"/>
                            <constraint firstItem="i8o-ka-GaD" firstAttribute="leading" secondItem="dij-n3-1qA" secondAttribute="leading" id="w79-s9-jYG"/>
                            <constraint firstItem="aNi-HX-ZUl" firstAttribute="top" secondItem="i8o-ka-GaD" secondAttribute="bottom" id="yyW-O9-mea"/>
                            <constraint firstAttribute="trailing" secondItem="qP0-T7-4Mu" secondAttribute="trailing" id="znd-XH-MmS"/>
                        </constraints>
                    </view>
                    <size key="freeformSize" width="320" height="240"/>
                    <connections>
                        <outlet property="tableView" destination="qP0-T7-4Mu" id="f9O-zG-MB3"/>
                        <outlet property="toolbar" destination="i8o-ka-GaD" id="bSk-ot-BSa"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="T75-AC-sjG" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1404" y="2043"/>
        </scene>
        <!--课程介绍-->
        <scene sceneID="hNT-4H-b3a">
            <objects>
                <viewController storyboardIdentifier="PLVCourseIntroductionController" title="课程介绍" id="QBE-b5-5OC" customClass="PLVCourseIntroductionController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="0ki-X0-2pq"/>
                        <viewControllerLayoutGuide type="bottom" id="RfW-Vu-xU6"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="w7k-ZP-UqA">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="180"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pIe-dx-bBw">
                                <rect key="frame" x="0.0" y="0.0" width="320" height="180"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="课程介绍" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UM0-e9-0xs">
                                        <rect key="frame" x="16" y="16" width="288" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="UM0-e9-0xs" secondAttribute="trailing" id="8IH-K1-CiS"/>
                                    <constraint firstItem="UM0-e9-0xs" firstAttribute="top" secondItem="pIe-dx-bBw" secondAttribute="top" constant="16" id="8MZ-Xv-YVF"/>
                                    <constraint firstItem="UM0-e9-0xs" firstAttribute="centerX" secondItem="pIe-dx-bBw" secondAttribute="centerX" id="8Nm-dK-XDF"/>
                                    <constraint firstItem="UM0-e9-0xs" firstAttribute="width" secondItem="pIe-dx-bBw" secondAttribute="width" constant="-32" id="Etp-PM-Tx4"/>
                                    <constraint firstAttribute="bottom" secondItem="UM0-e9-0xs" secondAttribute="bottomMargin" id="q2h-ff-5kp"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="pIe-dx-bBw" firstAttribute="leading" secondItem="w7k-ZP-UqA" secondAttribute="leading" id="cEu-qb-827"/>
                            <constraint firstItem="RfW-Vu-xU6" firstAttribute="top" secondItem="pIe-dx-bBw" secondAttribute="bottom" id="eT7-lG-Ak9"/>
                            <constraint firstItem="pIe-dx-bBw" firstAttribute="top" secondItem="0ki-X0-2pq" secondAttribute="bottom" id="gOm-KP-fzv"/>
                            <constraint firstAttribute="trailing" secondItem="pIe-dx-bBw" secondAttribute="trailing" id="z0n-ag-X8W"/>
                        </constraints>
                    </view>
                    <size key="freeformSize" width="320" height="180"/>
                    <connections>
                        <outlet property="introLabel" destination="UM0-e9-0xs" id="s3O-Ry-KfJ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="vYx-WC-Yki" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1389" y="2380"/>
        </scene>
        <!--上传管理-->
        <scene sceneID="8M3-ak-bJu">
            <objects>
                <viewController id="icg-fT-kZJ" customClass="PLVUploadTableViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="sJX-7b-71B"/>
                        <viewControllerLayoutGuide type="bottom" id="zgE-c5-8Pf"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="jPV-CN-YwO">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="480"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                    </view>
                    <navigationItem key="navigationItem" title="上传管理" id="opg-gz-aTi">
                        <barButtonItem key="rightBarButtonItem" title="Item" image="plv_btn_add" id="lB8-oD-fL0">
                            <connections>
                                <action selector="openLibrary:" destination="icg-fT-kZJ" id="V62-lP-tvx"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="openLibraryButton" destination="lB8-oD-fL0" id="n93-q0-p2A"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ugk-fr-pKU" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-579" y="1430"/>
        </scene>
        <!--缓存管理-->
        <scene sceneID="JaO-SJ-eXC">
            <objects>
                <viewController id="h0G-qx-d1C" customClass="PLVDownloadManagerViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="eT9-zz-xyn"/>
                        <viewControllerLayoutGuide type="bottom" id="SRc-57-pt5"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="xSr-U9-K3k">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="480"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                    </view>
                    <navigationItem key="navigationItem" title="缓存管理" id="n5z-uY-WrV"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="jcZ-s8-FJz" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-579" y="660"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="uPP-Tt-teV">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" id="TUW-h1-lx4" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="cY1-gb-RbW">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="50"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="u8u-iQ-7yV" kind="relationship" relationship="rootViewController" id="ez3-cn-qlW"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="L2h-LZ-oBp" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="383" y="-795"/>
        </scene>
        <!--Simple Detail Controller-->
        <scene sceneID="9AZ-8e-ZTo">
            <objects>
                <viewController id="fJC-la-rR2" customClass="PLVSimpleDetailController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="GRE-EY-qtD"/>
                        <viewControllerLayoutGuide type="bottom" id="JiK-kC-8gQ"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="mI3-lv-IHP">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="480"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3kH-KD-fSh">
                                <rect key="frame" x="0.0" y="44" width="320" height="180"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="3kH-KD-fSh" secondAttribute="height" multiplier="16:9" id="wnP-P4-cYF"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="3kH-KD-fSh" firstAttribute="leading" secondItem="mI3-lv-IHP" secondAttribute="leading" id="Due-Th-jKE"/>
                            <constraint firstItem="3kH-KD-fSh" firstAttribute="top" secondItem="GRE-EY-qtD" secondAttribute="bottom" id="Qe1-ny-u4q"/>
                            <constraint firstAttribute="trailing" secondItem="3kH-KD-fSh" secondAttribute="trailing" id="d0A-TB-BJs"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="playerPlaceholder" destination="3kH-KD-fSh" id="Kpm-nq-QPZ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="QGJ-AT-e4Q" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="234" y="-110"/>
        </scene>
    </scenes>
    <resources>
        <image name="plv_bg_round4" width="8.5" height="8.5"/>
        <image name="plv_btn_add" width="20" height="20"/>
        <image name="plv_btn_cache" width="20" height="20"/>
        <image name="plv_btn_onlineVideo" width="18" height="18"/>
        <image name="plv_btn_upload" width="20" height="20"/>
        <image name="plv_icon_teacher" width="11" height="12"/>
        <image name="plv_icon_time" width="14" height="14"/>
        <image name="plv_ph_courseCover" width="170" height="106"/>
        <image name="plv_vod_btn_settings" width="20" height="20"/>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="linkColor">
            <color red="0.0" green="0.47843137254901963" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="secondarySystemBackgroundColor">
            <color red="0.94901960784313721" green="0.94901960784313721" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
