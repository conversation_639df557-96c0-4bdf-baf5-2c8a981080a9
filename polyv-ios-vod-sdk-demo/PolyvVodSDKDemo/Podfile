source 'https://github.com/CocoaPods/Specs.git'
source 'https://gitee.com/polyv_ef/plvspecs.git'

project 'PolyvVodSDKDemo.xcodeproj'

platform :ios, '11.0'
inhibit_all_warnings!

target 'PolyvVodSDKDemo' do
	# Uncomment the next line if you're using Swift or would like to use dynamic frameworks
	# use_frameworks!

	# Pods for PolyvVodSDKDemo
	pod 'XRCarouselView', '~> 2.6.1'
	pod 'YYWebImage', '~> 1.0.5'
	pod 'FDStackView', '~> 1.0.1'
    pod 'PLVMasonry', '~> 1.1.2'
    pod 'TZImagePickerController', '~> 3.2.0'

	# PLVVodSDK
    pod 'PolyvVodSDK', '~> 2.20.1'
    
    # 使用投屏功能的客户需要集成
    #pod 'LBLelinkKit', '~> 30503'
    pod 'PLVDLNASender', '1.4.1'

	# POLYV Open Source
	pod 'PLVVodDanmu', '~> 0.0.1'
	pod 'PLVSubtitle', '~> 0.1.0'

    # 上传SDK，不使用上传功能无需集成
    pod 'PLVVodUploadSDK', '~> 0.1.0'
  
	target 'PolyvVodSDKDemoTests' do
		inherit! :search_paths
		# Pods for testing
	end

	target 'PolyvVodSDKDemoUITests' do
		inherit! :search_paths
		# Pods for testing
	end

end
