#!/bin/bash
# -------------------------------------------------------------------------------
# Date:     2020/07/02
# Author:   Miss<PERSON><PERSON><PERSON>
# Description: 该脚本用于生成全架构的 framework 并进行 zip 压缩
# -------------------------------------------------------------------------------

# 获取脚本所在路径
SOURCE="$0"
while [ -h "$SOURCE"  ]; do # resolve $SOURCE until the file is no longer a symlink
    DIR="$( cd -P "$( dirname "$SOURCE"  )" && pwd  )"
    SOURCE="$(readlink "$SOURCE")"
    [[ $SOURCE != /*  ]] && SOURCE="$DIR/$SOURCE" # if $SOURCE was a relative symlink, we need to resolve it relative to the path where the symlink file was located
done
CURRENT_PATH="$( cd -P "$( dirname "$SOURCE"  )" && pwd  )"
ROOT_PATH=$(dirname ${CURRENT_PATH})
SDK_PROJECT_FLODER="${ROOT_PATH}/polyv-ios-vod-sdk"

cd ${CURRENT_PATH}

# 设置变量
FRAMEWORK_NAME="PLVVodSDK"
SCHEME_NAME="PLVVodSDK"
WORKSPACE_NAME="VodSDKWK.xcworkspace"
Current_Configuration="Release"

# 设置路径变量
BUILD_DIR="${CURRENT_PATH}/Build/Products"
iPhoneos_Framework_File="${BUILD_DIR}/${Current_Configuration}-iphoneos/${FRAMEWORK_NAME}.framework"
iPhoneos_Executable_File="${BUILD_DIR}/${Current_Configuration}-iphoneos/${FRAMEWORK_NAME}.framework/${FRAMEWORK_NAME}"
iPhonesimulator_Executable_File="${BUILD_DIR}/${Current_Configuration}-iphonesimulator/${FRAMEWORK_NAME}.framework/${FRAMEWORK_NAME}"

FRAMEWORK_DIR="${CURRENT_PATH}/Frameworks"
OUTPUT_Framework_File="${FRAMEWORK_DIR}/${FRAMEWORK_NAME}.framework"
OUTPUT_Executable_File="${FRAMEWORK_DIR}/${FRAMEWORK_NAME}.framework/${FRAMEWORK_NAME}"

FRAMEWORK_HEADER_FILE="${FRAMEWORK_DIR}/${FRAMEWORK_NAME}.framework/Headers/${FRAMEWORK_NAME}.h"
DOCS_HEADER_FILE="${SDK_PROJECT_FLODER}/${FRAMEWORK_NAME}.h"

# 设置压缩包名称、路径
read -p "⌨️  请输入点播 SDK 版本号（格式：x.x.x): " SDK_VERSION
time=$(date "+%y%m%d")
ZIP_NAME="${FRAMEWORK_NAME}_${SDK_VERSION}-${time}.zip"
ZIP_DIR="${CURRENT_PATH}/zip"

pod install

# 项目 clean
echo "\033[32m 项目 Cleaning... \033[0m"
xcodebuild clean -workspace ${WORKSPACE_NAME} -scheme ${SCHEME_NAME} | xcpretty
rm -r ${BUILD_DIR}
mkdir -p ${BUILD_DIR}
echo " "

# 真机架构
echo "\033[32m【${SCHEME_NAME}】- 开始打 ✨${Current_Configuration}真机架构✨ \033[0m"
xcodebuild -workspace ${WORKSPACE_NAME} -scheme ${SCHEME_NAME} ONLY_ACTIVE_ARCH=NO -configuration Release -sdk iphoneos BUILD_DIR="${BUILD_DIR}" BUILD_ROOT="${BUILD_DIR}" | xcpretty
echo " "

# 确认真机架构已存在
if [ ! -e $iPhoneos_Executable_File ]; then
echo "\033[31m【${SCHEME_NAME}】- ❌${Current_Configuration}真机架构不存在 \033[0m"
echo " "
exit 1
fi
echo " "

# 模拟器架构
echo "\033[32m【${SCHEME_NAME}】- 开始打 ✨${Current_Configuration}模拟器架构✨ \033[0m"
xcodebuild -workspace ${WORKSPACE_NAME} -scheme ${SCHEME_NAME} ONLY_ACTIVE_ARCH=NO -configuration Release -sdk iphonesimulator BUILD_DIR="${BUILD_DIR}" BUILD_ROOT="${BUILD_DIR}" | xcpretty
echo " "

# 确认模拟器架构已存在
if [ ! -e $iPhonesimulator_Executable_File ]; then
echo "\033[31m【${SCHEME_NAME}】- ❌${Current_Configuration}模拟器架构不存在 \033[0m"
echo " "
exit 1
fi
echo " "


lipo -remove arm64 ${iPhonesimulator_Executable_File} -output ${BUILD_DIR}/${FRAMEWORK_NAME}
cp -R ${BUILD_DIR}/${FRAMEWORK_NAME} ${iPhonesimulator_Executable_File}
rm -r ${BUILD_DIR}/${FRAMEWORK_NAME}
# 检查输出
echo "检查模拟器framework架构："
lipo -info "${iPhonesimulator_Executable_File}"

# 拷贝架构到 FRAMEWORK_DIR 路径下
mkdir -p "${FRAMEWORK_DIR}"
rm -r "${OUTPUT_Framework_File}"
cp -R "${iPhoneos_Framework_File}" "${FRAMEWORK_DIR}"
if [ ! -e ${OUTPUT_Executable_File} ]; then
echo "\033[31m 拷贝 Framework 失败 \033[0m"
exit 1
fi

# 合并四架构包
echo "\033[32m 合并真机、模拟器架构包... \033[0m"
lipo -create -output "${OUTPUT_Executable_File}" "${iPhoneos_Executable_File}" "${iPhonesimulator_Executable_File}" 
echo " "

echo "\033[32m Framework 路径：\033[0m ${FRAMEWORK_DIR} 下 "
echo " "

# 检查输出
echo "【\033[32m${FRAMEWORK_NAME}.framework\033[0m】检查架构："
lipo -info "${OUTPUT_Executable_File}"

# 压缩 framework 包
echo "\033[32m 准备生成压缩包【${ZIP_NAME}】 \033[0m"
cd ${FRAMEWORK_DIR}
pwd
zip -r -q ${ZIP_NAME} "${FRAMEWORK_NAME}.framework"

# 检查压缩包是否存在
if [ ! -e ${ZIP_NAME} ]; then
echo "\033[31m ❌ 压缩包【${ZIP_NAME}】不存在 \033[0m"
exit 1
fi
echo " "
echo "\033[32m 成功生成压缩包【${ZIP_NAME}】 \033[0m"

cp ${FRAMEWORK_HEADER_FILE} ${DOCS_HEADER_FILE}

sed -i '' 's$#import <UIKit/UIKit.h>$//#import <UIKit/UIKit.h>$' ${DOCS_HEADER_FILE}
sed -i '' 's$FOUNDATION_EXPORT$//FOUNDATION_EXPORT$' ${DOCS_HEADER_FILE}
sed -i '' 's$FOUNDATION_EXPORT$//FOUNDATION_EXPORT$' ${DOCS_HEADER_FILE}
sed -i '' 's$<PLVVodSDK/$"$' ${DOCS_HEADER_FILE}
sed -i '' 's$>$"$' ${DOCS_HEADER_FILE}

cat ${DOCS_HEADER_FILE}

PODSPEC_NAME="PolyvVodSDK"
GITHUB_URL="https://github.com/polyv/polyv-ios-vod-sdk"
DOCS_OUTPUT="${SDK_VERSION}-${time}"

echo -e "\033[32m 生成 API 文档 \033[0m"
cd ${SDK_PROJECT_FLODER}
jazzy --objc --clean --umbrella-header ${FRAMEWORK_NAME}.h --framework-root . --module ${PODSPEC_NAME} --github_url ${GITHUB_URL} --module-version ${SDK_VERSION} --output ${DOCS_OUTPUT}
echo ""

echo -e "\033[32m 清理中间文件 \033[0m"
rm ${DOCS_HEADER_FILE}
echo ""

echo -e "\a \033[32m \a运行结束 \033[0m"

