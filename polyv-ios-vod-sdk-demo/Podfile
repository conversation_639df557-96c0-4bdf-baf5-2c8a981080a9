source 'https://github.com/CocoaPods/Specs.git'
source 'https://gitee.com/polyv_ef/plvspecs.git'
source 'https://github.com/aliyun/aliyun-specs.git'

platform :ios, '11.0'
inhibit_all_warnings!
#use_frameworks!

#指定workspace名称，没有则生成新的，路径是相对于Podfile路径
workspace 'VodSDKWK.xcworkspace'

#配置默认xcodeproj ,路径相对于Podfile 路径
project 'PolyvVodSDKDemo/PolyvVodSDKDemo.xcodeproj'

abstract_target 'vod' do

  pod 'AliyunOSSiOS', '~> 2.10.7'
  
  # 上传sdk
  target 'PLVVodUploadSDK' do
      project '../PLVVodUploadSDK/PLVVodUploadSDK'
  end
  
  pod 'PLVTimer', '0.0.4'
  pod 'SSZipArchive', '~> 2.1.5'
  pod 'PLVIJKPlayer', '~> 0.15.0'

  pod 'PLVAliHttpDNS', '3.2.0'
  pod 'PLVDLNASender', '1.4.2'
  pod 'PLVFDB', '1.0.5'
  
  #点播 SDK
  target :PLVVodSDK do
    project '../polyv-ios-vod-sdk/PolyvVodSDK/PolyvVodSDK'
  end
  
  #点播 Demo
  target :PolyvVodSDKDemo do
    project 'PolyvVodSDKDemo/PolyvVodSDKDemo'
    
    pod 'XRCarouselView', '~> 2.6.1'
    pod 'YYWebImage', '~> 1.0.5'
    pod 'PLVMasonry', '~> 1.1.2'

    # 使用开源组件的客户需要集成
    pod 'FDStackView', '~> 1.0.1'
    pod 'PLVVodDanmu', '~> 0.0.1'
    
    # 使用上传功能的客户需要集成
    pod 'TZImagePickerController', '~> 3.2.0'
  end
end

post_install do |installer|
  installer.generated_projects.each do |project|
    project.targets.each do |target|
        target.build_configurations.each do |config|
            config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '11.0'
         end
    end
  end
end
