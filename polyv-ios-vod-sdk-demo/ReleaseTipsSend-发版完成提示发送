#!/bin/bash
# -------------------------------------------------------------------------------
# Filename: 点播发版提示完成发送脚本
# Date:     2022/03/31
# Author:   Dhan
# update:   2022/03/31
# Description: 本脚本负责将版本更新说明提示自动发送至移动端群、移动端SDK版本发布公告群中。
# -------------------------------------------------------------------------------

ReleaseVersionShell_Path="$(cd "$(dirname "$0")"; pwd)"
cd $ReleaseVersionShell_Path
echo " "
read -p "⌨️ （1/3）请输入当前发版版本，例如1.8.3:" currentVersion 
dataString=$(date +"%Y年%m月%d日")
echo "当前发版日期：${dataString}"
read -p "⌨️ （2/3）请确认当前发版日期是否正确,正确回车跳过该步骤。若非当日请输入“xxxx年xx月xx日”格式，例如输入2022年03月21日" dataString_New
if [ "${dataString_New}" != "" ] && [ "${dataString_New: 4: 1}" = "年"  ] && [ "${dataString_New: 7: 1}" = "月"  ] && [ "${dataString_New: 10: 1}" = "日"  ]; then
     dataString=$dataString_New
     echo "当前发版日期已改为：${dataString}"
 fi 
full_String="（${dataString}发布）iOS 点播 PolyvVodSDK ${currentVersion} 已发布，版本说明如下：\n"
title_String="（${dataString}发布）iOS 点播 PolyvVodSDK ${currentVersion} 已发布"

while read line || [[ -n ${line} ]]
do
    full_String="${full_String}${line}\n"
done < ReleaseNotes.md

# releaseDemoString="Github 版本说明：https://github.com/polyv/polyv-ios-livescenes-sdk-demo/releases\n蒲公英体验地址：https://www.pgyer.com/IzFQ 密码：polyv\n"

# full_String="${full_String}----------------------------------\n${releaseDemoString}"


echo " "
echo "\033[36m 即将发送以下提示至群内，请确认： \033[0m\n\n ${full_String}"

echo " "
read -p "⌨️ （3/3）请确认提示内容无误。输入y发送提示:" sendConfirm
if [ ! "$sendConfirm" = "y" ]; then
    echo "❌ \033[31m 输入非y，未确认，脚本停止 \033[0m"
    exit 1
fi

echo " "
#需替换为对应场景发版
#智能机器人测试群
# curl -H 'Content-Type: application/json' \
#    -d "{\"msgtype\": \"markdown\", 
#         \"markdown\": {
#             \"title\": \"${title_String}\",
#              \"text\": \"${full_String}\"
#         },
#         \"at\": {
#             \"isAtAll\": 1
#         }
#       }" \
#       -X POST 'https://oapi.dingtalk.com/robot/send?access_token=38c17ca46c7b588969404f6024683899cf8a573b4bdc0a85aa12a5af48cb481d'

#移动端 SDK 版本发布公告群
curl -H 'Content-Type: application/json' \
   -d "{\"msgtype\": \"markdown\", 
        \"markdown\": {
            \"title\": \"${title_String}\",
             \"text\": \"${full_String}\"
        },
        \"at\": {
            \"isAtAll\": 1
        }
      }" \
      -X POST 'https://oapi.dingtalk.com/robot/send?access_token=b6c23ff94320c64f2fd5e058b619614fe00ed38c9d0ca8ce23d29c0fecc0e749'

#iOS技术支持群
curl -H 'Content-Type: application/json' \
   -d "{\"msgtype\": \"markdown\", 
        \"markdown\": {
            \"title\": \"${title_String}\",
             \"text\": \"${full_String}\"
        },
        \"at\": {
            \"isAtAll\": 1
        }
      }" \
      -X POST 'https://oapi.dingtalk.com/robot/send?access_token=946cf368ffb402946792ac3d331cdc77993a99b6e92d345b6abbfe7835d5d67e'

#Android技术支持群
# curl -H 'Content-Type: application/json' \
#    -d "{\"msgtype\": \"markdown\", 
#         \"markdown\": {
#             \"title\": \"${title_String}\",
#              \"text\": \"${full_String}\"
#         },
#         \"at\": {
#             \"isAtAll\": 1
#         }
#       }" \
#       -X POST 'https://oapi.dingtalk.com/robot/send?access_token=d2645d7504230a7afbe4de770c8f81af8fadc25879c55e8cb6e33f3607c833ee'

echo "\n\033[32m✨iOS点播发版提示已发送✨\033[0m"
