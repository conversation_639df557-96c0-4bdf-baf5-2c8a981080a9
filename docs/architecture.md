# Polyv iOS VOD SDK - 深度架构分析文档

## 1. 概述

本文档提供了对保利威（Polyv）iOS视频点播（VOD）SDK套件的全面架构分析。该项目是一个成熟、功能丰富的解决方案，旨在为iOS应用程序提供视频点播服务，包括视频播放、离线下载和视频上传。

其架构设计的核心思想是 **模块化** 和 **关注点分离**。通过将核心功能封装到独立的SDK中，该项目实现了业务逻辑与客户端实现的高度解耦，使得开发者可以根据需求灵活地集成这些功能。

## 2. 核心组件与系统架构

系统由三个主要部分组成：

1.  **`polyv-ios-vod-sdk` (核心VOD SDK)**: 提供视频播放、下载、加密和UI定制等功能。
2.  **`PLVVodUploadSDK` (上传SDK)**: 专门负责视频上传，并支持断点续传。
3.  **`polyv-ios-vod-sdk-demo` (演示应用)**: 作为官方的参考实现，展示了如何集成和使用上述两个SDK。

---

## 3. `polyv-ios-vod-sdk` 深度剖析

这是系统中最复杂、功能最丰富的组件。

### 3.1 视频播放器 (Player) 模块

播放器是整个SDK的核心，是一个由多个协作组件构成的子系统。

#### 播放器模块架构图

```mermaid
graph TD;
    subgraph "Player Module (polyv-ios-vod-sdk)"
        direction TB
        A["PLVVodPlayerViewController (Controller)"];
        B["Player Skin (UI Layer)"];
        C["Player Core (AVPlayer Wrapper)"];
        D["Data Models (e.g., PLVVodVideo)"];
        E["Feature Plugins"];
    end

    subgraph "Feature Plugins"
        direction LR
        E1["Danmu Manager"];
        E2["Subtitle Manager"];
        E3["Advertisement Manager"];
        E4["Picture-in-Picture"];
    end

    A -- "Manages / Coordinates" --> B;
    A -- "Controls" --> C;
    A -- "Uses" --> D;
    A -- "Integrates" --> E;

    B -- "Sends User Actions to" --> A;
    C -- "Sends Player Events to" --> A;
    E -- "Injects Features into" --> A;
    E -- "Renders on" --> B
    
    classDef default fill:#f9f9f9,stroke:#333,stroke-width:2px;
    classDef plugins fill:#e8f0ff,stroke:#555;
    class A,B,C,D,E default;
    class E1,E2,E3,E4 plugins;
```

#### 视频播放流程时序图

此图展示了从用户点击视频到视频开始播放的完整内部事件序列。

```mermaid
sequenceDiagram
    participant User
    participant App UI
    participant PlayerViewController
    participant VodSDK_API
    participant PlayerCore (AVPlayer)

    User->>App UI: 点击视频<br>Taps on a video
    App UI->>PlayerViewController: 初始化并传入 videoId<br>Initialize with videoId
    
    activate PlayerViewController
    PlayerViewController->>VodSDK_API: 根据 videoId 请求视频数据<br>Request video data by videoId
    activate VodSDK_API
    VodSDK_API-->>PlayerViewController: 返回 PLVVodVideo 对象<br>Return PLVVodVideo object
    deactivate VodSDK_API
    
    PlayerViewController->>PlayerCore (AVPlayer): 使用视频URL创建播放项<br>Create player item with URL
    activate PlayerCore (AVPlayer)
    Note over PlayerCore (AVPlayer): 开始缓冲视频数据<br>Starts buffering video data
    PlayerCore (AVPlayer)-->>PlayerViewController: 报告播放器状态 (e.g., readyToPlay)
    
    PlayerViewController->>PlayerCore (AVPlayer): play()
    PlayerViewController-->>App UI: 播放器UI显示为“播放中”<br>Update UI to 'playing' state
    deactivate PlayerViewController
    
    Note over PlayerCore (AVPlayer): 视频开始渲染和播放<br>Video renders and plays
    deactivate PlayerCore (AVPlayer)
```

### 3.2 离线下载 (Downloader) 模块

下载模块的设计核心是**可靠性**和**状态管理**。

#### 下载模块架构图

```mermaid
graph TD;
    subgraph "Downloader Module"
        direction LR
        A["PLVVodDownloader (Singleton)"];
        B["Download Task Queue"];
        C["PLVVodDownloadTask (Model)"];
        D["PLVDownloadDBMgr (Database)"];
        E["Networking Engine (NSURLSession)"];
    end

    A -- "Manages" --> B;
    B -- "Contains" --> C;
    A -- "Creates/Updates" --> C;
    A -- "Uses" --> E;
    A -- "Interacts with" --> D;
    
    C -- "Is Persisted by" --> D;
    E -- "Downloads Data for" --> C;
    
    subgraph "Application Layer"
        F["Download UI (ViewController)"];
    end
    
    F -- "Adds/Controls Tasks via" --> A;
    A -- "Notifies Changes to" --> F;
    
    classDef default fill:#f9f9f9,stroke:#333,stroke-width:2px;
    classDef singleton fill:#fffbe6,stroke:#8a6d3b;
    classDef db fill:#e6f7ff,stroke:#31708f;
    class A singleton;
    class D db;
```

#### 离线下载流程时序图

此图展示了下载任务从创建到完成（或中断后恢复）的完整生命周期。

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant Downloader
    participant Database
    participant Network

    User->>UI: Click Download
    UI->>Downloader: Add Task
    
    activate Downloader
    Downloader->>Database: Save New Task (Status: Waiting)
    activate Database
    Database-->>Downloader: OK
    deactivate Database
    
    Downloader->>Network: Start Download
    activate Network
    
    loop Progress Loop
        Network-->>Downloader: Progress Update
        Downloader->>Database: Update Progress in DB
        Downloader-->>UI: Notify UI Refresh
    end
    
    alt Success
        Network-->>Downloader: Complete
        Downloader->>Database: Update Status to 'Completed'
    else Interruption
        Note over User, Network: App Restarts
        Downloader->>Database: Load Unfinished Tasks
        Database-->>Downloader: Return Tasks
        Downloader->>Network: Resume Downloads
    end

    deactivate Network
    deactivate Downloader
```

---

## 4. `PLVVodUploadSDK` 深度剖析

上传SDK的核心目标是 **可靠地将大视频文件传输到服务器**。

### 上传模块架构图

```mermaid
graph TD;
    subgraph "Upload SDK"
        Uploader;
        Chunker;
        ResumeUtil["Resume Util (State)"];
        NetworkClient;
    end
    
    subgraph "App"
        UI;
    end

    UI --> Uploader;
    Uploader --> Chunker;
    Uploader --> ResumeUtil;
    Uploader --> NetworkClient;
    Uploader --> UI;
    
    classDef state fill:#fff0e6,stroke:#d9534f;
    class ResumeUtil state;
```

### 断点续传流程时序图

该流程是上传SDK中最关键的部分，详细说明了系统如何处理分块上传和异常恢复。

```mermaid
sequenceDiagram
    participant User
    participant AppUI
    participant Uploader
    participant ResumeUtil
    participant NetworkClient

    User->>AppUI: 选择视频并点击上传<br>Selects video, taps Upload
    AppUI->>Uploader: startUpload(filePath)
    
    activate Uploader
    Uploader->>ResumeUtil: 检查是否存在未完成的记录<br>Check for incomplete record
    activate ResumeUtil
    ResumeUtil-->>Uploader: 返回下一个要上传的分块索引<br>Return next chunk index to upload
    deactivate ResumeUtil
    
    Uploader->>Uploader: 将文件分割成多个分块<br>Split file into chunks

    loop 对于每个未上传的分块 (For each un-uploaded chunk)
        Uploader->>NetworkClient: 上传分块 `N`<br>Upload chunk `N`
        activate NetworkClient
        NetworkClient-->>Uploader: 分块 `N` 上传成功<br>Chunk `N` uploaded successfully
        deactivate NetworkClient
        
        Uploader->>ResumeUtil: 更新记录，标记分块 `N` 已完成<br>Update record, mark chunk `N` as done
        Uploader-->>AppUI: 更新UI进度<br>Update UI progress
    end
    
    alt 上传被中断 (Upload is interrupted)
        Note over User, NetworkClient: 网络错误或应用关闭<br>Network error or app closes
    end

    Uploader->>NetworkClient: 发送“合并文件”请求<br>Send "merge files" request
    NetworkClient-->>Uploader: 文件合并成功<br>File merged successfully
    Uploader->>ResumeUtil: 删除上传记录<br>Delete upload record
    Uploader-->>AppUI: 报告上传完成<br>Report upload complete
    deactivate Uploader
```

---

## 5. 架构总结

### 5.1 设计模式与原则

*   **模块化**: 核心设计，将功能解耦到独立的SDK中。
*   **分层架构**: 在VOD SDK内部，UI、控制、核心引擎和插件层各司其职。
*   **外观模式 (Facade)**: `PLVVodPlayerViewController`为复杂的播放器子系统提供了简单的接口。
*   **单例模式 (Singleton)**: `PLVVodDownloader`作为全局下载管理器。
*   **状态持久化**: 下载和上传模块都设计了健壮的状态持久化机制，以实现可靠的断点续传。
*   **观察者模式 (Observer)**: 通过`NSNotificationCenter`或KVO在组件间传递状态更新，实现松散耦合。

### 5.2 架构优势

*   **高内聚，低耦合**: 每个模块职责单一，依赖关系清晰。
*   **可扩展性**: 插件式的设计使得添加新功能（如广告、弹幕）变得容易。
*   **可靠性**: 对下载和上传的持久化和状态管理设计得非常健壮，能应对不稳定的移动网络环境。
*   **可测试性**: 核心逻辑被封装在独立的类中，便于进行单元测试。

### 5.3 潜在的现代化方向

*   **引入响应式编程**: 使用Apple的`Combine`框架或`RxSwift`来处理异步事件流，可以简化状态管理和回调逻辑。
*   **依赖注入**: 引入依赖注入容器来替代单例模式，以降低耦合、提升代码的灵活性和可测试性。
*   **逐步Swift化**: 从数据模型和新的UI层开始，逐步将项目迁移到Swift，以利用其类型安全和现代语法特性。 