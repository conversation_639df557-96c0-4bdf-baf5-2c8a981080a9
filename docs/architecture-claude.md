# POLYV iOS VOD SDK Architecture Document

## Introduction

This document outlines the overall project architecture for the POLYV iOS VOD SDK ecosystem, including backend systems, shared services, and non-UI specific concerns. Its primary goal is to serve as the guiding architectural blueprint for development, ensuring consistency and adherence to chosen patterns and technologies.

**Relationship to Frontend Architecture:**
The project includes significant mobile UI components across multiple iOS applications. The architecture encompasses upload SDK, core VOD SDK, and demo applications with distinct frontend responsibilities handled through native iOS frameworks.

### Starter Template or Existing Project

**Status:** Existing Project - Brownfield Development

This architecture documents an existing, mature iOS SDK ecosystem consisting of:
- **PLVVodUploadSDK** - Standalone video upload SDK
- **polyv-ios-vod-sdk** - Core VOD playback SDK framework  
- **polyv-ios-vod-sdk-demo** - Comprehensive demo application
- **Multiple podspec variants** for different distribution scenarios

The project follows established iOS development patterns with Xcode workspaces, CocoaPods dependency management, and modular framework architecture. All new development should align with existing patterns and conventions.

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-16 | 1.0 | Initial architecture documentation | Winston |

## High Level Architecture

### Technical Summary

The POLYV iOS VOD SDK employs a modular framework architecture supporting video upload, playback, and content management. The system utilizes native iOS frameworks with CocoaPods for dependency management, integrates with Aliyun OSS for storage, and provides comprehensive video functionality including offline download, Picture-in-Picture, custom skins, and DLNA casting. The architecture supports multiple distribution models through various podspec configurations.

### High Level Overview

1. **Architectural Style:** Modular Framework Architecture with separate SDKs for distinct concerns
2. **Repository Structure:** Polyrepo approach with three main project repositories under umbrella organization
3. **Service Architecture:** Framework-based services with clear separation between upload, playback, and demo layers
4. **User Interaction Flow:** Native iOS UI components → SDK APIs → Aliyun OSS/POLYV backend services
5. **Key Decisions:**
   - Framework distribution model for reusability across client applications
   - CocoaPods integration for dependency management and distribution
   - Aliyun OSS integration for scalable video storage and delivery

### High Level Project Diagram

```mermaid
graph TB
    Demo[Demo App] --> Upload[PLVVodUploadSDK]
    Demo --> Core[PLVVodSDK Core]
    Upload --> OSS[Aliyun OSS]
    Upload --> POLYV[POLYV Platform]
    Core --> OSS
    Core --> Player[PLVIJKPlayer]
    Core --> DB[(Local Database)]
    Demo --> Cast[DLNA Casting]
    Demo --> UI[Custom UI Components]
    
    subgraph "External Services"
        OSS
        POLYV
    end
    
    subgraph "Local Storage"
        DB
        Files[Downloaded Videos]
    end
```

### Architectural and Design Patterns

- **Framework Pattern:** Modular SDK design with clear API boundaries - _Rationale:_ Enables reusability across multiple client applications and clean separation of concerns
- **Repository Pattern:** Database abstraction through PLVFDB wrapper - _Rationale:_ Provides testable data access layer and potential for future database migration
- **Facade Pattern:** PLVUploadClient and PLVVodPlayerViewController as main entry points - _Rationale:_ Simplifies complex subsystem interactions for SDK consumers
- **Observer Pattern:** Delegate protocols for video events and upload progress - _Rationale:_ Decouples SDK components from UI implementations
- **Command Pattern:** PLVUploadQueue for upload task management - _Rationale:_ Enables queue management, retry logic, and pause/resume functionality

## Tech Stack

### Cloud Infrastructure

- **Provider:** Aliyun (Alibaba Cloud)
- **Key Services:** Object Storage Service (OSS) for video storage and delivery
- **Deployment Regions:** China-focused deployment with global CDN capability

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| **Language** | Objective-C | Latest | Primary development language | Mature iOS development, C integration, established codebase |
| **Runtime** | iOS | 11.0+ | Mobile platform | Broad device compatibility while enabling modern features |
| **Framework** | Native iOS SDKs | System | Foundation, UIKit, AVFoundation | Performance, platform integration, native capabilities |
| **Video Player** | PLVIJKPlayer | Custom | Video playback engine | Customizable player with advanced codec support |
| **Storage** | Aliyun OSS | ~2.10.7 | Object storage | Scalable storage with CDN integration for China market |
| **Database** | PLVFDB (SQLite) | Custom | Local data management | Lightweight, embedded database with custom ORM wrapper |
| **Build System** | Xcode | Latest | IDE and build tools | Native iOS development environment |
| **Dependency Mgmt** | CocoaPods | Latest | Package management | Mature dependency management for iOS ecosystem |
| **Archive** | SSZipArchive | Latest | File compression | Download package management and extraction |
| **Threading** | PLVTimer | Custom | Timer management | Custom timing utilities for video functionality |
| **UI Layout** | PLVMasonry | Auto Layout | UI constraints | Programmatic auto layout with readable syntax |

## Data Models

### PLVVodVideo

**Purpose:** Core video entity representing a video asset with metadata, playback information, and download capabilities

**Key Attributes:**
- `videoId`: String - Unique identifier for the video
- `title`: String - Display title of the video  
- `duration`: NSTimeInterval - Video duration in seconds
- `fileSize`: NSNumber - Video file size in bytes
- `downloadUrl`: String - Direct download URL for the video
- `status`: PLVVodVideoStatus - Current video status (ready, downloading, error)

**Relationships:**
- Has many PLVVodDownloadInfo (for different quality versions)
- Belongs to PLVVodLocalVideo (when downloaded)

### PLVVodDownloadInfo

**Purpose:** Manages download metadata and progress tracking for video assets

**Key Attributes:**
- `videoId`: String - Reference to parent video
- `quality`: String - Video quality identifier (HD, SD, etc.)
- `downloadProgress`: Float - Download completion percentage
- `localPath`: String - File system path to downloaded content
- `downloadDate`: NSDate - When download was initiated/completed

**Relationships:**
- Belongs to PLVVodVideo
- Has one PLVVodLocalVideo when download completes

### PLVUploadVideo

**Purpose:** Represents video upload tasks with progress tracking and resume capability

**Key Attributes:**
- `localVideoPath`: String - Local file path of video to upload
- `uploadProgress`: Float - Upload completion percentage
- `resumeData`: NSData - Resume data for interrupted uploads
- `uploadState`: PLVUploadState - Current upload status
- `stsToken`: String - Security Token Service credentials

**Relationships:**
- Managed by PLVUploadQueue
- Uses PLVSTSTokenUtil for authentication

## Components

### PLVVodUploadSDK

**Responsibility:** Handles video upload functionality with resume capability and STS token management

**Key Interfaces:**
- `PLVUploadClient` - Main upload interface with delegate callbacks
- `PLVUploadQueue` - Queue management for multiple uploads
- `PLVResumeUtil` - Resume interrupted upload functionality

**Dependencies:** Aliyun OSS SDK, PLVTimer

**Technology Stack:** Objective-C, Aliyun OSS SDK 2.10.7, native file I/O

### PLVVodSDK Core

**Responsibility:** Core video playback, download management, and offline functionality

**Key Interfaces:**
- `PLVVodPlayerViewController` - Main video player interface
- `PLVVodDownloadManager` - Handles video download and offline storage
- `PLVVodSettings` - SDK configuration and global settings

**Dependencies:** PLVIJKPlayer, PLVFDB, PLVTimer, AliyunOSSiOS

**Technology Stack:** Objective-C, custom video player engine, SQLite database

### Demo Application Components

**Responsibility:** Comprehensive demonstration of SDK capabilities including UI customization and casting

**Key Interfaces:**
- Custom player skins and UI components
- `PLVCastManager` - DLNA casting functionality
- Integration examples for both upload and playback SDKs

**Dependencies:** Both upload and core SDKs, PLVDLNASender, UI frameworks

**Technology Stack:** Objective-C, TZImagePickerController, YYWebImage, XRCarouselView

### Component Diagrams

```mermaid
graph TB
    subgraph "PLVVodUploadSDK"
        UC[PLVUploadClient]
        UQ[PLVUploadQueue]
        RU[PLVResumeUtil]
        ST[PLVSTSTokenUtil]
    end
    
    subgraph "PLVVodSDK Core"
        PVC[PLVVodPlayerViewController]
        DM[PLVVodDownloadManager]
        VS[PLVVodSettings]
        DB[PLVFDB Database]
    end
    
    subgraph "Demo Application"
        UI[Custom UI Components]
        CM[PLVCastManager]
        SK[Custom Skins]
    end
    
    UC --> UQ
    UQ --> RU
    UC --> ST
    PVC --> DM
    DM --> DB
    UI --> PVC
    UI --> UC
    CM --> PVC
```

## External APIs

### Aliyun OSS API

- **Purpose:** Video storage, upload, and content delivery
- **Documentation:** https://help.aliyun.com/product/31815.html
- **Base URL(s):** Region-specific OSS endpoints (configurable)
- **Authentication:** STS (Security Token Service) with temporary credentials
- **Rate Limits:** Based on OSS service plan and region

**Key Endpoints Used:**
- `PUT /{object}` - Upload video content to OSS bucket
- `GET /{object}` - Download video content for offline viewing
- `POST /?uploads` - Initiate multipart upload for large videos

**Integration Notes:** Uses PLVSTSTokenUtil for token refresh, supports resume for interrupted uploads, integrates with CDN for optimized delivery

### POLYV Platform API

- **Purpose:** Video metadata management, authentication, and platform integration
- **Documentation:** POLYV developer documentation (internal)
- **Base URL(s):** POLYV platform endpoints (production/staging)
- **Authentication:** API key and signature-based authentication
- **Rate Limits:** Platform-specific limits based on account tier

**Key Endpoints Used:**
- `GET /video/info` - Retrieve video metadata and playback information
- `POST /upload/auth` - Obtain upload authorization and STS tokens
- `GET /video/list` - List user's video content with pagination

**Integration Notes:** Coordinates with OSS for complete video lifecycle management

## Core Workflows

### Video Upload Workflow

```mermaid
sequenceDiagram
    participant App as Demo App
    participant UC as PLVUploadClient
    participant UQ as PLVUploadQueue
    participant STS as PLVSTSTokenUtil
    participant OSS as Aliyun OSS
    participant PLV as POLYV Platform
    
    App->>UC: uploadVideo(localPath)
    UC->>STS: requestSTSToken()
    STS->>PLV: authenticate()
    PLV-->>STS: return credentials
    STS-->>UC: return stsToken
    UC->>UQ: enqueueUpload(video, token)
    UQ->>OSS: initiateUpload()
    
    loop Upload Progress
        OSS-->>UQ: progress update
        UQ-->>UC: progress callback
        UC-->>App: delegate callback
    end
    
    OSS-->>UQ: upload complete
    UQ->>PLV: notifyUploadComplete()
    PLV-->>UQ: confirm registration
    UQ-->>UC: upload success
    UC-->>App: completion callback
```

### Video Download and Playback Workflow

```mermaid
sequenceDiagram
    participant App as Demo App
    participant PVC as PLVVodPlayerViewController
    participant DM as PLVVodDownloadManager
    participant DB as PLVFDB Database
    participant OSS as Aliyun OSS
    participant Player as PLVIJKPlayer
    
    App->>PVC: loadVideo(videoId)
    PVC->>DB: checkLocalVideo(videoId)
    
    alt Video Downloaded
        DB-->>PVC: return localPath
        PVC->>Player: playLocalVideo(localPath)
    else Video Not Downloaded
        PVC->>DM: downloadVideo(videoId)
        DM->>OSS: downloadRequest()
        
        loop Download Progress
            OSS-->>DM: progress update
            DM-->>PVC: progress callback
            PVC-->>App: download progress
        end
        
        OSS-->>DM: download complete
        DM->>DB: saveLocalVideo()
        DM-->>PVC: download success
        PVC->>Player: playLocalVideo(localPath)
    end
    
    Player-->>PVC: playback events
    PVC-->>App: player delegate callbacks
```

## REST API Spec

The project primarily consumes external REST APIs rather than exposing them. The SDKs provide native iOS interfaces that abstract the underlying REST communication with POLYV platform and Aliyun OSS services.

## Database Schema

### Local Video Database (SQLite via PLVFDB)

```sql
-- Video metadata table
CREATE TABLE PLVVodVideo (
    videoId TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    duration REAL,
    fileSize INTEGER,
    downloadUrl TEXT,
    status INTEGER,
    createdDate REAL,
    updatedDate REAL
);

-- Download information table
CREATE TABLE PLVVodDownloadInfo (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    videoId TEXT,
    quality TEXT,
    downloadProgress REAL DEFAULT 0.0,
    localPath TEXT,
    downloadDate REAL,
    fileSize INTEGER,
    FOREIGN KEY (videoId) REFERENCES PLVVodVideo(videoId)
);

-- Local video files table
CREATE TABLE PLVVodLocalVideo (
    videoId TEXT PRIMARY KEY,
    localPath TEXT NOT NULL,
    quality TEXT,
    downloadedDate REAL,
    lastAccessDate REAL,
    FOREIGN KEY (videoId) REFERENCES PLVVodVideo(videoId)
);

-- Upload queue table
CREATE TABLE PLVUploadQueue (
    uploadId TEXT PRIMARY KEY,
    localVideoPath TEXT NOT NULL,
    uploadProgress REAL DEFAULT 0.0,
    uploadState INTEGER,
    resumeData BLOB,
    createdDate REAL,
    updatedDate REAL
);

-- Create indexes for performance
CREATE INDEX idx_video_status ON PLVVodVideo(status);
CREATE INDEX idx_download_videoid ON PLVVodDownloadInfo(videoId);
CREATE INDEX idx_upload_state ON PLVUploadQueue(uploadState);
```

## Source Tree

```
PLVVodSDK-Ecosystem/
├── PLVVodUploadSDK/                    # Upload SDK project
│   ├── PLVVodUploadSDK.xcworkspace     # Xcode workspace
│   ├── PLVVodUploadSDK.xcodeproj       # Xcode project
│   ├── PLVVodUploadSDK/                # SDK source code
│   │   ├── PLVUploadClient.h/m         # Main upload interface
│   │   ├── PLVUploadQueue.h/m          # Upload queue management
│   │   ├── PLVResumeUtil.h/m           # Resume functionality
│   │   ├── PLVSTSTokenUtil.h/m         # STS token management
│   │   └── PLVUploadVideo.h/m          # Upload video model
│   ├── Podfile                         # CocoaPods dependencies
│   └── PLVVodUploadSDK.podspec         # Pod specification
│
├── polyv-ios-vod-sdk/                  # Core VOD SDK project
│   ├── PolyvVodSDK.xcworkspace         # Xcode workspace
│   ├── PolyvVodSDK/                    # SDK source code
│   │   ├── Classes/                    # Core classes
│   │   │   ├── PLVVodSettings.h/m      # SDK settings
│   │   │   ├── PLVVodPlayerViewController.h/m
│   │   │   ├── PLVVodDownloadManager.h/m
│   │   │   └── Models/                 # Data models
│   │   └── Resources/                  # SDK resources
│   ├── BuildTool/                      # Framework build scripts
│   │   └── build_framework.sh          # Framework build script
│   ├── Library/                        # Embedded libraries
│   │   ├── Danmu/                      # Danmu (bullet comments)
│   │   ├── FDStackView/                # Stack view implementation
│   │   └── Timer/                      # Timer utilities
│   ├── Podfile                         # CocoaPods dependencies
│   └── podsepcs/                       # Multiple podspec variants
│       ├── Dylib/                      # Dynamic library variant
│       ├── Custom/                     # Customer-specific variants
│       └── ABN/                        # Additional build variants
│
├── polyv-ios-vod-sdk-demo/            # Demo application
│   ├── VodSDKWK.xcworkspace            # Combined workspace
│   ├── PolyvVodSDKDemo/                # Demo app source
│   │   ├── AppDelegate.h/m             # Application delegate
│   │   ├── ViewControllers/            # Demo view controllers
│   │   ├── UI Components/              # Custom UI components
│   │   └── Resources/                  # App resources
│   ├── Frameworks/                     # Embedded frameworks
│   │   └── PLVVodSDK.framework         # Prebuilt SDK framework
│   └── Podfile                         # Demo dependencies
│
├── docs/                               # Documentation
│   └── architecture.md                # This document
│
└── CLAUDE.md                           # Development guidelines
```

## Infrastructure and Deployment

### Infrastructure as Code

- **Tool:** Xcode Build System with shell scripts
- **Location:** `BuildTool/` directories in each project
- **Approach:** Framework build automation with version management

### Deployment Strategy

- **Strategy:** Framework distribution via CocoaPods and direct integration
- **CI/CD Platform:** Manual build process with shell script automation
- **Pipeline Configuration:** `build_framework.sh` scripts in respective projects

### Environments

- **Development:** Local Xcode projects with simulator testing
- **Testing:** Device testing with staging POLYV platform endpoints
- **Production:** App Store distribution with production POLYV platform

### Environment Promotion Flow

```
Developer Workspace → Framework Build → Pod Distribution → Client Integration → App Store
```

### Rollback Strategy

- **Primary Method:** Version pinning in podspec and semantic versioning
- **Trigger Conditions:** API breaking changes, critical bugs, performance regression
- **Recovery Time Objective:** Immediate rollback via pod version downgrade

## Error Handling Strategy

### General Approach

- **Error Model:** NSError-based error handling with custom error domains
- **Exception Hierarchy:** PLVVodError domain with specific error codes
- **Error Propagation:** Delegate callbacks and completion blocks with error parameters

### Logging Standards

- **Library:** Native NSLog with custom PLVLog wrapper
- **Format:** Structured logging with component prefixes
- **Levels:** Error, Warning, Info, Debug with compile-time configuration
- **Required Context:**
  - Correlation ID: UUID-based request tracking
  - Service Context: SDK component and version information
  - User Context: Anonymized session and video identifiers

### Error Handling Patterns

#### External API Errors

- **Retry Policy:** Exponential backoff with maximum retry limits
- **Circuit Breaker:** Temporary disable on consecutive failures
- **Timeout Configuration:** 30s for uploads, 10s for metadata requests
- **Error Translation:** Map HTTP errors to user-friendly messages

#### Business Logic Errors

- **Custom Exceptions:** PLVVodErrorDomain with specific error codes
- **User-Facing Errors:** Localized error messages for common scenarios
- **Error Codes:** Numeric codes for programmatic error handling

#### Data Consistency

- **Transaction Strategy:** PLVFDB transaction wrapper for atomic operations
- **Compensation Logic:** Cleanup incomplete downloads and uploads
- **Idempotency:** Unique identifiers prevent duplicate operations

## Coding Standards

### Core Standards

- **Languages & Runtimes:** Objective-C (latest), iOS 11.0+ deployment target
- **Style & Linting:** Follow Apple's Objective-C conventions, use Xcode static analyzer
- **Test Organization:** XCTest framework, test files named *Tests.m

### Naming Conventions

| Element | Convention | Example |
|---------|------------|---------|
| Classes | PLV prefix + PascalCase | PLVVodPlayerViewController |
| Methods | camelCase with descriptive names | uploadVideoWithPath:completion: |
| Properties | camelCase | downloadProgress |
| Constants | k prefix + PascalCase | kPLVVodDefaultTimeout |

### Critical Rules

- **Logging:** Use PLVLog macros instead of NSLog for production code
- **Memory Management:** Follow ARC guidelines, use weak references for delegates
- **Thread Safety:** Use dispatch queues for background operations, update UI on main thread
- **Error Handling:** Always check for errors in completion blocks and delegate methods
- **API Design:** Use delegate protocols for events, completion blocks for async operations

## Test Strategy and Standards

### Testing Philosophy

- **Approach:** Test after development with focus on integration points
- **Coverage Goals:** 70% minimum for core SDK components
- **Test Pyramid:** Unit tests for business logic, integration tests for external APIs

### Test Types and Organization

#### Unit Tests

- **Framework:** XCTest
- **File Convention:** ClassNameTests.m in Tests/ directory
- **Location:** Each project's test target
- **Mocking Library:** OCMock for external dependencies
- **Coverage Requirement:** 80% for core business logic

**AI Agent Requirements:**
- Generate tests for all public methods
- Cover edge cases and error conditions
- Follow AAA pattern (Arrange, Act, Assert)
- Mock all external dependencies

#### Integration Tests

- **Scope:** SDK integration with external services
- **Location:** Separate integration test targets
- **Test Infrastructure:**
  - **Aliyun OSS:** Test bucket with cleanup procedures
  - **POLYV Platform:** Staging environment with test credentials

#### End-to-End Tests

- **Framework:** XCUITest for demo application
- **Scope:** Complete user workflows from upload to playback
- **Environment:** Staging platform with test video content
- **Test Data:** Predefined test videos and user accounts

### Test Data Management

- **Strategy:** Test fixtures with sample video files and metadata
- **Fixtures:** Resources/TestData/ in each test target
- **Factories:** PLVTestVideoFactory for creating test video objects
- **Cleanup:** Automated cleanup after test completion

### Continuous Testing

- **CI Integration:** Manual test execution with build scripts
- **Performance Tests:** XCTest performance testing for critical paths
- **Security Tests:** Static analysis via Xcode security recommendations

## Security

### Input Validation

- **Validation Library:** Native Foundation NSString/NSData validation
- **Validation Location:** SDK public interface methods before processing
- **Required Rules:**
  - All file paths must be validated for sandbox compliance
  - Video URLs must use HTTPS protocol
  - User input sanitized before logging

### Authentication & Authorization

- **Auth Method:** STS token-based authentication with Aliyun OSS
- **Session Management:** Automatic token refresh via PLVSTSTokenUtil
- **Required Patterns:**
  - Never store permanent credentials in SDK
  - Use temporary STS tokens with limited scope
  - Validate token expiration before API calls

### Secrets Management

- **Development:** No hardcoded secrets, use configuration plist files
- **Production:** Client applications provide API keys via SDK initialization
- **Code Requirements:**
  - NEVER hardcode API keys or secrets
  - Access credentials via PLVVodSettings configuration only
  - No sensitive data in logs or error messages

### API Security

- **Rate Limiting:** Respect OSS and POLYV platform rate limits
- **CORS Policy:** N/A for mobile SDK
- **Security Headers:** HTTPS enforcement for all network requests
- **HTTPS Enforcement:** All network requests must use TLS 1.2+

### Data Protection

- **Encryption at Rest:** iOS keychain for sensitive configuration
- **Encryption in Transit:** HTTPS/TLS for all network communication
- **PII Handling:** No personal information stored in SDK logs
- **Logging Restrictions:** Never log credentials, tokens, or user data

### Dependency Security

- **Scanning Tool:** Manual review of CocoaPods dependencies
- **Update Policy:** Quarterly review and update of third-party dependencies
- **Approval Process:** Technical review required for new dependencies

### Security Testing

- **SAST Tool:** Xcode static analyzer for code security issues
- **DAST Tool:** Manual security review of network communications
- **Penetration Testing:** Annual security audit by third-party provider

## Next Steps

After completing the architecture:

1. **For UI-heavy components:**
   - Review demo application architecture for UI patterns
   - Ensure consistency across custom skins and player interfaces
   - Consider iOS Human Interface Guidelines compliance

2. **For all projects:**
   - Review with Product Owner and development team
   - Begin implementation using existing SDK patterns
   - Set up build automation and distribution workflows

3. **Development priorities:**
   - Maintain backward compatibility with existing SDK versions
   - Follow established Objective-C patterns and conventions
   - Ensure proper integration testing with external services

The architecture documents an existing, mature codebase. Future development should prioritize maintaining stability while enabling new features within the established framework patterns.