source 'https://github.com/CocoaPods/Specs.git'
source 'https://github.com/aliyun/aliyun-specs.git'

workspace 'PolyvVodSDK.xcworkspace'
platform :ios, '11.0'
#use_frameworks!
inhibit_all_warnings!

pod 'PLVMarquee', '~> 0.0.2'
pod 'PLVTimer', '~> 0.0.2'
pod 'PLVVodDanmu', '~> 0.0.1'
pod 'PLVNetworkDiagnosisTool', '~> 0.0.1'
pod 'AlicloudHTTPDNS', '~> 1.6.9'
pod 'PolyvIJKPlayer_Dylib', '~> 0.5.0'
pod 'SSZipArchive', '~> 2.1.1'
pod 'PLVFDB', '1.0.3'
pod 'FDStackView', '~> 1.0.1'
pod 'Masonry'

target '_PolyvVodSDK' do
	project 'PolyvVodSDK/PolyvVodSDK.xcodeproj'

	# target 'PolyvVodSDKTests' do
	# 	inherit! :search_paths
	# 	# Pods for testing
	# end
end

target 'PLVVodSDK' do
    project 'PolyvVodSDK/PolyvVodSDK.xcodeproj'
    
    # target 'PolyvVodSDKTests' do
    #     inherit! :search_paths
    #     # Pods for testing
    # end
end

target 'PLVVodSDKFrameworkTest' do
	project 'PLVVodSDKFrameworkTest/PLVVodSDKFrameworkTest.xcodeproj'
end
