// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXBuildFile section */
		0317F55E1FD697E200AE6BD8 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 0317F55D1FD697E200AE6BD8 /* AppDelegate.m */; };
		0317F5611FD697E200AE6BD8 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0317F5601FD697E200AE6BD8 /* ViewController.m */; };
		0317F5641FD697E200AE6BD8 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 0317F5621FD697E200AE6BD8 /* Main.storyboard */; };
		0317F5661FD697E200AE6BD8 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 0317F5651FD697E200AE6BD8 /* Assets.xcassets */; };
		0317F56C1FD697E200AE6BD8 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 0317F56B1FD697E200AE6BD8 /* main.m */; };
		0317F5771FD699DC00AE6BD8 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 0317F5761FD699DC00AE6BD8 /* libresolv.tbd */; };
		0317F5791FD699E600AE6BD8 /* libstdc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 0317F5781FD699E600AE6BD8 /* libstdc++.tbd */; };
		0317F57B1FD699F400AE6BD8 /* libbz2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 0317F57A1FD699F300AE6BD8 /* libbz2.tbd */; };
		0317F57D1FD699FC00AE6BD8 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 0317F57C1FD699FC00AE6BD8 /* libz.tbd */; };
		0346BFCD1FD6AD3B00752E11 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 0346BFCC1FD6AD3B00752E11 /* LaunchScreen.storyboard */; };
		03E07FC51FDE91EA00C62D3D /* IJKMediaFramework.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 03E07FC41FDE91EA00C62D3D /* IJKMediaFramework.framework */; };
		03EEEC5B1FDF6F1000CCB7DB /* PLVVodSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 03EEEC5A1FDF6F1000CCB7DB /* PLVVodSDK.framework */; };
		FBE53C09B0EBEF7A250B3623 /* libPods-PLVVodSDKFrameworkTest.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 23923B25A9032603EC10D3EC /* libPods-PLVVodSDKFrameworkTest.a */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0317F5591FD697E200AE6BD8 /* PLVVodSDKFrameworkTest.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PLVVodSDKFrameworkTest.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0317F55C1FD697E200AE6BD8 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		0317F55D1FD697E200AE6BD8 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		0317F55F1FD697E200AE6BD8 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		0317F5601FD697E200AE6BD8 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		0317F5631FD697E200AE6BD8 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		0317F5651FD697E200AE6BD8 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		0317F56A1FD697E200AE6BD8 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		0317F56B1FD697E200AE6BD8 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		0317F5761FD699DC00AE6BD8 /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		0317F5781FD699E600AE6BD8 /* libstdc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libstdc++.tbd"; path = "usr/lib/libstdc++.tbd"; sourceTree = SDKROOT; };
		0317F57A1FD699F300AE6BD8 /* libbz2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libbz2.tbd; path = usr/lib/libbz2.tbd; sourceTree = SDKROOT; };
		0317F57C1FD699FC00AE6BD8 /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		0346BFCC1FD6AD3B00752E11 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		03E07FC41FDE91EA00C62D3D /* IJKMediaFramework.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IJKMediaFramework.framework; path = ../../PolyvVodSDK/Library/IJKMediaFramework.framework; sourceTree = "<group>"; };
		03EEEC5A1FDF6F1000CCB7DB /* PLVVodSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = PLVVodSDK.framework; path = ../../PolyvVodSDK/Products/PLVVodSDK.framework; sourceTree = "<group>"; };
		23923B25A9032603EC10D3EC /* libPods-PLVVodSDKFrameworkTest.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-PLVVodSDKFrameworkTest.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		3BD2BFE6478BD64ED34D994A /* Pods-PLVVodSDKFrameworkTest.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PLVVodSDKFrameworkTest.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-PLVVodSDKFrameworkTest/Pods-PLVVodSDKFrameworkTest.debug.xcconfig"; sourceTree = "<group>"; };
		93BC84C6B6064B8BA8C3D944 /* Pods-PLVVodSDKFrameworkTest.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PLVVodSDKFrameworkTest.release.xcconfig"; path = "../Pods/Target Support Files/Pods-PLVVodSDKFrameworkTest/Pods-PLVVodSDKFrameworkTest.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0317F5561FD697E200AE6BD8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0317F57D1FD699FC00AE6BD8 /* libz.tbd in Frameworks */,
				0317F5791FD699E600AE6BD8 /* libstdc++.tbd in Frameworks */,
				0317F57B1FD699F400AE6BD8 /* libbz2.tbd in Frameworks */,
				FBE53C09B0EBEF7A250B3623 /* libPods-PLVVodSDKFrameworkTest.a in Frameworks */,
				03EEEC5B1FDF6F1000CCB7DB /* PLVVodSDK.framework in Frameworks */,
				0317F5771FD699DC00AE6BD8 /* libresolv.tbd in Frameworks */,
				03E07FC51FDE91EA00C62D3D /* IJKMediaFramework.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0317F5501FD697E200AE6BD8 = {
			isa = PBXGroup;
			children = (
				0317F55B1FD697E200AE6BD8 /* PLVVodSDKFrameworkTest */,
				0317F55A1FD697E200AE6BD8 /* Products */,
				C40B5C7517617AADB4CAAFE3 /* Pods */,
				7831264FC42112A1AC60D8B1 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		0317F55A1FD697E200AE6BD8 /* Products */ = {
			isa = PBXGroup;
			children = (
				0317F5591FD697E200AE6BD8 /* PLVVodSDKFrameworkTest.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0317F55B1FD697E200AE6BD8 /* PLVVodSDKFrameworkTest */ = {
			isa = PBXGroup;
			children = (
				03EEEC5A1FDF6F1000CCB7DB /* PLVVodSDK.framework */,
				03E07FC41FDE91EA00C62D3D /* IJKMediaFramework.framework */,
				0317F55C1FD697E200AE6BD8 /* AppDelegate.h */,
				0317F55D1FD697E200AE6BD8 /* AppDelegate.m */,
				0317F55F1FD697E200AE6BD8 /* ViewController.h */,
				0317F5601FD697E200AE6BD8 /* ViewController.m */,
				0317F5621FD697E200AE6BD8 /* Main.storyboard */,
				0346BFCC1FD6AD3B00752E11 /* LaunchScreen.storyboard */,
				0317F5651FD697E200AE6BD8 /* Assets.xcassets */,
				0317F56A1FD697E200AE6BD8 /* Info.plist */,
				0317F56B1FD697E200AE6BD8 /* main.m */,
			);
			path = PLVVodSDKFrameworkTest;
			sourceTree = "<group>";
		};
		7831264FC42112A1AC60D8B1 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				0317F57C1FD699FC00AE6BD8 /* libz.tbd */,
				0317F57A1FD699F300AE6BD8 /* libbz2.tbd */,
				0317F5781FD699E600AE6BD8 /* libstdc++.tbd */,
				0317F5761FD699DC00AE6BD8 /* libresolv.tbd */,
				23923B25A9032603EC10D3EC /* libPods-PLVVodSDKFrameworkTest.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		C40B5C7517617AADB4CAAFE3 /* Pods */ = {
			isa = PBXGroup;
			children = (
				3BD2BFE6478BD64ED34D994A /* Pods-PLVVodSDKFrameworkTest.debug.xcconfig */,
				93BC84C6B6064B8BA8C3D944 /* Pods-PLVVodSDKFrameworkTest.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		0317F5581FD697E200AE6BD8 /* PLVVodSDKFrameworkTest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0317F56F1FD697E200AE6BD8 /* Build configuration list for PBXNativeTarget "PLVVodSDKFrameworkTest" */;
			buildPhases = (
				7B50438D13CAC91C52940E89 /* [CP] Check Pods Manifest.lock */,
				0317F5551FD697E200AE6BD8 /* Sources */,
				0317F5561FD697E200AE6BD8 /* Frameworks */,
				0317F5571FD697E200AE6BD8 /* Resources */,
				3E130A4A0D9686D21171DD0F /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PLVVodSDKFrameworkTest;
			productName = PLVVodSDKFrameworkTest;
			productReference = 0317F5591FD697E200AE6BD8 /* PLVVodSDKFrameworkTest.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0317F5511FD697E200AE6BD8 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0910;
				ORGANIZATIONNAME = POLYV;
				TargetAttributes = {
					0317F5581FD697E200AE6BD8 = {
						CreatedOnToolsVersion = 9.1;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 0317F5541FD697E200AE6BD8 /* Build configuration list for PBXProject "PLVVodSDKFrameworkTest" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 0317F5501FD697E200AE6BD8;
			productRefGroup = 0317F55A1FD697E200AE6BD8 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0317F5581FD697E200AE6BD8 /* PLVVodSDKFrameworkTest */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0317F5571FD697E200AE6BD8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0346BFCD1FD6AD3B00752E11 /* LaunchScreen.storyboard in Resources */,
				0317F5661FD697E200AE6BD8 /* Assets.xcassets in Resources */,
				0317F5641FD697E200AE6BD8 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		3E130A4A0D9686D21171DD0F /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-PLVVodSDKFrameworkTest/Pods-PLVVodSDKFrameworkTest-frameworks.sh",
				"${PODS_ROOT}/PolyvIJKPlayer_Dylib/PolyvIJKMediaFramework.framework",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/PolyvIJKMediaFramework.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-PLVVodSDKFrameworkTest/Pods-PLVVodSDKFrameworkTest-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		7B50438D13CAC91C52940E89 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-PLVVodSDKFrameworkTest-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0317F5551FD697E200AE6BD8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0317F5611FD697E200AE6BD8 /* ViewController.m in Sources */,
				0317F56C1FD697E200AE6BD8 /* main.m in Sources */,
				0317F55E1FD697E200AE6BD8 /* AppDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		0317F5621FD697E200AE6BD8 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				0317F5631FD697E200AE6BD8 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		0317F56D1FD697E200AE6BD8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.1;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		0317F56E1FD697E200AE6BD8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.1;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		0317F5701FD697E200AE6BD8 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3BD2BFE6478BD64ED34D994A /* Pods-PLVVodSDKFrameworkTest.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/AlicloudBeacon/beacon\"",
					"\"${PODS_ROOT}/AlicloudHTTPDNS/httpdns\"",
					"\"${PODS_ROOT}/AlicloudUT/ut\"",
					"\"${PODS_ROOT}/AlicloudUTDID/utdid\"",
					"\"${PODS_ROOT}/AlicloudUtils/utils\"",
					"\"${SRCROOT}/../PolyvVodSDK/Library\"",
					"\"${SRCROOT}/../PolyvVodSDK/Products\"",
				);
				INFOPLIST_FILE = PLVVodSDKFrameworkTest/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = net.polyv.PLVVodSDKFrameworkTest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		0317F5711FD697E200AE6BD8 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 93BC84C6B6064B8BA8C3D944 /* Pods-PLVVodSDKFrameworkTest.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/AlicloudBeacon/beacon\"",
					"\"${PODS_ROOT}/AlicloudHTTPDNS/httpdns\"",
					"\"${PODS_ROOT}/AlicloudUT/ut\"",
					"\"${PODS_ROOT}/AlicloudUTDID/utdid\"",
					"\"${PODS_ROOT}/AlicloudUtils/utils\"",
					"\"${SRCROOT}/../PolyvVodSDK/Library\"",
					"\"${SRCROOT}/../PolyvVodSDK/Products\"",
				);
				INFOPLIST_FILE = PLVVodSDKFrameworkTest/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = net.polyv.PLVVodSDKFrameworkTest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0317F5541FD697E200AE6BD8 /* Build configuration list for PBXProject "PLVVodSDKFrameworkTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0317F56D1FD697E200AE6BD8 /* Debug */,
				0317F56E1FD697E200AE6BD8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0317F56F1FD697E200AE6BD8 /* Build configuration list for PBXNativeTarget "PLVVodSDKFrameworkTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0317F5701FD697E200AE6BD8 /* Debug */,
				0317F5711FD697E200AE6BD8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0317F5511FD697E200AE6BD8 /* Project object */;
}
