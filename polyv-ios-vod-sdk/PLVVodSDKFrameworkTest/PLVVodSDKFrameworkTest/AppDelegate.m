//
//  AppDelegate.m
//  PLVVodSDKFrameworkTest
//
//  Created by <PERSON><PERSON> <PERSON> on 2017/12/5.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "AppDelegate.h"
#import <IJKMediaFramework/IJKMediaFramework.h>
#import <PLVVodSDK/PLVVodSDK.h>
//#import <PolyvVodSDK/PLVVodSDK.h>

@interface AppDelegate ()

@end

@implementation AppDelegate


- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
	// Override point for customization after application launch.
	
	NSString *sdkKey = @"v4yoqNIHwZ69WNbOTI4rzDRHbwjUYsh14V1Czv7CNhwRE3EGBEleaezLNZms14CKhxu+KB+OPH341zknQ5+7gE5UZnz4u5V0jP+SCO9kaRwthY4UyvZ3ClHgnSBEZoTCkwrYQ+sgLVIRhjo2y+uZIQ==";
	PLVVodSettings *settings = [PLVVodSettings settingsWithConfigString:sdkKey key:@"VXtlHmwfS2oYm0CZ" iv:@"2u9gDPKdX6GyQJKU"];
	NSLog(@"settings: %@", settings);
	
	return YES;
}


- (void)applicationWillResignActive:(UIApplication *)application {
	// Sent when the application is about to move from active to inactive state. This can occur for certain types of temporary interruptions (such as an incoming phone call or SMS message) or when the user quits the application and it begins the transition to the background state.
	// Use this method to pause ongoing tasks, disable timers, and invalidate graphics rendering callbacks. Games should use this method to pause the game.
}


- (void)applicationDidEnterBackground:(UIApplication *)application {
	// Use this method to release shared resources, save user data, invalidate timers, and store enough application state information to restore your application to its current state in case it is terminated later.
	// If your application supports background execution, this method is called instead of applicationWillTerminate: when the user quits.
}


- (void)applicationWillEnterForeground:(UIApplication *)application {
	// Called as part of the transition from the background to the active state; here you can undo many of the changes made on entering the background.
}


- (void)applicationDidBecomeActive:(UIApplication *)application {
	// Restart any tasks that were paused (or not yet started) while the application was inactive. If the application was previously in the background, optionally refresh the user interface.
}


- (void)applicationWillTerminate:(UIApplication *)application {
	// Called when the application is about to terminate. Save data if appropriate. See also applicationDidEnterBackground:.
}


@end
