# POLV VOD SDK

## 项目信息

本项目用于SDK静态库的构建，以及进行一些基本的测试。详细使用SDK的部分放在Demo项目，与本项目分离。

### 打包信息

net.polyv.PLVVodSDK
PLVVodSDK.framework

需要链接的库

/usr/lib

- libresolv
- libstdc++
- libbz2
- libz

使用 CocoaPods 链接的库：

source 'https://github.com/CocoaPods/Specs.git'
source 'https://github.com/aliyun/aliyun-specs.git'

- PLVTimer
- SSZipArchive
- AlicloudHTTPDNS

## 头文件

常量：
- PLVVodConstans.h

模型：
- PLVVodVideo.h
- PLVVodLocalVideo.h
- PLVVodDownloadInfo.h

配置器：
- PLVVodSettings.h

播放器：
- PLVVodPlayerViewController.h

广告：
- PLVVodAd.h
- PLVVodAdPlayerViewController.h

下载器管理器：
- PLVVodDownloaderManager.h

皮肤协议：
- PLVVodPlayerSkinProtocol.h

```objective-c
#import <PLVVodSDK/PLVVodConstans.h>
#import <PLVVodSDK/PLVVodVideo.h>
#import <PLVVodSDK/PLVVodLocalVideo.h>
#import <PLVVodSDK/PLVVodDownloadInfo.h>
#import <PLVVodSDK/PLVVodSettings.h>
#import <PLVVodSDK/PLVVodPlayerViewController.h>
#import <PLVVodSDK/PLVVodDownloaderManager.h>
#import <PLVVodSDK/PLVVodPlayerSkinProtocol.h>
```

## 签名信息

### PLVVodSDK

Bundle Identifier:
net.polyv.PLVVodSDK

Team:
Company

### net.polyv.vod.sdk.demo

Display Name:
SDK Demo

Bundle Identifier:
net.polyv.vod.sdk.demo

Team:
None

#### 内测

Display Name:
SDK Demo Intest

Bundle Identifier:
net.polyv.vod.sdk.demo.intest

Team:
Enterprise
