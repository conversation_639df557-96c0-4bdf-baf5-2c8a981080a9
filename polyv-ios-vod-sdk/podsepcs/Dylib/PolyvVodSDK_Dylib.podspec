Pod::Spec.new do |s|

s.name         = "PolyvVodSDK_Dylib"
s.version      = "2.6.5"
s.summary      = "保利威视点播 iOS SDK (动态库版本)"
s.description  = <<-DESC
    PolyvVodSDK_Dylib
    保利威视点播 iOS SDK。
    集成点播业务播放、下载功能。
DESC
s.homepage = "https://github.com/polyv/polyv-ios-vod-sdk"
s.screenshots = [
    "http://repo.polyv.net/ios/documents/vodsdk/screenshots/Simulator_Screen_Shot_-_iPhone_SE_-_2018-03-19_at_10.51.40.png",
    "http://repo.polyv.net/ios/documents/vodsdk/screenshots/Simulator_Screen_Shot_-_iPhone_SE_-_2018-03-19_at_10.52.02.png",
    "http://repo.polyv.net/ios/documents/vodsdk/screenshots/Simulator_Screen_Shot_-_iPhone_SE_-_2018-03-19_at_10.52.34.png",
    "http://repo.polyv.net/ios/documents/vodsdk/screenshots/Simulator_Screen_Shot_-_iPhone_SE_-_2018-03-19_at_10.54.12.png",
    "http://repo.polyv.net/ios/documents/vodsdk/screenshots/Simulator_Screen_Shot_-_iPhone_SE_-_2018-03-19_at_10.54.42.png",
    "http://repo.polyv.net/ios/documents/vodsdk/screenshots/Simulator_Screen_Shot_-_iPhone_SE_-_2018-03-19_at_10.54.48.png",
    "http://repo.polyv.net/ios/documents/vodsdk/screenshots/Simulator_Screen_Shot_-_iPhone_SE_-_2018-03-19_at_10.56.50.png",
    "http://repo.polyv.net/ios/documents/vodsdk/screenshots/Simulator_Screen_Shot_-_iPhone_SE_-_2018-03-19_at_10.57.11.png",
    "http://repo.polyv.net/ios/documents/vodsdk/screenshots/Simulator_Screen_Shot_-_iPhone_SE_-_2018-03-19_at_10.57.44.png",
    "http://repo.polyv.net/ios/documents/vodsdk/screenshots/Simulator_Screen_Shot_-_iPhone_SE_-_2018-03-19_at_10.57.57.png",
    "http://repo.polyv.net/ios/documents/vodsdk/screenshots/Simulator_Screen_Shot_-_iPhone_SE_-_2018-03-19_at_10.59.14.png",
]

s.license      = { :type => "MIT", :text => <<-LICENSE
MIT License

Copyright (c) 2017

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
LICENSE
}

s.author = {
"ftao" => "<EMAIL>",
"bqlin" => "<EMAIL>"
}
s.source = {
#:http => "http://repo.polyv.net/ios/download/vodsdk/PLVVodSDK_dylib_2.6.4+190827.zip"
:http => "http://repo.polyv.net/ios/download/vodsdk/PLVVodSDK_dylib_2.6.5+191028.zip"

}
s.vendored_frameworks = "PLVVodSDK.framework"

s.requires_arc = true
s.platform = :ios, "8.0"
s.dependency "PolyvIJKPlayer_Dylib", '~> 0.4.0'
s.dependency "PolyvAliHttpDNS", '~> 1.6.7'
s.dependency "PLVMarquee", '~> 0.0.2'
s.dependency "PLVTimer", '~> 0.0.2'
s.dependency "PLVNetworkDiagnosisTool", '~> 0.0.1'
s.dependency "SSZipArchive", '~> 2.1.1'
s.dependency "WCDB", '~> 1.0.6'

# pod for demo
# "PLVVodDanmu"

end

