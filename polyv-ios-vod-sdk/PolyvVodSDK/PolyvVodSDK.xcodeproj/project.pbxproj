// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXAggregateTarget section */
		0314ED701FE7ACC800BC0A54 /* BuildFramework */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 0314ED711FE7ACC800BC0A54 /* Build configuration list for PBXAggregateTarget "BuildFramework" */;
			buildPhases = (
				0314ED741FE7ACD000BC0A54 /* ShellScript */,
			);
			dependencies = (
			);
			name = BuildFramework;
			productName = BuildFramework;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		0012317B22E8596800CE6238 /* PLVVodPPT.h in Headers */ = {isa = PBXBuildFile; fileRef = 0012317922E8596800CE6238 /* PLVVodPPT.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0012317C22E8596800CE6238 /* PLVVodPPT.m in Sources */ = {isa = PBXBuildFile; fileRef = 0012317A22E8596800CE6238 /* PLVVodPPT.m */; };
		0012317D22E8596E00CE6238 /* PLVVodPPT.m in Sources */ = {isa = PBXBuildFile; fileRef = 0012317A22E8596800CE6238 /* PLVVodPPT.m */; };
		0042B3292444778300F6C1E7 /* PLVVodPlayerLogo.h in Headers */ = {isa = PBXBuildFile; fileRef = 0042B3252444615500F6C1E7 /* PLVVodPlayerLogo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0042B32B24447B9000F6C1E7 /* PLVVodPlayerLogo.m in Sources */ = {isa = PBXBuildFile; fileRef = 0042B3262444615500F6C1E7 /* PLVVodPlayerLogo.m */; };
		0042B32C24447B9100F6C1E7 /* PLVVodPlayerLogo.m in Sources */ = {isa = PBXBuildFile; fileRef = 0042B3262444615500F6C1E7 /* PLVVodPlayerLogo.m */; };
		0316EA981F8E50240019E17A /* PLVKVOController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0316EA951F8E50240019E17A /* PLVKVOController.m */; };
		0316EA991F8E50240019E17A /* Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 0316EA971F8E50240019E17A /* Info.plist */; };
		0316EAA01F8F17BE0019E17A /* PLVVodDownloadInfo.mm in Sources */ = {isa = PBXBuildFile; fileRef = 0316EA9F1F8F17BE0019E17A /* PLVVodDownloadInfo.mm */; };
		0316EAA31F8F18970019E17A /* PLVVodDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 0316EAA21F8F18970019E17A /* PLVVodDownloader.m */; };
		0316EAA61F8F19F20019E17A /* PLVVodLocalVideo.m in Sources */ = {isa = PBXBuildFile; fileRef = 0316EAA51F8F19F20019E17A /* PLVVodLocalVideo.m */; };
		0316EAA81F8F5E130019E17A /* Foundation+Log.m in Sources */ = {isa = PBXBuildFile; fileRef = 0316EAA71F8F5E120019E17A /* Foundation+Log.m */; };
		0316EAAB1F8F61A20019E17A /* PLVVodHlsHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 0316EAAA1F8F61A20019E17A /* PLVVodHlsHelper.m */; };
		0317F4EB1FD63DBA00AE6BD8 /* PLVVodSDK.h in Headers */ = {isa = PBXBuildFile; fileRef = 0317F4E91FD63DBA00AE6BD8 /* PLVVodSDK.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0317F4F51FD64D1C00AE6BD8 /* PLVKVOController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0316EA951F8E50240019E17A /* PLVKVOController.m */; };
		0317F4F71FD64D1C00AE6BD8 /* PLVVodDownloadInfo.mm in Sources */ = {isa = PBXBuildFile; fileRef = 0316EA9F1F8F17BE0019E17A /* PLVVodDownloadInfo.mm */; };
		0317F4F91FD64D1C00AE6BD8 /* PLVVodLocalVideo.m in Sources */ = {isa = PBXBuildFile; fileRef = 0316EAA51F8F19F20019E17A /* PLVVodLocalVideo.m */; };
		0317F4FB1FD64D1C00AE6BD8 /* PLVVodTsDownloadTask.m in Sources */ = {isa = PBXBuildFile; fileRef = 03C8BC931F945D1F009FA08A /* PLVVodTsDownloadTask.m */; };
		0317F4FD1FD64D1C00AE6BD8 /* PLVVodDownloadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0316EA9B1F8F147D0019E17A /* PLVVodDownloadManager.m */; };
		0317F4FF1FD64D1C00AE6BD8 /* PLVVodDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 0316EAA21F8F18970019E17A /* PLVVodDownloader.m */; };
		0317F5081FD64D1C00AE6BD8 /* PLVVodVideo.m in Sources */ = {isa = PBXBuildFile; fileRef = 03A15AF21F8B82ED00D5D3F5 /* PLVVodVideo.m */; };
		0317F50A1FD64D1C00AE6BD8 /* PLVVodAd.m in Sources */ = {isa = PBXBuildFile; fileRef = 03A15AF51F8B858D00D5D3F5 /* PLVVodAd.m */; };
		0317F50C1FD64D1C00AE6BD8 /* PLVVodExam.m in Sources */ = {isa = PBXBuildFile; fileRef = 0366B0BA1F8C9ACC00A5995B /* PLVVodExam.m */; };
		0317F50E1FD64D1C00AE6BD8 /* PLVVodPlayerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 03A15AE51F8B6CB800D5D3F5 /* PLVVodPlayerViewController.m */; };
		0317F5101FD64D1C00AE6BD8 /* PLVVodAdPlayerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 03F2DFE31FB29E7A0058D858 /* PLVVodAdPlayerViewController.m */; };
		0317F5121FD64D1C00AE6BD8 /* PLVVodUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 03A15ADD1F8B55A600D5D3F5 /* PLVVodUtil.m */; };
		0317F5141FD64D1C00AE6BD8 /* PLVVodHlsHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 0316EAAA1F8F61A20019E17A /* PLVVodHlsHelper.m */; };
		0317F5161FD64D1C00AE6BD8 /* PLVVodNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = 03BD61431F8C54E000FA5CB4 /* PLVVodNetworking.m */; };
		0317F5191FD64D1C00AE6BD8 /* PLVVodSettings.m in Sources */ = {isa = PBXBuildFile; fileRef = 03A15AD81F8B4E3B00D5D3F5 /* PLVVodSettings.m */; };
		0317F51E1FD64F1200AE6BD8 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 03C8BC951F945F8D009FA08A /* libresolv.tbd */; };
		0317F51F1FD64F2300AE6BD8 /* libstdc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 03A15AEF1F8B7FFE00D5D3F5 /* libstdc++.tbd */; };
		0317F5201FD64F2C00AE6BD8 /* libbz2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 03A15AED1F8B7FC800D5D3F5 /* libbz2.tbd */; };
		0317F5211FD64F3400AE6BD8 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 03A15AEB1F8B7EE700D5D3F5 /* libz.tbd */; };
		0317F5261FD64FAB00AE6BD8 /* PLVKVOController.h in Headers */ = {isa = PBXBuildFile; fileRef = 0316EA961F8E50240019E17A /* PLVKVOController.h */; };
		0317F5271FD64FAB00AE6BD8 /* PLVVodDownloadInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 0316EA9E1F8F17BE0019E17A /* PLVVodDownloadInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0317F5281FD64FAB00AE6BD8 /* PLVVodLocalVideo.h in Headers */ = {isa = PBXBuildFile; fileRef = 0316EAA41F8F19F20019E17A /* PLVVodLocalVideo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0317F5291FD64FAB00AE6BD8 /* PLVVodTsDownloadTask.h in Headers */ = {isa = PBXBuildFile; fileRef = 03C8BC921F945D1F009FA08A /* PLVVodTsDownloadTask.h */; };
		0317F52A1FD64FAB00AE6BD8 /* PLVVodDownloadManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 0316EA9A1F8F147D0019E17A /* PLVVodDownloadManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0317F52B1FD64FAB00AE6BD8 /* PLVVodDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 0316EAA11F8F18970019E17A /* PLVVodDownloader.h */; };
		0317F52F1FD64FAB00AE6BD8 /* PLVVodPlayerSkinProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 039B15441FA6CCF100CCA745 /* PLVVodPlayerSkinProtocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0317F5301FD64FAB00AE6BD8 /* PLVVodVideo.h in Headers */ = {isa = PBXBuildFile; fileRef = 03A15AF11F8B82ED00D5D3F5 /* PLVVodVideo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0317F5311FD64FAB00AE6BD8 /* PLVVodAd.h in Headers */ = {isa = PBXBuildFile; fileRef = 03A15AF41F8B858D00D5D3F5 /* PLVVodAd.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0317F5321FD64FAB00AE6BD8 /* PLVVodExam.h in Headers */ = {isa = PBXBuildFile; fileRef = 0366B0B91F8C9ACC00A5995B /* PLVVodExam.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0317F5331FD64FAB00AE6BD8 /* PLVVodPlayerViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 03A15AE41F8B6CB800D5D3F5 /* PLVVodPlayerViewController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0317F5341FD64FAB00AE6BD8 /* PLVVodAdPlayerViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 03F2DFE21FB29E7A0058D858 /* PLVVodAdPlayerViewController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0317F5351FD64FAB00AE6BD8 /* PLVVodUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 03A15ADC1F8B55A600D5D3F5 /* PLVVodUtil.h */; };
		0317F5361FD64FAB00AE6BD8 /* PLVVodHlsHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 0316EAA91F8F61A20019E17A /* PLVVodHlsHelper.h */; };
		0317F5371FD64FAB00AE6BD8 /* PLVVodNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = 03BD61421F8C54E000FA5CB4 /* PLVVodNetworking.h */; };
		0317F5381FD64FAB00AE6BD8 /* PLVVodConstans.h in Headers */ = {isa = PBXBuildFile; fileRef = 03A15AD61F8B4CC600D5D3F5 /* PLVVodConstans.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0317F5391FD64FAB00AE6BD8 /* PLVVodSettings.h in Headers */ = {isa = PBXBuildFile; fileRef = 03A15AD71F8B4E3B00D5D3F5 /* PLVVodSettings.h */; settings = {ATTRIBUTES = (Public, ); }; };
		032E91392010A30E00DF2634 /* PLVVodDownloadInfoManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 032E91382010A30E00DF2634 /* PLVVodDownloadInfoManager.mm */; };
		032E913B2010B09F00DF2634 /* PLVVodDownloadInfoManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 032E91372010A30E00DF2634 /* PLVVodDownloadInfoManager.h */; };
		0362A82C20117DB900573096 /* PLVVodDownloadInfoManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 032E91382010A30E00DF2634 /* PLVVodDownloadInfoManager.mm */; };
		0366B0BB1F8C9ACC00A5995B /* PLVVodExam.m in Sources */ = {isa = PBXBuildFile; fileRef = 0366B0BA1F8C9ACC00A5995B /* PLVVodExam.m */; };
		0366B0C31F8CB9A700A5995B /* PolyvVodSDKTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 0366B0C21F8CB9A700A5995B /* PolyvVodSDKTests.m */; };
		03A15ABF1F8B4A9100D5D3F5 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 03A15ABE1F8B4A9100D5D3F5 /* AppDelegate.m */; };
		03A15AC21F8B4A9100D5D3F5 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 03A15AC11F8B4A9100D5D3F5 /* ViewController.m */; };
		03A15AC51F8B4A9100D5D3F5 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 03A15AC31F8B4A9100D5D3F5 /* Main.storyboard */; };
		03A15AC71F8B4A9100D5D3F5 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 03A15AC61F8B4A9100D5D3F5 /* Assets.xcassets */; };
		03A15ACD1F8B4A9100D5D3F5 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 03A15ACC1F8B4A9100D5D3F5 /* main.m */; };
		03A15AD91F8B4E3B00D5D3F5 /* PLVVodSettings.m in Sources */ = {isa = PBXBuildFile; fileRef = 03A15AD81F8B4E3B00D5D3F5 /* PLVVodSettings.m */; };
		03A15ADB1F8B507C00D5D3F5 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 03A15ADA1F8B507C00D5D3F5 /* LaunchScreen.storyboard */; };
		03A15ADE1F8B55A600D5D3F5 /* PLVVodUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 03A15ADD1F8B55A600D5D3F5 /* PLVVodUtil.m */; };
		03A15AE61F8B6CB800D5D3F5 /* PLVVodPlayerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 03A15AE51F8B6CB800D5D3F5 /* PLVVodPlayerViewController.m */; };
		03A15AF31F8B82ED00D5D3F5 /* PLVVodVideo.m in Sources */ = {isa = PBXBuildFile; fileRef = 03A15AF21F8B82ED00D5D3F5 /* PLVVodVideo.m */; };
		03A15AF61F8B858D00D5D3F5 /* PLVVodAd.m in Sources */ = {isa = PBXBuildFile; fileRef = 03A15AF51F8B858D00D5D3F5 /* PLVVodAd.m */; };
		03BD61441F8C54E000FA5CB4 /* PLVVodNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = 03BD61431F8C54E000FA5CB4 /* PLVVodNetworking.m */; };
		03BE8B582057B82E00281DFF /* PLVVodReportManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 03BE8B572057B82E00281DFF /* PLVVodReportManager.m */; };
		03BE8B592057B85000281DFF /* PLVVodReportManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 03BE8B572057B82E00281DFF /* PLVVodReportManager.m */; };
		03BE8B5A2057B86B00281DFF /* PLVVodReportManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 03BE8B562057B82E00281DFF /* PLVVodReportManager.h */; };
		03C8BC941F945D1F009FA08A /* PLVVodTsDownloadTask.m in Sources */ = {isa = PBXBuildFile; fileRef = 03C8BC931F945D1F009FA08A /* PLVVodTsDownloadTask.m */; };
		03C8BC991F94B7B0009FA08A /* PLVVodDownloaderExpTableViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 03C8BC981F94B7B0009FA08A /* PLVVodDownloaderExpTableViewController.m */; };
		03F2DFE41FB29E7A0058D858 /* PLVVodAdPlayerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 03F2DFE31FB29E7A0058D858 /* PLVVodAdPlayerViewController.m */; };
		04094CE72249DB140026EE6E /* PLVVodLocalVideo+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = 04094CE52249DB140026EE6E /* PLVVodLocalVideo+Private.h */; };
		04094CE82249DB140026EE6E /* PLVVodLocalVideo+Private.m in Sources */ = {isa = PBXBuildFile; fileRef = 04094CE62249DB140026EE6E /* PLVVodLocalVideo+Private.m */; };
		04094CE92249DB140026EE6E /* PLVVodLocalVideo+Private.m in Sources */ = {isa = PBXBuildFile; fileRef = 04094CE62249DB140026EE6E /* PLVVodLocalVideo+Private.m */; };
		04094CEC224A26BB0026EE6E /* PLVVodVideoParams.h in Headers */ = {isa = PBXBuildFile; fileRef = 04094CEA224A26BB0026EE6E /* PLVVodVideoParams.h */; settings = {ATTRIBUTES = (Public, ); }; };
		04094CED224A26BB0026EE6E /* PLVVodVideoParams.m in Sources */ = {isa = PBXBuildFile; fileRef = 04094CEB224A26BB0026EE6E /* PLVVodVideoParams.m */; };
		04094CEE224A26BB0026EE6E /* PLVVodVideoParams.m in Sources */ = {isa = PBXBuildFile; fileRef = 04094CEB224A26BB0026EE6E /* PLVVodVideoParams.m */; };
		040E71B121A3E37100FC62CE /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 040E71B021A3E37000FC62CE /* libc++.tbd */; };
		0413BAF220EC744000CFA59A /* PLVVodHlsTsDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 046CD7E320E9B4B4003DB372 /* PLVVodHlsTsDownloader.m */; };
		0413BAF320EC747900CFA59A /* PLVVodHlsTsDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 046CD7E220E9B4B4003DB372 /* PLVVodHlsTsDownloader.h */; };
		041837E9225C826F00F7F267 /* PLVFileUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 041837E7225C826F00F7F267 /* PLVFileUtils.h */; };
		041837EA225C826F00F7F267 /* PLVFileUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 041837E8225C826F00F7F267 /* PLVFileUtils.m */; };
		041837EB225C826F00F7F267 /* PLVFileUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 041837E8225C826F00F7F267 /* PLVFileUtils.m */; };
		042277052170ABC9005B7EC9 /* PLVExtendDownloadInfo.mm in Sources */ = {isa = PBXBuildFile; fileRef = 042277022170ABC9005B7EC9 /* PLVExtendDownloadInfo.mm */; };
		0424244522E5D4730012FBD1 /* UIImageView+PLVGif.h in Headers */ = {isa = PBXBuildFile; fileRef = 0424244322E5D4730012FBD1 /* UIImageView+PLVGif.h */; };
		0424244622E5D4730012FBD1 /* UIImageView+PLVGif.m in Sources */ = {isa = PBXBuildFile; fileRef = 0424244422E5D4730012FBD1 /* UIImageView+PLVGif.m */; };
		0424244722E5D4730012FBD1 /* UIImageView+PLVGif.m in Sources */ = {isa = PBXBuildFile; fileRef = 0424244422E5D4730012FBD1 /* UIImageView+PLVGif.m */; };
		042FD094248F6580008A2C01 /* PLVVodTsOneByOneDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 042FD092248F6580008A2C01 /* PLVVodTsOneByOneDownloader.h */; };
		042FD095248F6580008A2C01 /* PLVVodTsOneByOneDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 042FD093248F6580008A2C01 /* PLVVodTsOneByOneDownloader.m */; };
		04383E5E211827E4007B4C56 /* PLVVodHlsZipDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 04383E5D211827E4007B4C56 /* PLVVodHlsZipDownloader.m */; };
		043C803A2D9BC09800DF5938 /* PLVVodNetworkRetryManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 043C80392D9BC09800DF5938 /* PLVVodNetworkRetryManager.m */; };
		043C803B2D9BC09800DF5938 /* PLVVodNetworkRetryManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 043C80392D9BC09800DF5938 /* PLVVodNetworkRetryManager.m */; };
		043C803C2D9BC09800DF5938 /* PLVVodNetworkRetryManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 043C80382D9BC09800DF5938 /* PLVVodNetworkRetryManager.h */; };
		043C80442DA61CFF00DF5938 /* PLVVodNSURLProtocol.m in Sources */ = {isa = PBXBuildFile; fileRef = 043C80432DA61CFF00DF5938 /* PLVVodNSURLProtocol.m */; };
		043C80452DA61CFF00DF5938 /* PLVVodNSURLProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 043C80422DA61CFF00DF5938 /* PLVVodNSURLProtocol.h */; };
		043C80462DA61CFF00DF5938 /* PLVVodNSURLProtocol.m in Sources */ = {isa = PBXBuildFile; fileRef = 043C80432DA61CFF00DF5938 /* PLVVodNSURLProtocol.m */; };
		043E2B9F2D94EA12007A0CCC /* PLVVodReachability.m in Sources */ = {isa = PBXBuildFile; fileRef = 043E2B9D2D94EA12007A0CCC /* PLVVodReachability.m */; };
		043E2BA02D94EA12007A0CCC /* PLVVodReachability.h in Headers */ = {isa = PBXBuildFile; fileRef = 043E2B9C2D94EA12007A0CCC /* PLVVodReachability.h */; settings = {ATTRIBUTES = (Public, ); }; };
		043E58A722783897009481C2 /* PLVVodElogModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 043E58A522783897009481C2 /* PLVVodElogModel.h */; };
		043E58A922783897009481C2 /* PLVVodElogModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 043E58A622783897009481C2 /* PLVVodElogModel.m */; };
		043E58AC227845C7009481C2 /* PLVVodUtil+Device.h in Headers */ = {isa = PBXBuildFile; fileRef = 043E58AA227845C6009481C2 /* PLVVodUtil+Device.h */; };
		043E58AD227845C7009481C2 /* PLVVodUtil+Device.m in Sources */ = {isa = PBXBuildFile; fileRef = 043E58AB227845C6009481C2 /* PLVVodUtil+Device.m */; };
		043E58AE227845C7009481C2 /* PLVVodUtil+Device.m in Sources */ = {isa = PBXBuildFile; fileRef = 043E58AB227845C6009481C2 /* PLVVodUtil+Device.m */; };
		043EAC472282B87400C658D2 /* PLVVodJsonModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 043EAC452282B87400C658D2 /* PLVVodJsonModel.h */; };
		043EAC482282B87400C658D2 /* PLVVodJsonModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 043EAC462282B87400C658D2 /* PLVVodJsonModel.m */; };
		043EAC492282B87400C658D2 /* PLVVodJsonModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 043EAC462282B87400C658D2 /* PLVVodJsonModel.m */; };
		04516E3022FAE6CE00457634 /* PLVVodPPTDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 04516E2E22FAE6CE00457634 /* PLVVodPPTDownloader.h */; };
		04516E3122FAE6CE00457634 /* PLVVodPPTDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 04516E2F22FAE6CE00457634 /* PLVVodPPTDownloader.m */; };
		04516E3222FAE6CE00457634 /* PLVVodPPTDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 04516E2F22FAE6CE00457634 /* PLVVodPPTDownloader.m */; };
		0459F88C22F40D6200044144 /* PLVVodAttachMgr.m in Sources */ = {isa = PBXBuildFile; fileRef = 0459F88A22F40D5800044144 /* PLVVodAttachMgr.m */; };
		045B3CE6237A5075000F9610 /* PLVVodElogSeekModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 045B3CE4237A5075000F9610 /* PLVVodElogSeekModel.h */; };
		045B3CE7237A5075000F9610 /* PLVVodElogSeekModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 045B3CE5237A5075000F9610 /* PLVVodElogSeekModel.m */; };
		0462F58124359AB600B11D46 /* PLVVodPlayerViewController+Reconnect.h in Headers */ = {isa = PBXBuildFile; fileRef = 0462F57F24359AB600B11D46 /* PLVVodPlayerViewController+Reconnect.h */; };
		0462F58224359AB600B11D46 /* PLVVodPlayerViewController+Reconnect.m in Sources */ = {isa = PBXBuildFile; fileRef = 0462F58024359AB600B11D46 /* PLVVodPlayerViewController+Reconnect.m */; };
		0462F58524359FB700B11D46 /* PLVVodPlayerViewController+Ad.h in Headers */ = {isa = PBXBuildFile; fileRef = 0462F58324359FB700B11D46 /* PLVVodPlayerViewController+Ad.h */; };
		0462F58624359FB700B11D46 /* PLVVodPlayerViewController+Ad.m in Sources */ = {isa = PBXBuildFile; fileRef = 0462F58424359FB700B11D46 /* PLVVodPlayerViewController+Ad.m */; };
		0462F5892435B7D500B11D46 /* PLVVodPlayerViewController+Switch.h in Headers */ = {isa = PBXBuildFile; fileRef = 0462F5872435B7D500B11D46 /* PLVVodPlayerViewController+Switch.h */; };
		0462F58A2435B7D500B11D46 /* PLVVodPlayerViewController+Switch.m in Sources */ = {isa = PBXBuildFile; fileRef = 0462F5882435B7D500B11D46 /* PLVVodPlayerViewController+Switch.m */; };
		0462F58D2435C94300B11D46 /* PLVVodPlayerViewController+Position.h in Headers */ = {isa = PBXBuildFile; fileRef = 0462F58B2435C94300B11D46 /* PLVVodPlayerViewController+Position.h */; };
		0462F58E2435C94300B11D46 /* PLVVodPlayerViewController+Position.m in Sources */ = {isa = PBXBuildFile; fileRef = 0462F58C2435C94300B11D46 /* PLVVodPlayerViewController+Position.m */; };
		0462F5912435C9FD00B11D46 /* PLVVodPlayerViewController+Capture.h in Headers */ = {isa = PBXBuildFile; fileRef = 0462F58F2435C9FD00B11D46 /* PLVVodPlayerViewController+Capture.h */; };
		0462F5922435C9FD00B11D46 /* PLVVodPlayerViewController+Capture.m in Sources */ = {isa = PBXBuildFile; fileRef = 0462F5902435C9FD00B11D46 /* PLVVodPlayerViewController+Capture.m */; };
		0462F5932435D09700B11D46 /* PLVVodPlayerViewController+Log.m in Sources */ = {isa = PBXBuildFile; fileRef = 0462F57E24358C1400B11D46 /* PLVVodPlayerViewController+Log.m */; };
		0462F5942435D0E200B11D46 /* PLVVodPlayerViewController+Log.h in Headers */ = {isa = PBXBuildFile; fileRef = 0462F57D24358C1400B11D46 /* PLVVodPlayerViewController+Log.h */; };
		0469B6AC2DEF10830090FC79 /* PLVVodPlayerViewController+Skin.m in Sources */ = {isa = PBXBuildFile; fileRef = 0469B6AB2DEF10830090FC79 /* PLVVodPlayerViewController+Skin.m */; };
		0469B6AD2DEF10830090FC79 /* PLVVodPlayerViewController+Skin.h in Headers */ = {isa = PBXBuildFile; fileRef = 0469B6AA2DEF10830090FC79 /* PLVVodPlayerViewController+Skin.h */; };
		0469B6B02DEF1C880090FC79 /* PLVVodPlayerViewController+IJK.m in Sources */ = {isa = PBXBuildFile; fileRef = 0469B6AF2DEF1C880090FC79 /* PLVVodPlayerViewController+IJK.m */; };
		0469B6B12DEF1C880090FC79 /* PLVVodPlayerViewController+IJK.h in Headers */ = {isa = PBXBuildFile; fileRef = 0469B6AE2DEF1C880090FC79 /* PLVVodPlayerViewController+IJK.h */; };
		0469B6B42DEFE3BB0090FC79 /* PLVVodPlayerViewController+Token.m in Sources */ = {isa = PBXBuildFile; fileRef = 0469B6B32DEFE3BB0090FC79 /* PLVVodPlayerViewController+Token.m */; };
		0469B6B52DEFE3BB0090FC79 /* PLVVodPlayerViewController+Token.h in Headers */ = {isa = PBXBuildFile; fileRef = 0469B6B22DEFE3BB0090FC79 /* PLVVodPlayerViewController+Token.h */; };
		0469B6B82DEFE7360090FC79 /* PLVVodPlayerViewController+PIP.h in Headers */ = {isa = PBXBuildFile; fileRef = 0469B6B62DEFE7360090FC79 /* PLVVodPlayerViewController+PIP.h */; };
		0469B6B92DEFE7360090FC79 /* PLVVodPlayerViewController+PIP.m in Sources */ = {isa = PBXBuildFile; fileRef = 0469B6B72DEFE7360090FC79 /* PLVVodPlayerViewController+PIP.m */; };
		046CD7E420E9B4B4003DB372 /* PLVVodHlsTsDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 046CD7E320E9B4B4003DB372 /* PLVVodHlsTsDownloader.m */; };
		047B72C721B7D8C400DC1CE3 /* PLVVodDefines.h in Headers */ = {isa = PBXBuildFile; fileRef = 047B72C621B7D8C400DC1CE3 /* PLVVodDefines.h */; };
		0483C06C2118443E00F78CFD /* PLVVodHlsZipDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 04383E5D211827E4007B4C56 /* PLVVodHlsZipDownloader.m */; };
		0483C06D2118445C00F78CFD /* PLVVodHlsZipDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 04383E5C211827E4007B4C56 /* PLVVodHlsZipDownloader.h */; };
		04959ECD23069001009A17C6 /* PLVVodPPTMgr.h in Headers */ = {isa = PBXBuildFile; fileRef = 044CAF2C23063377005E6539 /* PLVVodPPTMgr.h */; };
		04959ECE23069006009A17C6 /* PLVVodPPTMgr.m in Sources */ = {isa = PBXBuildFile; fileRef = 044CAF2D23063377005E6539 /* PLVVodPPTMgr.m */; };
		0498B05C212158AD00C22955 /* PLVVodSimpleDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 0498B05B212158AD00C22955 /* PLVVodSimpleDownloader.m */; };
		04A72D7121716893008319D0 /* PLVDownloadDBMgr.mm in Sources */ = {isa = PBXBuildFile; fileRef = 04A72D7021716893008319D0 /* PLVDownloadDBMgr.mm */; };
		04A72D7721721D12008319D0 /* PLVCusDownloadMgr.m in Sources */ = {isa = PBXBuildFile; fileRef = 04A72D7621721D12008319D0 /* PLVCusDownloadMgr.m */; };
		04CBEC992244F64F00A1D0D0 /* PLVVodAudioDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 04CBEC972244F64F00A1D0D0 /* PLVVodAudioDownloader.h */; };
		04CBEC9A2244F64F00A1D0D0 /* PLVVodAudioDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 04CBEC982244F64F00A1D0D0 /* PLVVodAudioDownloader.m */; };
		04CBEC9B2244F64F00A1D0D0 /* PLVVodAudioDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 04CBEC982244F64F00A1D0D0 /* PLVVodAudioDownloader.m */; };
		04D340BF217110120000E989 /* PLVVodDownloadManager+Database.h in Headers */ = {isa = PBXBuildFile; fileRef = 04D340BD217110120000E989 /* PLVVodDownloadManager+Database.h */; settings = {ATTRIBUTES = (Public, ); }; };
		04D340C1217110120000E989 /* PLVVodDownloadManager+Database.mm in Sources */ = {isa = PBXBuildFile; fileRef = 04D340BE217110120000E989 /* PLVVodDownloadManager+Database.mm */; };
		04D495BC21885B4600EBBF9F /* PLVVodPlayerUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 04D495BA21885B4600EBBF9F /* PLVVodPlayerUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		04D495BD21885B4600EBBF9F /* PLVVodPlayerUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 04D495BB21885B4600EBBF9F /* PLVVodPlayerUtil.m */; };
		04D495BE21885B4600EBBF9F /* PLVVodPlayerUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 04D495BB21885B4600EBBF9F /* PLVVodPlayerUtil.m */; };
		04E0C1C821227BCF0032F7A7 /* PLVVodSimpleDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 0498B05B212158AD00C22955 /* PLVVodSimpleDownloader.m */; };
		04EA21E6214605A300C663E2 /* PLVVodVideoJson.mm in Sources */ = {isa = PBXBuildFile; fileRef = 04EA21E4214605A300C663E2 /* PLVVodVideoJson.mm */; };
		04EA21E7214605A300C663E2 /* PLVVodVideoJson.mm in Sources */ = {isa = PBXBuildFile; fileRef = 04EA21E4214605A300C663E2 /* PLVVodVideoJson.mm */; };
		04EFF5342434F3A10034E5CB /* PLVVodPlayerViewController+internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 04EFF5332434F3A00034E5CB /* PLVVodPlayerViewController+internal.h */; };
		04EFF5402434F64E0034E5CB /* PLVVodPlayerViewController+URL.h in Headers */ = {isa = PBXBuildFile; fileRef = 04EFF53E2434F64E0034E5CB /* PLVVodPlayerViewController+URL.h */; };
		04EFF5412434F64E0034E5CB /* PLVVodPlayerViewController+URL.m in Sources */ = {isa = PBXBuildFile; fileRef = 04EFF53F2434F64E0034E5CB /* PLVVodPlayerViewController+URL.m */; };
		651DDBC32A2D7BE0005EF791 /* PLVVodQosLoadingTracer.m in Sources */ = {isa = PBXBuildFile; fileRef = 656870D72A29C5000044FEFE /* PLVVodQosLoadingTracer.m */; };
		656870D82A29C5000044FEFE /* PLVVodQosLoadingTracer.m in Sources */ = {isa = PBXBuildFile; fileRef = 656870D72A29C5000044FEFE /* PLVVodQosLoadingTracer.m */; };
		B0DE39866F971446D39F395D /* libPods-vod-PLVVodSDK.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 8BB41A7835887A3C691F6A14 /* libPods-vod-PLVVodSDK.a */; };
		BFA0A4EB2A4AD87A00F0003E /* PLVVodSimplePing.h in Headers */ = {isa = PBXBuildFile; fileRef = BFA0A4E92A4AD87A00F0003E /* PLVVodSimplePing.h */; };
		BFA0A4EC2A4AD87A00F0003E /* PLVVodSimplePing.m in Sources */ = {isa = PBXBuildFile; fileRef = BFA0A4EA2A4AD87A00F0003E /* PLVVodSimplePing.m */; };
		BFA0A4EF2A4AD90400F0003E /* PLVPingUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = BFA0A4EE2A4AD90400F0003E /* PLVPingUtil.m */; };
		DA0088E520B6B294006CEBBE /* PlayViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DA0088E420B6B294006CEBBE /* PlayViewController.m */; };
		FA4F04C727FEC23B000EBBA0 /* PLVPictureInPictureManager.h in Headers */ = {isa = PBXBuildFile; fileRef = FA4F04C527FEC23B000EBBA0 /* PLVPictureInPictureManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA4F04C827FEC23B000EBBA0 /* PLVPictureInPictureManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FA4F04C627FEC23B000EBBA0 /* PLVPictureInPictureManager.m */; };
		FA4F04CA27FEC2C1000EBBA0 /* PLVPictureInPictureManager+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = FA4F04C927FEC2C1000EBBA0 /* PLVPictureInPictureManager+Private.h */; };
		************************ /* PLVAccountModel.h in Headers */ = {isa = PBXBuildFile; fileRef = ************************ /* PLVAccountModel.h */; };
		************************ /* PLVAccountModel.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* PLVAccountModel.m */; };
		************************ /* PLVAccountModel.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* PLVAccountModel.m */; };
		FAAFEE4A27CCA7A2006D7362 /* PLVVodHttpDnsManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FAAFEE4927CCA7A2006D7362 /* PLVVodHttpDnsManager.m */; };
		FAAFEE4B27CCA7CC006D7362 /* PLVVodHttpDnsManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FAAFEE4927CCA7A2006D7362 /* PLVVodHttpDnsManager.m */; };
		FAAFEE4C27CCA7D1006D7362 /* PLVVodHttpDnsManager.h in Headers */ = {isa = PBXBuildFile; fileRef = FAAFEE4827CCA7A2006D7362 /* PLVVodHttpDnsManager.h */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		0366B0C51F8CB9A700A5995B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 03A15AB21F8B4A9100D5D3F5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 03A15AB91F8B4A9100D5D3F5;
			remoteInfo = PolyvVodSDK;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0012317922E8596800CE6238 /* PLVVodPPT.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodPPT.h; sourceTree = "<group>"; };
		0012317A22E8596800CE6238 /* PLVVodPPT.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodPPT.m; sourceTree = "<group>"; };
		0042B3252444615500F6C1E7 /* PLVVodPlayerLogo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodPlayerLogo.h; sourceTree = "<group>"; };
		0042B3262444615500F6C1E7 /* PLVVodPlayerLogo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodPlayerLogo.m; sourceTree = "<group>"; };
		0042B32D24449E5C00F6C1E7 /* PLVVodPlayerLogo+internal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodPlayerLogo+internal.h"; sourceTree = "<group>"; };
		0316EA951F8E50240019E17A /* PLVKVOController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVKVOController.m; sourceTree = "<group>"; };
		0316EA961F8E50240019E17A /* PLVKVOController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVKVOController.h; sourceTree = "<group>"; };
		0316EA971F8E50240019E17A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		0316EA9A1F8F147D0019E17A /* PLVVodDownloadManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodDownloadManager.h; sourceTree = "<group>"; };
		0316EA9B1F8F147D0019E17A /* PLVVodDownloadManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodDownloadManager.m; sourceTree = "<group>"; };
		0316EA9E1F8F17BE0019E17A /* PLVVodDownloadInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodDownloadInfo.h; sourceTree = "<group>"; };
		0316EA9F1F8F17BE0019E17A /* PLVVodDownloadInfo.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVVodDownloadInfo.mm; sourceTree = "<group>"; };
		0316EAA11F8F18970019E17A /* PLVVodDownloader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodDownloader.h; sourceTree = "<group>"; };
		0316EAA21F8F18970019E17A /* PLVVodDownloader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodDownloader.m; sourceTree = "<group>"; };
		0316EAA41F8F19F20019E17A /* PLVVodLocalVideo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodLocalVideo.h; sourceTree = "<group>"; };
		0316EAA51F8F19F20019E17A /* PLVVodLocalVideo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodLocalVideo.m; sourceTree = "<group>"; };
		0316EAA71F8F5E120019E17A /* Foundation+Log.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "Foundation+Log.m"; sourceTree = "<group>"; };
		0316EAA91F8F61A20019E17A /* PLVVodHlsHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodHlsHelper.h; sourceTree = "<group>"; };
		0316EAAA1F8F61A20019E17A /* PLVVodHlsHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodHlsHelper.m; sourceTree = "<group>"; };
		0317F4E71FD63DBA00AE6BD8 /* PLVVodSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = PLVVodSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		0317F4E91FD63DBA00AE6BD8 /* PLVVodSDK.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodSDK.h; sourceTree = "<group>"; };
		0317F4EA1FD63DBA00AE6BD8 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		032E91372010A30E00DF2634 /* PLVVodDownloadInfoManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodDownloadInfoManager.h; sourceTree = "<group>"; };
		032E91382010A30E00DF2634 /* PLVVodDownloadInfoManager.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVVodDownloadInfoManager.mm; sourceTree = "<group>"; };
		0346BFC01FD6AB9500752E11 /* libz.1.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libz.1.dylib; path = ../../../../../../../usr/lib/libz.1.dylib; sourceTree = "<group>"; };
		0346BFC21FD6ABA600752E11 /* libbz2.1.0.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libbz2.1.0.dylib; path = ../../../../../../../usr/lib/libbz2.1.0.dylib; sourceTree = "<group>"; };
		0346BFC41FD6ABBC00752E11 /* libstdc++.6.0.9.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = "libstdc++.6.0.9.dylib"; path = "../../../../../../../usr/lib/libstdc++.6.0.9.dylib"; sourceTree = "<group>"; };
		0346BFC61FD6ABCA00752E11 /* libresolv.9.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libresolv.9.dylib; path = ../../../../../../../usr/lib/libresolv.9.dylib; sourceTree = "<group>"; };
		0366B0B91F8C9ACC00A5995B /* PLVVodExam.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodExam.h; sourceTree = "<group>"; };
		0366B0BA1F8C9ACC00A5995B /* PLVVodExam.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodExam.m; sourceTree = "<group>"; };
		0366B0C01F8CB9A700A5995B /* PolyvVodSDKTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PolyvVodSDKTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		0366B0C21F8CB9A700A5995B /* PolyvVodSDKTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PolyvVodSDKTests.m; sourceTree = "<group>"; };
		0366B0C41F8CB9A700A5995B /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		036BFBD4206B731E001CAB1B /* README.MD */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; name = README.MD; path = ../README.MD; sourceTree = "<group>"; };
		039B15441FA6CCF100CCA745 /* PLVVodPlayerSkinProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodPlayerSkinProtocol.h; sourceTree = "<group>"; };
		03A15ABA1F8B4A9100D5D3F5 /* _PolyvVodSDK.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = _PolyvVodSDK.app; sourceTree = BUILT_PRODUCTS_DIR; };
		03A15ABD1F8B4A9100D5D3F5 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		03A15ABE1F8B4A9100D5D3F5 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		03A15AC01F8B4A9100D5D3F5 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		03A15AC11F8B4A9100D5D3F5 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		03A15AC41F8B4A9100D5D3F5 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		03A15AC61F8B4A9100D5D3F5 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		03A15ACB1F8B4A9100D5D3F5 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		03A15ACC1F8B4A9100D5D3F5 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		03A15AD61F8B4CC600D5D3F5 /* PLVVodConstans.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodConstans.h; sourceTree = "<group>"; };
		03A15AD71F8B4E3B00D5D3F5 /* PLVVodSettings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodSettings.h; sourceTree = "<group>"; };
		03A15AD81F8B4E3B00D5D3F5 /* PLVVodSettings.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodSettings.m; sourceTree = "<group>"; };
		03A15ADA1F8B507C00D5D3F5 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		03A15ADC1F8B55A600D5D3F5 /* PLVVodUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodUtil.h; sourceTree = "<group>"; };
		03A15ADD1F8B55A600D5D3F5 /* PLVVodUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodUtil.m; sourceTree = "<group>"; };
		03A15AE41F8B6CB800D5D3F5 /* PLVVodPlayerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodPlayerViewController.h; sourceTree = "<group>"; };
		03A15AE51F8B6CB800D5D3F5 /* PLVVodPlayerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodPlayerViewController.m; sourceTree = "<group>"; };
		03A15AEB1F8B7EE700D5D3F5 /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		03A15AED1F8B7FC800D5D3F5 /* libbz2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libbz2.tbd; path = usr/lib/libbz2.tbd; sourceTree = SDKROOT; };
		03A15AEF1F8B7FFE00D5D3F5 /* libstdc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libstdc++.tbd"; path = "usr/lib/libstdc++.tbd"; sourceTree = SDKROOT; };
		03A15AF11F8B82ED00D5D3F5 /* PLVVodVideo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodVideo.h; sourceTree = "<group>"; };
		03A15AF21F8B82ED00D5D3F5 /* PLVVodVideo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodVideo.m; sourceTree = "<group>"; };
		03A15AF41F8B858D00D5D3F5 /* PLVVodAd.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodAd.h; sourceTree = "<group>"; };
		03A15AF51F8B858D00D5D3F5 /* PLVVodAd.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodAd.m; sourceTree = "<group>"; };
		03BD61421F8C54E000FA5CB4 /* PLVVodNetworking.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodNetworking.h; sourceTree = "<group>"; };
		03BD61431F8C54E000FA5CB4 /* PLVVodNetworking.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodNetworking.m; sourceTree = "<group>"; };
		03BE8B562057B82E00281DFF /* PLVVodReportManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodReportManager.h; sourceTree = "<group>"; };
		03BE8B572057B82E00281DFF /* PLVVodReportManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodReportManager.m; sourceTree = "<group>"; };
		03C8BC921F945D1F009FA08A /* PLVVodTsDownloadTask.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodTsDownloadTask.h; sourceTree = "<group>"; };
		03C8BC931F945D1F009FA08A /* PLVVodTsDownloadTask.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodTsDownloadTask.m; sourceTree = "<group>"; };
		03C8BC951F945F8D009FA08A /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		03C8BC971F94B7B0009FA08A /* PLVVodDownloaderExpTableViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodDownloaderExpTableViewController.h; sourceTree = "<group>"; };
		03C8BC981F94B7B0009FA08A /* PLVVodDownloaderExpTableViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodDownloaderExpTableViewController.m; sourceTree = "<group>"; };
		03EC3BA2200F3CF3003274CF /* Header */ = {isa = PBXFileReference; lastKnownFileType = folder; path = Header; sourceTree = "<group>"; };
		03F2DFE21FB29E7A0058D858 /* PLVVodAdPlayerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodAdPlayerViewController.h; sourceTree = "<group>"; };
		03F2DFE31FB29E7A0058D858 /* PLVVodAdPlayerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodAdPlayerViewController.m; sourceTree = "<group>"; };
		04094CE52249DB140026EE6E /* PLVVodLocalVideo+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodLocalVideo+Private.h"; sourceTree = "<group>"; };
		04094CE62249DB140026EE6E /* PLVVodLocalVideo+Private.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PLVVodLocalVideo+Private.m"; sourceTree = "<group>"; };
		04094CEA224A26BB0026EE6E /* PLVVodVideoParams.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodVideoParams.h; sourceTree = "<group>"; };
		04094CEB224A26BB0026EE6E /* PLVVodVideoParams.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodVideoParams.m; sourceTree = "<group>"; };
		040E71B021A3E37000FC62CE /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		041837E7225C826F00F7F267 /* PLVFileUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFileUtils.h; sourceTree = "<group>"; };
		041837E8225C826F00F7F267 /* PLVFileUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFileUtils.m; sourceTree = "<group>"; };
		042277022170ABC9005B7EC9 /* PLVExtendDownloadInfo.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVExtendDownloadInfo.mm; sourceTree = "<group>"; };
		042277042170ABC9005B7EC9 /* PLVExtendDownloadInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVExtendDownloadInfo.h; sourceTree = "<group>"; };
		0424244322E5D4730012FBD1 /* UIImageView+PLVGif.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImageView+PLVGif.h"; sourceTree = "<group>"; };
		0424244422E5D4730012FBD1 /* UIImageView+PLVGif.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+PLVGif.m"; sourceTree = "<group>"; };
		042FD092248F6580008A2C01 /* PLVVodTsOneByOneDownloader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodTsOneByOneDownloader.h; sourceTree = "<group>"; };
		042FD093248F6580008A2C01 /* PLVVodTsOneByOneDownloader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodTsOneByOneDownloader.m; sourceTree = "<group>"; };
		04383E5C211827E4007B4C56 /* PLVVodHlsZipDownloader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodHlsZipDownloader.h; sourceTree = "<group>"; };
		04383E5D211827E4007B4C56 /* PLVVodHlsZipDownloader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodHlsZipDownloader.m; sourceTree = "<group>"; };
		043C80382D9BC09800DF5938 /* PLVVodNetworkRetryManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodNetworkRetryManager.h; sourceTree = "<group>"; };
		043C80392D9BC09800DF5938 /* PLVVodNetworkRetryManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodNetworkRetryManager.m; sourceTree = "<group>"; };
		043C80422DA61CFF00DF5938 /* PLVVodNSURLProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodNSURLProtocol.h; sourceTree = "<group>"; };
		043C80432DA61CFF00DF5938 /* PLVVodNSURLProtocol.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodNSURLProtocol.m; sourceTree = "<group>"; };
		043E2B9C2D94EA12007A0CCC /* PLVVodReachability.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodReachability.h; sourceTree = "<group>"; };
		043E2B9D2D94EA12007A0CCC /* PLVVodReachability.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodReachability.m; sourceTree = "<group>"; };
		043E58A522783897009481C2 /* PLVVodElogModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodElogModel.h; sourceTree = "<group>"; };
		043E58A622783897009481C2 /* PLVVodElogModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodElogModel.m; sourceTree = "<group>"; };
		043E58AA227845C6009481C2 /* PLVVodUtil+Device.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodUtil+Device.h"; sourceTree = "<group>"; };
		043E58AB227845C6009481C2 /* PLVVodUtil+Device.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PLVVodUtil+Device.m"; sourceTree = "<group>"; };
		043EAC452282B87400C658D2 /* PLVVodJsonModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodJsonModel.h; sourceTree = "<group>"; };
		043EAC462282B87400C658D2 /* PLVVodJsonModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodJsonModel.m; sourceTree = "<group>"; };
		044CAF2C23063377005E6539 /* PLVVodPPTMgr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodPPTMgr.h; sourceTree = "<group>"; };
		044CAF2D23063377005E6539 /* PLVVodPPTMgr.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodPPTMgr.m; sourceTree = "<group>"; };
		04516E2E22FAE6CE00457634 /* PLVVodPPTDownloader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodPPTDownloader.h; sourceTree = "<group>"; };
		04516E2F22FAE6CE00457634 /* PLVVodPPTDownloader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodPPTDownloader.m; sourceTree = "<group>"; };
		0459F88922F40D5800044144 /* PLVVodAttachMgr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodAttachMgr.h; sourceTree = "<group>"; };
		0459F88A22F40D5800044144 /* PLVVodAttachMgr.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodAttachMgr.m; sourceTree = "<group>"; };
		045B3CE4237A5075000F9610 /* PLVVodElogSeekModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodElogSeekModel.h; sourceTree = "<group>"; };
		045B3CE5237A5075000F9610 /* PLVVodElogSeekModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodElogSeekModel.m; sourceTree = "<group>"; };
		0462F57D24358C1400B11D46 /* PLVVodPlayerViewController+Log.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodPlayerViewController+Log.h"; sourceTree = "<group>"; };
		0462F57E24358C1400B11D46 /* PLVVodPlayerViewController+Log.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PLVVodPlayerViewController+Log.m"; sourceTree = "<group>"; };
		0462F57F24359AB600B11D46 /* PLVVodPlayerViewController+Reconnect.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodPlayerViewController+Reconnect.h"; sourceTree = "<group>"; };
		0462F58024359AB600B11D46 /* PLVVodPlayerViewController+Reconnect.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PLVVodPlayerViewController+Reconnect.m"; sourceTree = "<group>"; };
		0462F58324359FB700B11D46 /* PLVVodPlayerViewController+Ad.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodPlayerViewController+Ad.h"; sourceTree = "<group>"; };
		0462F58424359FB700B11D46 /* PLVVodPlayerViewController+Ad.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PLVVodPlayerViewController+Ad.m"; sourceTree = "<group>"; };
		0462F5872435B7D500B11D46 /* PLVVodPlayerViewController+Switch.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodPlayerViewController+Switch.h"; sourceTree = "<group>"; };
		0462F5882435B7D500B11D46 /* PLVVodPlayerViewController+Switch.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PLVVodPlayerViewController+Switch.m"; sourceTree = "<group>"; };
		0462F58B2435C94300B11D46 /* PLVVodPlayerViewController+Position.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodPlayerViewController+Position.h"; sourceTree = "<group>"; };
		0462F58C2435C94300B11D46 /* PLVVodPlayerViewController+Position.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PLVVodPlayerViewController+Position.m"; sourceTree = "<group>"; };
		0462F58F2435C9FD00B11D46 /* PLVVodPlayerViewController+Capture.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodPlayerViewController+Capture.h"; sourceTree = "<group>"; };
		0462F5902435C9FD00B11D46 /* PLVVodPlayerViewController+Capture.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PLVVodPlayerViewController+Capture.m"; sourceTree = "<group>"; };
		0469B6AA2DEF10830090FC79 /* PLVVodPlayerViewController+Skin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodPlayerViewController+Skin.h"; sourceTree = "<group>"; };
		0469B6AB2DEF10830090FC79 /* PLVVodPlayerViewController+Skin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PLVVodPlayerViewController+Skin.m"; sourceTree = "<group>"; };
		0469B6AE2DEF1C880090FC79 /* PLVVodPlayerViewController+IJK.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodPlayerViewController+IJK.h"; sourceTree = "<group>"; };
		0469B6AF2DEF1C880090FC79 /* PLVVodPlayerViewController+IJK.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PLVVodPlayerViewController+IJK.m"; sourceTree = "<group>"; };
		0469B6B22DEFE3BB0090FC79 /* PLVVodPlayerViewController+Token.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodPlayerViewController+Token.h"; sourceTree = "<group>"; };
		0469B6B32DEFE3BB0090FC79 /* PLVVodPlayerViewController+Token.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PLVVodPlayerViewController+Token.m"; sourceTree = "<group>"; };
		0469B6B62DEFE7360090FC79 /* PLVVodPlayerViewController+PIP.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodPlayerViewController+PIP.h"; sourceTree = "<group>"; };
		0469B6B72DEFE7360090FC79 /* PLVVodPlayerViewController+PIP.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PLVVodPlayerViewController+PIP.m"; sourceTree = "<group>"; };
		046CD7E220E9B4B4003DB372 /* PLVVodHlsTsDownloader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodHlsTsDownloader.h; sourceTree = "<group>"; };
		046CD7E320E9B4B4003DB372 /* PLVVodHlsTsDownloader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodHlsTsDownloader.m; sourceTree = "<group>"; };
		047B72C621B7D8C400DC1CE3 /* PLVVodDefines.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodDefines.h; sourceTree = "<group>"; };
		0498B05A212158AD00C22955 /* PLVVodSimpleDownloader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodSimpleDownloader.h; sourceTree = "<group>"; };
		0498B05B212158AD00C22955 /* PLVVodSimpleDownloader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodSimpleDownloader.m; sourceTree = "<group>"; };
		04A72D6F21716893008319D0 /* PLVDownloadDBMgr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadDBMgr.h; sourceTree = "<group>"; };
		04A72D7021716893008319D0 /* PLVDownloadDBMgr.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVDownloadDBMgr.mm; sourceTree = "<group>"; };
		04A72D7521721D12008319D0 /* PLVCusDownloadMgr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCusDownloadMgr.h; sourceTree = "<group>"; };
		04A72D7621721D12008319D0 /* PLVCusDownloadMgr.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCusDownloadMgr.m; sourceTree = "<group>"; };
		04CBEC972244F64F00A1D0D0 /* PLVVodAudioDownloader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodAudioDownloader.h; sourceTree = "<group>"; };
		04CBEC982244F64F00A1D0D0 /* PLVVodAudioDownloader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodAudioDownloader.m; sourceTree = "<group>"; };
		04D340BD217110120000E989 /* PLVVodDownloadManager+Database.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodDownloadManager+Database.h"; sourceTree = "<group>"; };
		04D340BE217110120000E989 /* PLVVodDownloadManager+Database.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = "PLVVodDownloadManager+Database.mm"; sourceTree = "<group>"; };
		04D495BA21885B4600EBBF9F /* PLVVodPlayerUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodPlayerUtil.h; sourceTree = "<group>"; };
		04D495BB21885B4600EBBF9F /* PLVVodPlayerUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodPlayerUtil.m; sourceTree = "<group>"; };
		04EA21E12146055F00C663E2 /* PLVVodVideoJson.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodVideoJson.h; sourceTree = "<group>"; };
		04EA21E4214605A300C663E2 /* PLVVodVideoJson.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVVodVideoJson.mm; sourceTree = "<group>"; };
		04EFF5332434F3A00034E5CB /* PLVVodPlayerViewController+internal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodPlayerViewController+internal.h"; sourceTree = "<group>"; };
		04EFF53E2434F64E0034E5CB /* PLVVodPlayerViewController+URL.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVVodPlayerViewController+URL.h"; sourceTree = "<group>"; };
		04EFF53F2434F64E0034E5CB /* PLVVodPlayerViewController+URL.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PLVVodPlayerViewController+URL.m"; sourceTree = "<group>"; };
		258BF8D99F7B2BDED7276E57 /* Pods-vod-PLVVodSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-vod-PLVVodSDK.release.xcconfig"; path = "../../polyv-ios-vod-sdk-demo/Pods/Target Support Files/Pods-vod-PLVVodSDK/Pods-vod-PLVVodSDK.release.xcconfig"; sourceTree = "<group>"; };
		2E99F725F1A856D3FA4B3D58 /* Pods-vod-PLVVodSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-vod-PLVVodSDK.debug.xcconfig"; path = "../../polyv-ios-vod-sdk-demo/Pods/Target Support Files/Pods-vod-PLVVodSDK/Pods-vod-PLVVodSDK.debug.xcconfig"; sourceTree = "<group>"; };
		656870D62A29C5000044FEFE /* PLVVodQosLoadingTracer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodQosLoadingTracer.h; sourceTree = "<group>"; };
		656870D72A29C5000044FEFE /* PLVVodQosLoadingTracer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodQosLoadingTracer.m; sourceTree = "<group>"; };
		7C34F9724C11B9241D2F3DA8 /* Pods-PLVVodSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PLVVodSDK.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-PLVVodSDK/Pods-PLVVodSDK.debug.xcconfig"; sourceTree = "<group>"; };
		8BB41A7835887A3C691F6A14 /* libPods-vod-PLVVodSDK.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-vod-PLVVodSDK.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		A3D6C3668D3D39D3D183AC16 /* Pods-PLVVodSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PLVVodSDK.release.xcconfig"; path = "../Pods/Target Support Files/Pods-PLVVodSDK/Pods-PLVVodSDK.release.xcconfig"; sourceTree = "<group>"; };
		BFA0A4E92A4AD87A00F0003E /* PLVVodSimplePing.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVVodSimplePing.h; sourceTree = "<group>"; };
		BFA0A4EA2A4AD87A00F0003E /* PLVVodSimplePing.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVVodSimplePing.m; sourceTree = "<group>"; };
		BFA0A4ED2A4AD8E700F0003E /* PLVPingUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPingUtil.h; sourceTree = "<group>"; };
		BFA0A4EE2A4AD90400F0003E /* PLVPingUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPingUtil.m; sourceTree = "<group>"; };
		DA0088E320B6B294006CEBBE /* PlayViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PlayViewController.h; sourceTree = "<group>"; };
		DA0088E420B6B294006CEBBE /* PlayViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PlayViewController.m; sourceTree = "<group>"; };
		FA4F04C527FEC23B000EBBA0 /* PLVPictureInPictureManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPictureInPictureManager.h; sourceTree = "<group>"; };
		FA4F04C627FEC23B000EBBA0 /* PLVPictureInPictureManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPictureInPictureManager.m; sourceTree = "<group>"; };
		FA4F04C927FEC2C1000EBBA0 /* PLVPictureInPictureManager+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVPictureInPictureManager+Private.h"; sourceTree = "<group>"; };
		************************ /* PLVAccountModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVAccountModel.h; sourceTree = "<group>"; };
		************************ /* PLVAccountModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVAccountModel.m; sourceTree = "<group>"; };
		FAAFEE4827CCA7A2006D7362 /* PLVVodHttpDnsManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVVodHttpDnsManager.h; sourceTree = "<group>"; };
		FAAFEE4927CCA7A2006D7362 /* PLVVodHttpDnsManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVVodHttpDnsManager.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0317F4E31FD63DBA00AE6BD8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				B0DE39866F971446D39F395D /* libPods-vod-PLVVodSDK.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0366B0BD1F8CB9A700A5995B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		03A15AB71F8B4A9100D5D3F5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				040E71B121A3E37100FC62CE /* libc++.tbd in Frameworks */,
				0317F5211FD64F3400AE6BD8 /* libz.tbd in Frameworks */,
				0317F5201FD64F2C00AE6BD8 /* libbz2.tbd in Frameworks */,
				0317F51F1FD64F2300AE6BD8 /* libstdc++.tbd in Frameworks */,
				0317F51E1FD64F1200AE6BD8 /* libresolv.tbd in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0042B324244460F500F6C1E7 /* Widget */ = {
			isa = PBXGroup;
			children = (
				0042B32824446CE600F6C1E7 /* Logo */,
			);
			path = Widget;
			sourceTree = "<group>";
		};
		0042B32824446CE600F6C1E7 /* Logo */ = {
			isa = PBXGroup;
			children = (
				0042B32D24449E5C00F6C1E7 /* PLVVodPlayerLogo+internal.h */,
				0042B3252444615500F6C1E7 /* PLVVodPlayerLogo.h */,
				0042B3262444615500F6C1E7 /* PLVVodPlayerLogo.m */,
			);
			path = Logo;
			sourceTree = "<group>";
		};
		0316EA941F8E50240019E17A /* FBKVOController */ = {
			isa = PBXGroup;
			children = (
				0316EA951F8E50240019E17A /* PLVKVOController.m */,
				0316EA961F8E50240019E17A /* PLVKVOController.h */,
				0316EA971F8E50240019E17A /* Info.plist */,
			);
			path = FBKVOController;
			sourceTree = "<group>";
		};
		0316EA9D1F8F17AC0019E17A /* Model */ = {
			isa = PBXGroup;
			children = (
				0316EA9E1F8F17BE0019E17A /* PLVVodDownloadInfo.h */,
				0316EA9F1F8F17BE0019E17A /* PLVVodDownloadInfo.mm */,
				032E91372010A30E00DF2634 /* PLVVodDownloadInfoManager.h */,
				032E91382010A30E00DF2634 /* PLVVodDownloadInfoManager.mm */,
				0316EAA41F8F19F20019E17A /* PLVVodLocalVideo.h */,
				0316EAA51F8F19F20019E17A /* PLVVodLocalVideo.m */,
				03C8BC921F945D1F009FA08A /* PLVVodTsDownloadTask.h */,
				03C8BC931F945D1F009FA08A /* PLVVodTsDownloadTask.m */,
				04EA21E12146055F00C663E2 /* PLVVodVideoJson.h */,
				04EA21E4214605A300C663E2 /* PLVVodVideoJson.mm */,
				04094CE52249DB140026EE6E /* PLVVodLocalVideo+Private.h */,
				04094CE62249DB140026EE6E /* PLVVodLocalVideo+Private.m */,
				04094CEA224A26BB0026EE6E /* PLVVodVideoParams.h */,
				04094CEB224A26BB0026EE6E /* PLVVodVideoParams.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		0317F4E81FD63DBA00AE6BD8 /* FrameworkBuild */ = {
			isa = PBXGroup;
			children = (
				03EC3BA2200F3CF3003274CF /* Header */,
				0317F4E91FD63DBA00AE6BD8 /* PLVVodSDK.h */,
				0317F4EA1FD63DBA00AE6BD8 /* Info.plist */,
			);
			path = FrameworkBuild;
			sourceTree = "<group>";
		};
		036542591FB150B6009A79B2 /* Util */ = {
			isa = PBXGroup;
			children = (
				043C80322D9BBD4200DF5938 /* Network */,
				043E2B9E2D94EA12007A0CCC /* Reachability */,
				043E58A42278387E009481C2 /* Model */,
				03A15ADC1F8B55A600D5D3F5 /* PLVVodUtil.h */,
				03A15ADD1F8B55A600D5D3F5 /* PLVVodUtil.m */,
				0316EAA91F8F61A20019E17A /* PLVVodHlsHelper.h */,
				0316EAAA1F8F61A20019E17A /* PLVVodHlsHelper.m */,
				03BE8B562057B82E00281DFF /* PLVVodReportManager.h */,
				03BE8B572057B82E00281DFF /* PLVVodReportManager.m */,
				047B72C621B7D8C400DC1CE3 /* PLVVodDefines.h */,
				041837E7225C826F00F7F267 /* PLVFileUtils.h */,
				041837E8225C826F00F7F267 /* PLVFileUtils.m */,
				043E58AA227845C6009481C2 /* PLVVodUtil+Device.h */,
				043E58AB227845C6009481C2 /* PLVVodUtil+Device.m */,
				0424244322E5D4730012FBD1 /* UIImageView+PLVGif.h */,
				0424244422E5D4730012FBD1 /* UIImageView+PLVGif.m */,
			);
			path = Util;
			sourceTree = "<group>";
		};
		0366B0B41F8C5D8400A5995B /* Foundation */ = {
			isa = PBXGroup;
			children = (
				0316EA941F8E50240019E17A /* FBKVOController */,
			);
			path = Foundation;
			sourceTree = "<group>";
		};
		0366B0B81F8C9A9600A5995B /* Model */ = {
			isa = PBXGroup;
			children = (
				03A15AF11F8B82ED00D5D3F5 /* PLVVodVideo.h */,
				03A15AF21F8B82ED00D5D3F5 /* PLVVodVideo.m */,
				03A15AF41F8B858D00D5D3F5 /* PLVVodAd.h */,
				03A15AF51F8B858D00D5D3F5 /* PLVVodAd.m */,
				0366B0B91F8C9ACC00A5995B /* PLVVodExam.h */,
				0366B0BA1F8C9ACC00A5995B /* PLVVodExam.m */,
				0012317922E8596800CE6238 /* PLVVodPPT.h */,
				0012317A22E8596800CE6238 /* PLVVodPPT.m */,
				656870D62A29C5000044FEFE /* PLVVodQosLoadingTracer.h */,
				656870D72A29C5000044FEFE /* PLVVodQosLoadingTracer.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		0366B0C11F8CB9A700A5995B /* PolyvVodSDKTests */ = {
			isa = PBXGroup;
			children = (
				0366B0C21F8CB9A700A5995B /* PolyvVodSDKTests.m */,
				0366B0C41F8CB9A700A5995B /* Info.plist */,
			);
			path = PolyvVodSDKTests;
			sourceTree = "<group>";
		};
		039B15431FA6CCF100CCA745 /* Protocol */ = {
			isa = PBXGroup;
			children = (
				039B15441FA6CCF100CCA745 /* PLVVodPlayerSkinProtocol.h */,
			);
			path = Protocol;
			sourceTree = "<group>";
		};
		03A15AB11F8B4A9100D5D3F5 = {
			isa = PBXGroup;
			children = (
				036BFBD4206B731E001CAB1B /* README.MD */,
				03A15ABC1F8B4A9100D5D3F5 /* PolyvVodSDK */,
				0366B0C11F8CB9A700A5995B /* PolyvVodSDKTests */,
				0317F4E81FD63DBA00AE6BD8 /* FrameworkBuild */,
				03A15ABB1F8B4A9100D5D3F5 /* Products */,
				03A15AE81F8B6EF600D5D3F5 /* Frameworks */,
				E7C3604132B0FB09FAB25830 /* Pods */,
			);
			sourceTree = "<group>";
		};
		03A15ABB1F8B4A9100D5D3F5 /* Products */ = {
			isa = PBXGroup;
			children = (
				03A15ABA1F8B4A9100D5D3F5 /* _PolyvVodSDK.app */,
				0366B0C01F8CB9A700A5995B /* PolyvVodSDKTests.xctest */,
				0317F4E71FD63DBA00AE6BD8 /* PLVVodSDK.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		03A15ABC1F8B4A9100D5D3F5 /* PolyvVodSDK */ = {
			isa = PBXGroup;
			children = (
				04A72D7421721C7A008319D0 /* PolyvOpenDatabase */,
				03A15AD31F8B4C0C00D5D3F5 /* Classes */,
				03A15ABD1F8B4A9100D5D3F5 /* AppDelegate.h */,
				03A15ABE1F8B4A9100D5D3F5 /* AppDelegate.m */,
				03A15AC01F8B4A9100D5D3F5 /* ViewController.h */,
				03A15AC11F8B4A9100D5D3F5 /* ViewController.m */,
				DA0088E320B6B294006CEBBE /* PlayViewController.h */,
				DA0088E420B6B294006CEBBE /* PlayViewController.m */,
				03C8BC971F94B7B0009FA08A /* PLVVodDownloaderExpTableViewController.h */,
				03C8BC981F94B7B0009FA08A /* PLVVodDownloaderExpTableViewController.m */,
				03A15AC31F8B4A9100D5D3F5 /* Main.storyboard */,
				03A15ADA1F8B507C00D5D3F5 /* LaunchScreen.storyboard */,
				03A15AC61F8B4A9100D5D3F5 /* Assets.xcassets */,
				03A15ACB1F8B4A9100D5D3F5 /* Info.plist */,
				03A15ACC1F8B4A9100D5D3F5 /* main.m */,
				0316EAA71F8F5E120019E17A /* Foundation+Log.m */,
			);
			path = PolyvVodSDK;
			sourceTree = "<group>";
		};
		03A15AD31F8B4C0C00D5D3F5 /* Classes */ = {
			isa = PBXGroup;
			children = (
				0366B0B41F8C5D8400A5995B /* Foundation */,
				03A15AD51F8B4C8800D5D3F5 /* Downloader */,
				03A15AD41F8B4C7C00D5D3F5 /* Player */,
				036542591FB150B6009A79B2 /* Util */,
				03A15AD61F8B4CC600D5D3F5 /* PLVVodConstans.h */,
				************************ /* PLVAccountModel.h */,
				************************ /* PLVAccountModel.m */,
				03A15AD71F8B4E3B00D5D3F5 /* PLVVodSettings.h */,
				03A15AD81F8B4E3B00D5D3F5 /* PLVVodSettings.m */,
			);
			path = Classes;
			sourceTree = "<group>";
		};
		03A15AD41F8B4C7C00D5D3F5 /* Player */ = {
			isa = PBXGroup;
			children = (
				0042B324244460F500F6C1E7 /* Widget */,
				039B15431FA6CCF100CCA745 /* Protocol */,
				0366B0B81F8C9A9600A5995B /* Model */,
				03A15AE41F8B6CB800D5D3F5 /* PLVVodPlayerViewController.h */,
				03A15AE51F8B6CB800D5D3F5 /* PLVVodPlayerViewController.m */,
				FA4F04C527FEC23B000EBBA0 /* PLVPictureInPictureManager.h */,
				FA4F04C627FEC23B000EBBA0 /* PLVPictureInPictureManager.m */,
				FA4F04C927FEC2C1000EBBA0 /* PLVPictureInPictureManager+Private.h */,
				03F2DFE21FB29E7A0058D858 /* PLVVodAdPlayerViewController.h */,
				03F2DFE31FB29E7A0058D858 /* PLVVodAdPlayerViewController.m */,
				04D495BA21885B4600EBBF9F /* PLVVodPlayerUtil.h */,
				04D495BB21885B4600EBBF9F /* PLVVodPlayerUtil.m */,
				04EFF5332434F3A00034E5CB /* PLVVodPlayerViewController+internal.h */,
				04EFF53E2434F64E0034E5CB /* PLVVodPlayerViewController+URL.h */,
				04EFF53F2434F64E0034E5CB /* PLVVodPlayerViewController+URL.m */,
				0462F57D24358C1400B11D46 /* PLVVodPlayerViewController+Log.h */,
				0462F57E24358C1400B11D46 /* PLVVodPlayerViewController+Log.m */,
				0462F57F24359AB600B11D46 /* PLVVodPlayerViewController+Reconnect.h */,
				0462F58024359AB600B11D46 /* PLVVodPlayerViewController+Reconnect.m */,
				0462F58324359FB700B11D46 /* PLVVodPlayerViewController+Ad.h */,
				0462F58424359FB700B11D46 /* PLVVodPlayerViewController+Ad.m */,
				0462F5872435B7D500B11D46 /* PLVVodPlayerViewController+Switch.h */,
				0462F5882435B7D500B11D46 /* PLVVodPlayerViewController+Switch.m */,
				0462F58B2435C94300B11D46 /* PLVVodPlayerViewController+Position.h */,
				0462F58C2435C94300B11D46 /* PLVVodPlayerViewController+Position.m */,
				0462F58F2435C9FD00B11D46 /* PLVVodPlayerViewController+Capture.h */,
				0462F5902435C9FD00B11D46 /* PLVVodPlayerViewController+Capture.m */,
				0469B6AA2DEF10830090FC79 /* PLVVodPlayerViewController+Skin.h */,
				0469B6AB2DEF10830090FC79 /* PLVVodPlayerViewController+Skin.m */,
				0469B6AE2DEF1C880090FC79 /* PLVVodPlayerViewController+IJK.h */,
				0469B6AF2DEF1C880090FC79 /* PLVVodPlayerViewController+IJK.m */,
				0469B6B22DEFE3BB0090FC79 /* PLVVodPlayerViewController+Token.h */,
				0469B6B32DEFE3BB0090FC79 /* PLVVodPlayerViewController+Token.m */,
				0469B6B62DEFE7360090FC79 /* PLVVodPlayerViewController+PIP.h */,
				0469B6B72DEFE7360090FC79 /* PLVVodPlayerViewController+PIP.m */,
			);
			path = Player;
			sourceTree = "<group>";
		};
		03A15AD51F8B4C8800D5D3F5 /* Downloader */ = {
			isa = PBXGroup;
			children = (
				041C05DB22E07979006A6D7B /* Manager */,
				0316EA9D1F8F17AC0019E17A /* Model */,
				0316EAA11F8F18970019E17A /* PLVVodDownloader.h */,
				0316EAA21F8F18970019E17A /* PLVVodDownloader.m */,
				046CD7E220E9B4B4003DB372 /* PLVVodHlsTsDownloader.h */,
				046CD7E320E9B4B4003DB372 /* PLVVodHlsTsDownloader.m */,
				04383E5C211827E4007B4C56 /* PLVVodHlsZipDownloader.h */,
				04383E5D211827E4007B4C56 /* PLVVodHlsZipDownloader.m */,
				0498B05A212158AD00C22955 /* PLVVodSimpleDownloader.h */,
				0498B05B212158AD00C22955 /* PLVVodSimpleDownloader.m */,
				04CBEC972244F64F00A1D0D0 /* PLVVodAudioDownloader.h */,
				04CBEC982244F64F00A1D0D0 /* PLVVodAudioDownloader.m */,
				04516E2E22FAE6CE00457634 /* PLVVodPPTDownloader.h */,
				04516E2F22FAE6CE00457634 /* PLVVodPPTDownloader.m */,
				042FD092248F6580008A2C01 /* PLVVodTsOneByOneDownloader.h */,
				042FD093248F6580008A2C01 /* PLVVodTsOneByOneDownloader.m */,
			);
			path = Downloader;
			sourceTree = "<group>";
		};
		03A15AE81F8B6EF600D5D3F5 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				040E71B021A3E37000FC62CE /* libc++.tbd */,
				0346BFC61FD6ABCA00752E11 /* libresolv.9.dylib */,
				0346BFC41FD6ABBC00752E11 /* libstdc++.6.0.9.dylib */,
				0346BFC21FD6ABA600752E11 /* libbz2.1.0.dylib */,
				0346BFC01FD6AB9500752E11 /* libz.1.dylib */,
				03C8BC951F945F8D009FA08A /* libresolv.tbd */,
				03A15AEF1F8B7FFE00D5D3F5 /* libstdc++.tbd */,
				03A15AED1F8B7FC800D5D3F5 /* libbz2.tbd */,
				03A15AEB1F8B7EE700D5D3F5 /* libz.tbd */,
				8BB41A7835887A3C691F6A14 /* libPods-vod-PLVVodSDK.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		041C05DB22E07979006A6D7B /* Manager */ = {
			isa = PBXGroup;
			children = (
				0459F88922F40D5800044144 /* PLVVodAttachMgr.h */,
				0459F88A22F40D5800044144 /* PLVVodAttachMgr.m */,
				0316EA9A1F8F147D0019E17A /* PLVVodDownloadManager.h */,
				0316EA9B1F8F147D0019E17A /* PLVVodDownloadManager.m */,
				04D340BD217110120000E989 /* PLVVodDownloadManager+Database.h */,
				04D340BE217110120000E989 /* PLVVodDownloadManager+Database.mm */,
				044CAF2C23063377005E6539 /* PLVVodPPTMgr.h */,
				044CAF2D23063377005E6539 /* PLVVodPPTMgr.m */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		043C80322D9BBD4200DF5938 /* Network */ = {
			isa = PBXGroup;
			children = (
				043C80412DA61C5800DF5938 /* HttpDNS */,
				BFA0A4E92A4AD87A00F0003E /* PLVVodSimplePing.h */,
				BFA0A4EA2A4AD87A00F0003E /* PLVVodSimplePing.m */,
				03BD61421F8C54E000FA5CB4 /* PLVVodNetworking.h */,
				03BD61431F8C54E000FA5CB4 /* PLVVodNetworking.m */,
				FAAFEE4827CCA7A2006D7362 /* PLVVodHttpDnsManager.h */,
				FAAFEE4927CCA7A2006D7362 /* PLVVodHttpDnsManager.m */,
				BFA0A4ED2A4AD8E700F0003E /* PLVPingUtil.h */,
				BFA0A4EE2A4AD90400F0003E /* PLVPingUtil.m */,
				043C80382D9BC09800DF5938 /* PLVVodNetworkRetryManager.h */,
				043C80392D9BC09800DF5938 /* PLVVodNetworkRetryManager.m */,
			);
			path = Network;
			sourceTree = "<group>";
		};
		043C80412DA61C5800DF5938 /* HttpDNS */ = {
			isa = PBXGroup;
			children = (
				043C80422DA61CFF00DF5938 /* PLVVodNSURLProtocol.h */,
				043C80432DA61CFF00DF5938 /* PLVVodNSURLProtocol.m */,
			);
			path = HttpDNS;
			sourceTree = "<group>";
		};
		043E2B9E2D94EA12007A0CCC /* Reachability */ = {
			isa = PBXGroup;
			children = (
				043E2B9C2D94EA12007A0CCC /* PLVVodReachability.h */,
				043E2B9D2D94EA12007A0CCC /* PLVVodReachability.m */,
			);
			path = Reachability;
			sourceTree = "<group>";
		};
		043E58A42278387E009481C2 /* Model */ = {
			isa = PBXGroup;
			children = (
				043E58A522783897009481C2 /* PLVVodElogModel.h */,
				043E58A622783897009481C2 /* PLVVodElogModel.m */,
				043EAC452282B87400C658D2 /* PLVVodJsonModel.h */,
				043EAC462282B87400C658D2 /* PLVVodJsonModel.m */,
				045B3CE4237A5075000F9610 /* PLVVodElogSeekModel.h */,
				045B3CE5237A5075000F9610 /* PLVVodElogSeekModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		04A72D7421721C7A008319D0 /* PolyvOpenDatabase */ = {
			isa = PBXGroup;
			children = (
				042277022170ABC9005B7EC9 /* PLVExtendDownloadInfo.mm */,
				042277042170ABC9005B7EC9 /* PLVExtendDownloadInfo.h */,
				04A72D6F21716893008319D0 /* PLVDownloadDBMgr.h */,
				04A72D7021716893008319D0 /* PLVDownloadDBMgr.mm */,
				04A72D7521721D12008319D0 /* PLVCusDownloadMgr.h */,
				04A72D7621721D12008319D0 /* PLVCusDownloadMgr.m */,
			);
			path = PolyvOpenDatabase;
			sourceTree = "<group>";
		};
		E7C3604132B0FB09FAB25830 /* Pods */ = {
			isa = PBXGroup;
			children = (
				7C34F9724C11B9241D2F3DA8 /* Pods-PLVVodSDK.debug.xcconfig */,
				A3D6C3668D3D39D3D183AC16 /* Pods-PLVVodSDK.release.xcconfig */,
				2E99F725F1A856D3FA4B3D58 /* Pods-vod-PLVVodSDK.debug.xcconfig */,
				258BF8D99F7B2BDED7276E57 /* Pods-vod-PLVVodSDK.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		0317F4E41FD63DBA00AE6BD8 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				0317F5311FD64FAB00AE6BD8 /* PLVVodAd.h in Headers */,
				043C80452DA61CFF00DF5938 /* PLVVodNSURLProtocol.h in Headers */,
				0012317B22E8596800CE6238 /* PLVVodPPT.h in Headers */,
				FA4F04C727FEC23B000EBBA0 /* PLVPictureInPictureManager.h in Headers */,
				045B3CE6237A5075000F9610 /* PLVVodElogSeekModel.h in Headers */,
				0317F5341FD64FAB00AE6BD8 /* PLVVodAdPlayerViewController.h in Headers */,
				0462F5942435D0E200B11D46 /* PLVVodPlayerViewController+Log.h in Headers */,
				0317F52A1FD64FAB00AE6BD8 /* PLVVodDownloadManager.h in Headers */,
				0317F52F1FD64FAB00AE6BD8 /* PLVVodPlayerSkinProtocol.h in Headers */,
				0317F5331FD64FAB00AE6BD8 /* PLVVodPlayerViewController.h in Headers */,
				0317F5391FD64FAB00AE6BD8 /* PLVVodSettings.h in Headers */,
				04EFF5342434F3A10034E5CB /* PLVVodPlayerViewController+internal.h in Headers */,
				0317F5271FD64FAB00AE6BD8 /* PLVVodDownloadInfo.h in Headers */,
				0317F5281FD64FAB00AE6BD8 /* PLVVodLocalVideo.h in Headers */,
				0462F5892435B7D500B11D46 /* PLVVodPlayerViewController+Switch.h in Headers */,
				0462F58D2435C94300B11D46 /* PLVVodPlayerViewController+Position.h in Headers */,
				0317F5301FD64FAB00AE6BD8 /* PLVVodVideo.h in Headers */,
				043E58AC227845C7009481C2 /* PLVVodUtil+Device.h in Headers */,
				0469B6AD2DEF10830090FC79 /* PLVVodPlayerViewController+Skin.h in Headers */,
				043E2BA02D94EA12007A0CCC /* PLVVodReachability.h in Headers */,
				0317F5381FD64FAB00AE6BD8 /* PLVVodConstans.h in Headers */,
				04EFF5402434F64E0034E5CB /* PLVVodPlayerViewController+URL.h in Headers */,
				0317F5321FD64FAB00AE6BD8 /* PLVVodExam.h in Headers */,
				04D340BF217110120000E989 /* PLVVodDownloadManager+Database.h in Headers */,
				04D495BC21885B4600EBBF9F /* PLVVodPlayerUtil.h in Headers */,
				0317F4EB1FD63DBA00AE6BD8 /* PLVVodSDK.h in Headers */,
				043E58A722783897009481C2 /* PLVVodElogModel.h in Headers */,
				FAAFEE4C27CCA7D1006D7362 /* PLVVodHttpDnsManager.h in Headers */,
				0469B6B12DEF1C880090FC79 /* PLVVodPlayerViewController+IJK.h in Headers */,
				0462F5912435C9FD00B11D46 /* PLVVodPlayerViewController+Capture.h in Headers */,
				04094CEC224A26BB0026EE6E /* PLVVodVideoParams.h in Headers */,
				0483C06D2118445C00F78CFD /* PLVVodHlsZipDownloader.h in Headers */,
				04CBEC992244F64F00A1D0D0 /* PLVVodAudioDownloader.h in Headers */,
				0462F58524359FB700B11D46 /* PLVVodPlayerViewController+Ad.h in Headers */,
				047B72C721B7D8C400DC1CE3 /* PLVVodDefines.h in Headers */,
				************************ /* PLVAccountModel.h in Headers */,
				04959ECD23069001009A17C6 /* PLVVodPPTMgr.h in Headers */,
				041837E9225C826F00F7F267 /* PLVFileUtils.h in Headers */,
				0413BAF320EC747900CFA59A /* PLVVodHlsTsDownloader.h in Headers */,
				04094CE72249DB140026EE6E /* PLVVodLocalVideo+Private.h in Headers */,
				0462F58124359AB600B11D46 /* PLVVodPlayerViewController+Reconnect.h in Headers */,
				03BE8B5A2057B86B00281DFF /* PLVVodReportManager.h in Headers */,
				042FD094248F6580008A2C01 /* PLVVodTsOneByOneDownloader.h in Headers */,
				032E913B2010B09F00DF2634 /* PLVVodDownloadInfoManager.h in Headers */,
				0317F5261FD64FAB00AE6BD8 /* PLVKVOController.h in Headers */,
				0317F5291FD64FAB00AE6BD8 /* PLVVodTsDownloadTask.h in Headers */,
				043C803C2D9BC09800DF5938 /* PLVVodNetworkRetryManager.h in Headers */,
				0469B6B82DEFE7360090FC79 /* PLVVodPlayerViewController+PIP.h in Headers */,
				0424244522E5D4730012FBD1 /* UIImageView+PLVGif.h in Headers */,
				0317F52B1FD64FAB00AE6BD8 /* PLVVodDownloader.h in Headers */,
				0317F5351FD64FAB00AE6BD8 /* PLVVodUtil.h in Headers */,
				BFA0A4EB2A4AD87A00F0003E /* PLVVodSimplePing.h in Headers */,
				0469B6B52DEFE3BB0090FC79 /* PLVVodPlayerViewController+Token.h in Headers */,
				FA4F04CA27FEC2C1000EBBA0 /* PLVPictureInPictureManager+Private.h in Headers */,
				0317F5361FD64FAB00AE6BD8 /* PLVVodHlsHelper.h in Headers */,
				043EAC472282B87400C658D2 /* PLVVodJsonModel.h in Headers */,
				0317F5371FD64FAB00AE6BD8 /* PLVVodNetworking.h in Headers */,
				0042B3292444778300F6C1E7 /* PLVVodPlayerLogo.h in Headers */,
				04516E3022FAE6CE00457634 /* PLVVodPPTDownloader.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		0317F4E61FD63DBA00AE6BD8 /* PLVVodSDK */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0317F4F21FD63DBA00AE6BD8 /* Build configuration list for PBXNativeTarget "PLVVodSDK" */;
			buildPhases = (
				14F9C95A4082712561309515 /* [CP] Check Pods Manifest.lock */,
				0317F4E21FD63DBA00AE6BD8 /* Sources */,
				0317F4E31FD63DBA00AE6BD8 /* Frameworks */,
				0317F4E41FD63DBA00AE6BD8 /* Headers */,
				0317F4E51FD63DBA00AE6BD8 /* Resources */,
				288400B3A24D7FFD73DD7FE6 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PLVVodSDK;
			productName = PLVVodSDK;
			productReference = 0317F4E71FD63DBA00AE6BD8 /* PLVVodSDK.framework */;
			productType = "com.apple.product-type.framework";
		};
		0366B0BF1F8CB9A700A5995B /* PolyvVodSDKTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0366B0C71F8CB9A700A5995B /* Build configuration list for PBXNativeTarget "PolyvVodSDKTests" */;
			buildPhases = (
				0366B0BC1F8CB9A700A5995B /* Sources */,
				0366B0BD1F8CB9A700A5995B /* Frameworks */,
				0366B0BE1F8CB9A700A5995B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0366B0C61F8CB9A700A5995B /* PBXTargetDependency */,
			);
			name = PolyvVodSDKTests;
			productName = PolyvVodSDKTests;
			productReference = 0366B0C01F8CB9A700A5995B /* PolyvVodSDKTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		03A15AB91F8B4A9100D5D3F5 /* _PolyvVodSDK */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 03A15AD01F8B4A9100D5D3F5 /* Build configuration list for PBXNativeTarget "_PolyvVodSDK" */;
			buildPhases = (
				03A15AB61F8B4A9100D5D3F5 /* Sources */,
				03A15AB71F8B4A9100D5D3F5 /* Frameworks */,
				03A15AB81F8B4A9100D5D3F5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = _PolyvVodSDK;
			productName = PolyvVodSDK;
			productReference = 03A15ABA1F8B4A9100D5D3F5 /* _PolyvVodSDK.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		03A15AB21F8B4A9100D5D3F5 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				CLASSPREFIX = PLVVod;
				LastUpgradeCheck = 0900;
				ORGANIZATIONNAME = POLYV;
				TargetAttributes = {
					0314ED701FE7ACC800BC0A54 = {
						CreatedOnToolsVersion = 9.2;
						ProvisioningStyle = Automatic;
					};
					0317F4E61FD63DBA00AE6BD8 = {
						CreatedOnToolsVersion = 9.1;
						ProvisioningStyle = Automatic;
					};
					0366B0BF1F8CB9A700A5995B = {
						CreatedOnToolsVersion = 9.0;
						ProvisioningStyle = Automatic;
						TestTargetID = 03A15AB91F8B4A9100D5D3F5;
					};
					03A15AB91F8B4A9100D5D3F5 = {
						CreatedOnToolsVersion = 9.0;
						ProvisioningStyle = Automatic;
						SystemCapabilities = {
							com.apple.BackgroundModes = {
								enabled = 1;
							};
						};
					};
				};
			};
			buildConfigurationList = 03A15AB51F8B4A9100D5D3F5 /* Build configuration list for PBXProject "PolyvVodSDK" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 03A15AB11F8B4A9100D5D3F5;
			productRefGroup = 03A15ABB1F8B4A9100D5D3F5 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				03A15AB91F8B4A9100D5D3F5 /* _PolyvVodSDK */,
				0366B0BF1F8CB9A700A5995B /* PolyvVodSDKTests */,
				0317F4E61FD63DBA00AE6BD8 /* PLVVodSDK */,
				0314ED701FE7ACC800BC0A54 /* BuildFramework */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0317F4E51FD63DBA00AE6BD8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0366B0BE1F8CB9A700A5995B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		03A15AB81F8B4A9100D5D3F5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0316EA991F8E50240019E17A /* Info.plist in Resources */,
				03A15ADB1F8B507C00D5D3F5 /* LaunchScreen.storyboard in Resources */,
				03A15AC71F8B4A9100D5D3F5 /* Assets.xcassets in Resources */,
				03A15AC51F8B4A9100D5D3F5 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0314ED741FE7ACD000BC0A54 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "sh ./BuildTool/build_framework.sh";
		};
		14F9C95A4082712561309515 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-vod-PLVVodSDK-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		288400B3A24D7FFD73DD7FE6 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-vod-PLVVodSDK/Pods-vod-PLVVodSDK-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/AliyunOSSiOS/AliyunOSSiOS_Privacy.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AliyunOSSiOS_Privacy.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-vod-PLVVodSDK/Pods-vod-PLVVodSDK-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0317F4E21FD63DBA00AE6BD8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				04E0C1C821227BCF0032F7A7 /* PLVVodSimpleDownloader.m in Sources */,
				0462F58624359FB700B11D46 /* PLVVodPlayerViewController+Ad.m in Sources */,
				04D495BE21885B4600EBBF9F /* PLVVodPlayerUtil.m in Sources */,
				0462F5922435C9FD00B11D46 /* PLVVodPlayerViewController+Capture.m in Sources */,
				0459F88C22F40D6200044144 /* PLVVodAttachMgr.m in Sources */,
				043E58AE227845C7009481C2 /* PLVVodUtil+Device.m in Sources */,
				0483C06C2118443E00F78CFD /* PLVVodHlsZipDownloader.m in Sources */,
				0413BAF220EC744000CFA59A /* PLVVodHlsTsDownloader.m in Sources */,
				0469B6AC2DEF10830090FC79 /* PLVVodPlayerViewController+Skin.m in Sources */,
				03BE8B592057B85000281DFF /* PLVVodReportManager.m in Sources */,
				0362A82C20117DB900573096 /* PLVVodDownloadInfoManager.mm in Sources */,
				0317F4F51FD64D1C00AE6BD8 /* PLVKVOController.m in Sources */,
				0042B32C24447B9100F6C1E7 /* PLVVodPlayerLogo.m in Sources */,
				BFA0A4EC2A4AD87A00F0003E /* PLVVodSimplePing.m in Sources */,
				04D340C1217110120000E989 /* PLVVodDownloadManager+Database.mm in Sources */,
				BFA0A4EF2A4AD90400F0003E /* PLVPingUtil.m in Sources */,
				043C80462DA61CFF00DF5938 /* PLVVodNSURLProtocol.m in Sources */,
				04094CEE224A26BB0026EE6E /* PLVVodVideoParams.m in Sources */,
				************************ /* PLVAccountModel.m in Sources */,
				043C803B2D9BC09800DF5938 /* PLVVodNetworkRetryManager.m in Sources */,
				042FD095248F6580008A2C01 /* PLVVodTsOneByOneDownloader.m in Sources */,
				04094CE92249DB140026EE6E /* PLVVodLocalVideo+Private.m in Sources */,
				04EFF5412434F64E0034E5CB /* PLVVodPlayerViewController+URL.m in Sources */,
				041837EB225C826F00F7F267 /* PLVFileUtils.m in Sources */,
				0462F58E2435C94300B11D46 /* PLVVodPlayerViewController+Position.m in Sources */,
				04EA21E7214605A300C663E2 /* PLVVodVideoJson.mm in Sources */,
				0317F4F71FD64D1C00AE6BD8 /* PLVVodDownloadInfo.mm in Sources */,
				0462F58224359AB600B11D46 /* PLVVodPlayerViewController+Reconnect.m in Sources */,
				0317F4F91FD64D1C00AE6BD8 /* PLVVodLocalVideo.m in Sources */,
				0317F4FB1FD64D1C00AE6BD8 /* PLVVodTsDownloadTask.m in Sources */,
				0462F5932435D09700B11D46 /* PLVVodPlayerViewController+Log.m in Sources */,
				0317F4FD1FD64D1C00AE6BD8 /* PLVVodDownloadManager.m in Sources */,
				04516E3222FAE6CE00457634 /* PLVVodPPTDownloader.m in Sources */,
				0317F4FF1FD64D1C00AE6BD8 /* PLVVodDownloader.m in Sources */,
				0469B6B92DEFE7360090FC79 /* PLVVodPlayerViewController+PIP.m in Sources */,
				043E2B9F2D94EA12007A0CCC /* PLVVodReachability.m in Sources */,
				0012317C22E8596800CE6238 /* PLVVodPPT.m in Sources */,
				0469B6B42DEFE3BB0090FC79 /* PLVVodPlayerViewController+Token.m in Sources */,
				045B3CE7237A5075000F9610 /* PLVVodElogSeekModel.m in Sources */,
				651DDBC32A2D7BE0005EF791 /* PLVVodQosLoadingTracer.m in Sources */,
				0469B6B02DEF1C880090FC79 /* PLVVodPlayerViewController+IJK.m in Sources */,
				043E58A922783897009481C2 /* PLVVodElogModel.m in Sources */,
				043EAC492282B87400C658D2 /* PLVVodJsonModel.m in Sources */,
				0317F5081FD64D1C00AE6BD8 /* PLVVodVideo.m in Sources */,
				0462F58A2435B7D500B11D46 /* PLVVodPlayerViewController+Switch.m in Sources */,
				0317F50A1FD64D1C00AE6BD8 /* PLVVodAd.m in Sources */,
				04959ECE23069006009A17C6 /* PLVVodPPTMgr.m in Sources */,
				0317F50C1FD64D1C00AE6BD8 /* PLVVodExam.m in Sources */,
				0424244722E5D4730012FBD1 /* UIImageView+PLVGif.m in Sources */,
				04CBEC9B2244F64F00A1D0D0 /* PLVVodAudioDownloader.m in Sources */,
				0317F50E1FD64D1C00AE6BD8 /* PLVVodPlayerViewController.m in Sources */,
				FAAFEE4B27CCA7CC006D7362 /* PLVVodHttpDnsManager.m in Sources */,
				0317F5101FD64D1C00AE6BD8 /* PLVVodAdPlayerViewController.m in Sources */,
				0317F5121FD64D1C00AE6BD8 /* PLVVodUtil.m in Sources */,
				0317F5141FD64D1C00AE6BD8 /* PLVVodHlsHelper.m in Sources */,
				FA4F04C827FEC23B000EBBA0 /* PLVPictureInPictureManager.m in Sources */,
				0317F5161FD64D1C00AE6BD8 /* PLVVodNetworking.m in Sources */,
				0317F5191FD64D1C00AE6BD8 /* PLVVodSettings.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0366B0BC1F8CB9A700A5995B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0366B0C31F8CB9A700A5995B /* PolyvVodSDKTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		03A15AB61F8B4A9100D5D3F5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				04A72D7721721D12008319D0 /* PLVCusDownloadMgr.m in Sources */,
				04CBEC9A2244F64F00A1D0D0 /* PLVVodAudioDownloader.m in Sources */,
				03BE8B582057B82E00281DFF /* PLVVodReportManager.m in Sources */,
				FAAFEE4A27CCA7A2006D7362 /* PLVVodHttpDnsManager.m in Sources */,
				04516E3122FAE6CE00457634 /* PLVVodPPTDownloader.m in Sources */,
				046CD7E420E9B4B4003DB372 /* PLVVodHlsTsDownloader.m in Sources */,
				043C80442DA61CFF00DF5938 /* PLVVodNSURLProtocol.m in Sources */,
				03A15AF31F8B82ED00D5D3F5 /* PLVVodVideo.m in Sources */,
				0316EAA01F8F17BE0019E17A /* PLVVodDownloadInfo.mm in Sources */,
				************************ /* PLVAccountModel.m in Sources */,
				03C8BC991F94B7B0009FA08A /* PLVVodDownloaderExpTableViewController.m in Sources */,
				656870D82A29C5000044FEFE /* PLVVodQosLoadingTracer.m in Sources */,
				0316EA981F8E50240019E17A /* PLVKVOController.m in Sources */,
				04094CED224A26BB0026EE6E /* PLVVodVideoParams.m in Sources */,
				03A15ADE1F8B55A600D5D3F5 /* PLVVodUtil.m in Sources */,
				043EAC482282B87400C658D2 /* PLVVodJsonModel.m in Sources */,
				03C8BC941F945D1F009FA08A /* PLVVodTsDownloadTask.m in Sources */,
				0316EAA81F8F5E130019E17A /* Foundation+Log.m in Sources */,
				03A15AC21F8B4A9100D5D3F5 /* ViewController.m in Sources */,
				042277052170ABC9005B7EC9 /* PLVExtendDownloadInfo.mm in Sources */,
				03A15AE61F8B6CB800D5D3F5 /* PLVVodPlayerViewController.m in Sources */,
				043C803A2D9BC09800DF5938 /* PLVVodNetworkRetryManager.m in Sources */,
				0424244622E5D4730012FBD1 /* UIImageView+PLVGif.m in Sources */,
				0042B32B24447B9000F6C1E7 /* PLVVodPlayerLogo.m in Sources */,
				03A15ACD1F8B4A9100D5D3F5 /* main.m in Sources */,
				04094CE82249DB140026EE6E /* PLVVodLocalVideo+Private.m in Sources */,
				04EA21E6214605A300C663E2 /* PLVVodVideoJson.mm in Sources */,
				03F2DFE41FB29E7A0058D858 /* PLVVodAdPlayerViewController.m in Sources */,
				04A72D7121716893008319D0 /* PLVDownloadDBMgr.mm in Sources */,
				04383E5E211827E4007B4C56 /* PLVVodHlsZipDownloader.m in Sources */,
				03A15AF61F8B858D00D5D3F5 /* PLVVodAd.m in Sources */,
				0316EAA61F8F19F20019E17A /* PLVVodLocalVideo.m in Sources */,
				0316EAA31F8F18970019E17A /* PLVVodDownloader.m in Sources */,
				0316EAAB1F8F61A20019E17A /* PLVVodHlsHelper.m in Sources */,
				0012317D22E8596E00CE6238 /* PLVVodPPT.m in Sources */,
				041837EA225C826F00F7F267 /* PLVFileUtils.m in Sources */,
				032E91392010A30E00DF2634 /* PLVVodDownloadInfoManager.mm in Sources */,
				0366B0BB1F8C9ACC00A5995B /* PLVVodExam.m in Sources */,
				03A15ABF1F8B4A9100D5D3F5 /* AppDelegate.m in Sources */,
				0498B05C212158AD00C22955 /* PLVVodSimpleDownloader.m in Sources */,
				03A15AD91F8B4E3B00D5D3F5 /* PLVVodSettings.m in Sources */,
				043E58AD227845C7009481C2 /* PLVVodUtil+Device.m in Sources */,
				03BD61441F8C54E000FA5CB4 /* PLVVodNetworking.m in Sources */,
				DA0088E520B6B294006CEBBE /* PlayViewController.m in Sources */,
				04D495BD21885B4600EBBF9F /* PLVVodPlayerUtil.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0366B0C61F8CB9A700A5995B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 03A15AB91F8B4A9100D5D3F5 /* _PolyvVodSDK */;
			targetProxy = 0366B0C51F8CB9A700A5995B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		03A15AC31F8B4A9100D5D3F5 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				03A15AC41F8B4A9100D5D3F5 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		0314ED721FE7ACC800BC0A54 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		0314ED731FE7ACC800BC0A54 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		0317F4F01FD63DBA00AE6BD8 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2E99F725F1A856D3FA4B3D58 /* Pods-vod-PLVVodSDK.debug.xcconfig */;
			buildSettings = {
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)$(LOCAL_LIBRARY_DIR)",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${SRCROOT}/../Pods/Headers/Public\"/**",
				);
				INFOPLIST_FILE = FrameworkBuild/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"$PODS_CONFIGURATION_BUILD_DIR\"",
					"\"$PODS_CONFIGURATION_BUILD_DIR/FDStackView\"",
					"\"$PODS_CONFIGURATION_BUILD_DIR/PLVMarquee\"",
					"\"$PODS_CONFIGURATION_BUILD_DIR/PLVTimer\"",
					"\"$PODS_CONFIGURATION_BUILD_DIR/PLVVodDanmu\"",
					"\"$PODS_CONFIGURATION_BUILD_DIR/SSZipArchive\"",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 2.24.0;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_CFLAGS = "-fembed-bitcode";
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = net.polyv.PLVVodSDK;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		0317F4F11FD63DBA00AE6BD8 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 258BF8D99F7B2BDED7276E57 /* Pods-vod-PLVVodSDK.release.xcconfig */;
			buildSettings = {
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)$(LOCAL_LIBRARY_DIR)",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${SRCROOT}/../Pods/Headers/Public\"/**",
				);
				INFOPLIST_FILE = FrameworkBuild/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"$PODS_CONFIGURATION_BUILD_DIR\"/**",
					"\"$PODS_CONFIGURATION_BUILD_DIR/FDStackView\"",
					"\"$PODS_CONFIGURATION_BUILD_DIR/PLVMarquee\"",
					"\"$PODS_CONFIGURATION_BUILD_DIR/PLVTimer\"",
					"\"$PODS_CONFIGURATION_BUILD_DIR/PLVVodDanmu\"",
					"\"$PODS_CONFIGURATION_BUILD_DIR/SSZipArchive\"",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 2.24.0;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_CFLAGS = "-fembed-bitcode";
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = net.polyv.PLVVodSDK;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		0366B0C81F8CB9A700A5995B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				INFOPLIST_FILE = PolyvVodSDKTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = POLYV.PolyvVodSDKTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PolyvVodSDK.app/PolyvVodSDK";
			};
			name = Debug;
		};
		0366B0C91F8CB9A700A5995B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				INFOPLIST_FILE = PolyvVodSDKTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = POLYV.PolyvVodSDKTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PolyvVodSDK.app/PolyvVodSDK";
			};
			name = Release;
		};
		03A15ACE1F8B4A9100D5D3F5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		03A15ACF1F8B4A9100D5D3F5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		03A15AD11F8B4A9100D5D3F5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				DEBUG_ACTIVITY_MODE = "";
				"DEBUG_ACTIVITY_MODE[sdk=iphonesimulator*]" = disable;
				DEVELOPMENT_TEAM = 8BMZBNN3Q5;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)$(LOCAL_LIBRARY_DIR)",
				);
				INFOPLIST_FILE = PolyvVodSDK/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = "2.16.7-abn";
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = POLYV.PolyvVodSDK;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		03A15AD21F8B4A9100D5D3F5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				DEBUG_ACTIVITY_MODE = "";
				DEVELOPMENT_TEAM = 8BMZBNN3Q5;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)$(LOCAL_LIBRARY_DIR)",
				);
				INFOPLIST_FILE = PolyvVodSDK/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = "2.16.7-abn";
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = POLYV.PolyvVodSDK;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0314ED711FE7ACC800BC0A54 /* Build configuration list for PBXAggregateTarget "BuildFramework" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0314ED721FE7ACC800BC0A54 /* Debug */,
				0314ED731FE7ACC800BC0A54 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0317F4F21FD63DBA00AE6BD8 /* Build configuration list for PBXNativeTarget "PLVVodSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0317F4F01FD63DBA00AE6BD8 /* Debug */,
				0317F4F11FD63DBA00AE6BD8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0366B0C71F8CB9A700A5995B /* Build configuration list for PBXNativeTarget "PolyvVodSDKTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0366B0C81F8CB9A700A5995B /* Debug */,
				0366B0C91F8CB9A700A5995B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		03A15AB51F8B4A9100D5D3F5 /* Build configuration list for PBXProject "PolyvVodSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				03A15ACE1F8B4A9100D5D3F5 /* Debug */,
				03A15ACF1F8B4A9100D5D3F5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		03A15AD01F8B4A9100D5D3F5 /* Build configuration list for PBXNativeTarget "_PolyvVodSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				03A15AD11F8B4A9100D5D3F5 /* Debug */,
				03A15AD21F8B4A9100D5D3F5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 03A15AB21F8B4A9100D5D3F5 /* Project object */;
}
