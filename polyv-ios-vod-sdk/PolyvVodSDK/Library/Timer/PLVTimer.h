//
//  PLVTimer.h
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/11.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface PLVTimer : NSObject

/// 当前时间
@property (nonatomic, assign) NSTimeInterval currentInterval;

/// 快速创建倒计时对象
+ (instancetype)countdownWithSecond:(long)second countBlock:(void (^)(long remainSecond))countBlock;

/// 快速创建每秒操作定时器对象
+ (instancetype)repeatWithBlock:(void (^)(void))repeatBlock;
+ (instancetype)repeatWithInterval:(double)interval repeatBlock:(void (^)(void))repeatBlock;

/// 快速创建自定义定时器
+ (instancetype)timerWithInterval:(double)interval dispatchQueue:(dispatch_queue_t)queue countdownSecond:(long)second countBlock:(void (^)(long remainSecond))countBlock;

/// 取消定时器及其操作
- (void)cancel;

@end
