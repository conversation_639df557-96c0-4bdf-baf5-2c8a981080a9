//
//  PLVVodDanmu.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/11/7.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodDanmu.h"
#import "PLVVodNetworking.h"
#import "PLVVodUtil.h"

@implementation PLVVodDanmu

/// 初始化
- (instancetype)init {
	if (self = [super init]) {
		_content = @"";
		_time = 0.0;
		_colorHex = 0;
		_fontSize = 12;
	}
	return self;
}

- (NSString *)description {
	NSMutableString *description = [super.description stringByAppendingString:@":\n"].mutableCopy;
	[description appendFormat:@" content: %@;\n", _content];
	[description appendFormat:@" time: %f;\n", _time];
	[description appendFormat:@" colorHex: %zx;\n", _colorHex];
	[description appendFormat:@" fontSize: %d;\n", _fontSize];
	[description appendFormat:@" mode: %zd;\n", _mode];
	return description;
}

@end
