//
//  PolyvVodSDKTests.m
//  PolyvVodSDKTests
//
//  Created by BqLin on 2017/10/10.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import <XCTest/XCTest.h>
#import "PLVVodVideo.h"
#import "PLVVodSettings.h"
#import "PLVVodNetworking.h"

@interface PolyvVodSDKTests : XCTestCase

@end

@implementation PolyvVodSDKTests

- (void)setUp {
    [super setUp];
    // Put setup code here. This method is called before the invocation of each test method in the class.
	
//    NSString *sdkKey = @"v4yoqNIHwZ69WNbOTI4rzDRHbwjUYsh14V1Czv7CNhwRE3EGBEleaezLNZms14CKhxu+KB+OPH341zknQ5+7gE5UZnz4u5V0jP+SCO9kaRwthY4UyvZ3ClHgnSBEZoTCkwrYQ+sgLVIRhjo2y+uZIQ==";
//    PLVVodSettings *settings = [PLVVodSettings settingsWithConfigString:sdkKey key:@"VXtlHmwfS2oYm0CZ" iv:@"2u9gDPKdX6GyQJKU"];
//    NSLog(@"settings = %@", settings);
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}

- (void)testExample {
    // This is an example of a functional test case.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
	
//	[PLVVodVideo requestVideoWithVid:@"f46ead66dec30461646947dea668c1c1_f" completion:^(PLVVodVideo *video, NSError *error) {
//		NSLog(@"video: %@", video);
//	}];
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

@end
