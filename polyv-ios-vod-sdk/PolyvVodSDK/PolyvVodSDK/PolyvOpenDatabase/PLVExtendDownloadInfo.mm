//
//  PLVExtendDownloadInfo.mm
//  _PolyvVodSDK
//
//  Created by mac on 2018/10/12.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import "PLVExtendDownloadInfo.h"
#import <PLVFDB/PLVFDatabase.h>

// 表名不能修改，否则无法访问sdk中已存在数据
#define kSDKDownloadListTable @"download_list"

#import <PLVFDB/PLVFDatabase.h>

@interface PLVExtendDownloadInfo () <PLVFDatabaseProtocol>

@end

@implementation PLVExtendDownloadInfo

+ (NSString *)tableName{
    
    return kSDKDownloadListTable;
}

#pragma mark PLVFDatabaseProtocol

+ (nonnull NSString *)primaryKey {
    return @"vid";
}

+ (nonnull NSArray<NSString *> *)propertyKeys {
    return @[
// SDK 中原有字段，这里不能修改，否则无法同步数据 -- start
        @"vid",
        @"quality",
        @"progress",
        @"state",
        @"downloadId",
        @"snapshot",
        @"title",
        @"fileSize",
        @"duration",
// SDK 中原有字段，这里不能修改，否则无法同步数据 -- end

// TODO：以下可拓展用户自定义字段
        @"Cusduration",
        @"CusfileSize",
    ];
}

@end
