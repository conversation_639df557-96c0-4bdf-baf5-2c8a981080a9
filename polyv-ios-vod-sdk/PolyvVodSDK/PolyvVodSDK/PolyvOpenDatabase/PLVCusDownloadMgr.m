//
//  PLVCusDownloadMgr.m
//  _PolyvVodSDK
//
//  Created by mac on 2018/10/13.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import "PLVCusDownloadMgr.h"
#import "PLVVodDownloadManager.h"
#import "PLVDownloadDBMgr.h"

@interface PLVCusDownloadMgr()

@property (nonatomic, strong) NSMutableDictionary<NSString *, PLVVodDownloadInfo *> *downloadDict;

@end

static PLVCusDownloadMgr *instance = nil;

@implementation PLVCusDownloadMgr

+ (PLVCusDownloadMgr *)shareInstance{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[PLVCusDownloadMgr alloc] init];
        
        [instance commonInit];
    });
    
    return instance;
}

- (void)commonInit{
    _downloadDict = [[NSMutableDictionary alloc] init];
}

#pragma public --

/// 加入视频到下载队列
- (void)downloadVideoWithVid:(NSString *)vid complete:(void (^)(PLVVodDownloadInfo * ))complete{
    
    if ([self.downloadDict objectForKey:vid]) return;
    
    [PLVVodVideo requestVideoPriorityCacheWithVid:vid completion:^(PLVVodVideo *video, NSError *error) {
        
        if (video){
            PLVVodDownloadInfo *downloadInfo = [[PLVVodDownloadManager sharedManager] downloadVideo:video];
            if (downloadInfo){
                [self.downloadDict setObject:downloadInfo forKey:video.vid];
                
                if (complete){
                    complete (downloadInfo);
                }
            }
            else{
                complete (nil);
            }
        }
        else{
            complete (nil);
        }
    }];
}

/// 更新下载进度/ 下载状态
- (void)updateVideoWithProgess:(float)progress downloadState:(PLVVodDownloadState )downloadState{
    
}

/// 获取下载队列，返回值可用于下载进度/状态 回调
- (NSArray<PLVVodDownloadInfo *> *)requestDownloadingList{
    return [self.downloadDict allValues];
}

/// 获取已缓存视频列表信息，用于UI展示
- (NSArray<PLVExtendDownloadInfo *> *)getCachedVideoList{
    NSArray *array = [[PLVDownloadDBMgr shareInstance] downloadCachedListFromDatabase];
    
    return array;
}

/// 获取所有未缓存完成视频列表信息，用于UI展示
- (NSArray<PLVExtendDownloadInfo *> *)getUnCacheVideoList{
    NSArray *array = [[PLVDownloadDBMgr shareInstance] downloadCachingListFromDatabase];
    
    return array;
}

/// 删除下载中任务
- (void)removeDownloadTaskWithVid:(NSString *)vid{
    
    NSError *error = nil;
    [[PLVVodDownloadManager sharedManager] removeDownloadWithVid:vid error:&error];
    
    [self.downloadDict removeObjectForKey:vid];
    
    [[PLVDownloadDBMgr shareInstance] removeDownloadInfoWithVid:vid];
}

/// 删除所有下载任务
- (void)removeAllDownloadTaskWithComplete:(void (^)(void * ))complete{
    [[PLVVodDownloadManager sharedManager] removeAllDownloadWithComplete:^(void *result) {
       
        //
        [[self.downloadDict allValues] enumerateObjectsUsingBlock:^(PLVVodDownloadInfo * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            
            [[PLVDownloadDBMgr shareInstance] removeDownloadInfoWithVid:obj.vid];
        }];
        
        //
        [self.downloadDict removeAllObjects];
    }];
}

/// 删除已缓存视频
+ (void)removeCachedVideoWithVid:(NSString *)vid error:(NSError **)error{
    [PLVVodDownloadManager removeVideoWithVid:vid error:error];
    
    //
    [[PLVDownloadDBMgr shareInstance] removeDownloadInfoWithVid:vid];
}


@end
