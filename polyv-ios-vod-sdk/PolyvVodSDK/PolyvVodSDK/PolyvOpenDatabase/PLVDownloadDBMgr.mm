//
//  PLVDownloadDBMgr.m
//  _PolyvVodSDK
//
//  Created by mac on 2018/10/13.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import "PLVDownloadDBMgr.h"
#import "PLVVodDownloadManager+Database.h"

#define PLVUserDownloadDBDir   @"net.polyv.download.vod"
#define PLVUserDownloadDBName  @"vod_cache.db"

#define PLVVodSDKDBName  @"vod_cache.db"


@interface PLVDownloadDBMgr()

@property (nonatomic, strong) WCTDatabase *database;
@property (nonatomic, strong) NSString *dbPath;
@property (nonatomic, strong) NSString *userDbFile;

@property (nonatomic, assign) BOOL isSyncSDKData;  // 默认NO

@end

static PLVDownloadDBMgr *instance = nil;

@implementation PLVDownloadDBMgr

+ (PLVDownloadDBMgr *)shareInstance{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[PLVDownloadDBMgr alloc] init];
        
        [instance commonInit];
    });
    
    return instance;
}

- (WCTDatabase *)database{
    if (!_database){
        
        _database = [[WCTDatabase alloc] initWithPath:self.userDbFile];
    }
    
    return _database;
}

- (void)commonInit{
    
    // 新集成用户无需同步sdk 数据
    self.isSyncSDKData = YES;
    if (self.isSyncSDKData){
        
        [self syncSDKDatabase];
        
        // 扩展SDK 表中字段
        [self expandSDKDownloadTable];
        
        // 同步扩展字段的值
        [self syncSDKDownloadTable];
    }
    else{
        
        // 客户App 保存数据库目录
        NSString *documentPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
        self.dbPath = [documentPath stringByAppendingPathComponent:PLVUserDownloadDBDir];
        
        self.userDbFile = [self.dbPath stringByAppendingPathComponent:PLVUserDownloadDBName];
    }
    
    [self database];
   
    // 初始化其他自定义数据库表
    [self initCustomTable];
}

- (void)initCustomTable{
    //
    // TODO: 可以建立起他数据库表
}

#pragma mark -- Database Sync

- (void)syncSDKDatabase{
    
    // 此处命名需要与sdk 中数据库文件同名
#warning -- 要与sdk 数据库文件同名
    NSString *dbName = PLVVodSDKDBName;
    
    // 客户App 保存数据库目录
    NSString *documentPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    self.dbPath = [documentPath stringByAppendingPathComponent:PLVUserDownloadDBDir];
    
    // TODO: 如果需要数据迁移，拷贝sdk 中数据库文件到目的路径dbPath
    self.userDbFile = [self.dbPath stringByAppendingPathComponent:dbName];
    if ([self.class fileExist:self.userDbFile]){
        // 已经迁移过数据
        
    }
    else{
        // 是否需要迁移同步数据
//        NSString *sdkDbfile = [PLVVodDownloadManager sdkDBPath];
        NSString *sdkDbfile = @"";
        BOOL fileExist = [[NSFileManager defaultManager] fileExistsAtPath:sdkDbfile];
        if (fileExist){
            
            // 需要迁移
            NSError *error;
            NSString *userDBPath = self.dbPath;
            // 创建文件夹
            if (![self.class fileExist:userDBPath]){
                [[NSFileManager defaultManager] createDirectoryAtPath:userDBPath withIntermediateDirectories:NO attributes:nil error:nil];
            }
            
            NSString *sdkDbFileDirectory = [sdkDbfile stringByDeletingLastPathComponent];
            
            // 此处需要复制 .db .wal 文件，数据库才能使用
            [self.class copyFileFromPath:sdkDbFileDirectory toPath:userDBPath];
            
            NSLog(@"----%@ --", error);
            NSLog(@"\n SDKDB:--%@\n", sdkDbfile);
            NSLog(@"\n USERDB:--%@\n", self.dbPath);
        }
    }
}

- (void)syncSDKDownloadTable{
    //
    // TODO: 将自定义数据库中的值更新到 PLVVodSDKDBName 数据库中
    
}

- (void)expandSDKDownloadTable{
    
    // 扩展sdk，download_list 表中字段
    [self.database createTableAndIndexesOfName:[PLVExtendDownloadInfo tableName] withClass:[PLVExtendDownloadInfo class]];
}

#pragma mark -- DB Operate

- (BOOL)insertOrUpdateDatabaseWithCusInfo:(PLVExtendDownloadInfo *)cusInfo{
    
    WCTDatabase *database = self.database;
    //    WCTPropertyList list = WCTPropertyList();
    //    list.push_front(PLVExtendDownloadInfo.Cusduration);
    //    list.push_front(PLVExtendDownloadInfo.CusfileSize);
    
    [database beginTransaction];
    
    BOOL result = [database insertOrReplaceObject:cusInfo onProperties:PLVExtendDownloadInfo.AllProperties into:[PLVExtendDownloadInfo tableName]];
    
    if (result) {
        [database commitTransaction];
    } else {
        [database rollbackTransaction];
    }
    return result;
}

- (PLVExtendDownloadInfo *)downloadInfoWithVid:(NSString *)vid{
    NSArray<PLVExtendDownloadInfo *> *dbDownloadList = [self.database getObjectsOfClass:[PLVExtendDownloadInfo class]
                                                                       fromTable:[PLVExtendDownloadInfo tableName]
                                                                           where:PLVExtendDownloadInfo.vid==vid];
    
    PLVExtendDownloadInfo *info = [dbDownloadList firstObject];
    
    return info;
}

- (NSArray<PLVExtendDownloadInfo *> *)downloadAllListFromDatabase{
    NSArray<PLVExtendDownloadInfo *> *dbDownloadlist = [self.database getAllObjectsOfClass:[PLVExtendDownloadInfo class]
                                                                          fromTable:[PLVExtendDownloadInfo tableName]];
    
    return dbDownloadlist;
}

- (NSArray<PLVExtendDownloadInfo *> *)downloadCachedListFromDatabase{
    NSArray<PLVExtendDownloadInfo *> *dbDownloadList = [self.database getObjectsOfClass:[PLVExtendDownloadInfo class]
                                                                       fromTable:[PLVExtendDownloadInfo tableName]
                                                                           where:PLVExtendDownloadInfo.state==PLVVodDownloadStateSuccess];
    
    return dbDownloadList;
}

- (NSArray<PLVExtendDownloadInfo *> *)downloadCachingListFromDatabase{
    NSArray<PLVExtendDownloadInfo *> *dbDownloadList = [self.database getObjectsOfClass:[PLVExtendDownloadInfo class]
                                                                       fromTable:[PLVExtendDownloadInfo tableName]
                                                                           where:PLVExtendDownloadInfo.state!=PLVVodDownloadStateSuccess];
    
    return dbDownloadList;
}

- (BOOL)removeDownloadInfoWithVid:(NSString *)vid{
    return [self.database deleteObjectsFromTable:[PLVExtendDownloadInfo class] where:PLVExtendDownloadInfo.vid==vid];
}

#pragma mark -- Utils
+ (BOOL)fileExist:(NSString *)filePath{
    return [[NSFileManager defaultManager] fileExistsAtPath:filePath];
}

+ (void)copyFileFromPath:(NSString *)sourcePath toPath:(NSString *)toPath
{
    NSFileManager *fileManager = [[NSFileManager alloc] init];
    NSArray* array = [fileManager contentsOfDirectoryAtPath:sourcePath error:nil];
    
    for(int i = 0; i<[array count]; i++)
    {
        NSString *fullPath = [sourcePath stringByAppendingPathComponent:[array objectAtIndex:i]];
        
        NSString *fullToPath = [toPath stringByAppendingPathComponent:[array objectAtIndex:i]];
        
        NSLog(@"%@",fullPath);
        
        NSLog(@"%@",fullToPath);
        
        //判断是不是文件夹
        
        BOOL isFolder = NO;
        
        //判断是不是存在路径 并且是不是文件夹
        
        BOOL isExist = [fileManager fileExistsAtPath:fullPath isDirectory:&isFolder];
        
        if (isExist)
        {
            NSError *err = nil;
            
            [[NSFileManager defaultManager] copyItemAtPath:fullPath toPath:fullToPath error:&err];
            
            if (isFolder)
            {
                [self copyFileFromPath:fullPath toPath:fullToPath];
                NSLog(@"------- isFolder -------");
            }
        }
    }
}


@end
