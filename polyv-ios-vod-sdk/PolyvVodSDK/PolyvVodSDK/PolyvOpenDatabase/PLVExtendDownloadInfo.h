//
//  PLVExtendDownloadInfo.h
//  _PolyvVodSDK
//
//  Created by mac on 2018/10/12.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>


@interface PLVExtendDownloadInfo : NSObject

@property(nonatomic, copy) NSString *vid;
@property(nonatomic, assign) NSInteger quality;
@property(nonatomic, assign) float progress;
@property(nonatomic, assign) NSInteger state;
@property(nonatomic, assign) NSInteger downloadId;
@property(nonatomic, strong) NSString *snapshot;
@property(nonatomic, strong) NSString *title;
@property(nonatomic, assign) NSInteger fileSize;
@property(nonatomic, assign) NSInteger duration;

@property(nonatomic, assign) NSInteger Cusduration;
@property(nonatomic, assign) NSInteger CusfileSize;


+ (NSString *)tableName;

@end
