//
//  PLVCusDownloadMgr.h
//  _PolyvVodSDK
//
//  Created by mac on 2018/10/13.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PLVVodDownloadInfo.h"

NS_ASSUME_NONNULL_BEGIN

@class PLVVodDownloadInfo;
@class PLVExtendDownloadInfo;     // 扩展sdk表结构的数据类型

@interface PLVCusDownloadMgr : NSObject

+ (PLVCusDownloadMgr *)shareInstance;

/// 加入下载队列
- (void)downloadVideoWithVid:(NSString *)vid complete:(void (^)(PLVVodDownloadInfo *downloadInfo))complete;

/// 更新下载进度/ 下载状态
- (void)updateVideoWithProgess:(float)progress downloadState:(PLVVodDownloadState )downloadState;

/// 获取下载队列，返回值可用于下载进度/状态 回调
- (NSArray<PLVVodDownloadInfo *> *)requestDownloadingList;

/// 获取已缓存视频列表信息，用于UI展示
- (NSArray<PLVExtendDownloadInfo *> *)getCachedVideoList;

/// 获取所有未缓存完成视频列表信息，用于UI展示
- (NSArray<PLVExtendDownloadInfo *> *)getUnCacheVideoList;

/// 删除下载中任务
- (void)removeDownloadTaskWithVid:(NSString *)vid;

/// 删除所有下载任务
- (void)removeAllDownloadTaskWithComplete:(void(^)(void *result))complete;

/// 删除已缓存视频
+ (void)removeCachedVideoWithVid:(NSString *)vid error:(NSError **)error;

@end

NS_ASSUME_NONNULL_END
