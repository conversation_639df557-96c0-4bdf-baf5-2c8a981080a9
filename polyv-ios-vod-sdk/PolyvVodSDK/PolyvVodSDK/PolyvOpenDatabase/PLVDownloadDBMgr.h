//
//  PLVDownloadDBMgr.h
//  _PolyvVodSDK
//
//  Created by mac on 2018/10/13.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class PLVExtendDownloadInfo;

@interface PLVDownloadDBMgr : NSObject

+ (PLVDownloadDBMgr *)shareInstance;

///
- (BOOL)insertOrUpdateDatabaseWithCusInfo:(PLVExtendDownloadInfo *)cusInfo;

///
- (PLVExtendDownloadInfo *)downloadInfoWithVid:(NSString *)vid;

///
- (NSArray<PLVExtendDownloadInfo *> *)downloadAllListFromDatabase;

///
- (NSArray<PLVExtendDownloadInfo *> *)downloadCachedListFromDatabase;

///
- (NSArray<PLVExtendDownloadInfo *> *)downloadCachingListFromDatabase;

///
- (BOOL)removeDownloadInfoWithVid:(NSString *)vid;

///


@end

NS_ASSUME_NONNULL_END
