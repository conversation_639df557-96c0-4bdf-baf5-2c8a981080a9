//
//  PLVFileUtils.h
//  PolyvVodSDK
//
//  Created by mac on 2019/4/9.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface PLVFileUtils : NSObject

+ (NSString *)homeDir;

+ (NSString *)documentsDir;

+ (NSString *)libraryDir;

+ (NSString *)cacheDir;

// 获取多账号缓存目录
+ (NSString *)getCacheDirectoryWithAccountId:(NSString *)accountId;

// 获取视频文件目录
+ (NSString *)getVideoDirectoryWithAccoutId:(NSString *)accountId;

// 获取数据库文件目录
+ (NSString *)getDbDirectoryWithAccountId:(NSString *)accountId;

@end

NS_ASSUME_NONNULL_END

