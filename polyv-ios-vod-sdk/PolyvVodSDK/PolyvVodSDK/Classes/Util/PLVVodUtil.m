//
//  PLVVodUtil.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/9.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodUtil.h"

#import <CommonCrypto/CommonDigest.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <net/if.h>
#include <ifaddrs.h>

#import <sys/utsname.h>

#import "PLVVodSettings.h"
#import "PLVVodDownloadManager.h"
#import "PLVVodLocalVideo.h"
#import "PLVVodHlsHelper.h"

void PLVVodInfoLog(NSString *format, ...) {
	
}

NSDictionary *PLVVodErrorInfoWithCode(PLVVodErrorCode code) {
	NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
	NSString *errDescription = @"";
	NSURL *errURL = nil;
	NSString *errPath = nil;
	NSString *errReason = nil;
	NSString *errHelp = nil;
	switch (code) {
		case account_video_illegal:{
			errDescription = @"视频或账号不合法";
			errHelp = @"视频或账号不合法，请向管理员反馈";
		}break;
		case account_flow_out:{
			errDescription = @"账户套餐流量不足";
			errHelp = @"流量超标，请向管理员反馈";
		}break;
		case account_time_out:{
			errDescription = @"账户套餐已过期";
			errHelp = @"账号过期，请向管理员反馈";
		}break;
		case video_unmatch_account: {
			errDescription = @"视频与账号不匹配";
			errHelp = @"视频与账号不匹配，请向管理员反馈";
		}break;
		case account_encode_key_illegal:{
			errDescription = @"加密秘钥不合法";
			errHelp = @"播放秘钥不合法，请向管理员反馈";
		}break;
		case account_encode_iv_illegal:{
			errDescription = @"加密向量不合法";
			errHelp = @"播放加密向量不合法，请向管理员反馈";
		}break;
        case video_universally_illegal:{
            errDescription = @"视频状态不合法";
            errHelp = @"视频信息不合法，请向管理员反馈";
        }break;
		case video_status_illegal:{
			errDescription = @"视频状态不合法";
            errHelp = @"视频状态不合法，请向管理员反馈";
		}break;
			
		case playback_token_fetch_error:{
			errDescription = @"播放令牌请求失败";
            errHelp = @"播放令牌请求失败,可尝试切换线路";
		}break;
		case video_type_unknown:{
			errDescription = @"未知视频对象";
            errHelp = @"视频播放参数类型错误，请尝试重新播放，或向管理员反馈";
		}break;
		case video_not_found:{
			errDescription = @"无法获取视频";
            errHelp = @"无法找到视频，请尝试重新播放/下载，或向管理员反馈";
		}break;
        case pdx_fetch_error:{
            errDescription = @"pdx请求失败";
            errHelp = @"PDX文件请求失败,可尝试切换线路";
        }break;
        case playback_videojson_fetch_error:{
            errDescription = @"videojson请求失败";
            errHelp = @"VideoJson文件请求失败,可尝试切换线路";
        }break;
		case teaser_type_illegal:{
			errDescription = @"片头格式不合法";
            errHelp = @"不支持的片头视频格式，请向管理员反馈";
		}break;
		case playback_duration_illegal:{
			errDescription = @"视频时长不合法";
            errHelp = @"视频时长错误，请向管理员反馈";
		}break;
		case ad_type_illegal:{
			errDescription = @"广告类型不合法";
            errHelp = @"广告类型不支持，请向管理员反馈";
		}break;
			
		case downloader_create_error:{
			errDescription = @"下载器创建失败";
			errHelp = @"下载对象创建失败，请尝试重新下载，或向管理员反馈";
		}break;
		case download_task_create_error:{
			errDescription = @"下载任务创建失败";
            errHelp = @"下载任务创建失败，请尝试重新下载，或向管理员反馈";
		}break;
		case download_error:{
			errDescription = @"下载错误";
            errHelp = @"下载失败，请尝试重新下载，或向管理员反馈";
		}break;
		case m3u8_write_error:{
			errDescription = @"m3u8写入失败";
            errHelp = @"m3u8文件写入失败，请尝试重新下载，或向管理员反馈";
		}break;
		case key_write_error:{
			errDescription = @"key写入失败";
            errHelp = @"key文件写入失败，请尝试重新下载，或向管理员反馈";
		}break;
		case token_write_error:{
			errDescription = @"token写入失败";
            errHelp = @"token文件写入失败，请尝试重新下载，或向管理员反馈";
		}break;
		case ts_path_fix_error:{
			errDescription = @"切片路径修复失败";
            errHelp = @"m3u8文件路径修复失败，请尝试重新下载，或向管理员反馈";
		}break;
		case unzip_error:{
			errDescription = @"解压失败";
            errHelp = @"下载文件解压失败，请尝试重新下载，或向管理员反馈";
		}break;
		case download_task_not_found:{
			errDescription = @"下载任务不存在";
            errHelp = @"下载任务丢失，请尝试重新下载，或向管理员反馈";
		}break;
			
		case argument_illegal:{
			errDescription = @"参数不合法";
			errHelp = @"传入参数错误，请向管理员反馈";
		}break;
		case download_dir_not_found:{
			errDescription = @"下载目录不存在";
			errHelp = @"下载目录不存在，请向管理员反馈";
		}break;
		case target_file_is_dir:{
			errDescription = @"无法检索文件，目标存在同名目录";
            errHelp = @"无法检索文件，目标存在同名目录,请向管理员反馈";
		}break;
		case key_fetch_error:{
			errDescription = @"播放秘钥请求失败";
            errHelp = @"播放秘钥请求失败,请尝试重新下载，或向管理员反馈";
		}break;
		case m3u8_fetch_error:{
			errDescription = @"m3u8请求失败";
            errHelp = @"m3u8文件请求失败,请尝试重新下载，或向管理员反馈";
		}break;
		case token_fetch_error:{
			errDescription = @"播放token请求失败";
            errHelp = @"播放token请求失败,请尝试重新下载，或向管理员反馈";
		}break;
        case videojson_fetch_error:{
            errDescription = @"videojson请求失败";
            errHelp = @"videojson文件请求失败,请尝试重新下载，或向管理员反馈";
        }break;
		case filename_not_found:{
			errDescription = @"无法获取文件名";
            errHelp = @"获取下载文件名失败，请尝试重新下载，或向管理员反馈";
		}break;
		case ts_not_found:{
			errDescription = @"无法获取切片";
            errHelp = @"获取ts切片索引失败，请尝试重新下载，或向管理员反馈";
		}break;
		case local_file_unaccessible:{
			errDescription = @"本地资源不可达";
            errHelp = @"本地资源无法访问，请向管理员反馈";
		}break;
		case local_key_illegal:{
			errDescription = @"本地视频播放秘钥不合法";
			errHelp = @"本地视频播放秘钥错误，请删除视频并重新下载，或向管理员反馈";
		}break;
		case hls_dir_not_found:{
			errDescription = @"hls目录索引失败";
            errHelp = @"HLS 视频目录索引失败，请向管理员反馈";
		}break;
			
		case video_remove_error:{
			errDescription = @"视频移除错误";
            errHelp = @"视频文件移除失败，请重新移除，或向管理员反馈";
		}break;
		case file_move_error:{
			errDescription = @"文件移动错误";
            errHelp = @"文件移动错误，请向管理员反馈";
		}break;
			
		case network_unreachable:{
			errDescription = @"网络中断";
            errHelp = @"当前无网络连接，请检查网络设置";

		}break;
		case network_error:{
			errDescription = @"网络错误";
            errHelp = @"视频加载失败，可尝试切换线路";

		}break;
        case player_load_timeout:{
            errDescription = @"视频加载超时";
            errHelp = @"视频加载超时，可尝试切换线路";

        }break;
        case httpdns_change_Cdn:{
            errDescription = @"内部切换线路播放";
            errHelp = @"视频播放错误，可尝试切换线路";

        }break;
		case server_error:{
			errDescription = @"服务器错误";
            errHelp = @"视频加载失败，可尝试切换线路";

		}break;
		case fetch_error:{
			errDescription = @"请求错误";
            errHelp = @"网络请求错误，可尝试切换线路";
		}break;
		case json_read_error:{
			errDescription = @"JSON解析错误";
            errHelp = @"JSON 解析错误，请稍后再试，或向管理员反馈";
		}break;
        case video_not_support_play_audio:{
            errDescription = @"当前视频不支持音频播放模式";
            errHelp = @"当前视频不支持音频播放模式，请向管理员反馈";
        }break;
        case play_local_video_error:{
            errDescription = @"本地视频播放失败";
            errHelp = @"本地视频加载失败，请重试播放，或向管理员反馈";
        }break;
        case player_decrypt_not_support:{
            errDescription = @"当前播放器版本不支持该私有加密版本";
            errHelp = @"播放器不支持播放该视频，请升级播放器版本";
        }break;
        case player_nkv_not_support:{
            errDescription = @"当前版本的SDK不支持该解密key算法";
            errHelp = @"播放器不支持播放该视频，请升级播放器版本";
        }break;
        case pictureinpicture_error:{
            errDescription = @"画中画开启失败";
            errHelp = @"画中画开启失败";
        }break;
        case pictureinpicture_private_decrypt_not_support:{
            errDescription = @"私有加密暂不支持开启系统画中画";
            errHelp = @"私有加密暂不支持开启系统画中画";
        }break;
        case pictureinpicture_audiomode_not_support:{
            errDescription = @"音频模式暂不支持开启系统画中画";
            errHelp = @"音频模式暂不支持开启系统画中画";
        }break;
        case pictureinpicture_external_not_support:{
            errDescription = @"外部视频暂不支持开启系统画中画";
            errHelp = @"外部视频暂不支持开启系统画中画";
        }break;
        case pictureinpicture_systemversion_not_support:{
            errDescription = @"该系统版本不支持开启系统画中画";
            errHelp = @"该系统版本不支持开启系统画中画";
        }break;
        case download_error_lost_ts:{
            errDescription = @"下载失败，部分ts文件下载不成功";
            errHelp = @"下载失败，请尝试重新下载，或向管理员反馈";
        }break;
        case vid_error:{
            errDescription = @"vid错误";
            errHelp = @"vid错误，请检查vid，或向管理员反馈";
        }break;
		
		//default:{}break;
	}
	userInfo[NSLocalizedDescriptionKey] = errDescription;
    userInfo[NSHelpAnchorErrorKey] = errHelp;

	userInfo[NSLocalizedFailureReasonErrorKey] = errReason;
	userInfo[NSURLErrorKey] = errURL;
	userInfo[NSFilePathErrorKey] = errPath;
	return userInfo;
}

NSError *PLVVodErrorWithCode(PLVVodErrorCode code) {
	NSError *error = [NSError errorWithDomain:PLVVodErrorDomain code:code userInfo:PLVVodErrorInfoWithCode(code)];
	return error;
}
NSError *PLVVodErrorMake(PLVVodErrorCode code, void (^userInfoHandler)(NSMutableDictionary *userInfo)) {
	NSMutableDictionary *userInfo = (NSMutableDictionary *)PLVVodErrorInfoWithCode(code);
	if (userInfoHandler) userInfoHandler(userInfo);
	NSError *error = [NSError errorWithDomain:PLVVodErrorDomain code:code userInfo:userInfo];
	return error;
}

NSError *PLVVodErrorMakeWithError(PLVVodErrorCode code, NSError *error, void (^userInfoHandler)(NSMutableDictionary *userInfo)) {
	NSMutableDictionary *userInfo = (NSMutableDictionary *)PLVVodErrorInfoWithCode(code);
	userInfo[NSURLErrorKey] = error.userInfo[NSURLErrorKey];
	userInfo[NSFilePathErrorKey] = error.userInfo[NSFilePathErrorKey];
	userInfo[NSLocalizedFailureReasonErrorKey] = error.localizedDescription;
    // add by libl [添加系统错误域，错误码] 2019-05-10 start
    userInfo[PLVSysErrorDomainKey] = error.domain;
    userInfo[PLVSysErrorCodeKey] = [@(error.code) stringValue];
    // add end
    
    // add by libl [执行自定义block] 2018-12-05 start
    if (userInfoHandler) {
        userInfoHandler(userInfo);
    }
    // add end
	NSError *plvError = [NSError errorWithDomain:PLVVodErrorDomain code:code userInfo:userInfo];
	return plvError;
}

/// vid -> videoPoolId
NSString *videoPoolIdWithVid(NSString *vid) {
	NSRange range = [vid rangeOfString:@"_"];
	if (range.location==NSNotFound) {
		return nil;
	}
	NSString *videoPoolId = [vid substringToIndex:range.location];
	return videoPoolId;
}

/// videoPoolId -> vid
NSString *vidWithVideoPoolId(NSString *videoPoolId) {
	NSString *suffix = [videoPoolId substringToIndex:1];
	NSString *vid = [NSString stringWithFormat:@"%@_%@", videoPoolId, suffix];
	return vid;
}

/// 方向
NSString *NSStringFromUIDeviceOrientation(UIDeviceOrientation orientation) {
	switch (orientation) {
		case UIDeviceOrientationUnknown:
			return @"未知方向";
		case UIDeviceOrientationPortrait:
			return @"竖屏";
		case UIDeviceOrientationPortraitUpsideDown:
			return @"倒置";
		case UIDeviceOrientationLandscapeLeft:
			return @"横屏-左";
		case UIDeviceOrientationLandscapeRight:
			return @"横屏-右";
		case UIDeviceOrientationFaceUp:
			return @"面朝上";
		case UIDeviceOrientationFaceDown:
			return @"面朝下";
	}
}

@implementation PLVVodUtil

/// 时间转字符串
+ (NSString *)timeStringWithSeconds:(NSTimeInterval)seconds {
	NSInteger time = seconds;
	NSInteger _hours = time / 60 / 60;
	NSInteger _minutes = (time / 60) % 60;
	NSInteger _seconds = time % 60;
	NSString *timeString = _hours > 0 ? [NSString stringWithFormat:@"%02zd:", _hours] : @"";
	timeString = [timeString stringByAppendingString:[NSString stringWithFormat:@"%02zd:%02zd", _minutes, _seconds]];
	return timeString;
}

/// 字符串转时间
+ (NSTimeInterval)secondsWithtimeString:(NSString *)timeString {
	NSArray *timeComponents = [timeString componentsSeparatedByString:@":"];
	NSTimeInterval seconds = 0;
	int componentCount = 3;
	if (timeComponents.count < componentCount) {
		componentCount = (int)timeComponents.count;
	}
	for (int i = 0; i < componentCount; i++) {
		NSInteger timeComponent = [timeComponents[i] integerValue];
		seconds += pow(60, componentCount-1-i) * timeComponent;
	}
	return seconds;
	//return [timeComponents[0] intValue] * 60 * 60
	//+ [timeComponents[1] intValue] * 60
	//+ [timeComponents[2] intValue];
}

/// 获取签名
+ (NSString *)signWithTimestamp:(NSString *)timestamp videoPoolId:(NSString *)videoPoolId {
	NSString *secretkey = [PLVVodSettings findSecretKeyWithVid:videoPoolId];
	if (!secretkey.length) {
		return nil;
	}
	NSString *plainSign = [NSString stringWithFormat:@"polyv.net%@%@%@", secretkey, timestamp, videoPoolId];
	//NSLog(@"plain:%@", plainSign);
	NSString *sign = [self md5String:plainSign];
	return sign;
}

/// 时间戳
+ (NSString *)timestamp {
	long long timeInterval = [NSDate date].timeIntervalSince1970 * 1000;
	return @(timeInterval).description;
}

/// 获取当前时间
+ (NSTimeInterval)currentSeconds {
	return [NSDate date].timeIntervalSince1970;
//	struct timeval time;
//	gettimeofday(&time, NULL);
//	return time.tv_usec;
}

/// 计算时间间隔
+ (NSTimeInterval)timeIntervalSinceSeconds:(NSTimeInterval)seconds {
	NSTimeInterval now = [self currentSeconds];
	return now - seconds;
}


/// pid
+ (NSString *)pid {
	long timestamp = (long)([[NSDate date] timeIntervalSince1970] * 1000.0);
	long rendom = arc4random_uniform(1000000) + 1000000;
	
	NSString *pid = [NSString stringWithFormat:@"%zdX%ld", timestamp, rendom];
	return pid;
}

/// 随机数 [from, to]
+ (NSInteger)randomIntegerFrom:(int)fromValue to:(int)toValue {
	NSInteger randomValue = arc4random() % (toValue - fromValue + 1) + fromValue;
	return randomValue;
}
+ (double)randomDoubleFrom:(double)fromValue to:(double)toValue accuracy:(NSInteger)accuracy {
    NSInteger processedAccuracy = (NSInteger)pow(10, accuracy);
	double randomValue = 1.0 * [self randomIntegerFrom:fromValue * processedAccuracy to:toValue * processedAccuracy] / processedAccuracy;
	return randomValue;
}

+ (NSString *)userAgent {
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSASCIIStringEncoding];
    
    NSString *uaString = [NSString stringWithFormat:@"%@%@+%@", [UIDevice currentDevice].systemName, [UIDevice currentDevice].systemVersion, deviceModel];
    
    return [NSString stringWithFormat:@"polyv-ios-sdk%@ %@",PLVVodSdkVersion, uaString];
}

/// ijkplayer使用的userAgent 值
+ (NSString *)ijkplayerUserAgent {
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSASCIIStringEncoding];
    
    NSString *uaString = [NSString stringWithFormat:@"%@%@+%@", [UIDevice currentDevice].systemName, [UIDevice currentDevice].systemVersion, deviceModel];
    
    return [NSString stringWithFormat:@"polyv-ios-sdk%@ %@",PLVVodSdkVersion, uaString];
}

/// 十六进制字符串转NSData
+ (NSData *)dataWithHexString:(NSString *)hexString {
	if (!hexString || hexString.length == 0) {
		return nil;
	}
	NSMutableData *hexData = [[NSMutableData alloc] initWithCapacity:8];
	NSRange range;
	if (hexString.length % 2 == 0){
		range = NSMakeRange(0, 2);
	} else range = NSMakeRange(0, 1);
	for (NSInteger i = range.location; i < hexString.length; i += 2) {
		unsigned int anInt;
		NSString *hexCharStr = [hexString substringWithRange:range];
		if (!hexCharStr.length) return nil;
		NSScanner *scanner = [NSScanner scannerWithString:hexCharStr];
		[scanner scanHexInt:&anInt];
		NSData *entity = [[NSData alloc] initWithBytes:&anInt length:1];
		[hexData appendData:entity];
		range.location += range.length;
		range.length = 2;
	}
	return hexData;
}

/// 十六进制转颜色
+ (UIColor *)colorWithHex:(NSUInteger)hexValue alpha:(CGFloat)alpha {
	return [UIColor colorWithRed:((float)((hexValue & 0xFF0000) >> 16))/255.0
						   green:((float)((hexValue & 0xFF00) >> 8))/255.0
							blue:((float)(hexValue & 0xFF))/255.0
						   alpha:alpha];
}

+ (UIColor *)colorWithHex:(NSUInteger)hexValue {
	return [self colorWithHex:hexValue alpha:1.0];
}

#pragma mark - 文件处理

/// 创建不存在的目录
+ (void)createDirIfNeed:(NSString *)dir error:(NSError **)error {
	NSFileManager *fileManager = [NSFileManager defaultManager];
	BOOL isDir;
	BOOL fileExist = [fileManager fileExistsAtPath:dir isDirectory:&isDir];
	if (fileExist && !isDir) {
		[fileManager removeItemAtPath:dir error:error];
		PLVVodInfoLog(@"!1删除文件 %@", dir);
	}
	if (!(fileExist && isDir)) {
		[fileManager createDirectoryAtPath:dir withIntermediateDirectories:YES attributes:nil error:error];
	}
}

/// 移动文件
+ (BOOL)movePath:(NSString *)fromPath toPath:(NSString *)toPath error:(NSError **)error {
	NSFileManager *fileManager = [NSFileManager defaultManager];
	//BOOL isDir = NO;
	
	if (!fromPath.length) {
		//PLVVodLogError(@"参数为空，%@", __FUNCTION__, fromPath);
		NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
		NSError *plvError = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
			userInfo[PLVVodErrorSelectorKey] = selector;
		});
        if (error) *error = plvError;
		return NO;
	}
	if (!toPath.length) {
		//PLVVodLogError(@"参数为空，%@", __FUNCTION__, toPath);
		NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
		NSError *plvError = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
			userInfo[PLVVodErrorSelectorKey] = selector;
		});
        if (error) *error = plvError;
		return NO;
	}
	
	// 检查源路径是否存在该资源
	if (![fileManager fileExistsAtPath:fromPath]) {
		//PLVVodLogError(@"文件源不存在，%@", fromPath.lastPathComponent);
		NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
		NSError *plvError = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
			userInfo[PLVVodErrorSelectorKey] = selector;
		});
        if (error) *error = plvError;
		return NO;
	}
	
	// 检查目标位置目录是否存在
	NSString *toDir = toPath.stringByDeletingLastPathComponent;
	BOOL isDir = NO;
	if ([fileManager fileExistsAtPath:toDir isDirectory:&isDir] == NO) { // 目录不存在，则建目录
		[fileManager createDirectoryAtPath:toDir withIntermediateDirectories:YES attributes:nil error:error];
	} else if (isDir == NO) { // 存在一个对应的文件
		[fileManager removeItemAtPath:toDir error:error];
		PLVVodLogWarn(@"删除文件，%@", toDir);
	}
	
	// 移动文件
	BOOL isMoved;
    if ([fileManager fileExistsAtPath:toPath]){
        PLVVodLogDebug(@"++++++++ movePath: file has exist !++++++++++");
        [fileManager removeItemAtPath:toPath error:nil];
    }
    
	isMoved = [fileManager moveItemAtPath:fromPath toPath:toPath error:error];
	if (*error) {
        PLVVodLogError(@"文件移动错误，%@", *error);
		NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
		NSError *plvError = PLVVodErrorMakeWithError(file_move_error, *error, ^(NSMutableDictionary *userInfo) {
			userInfo[PLVVodErrorSelectorKey] = selector;
		});
        if (error) *error = plvError;
	}
	return isMoved;
}

/// 兼容 1.0 SDK 离线视频
+ (void)compatibleWithPreviousDownloadDir:(NSString *)previousDownloadDir {
	// 修正本地m3u8文件中的ts路径
	NSArray<PLVVodLocalVideo *> *localVideos = [PLVVodLocalVideo localVideosWithDir:previousDownloadDir];
	for (PLVVodLocalVideo *localVideo in localVideos) {
		if (!localVideo.isHls) continue;
		NSString *m3u8Content = [NSString stringWithContentsOfFile:localVideo.path encoding:NSUTF8StringEncoding error:nil];
		NSString *newContent = [PLVVodHlsHelper fixPreviousM3u8:m3u8Content];
		if (newContent) {
			[newContent writeToFile:localVideo.path atomically:YES encoding:NSUTF8StringEncoding error:nil];
		}
	}
}

#pragma mark - 加密/解密

/// Base64 编码
+ (NSString *)base64String:(NSString *)inputString {
	NSData *data = [inputString dataUsingEncoding:NSUTF8StringEncoding];
	NSString *base64String = [data base64EncodedStringWithOptions:0];
	return base64String;
}

/// url safe Base64 编码
+ (NSString *)urlSafeBase64String:(NSString *)inputString {
	NSData *data = [inputString dataUsingEncoding:NSUTF8StringEncoding];
	NSString *base64String = [data base64EncodedStringWithOptions:0];
	base64String = [base64String stringByReplacingOccurrencesOfString:@"/" withString:@"_"];
	base64String = [base64String stringByReplacingOccurrencesOfString:@"+" withString:@"-"];
	return base64String;
}

/// SHA1 加密
+ (NSString *)sha1String:(NSString *)inputString {
    
    // mod by libl [含有中文字符时加密有问题] 2019-05-05 start
//    const char *cstr = [inputString cStringUsingEncoding:NSUTF8StringEncoding];
//
//    NSData *data = [NSData dataWithBytes:cstr length:inputString.length];
//    //使用对应的CC_SHA1,CC_SHA256,CC_SHA384,CC_SHA512的长度分别是20,32,48,64
//    uint8_t digest[CC_SHA1_DIGEST_LENGTH];
//    //使用对应的CC_SHA256,CC_SHA384,CC_SHA512
//    CC_SHA1(data.bytes, (CC_LONG)data.length, digest);
//
//    NSMutableString *output = [NSMutableString stringWithCapacity:CC_SHA1_DIGEST_LENGTH * 2];
//
//    for(int i = 0; i < CC_SHA1_DIGEST_LENGTH; i++)
//        [output appendFormat:@"%02x", digest[i]];
//
//    return output;
    
    //这两句容易造成 、中文字符串转data时会造成数据丢失
    //const char *cstr = [input cStringUsingEncoding:NSUTF8StringEncoding];
    //NSData *data = [NSData dataWithBytes:cstr length:input.length];
    NSData *data = [inputString dataUsingEncoding:NSUTF8StringEncoding];
    
    uint8_t digest[CC_SHA1_DIGEST_LENGTH];
    
    CC_SHA1(data.bytes, (unsigned int)data.length, digest);
    
    NSMutableString *output = [NSMutableString stringWithCapacity:CC_SHA1_DIGEST_LENGTH * 2];
    
    for(int i=0; i<CC_SHA1_DIGEST_LENGTH; i++) {
        [output appendFormat:@"%02x", digest[i]];
    }
    
    return output;
    
    // mod end
}

/// MD5 加密
+ (NSString *)md5String:(NSString *)inputString {
	const char* str = [inputString UTF8String];
	unsigned char result[CC_MD5_DIGEST_LENGTH];
	CC_MD5(str, (CC_LONG)strlen(str), result);
	
	NSMutableString *md5String = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH * 2];
	for(int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
		[md5String appendFormat:@"%02x", result[i]];
	}
	return md5String;
}

/// AES-128 加密，不使用加密向量
+ (NSData *)AES128EncryptedDataWithKey:(NSString *)key data:(NSData *)data {
	return [self AES128EncryptedDataWithKey:key iv:nil data:data];
}

/// AES-128 解密，不使用加密向量
+ (NSData *)AES128DecryptedDataWithKey:(NSString *)key data:(NSData *)data {
	return [self AES128DecryptedDataWithKey:key iv:nil data:data];
}

/// AES-128 加密
+ (NSData *)AES128EncryptedDataWithKey:(NSString *)key iv:(NSString *)iv data:(NSData *)data {
	return [self AES128Operation:kCCEncrypt key:[key dataUsingEncoding:NSUTF8StringEncoding] iv:[iv dataUsingEncoding:NSUTF8StringEncoding] data:data];
}

/// AES-128 解密
+ (NSData *)AES128DecryptedDataWithKey:(NSString *)key iv:(NSString *)iv data:(NSData *)data {
	return [self AES128Operation:kCCDecrypt key:[key dataUsingEncoding:NSUTF8StringEncoding] iv:[iv dataUsingEncoding:NSUTF8StringEncoding] data:data];
}

#pragma mark - 其他

+ (NSData *)AES128Operation:(CCOperation)operation key:(NSData *)keyData iv:(NSData *)ivData data:(NSData *)contentData {
	// KEY
//	char keyPtr[kCCKeySizeAES128 + 1];
//	bzero(keyPtr, sizeof(keyPtr));
//	[key getCString:keyPtr maxLength:sizeof(keyPtr) encoding:NSUTF8StringEncoding];
	
	// IV
//	char ivPtr[kCCBlockSizeAES128 + 1];
//	bzero(ivPtr, sizeof(ivPtr));
//	[iv getCString:ivPtr maxLength:sizeof(ivPtr) encoding:NSUTF8StringEncoding];
	
	size_t bufferSize = contentData.length + kCCBlockSizeAES128;
	void *buffer = malloc(bufferSize);
	size_t numBytesEncrypted = 0;
	
	CCCryptorStatus cryptorStatus = CCCrypt(operation, kCCAlgorithmAES128, kCCOptionPKCS7Padding,
											keyData.bytes, kCCKeySizeAES128,
											ivData.bytes,
											contentData.bytes, contentData.length,
											buffer, bufferSize,
											&numBytesEncrypted);
	
	if (cryptorStatus == kCCSuccess) {
		return [NSData dataWithBytesNoCopy:buffer length:numBytesEncrypted];
	}
	
	free(buffer);
	return nil;
}

+ (NSUInteger)preferredIndexWithArray:(NSArray *)array index:(NSInteger)index {
	NSUInteger preferredIndex = array.count-1;
	if (index < 0) {
		preferredIndex = 0;
	} else if (index >= array.count) {
		preferredIndex = array.count-1;
	} else {
		preferredIndex = index;
	}
	return preferredIndex;
}

/// 拼接参数
+ (NSString *)url:(NSString *)url appendParams:(NSDictionary *)paramDict {    
    NSString *paramString = [self convertDictionaryToSortedString:paramDict];
	// 拼接参数
	BOOL hasParam = [url rangeOfString:@"?"].location != NSNotFound;
	if (hasParam) {
        return [NSString stringWithFormat:@"%@&%@", url, paramString];
	}else{
		return [NSString stringWithFormat:@"%@?%@", url, paramString];
	}
}

+ (NSString *)convertDictionaryToSortedString:(NSDictionary *)params {
    NSArray *keys = [params allKeys];
    keys = [keys sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
        return [obj1 compare:obj2 options:NSCaseInsensitiveSearch];
    }];
    NSMutableString *paramStr = [NSMutableString string];
    for (int i = 0; i < keys.count; i ++) {
        NSString *key = keys[i];
        [paramStr appendFormat:@"%@=%@", key, params[key]];
        if (i == keys.count - 1) break;
        [paramStr appendString:@"&"];
    }
    return [paramStr copy];
}

/// NSData -> NSDictionary
+ (NSDictionary *)dictionaryWithData:(NSData *)data {
	if (!data.length) {
		return nil;
	}
	CFPropertyListFormat plistFormat = kCFPropertyListXMLFormat_v1_0;
	CFPropertyListRef plist = CFPropertyListCreateWithData(kCFAllocatorDefault, (__bridge CFDataRef)data, kCFPropertyListImmutable, &plistFormat, NULL);
	if ([(__bridge id)plist isKindOfClass:[NSDictionary class]]) {
        NSDictionary *transDic = (__bridge_transfer NSDictionary*)plist;
        return transDic;
	} else {
		CFRelease(plist);
		return nil;
	}
}

/// NSDictionary -> NSData
+ (NSData *)dataWithDictionary:(NSDictionary *)dic {
	if (!dic.count) {
		return nil;
	}
	CFDataRef data = CFPropertyListCreateData(kCFAllocatorDefault, (__bridge CFPropertyListRef)dic, kCFPropertyListXMLFormat_v1_0, kCFPropertyListImmutable, NULL);
	if ([(__bridge id)data isKindOfClass:[NSData class]]) {
        NSData *transData = (__bridge_transfer NSData *)data;
        return transData;
	} else {
		CFRelease(data);
		return nil;
	}
}

/// 判断是否为空字符串
+ (BOOL)isNilString:(NSString *)origStr{
    if (!origStr || [origStr isKindOfClass:[NSNull class]] || !origStr.length){
        return YES;
    }
    
    return NO;
}

/// 根据url 获取域名
+ (NSString *)getHostWithUrl:(NSString *)urlStr{
    
    if ([self isNilString:urlStr])
        return nil;
    
    NSURL *url = [NSURL URLWithString:urlStr];
    NSString *domain = url.host;
    
    return domain;
}

/// 根据Qos错误类型返回具体的错误码
+ (NSString *)getQosErrorCodeWithType:(PLVVodQosErrorType)type{
    NSString *errorCode = nil;
    if (type == ePLVVodQosErrorLoadVideoFail){
        errorCode = PLVVodQosErrorLoadVideoFail;
    }
    else if (type == ePLVVodQosErrorLoadVideoJsonFail){
        errorCode = PLVVodQosErrorLoadVideoJsonFail;
    }
    else if (type == ePLVVodQosErrorOther){
        errorCode = PLVVodQosErrorOther;
    }
    else if (type == ePLVVodQosErrorLoadVideoTokenFail){
        errorCode = PLVVodQosErrorLoadVideoTokenFail;
    }
    
    return errorCode;
}

/// 检查vid是否正确
+ (BOOL)validateVid:(NSString *)vid{
    BOOL isCorrectVid;
    if([self isNilString:vid]){
        isCorrectVid = NO;
    }else{
        NSString *VID = @"^[[a-z]|[0-9]]{32}_[[a-z]|[0-9]]$";
        NSPredicate *regexVid = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",VID];
        if([regexVid evaluateWithObject:VID]){
            isCorrectVid = YES;
        }else{
            isCorrectVid = NO;
        }
    }
    return isCorrectVid;
}

/// 当前系统语言是否中文语言
+ (BOOL)isChineseSystemLanguage{
    NSArray *languages = [NSLocale preferredLanguages];
    if(languages == nil){
        return YES;
    }else{
        if([languages count] == 0){
                return YES;
        }else{
            NSString *currentLanguage = [languages objectAtIndex:0];
            return [currentLanguage containsString:@"zh"];
        }
    }
}

@end
