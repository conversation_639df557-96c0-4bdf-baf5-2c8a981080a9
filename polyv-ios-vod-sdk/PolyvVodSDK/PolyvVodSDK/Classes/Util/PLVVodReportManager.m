//
//  PLVVodReportManager.m
//  _PolyvVodSDK
//
//  Created by <PERSON><PERSON> <PERSON> on 2018/3/13.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import "PLVVodReportManager.h"
#import "PLVVodReachability.h"
#import "PLVVodNetworking.h"
#import "PLVVodElogModel.h"

static NSString * const PidKey = @"pid";
static NSString * const VidKey = @"vid";
static NSString * const TimeKey = @"time";
static NSString * const TimePlayerKey = @"time_player";
static NSString * const TimeBusinessKey = @"time_business";
static NSString * const ErrorKey = @"error";
static NSString * const requestKey = @"request";
static NSString * const responseKey = @"response";

@interface PLVVodNetworking ()

#pragma mark 汇报后台

/// viewlog
+ (void)reportViewLogWithParams:(NSDictionary *)params;

/// loading qos
+ (void)reportLoadingQosWithPid:(NSString *)pid vid:(NSString *)vid time:(NSTimeInterval)time time_player:(NSTimeInterval)time_player time_business:(NSTimeInterval)time_business params:(NSDictionary *)extraParams;

/// buffer qos
+ (void)reportBufferQosWithPid:(NSString *)pid vid:(NSString *)vid time:(NSTimeInterval)time params:(NSDictionary *)extraParams;

///// error qos
//+ (void)reportErrorQosWithPid:(NSString *)pid
//                          vid:(NSString *)vid
//                        error:(NSString *)error
//                       params:(NSDictionary *)extraParams;

/// qos stalling
+ (void)reportQosStallingWithPid:(NSString *)pid
                             vid:(NSString *)vid
                            time:(NSTimeInterval)time
                          params:(NSDictionary *)extraParams;

/// play error qos
+ (void)reportErrorQosWithPid:(NSString *)pid
                          vid:(NSString *)vid
                    errorCode:(NSString *)errorCode
                   requestUri:(NSString *)requestUri
                 responseCode:(NSString *)responseCode
                  extraParams:(NSDictionary *)extraParams;


/// 上传elog日志
+ (void)reportElogWithLogParams:(NSString *)logParams withVid:(NSString *)vid completion:(void(^)(NSError *error))completion;

@end

@interface PLVVodReportManager ()

@property (nonatomic, strong) NSMutableArray *viewlogs;
@property (nonatomic, strong) NSMutableArray *loadingQoses;
@property (nonatomic, strong) NSMutableArray *bufferQoses;
@property (nonatomic, strong) NSMutableArray *errorQoses;
@property (nonatomic, strong) NSMutableArray *qosStallings;

@property (nonatomic, strong) dispatch_semaphore_t semaphore;

@end

@implementation PLVVodReportManager

#pragma mark - singleton init

static id _sharedManager = nil;

+ (instancetype)sharedManager {
	static dispatch_once_t onceToken;
	dispatch_once(&onceToken, ^{
		_sharedManager = [[self alloc] init];
		[_sharedManager commonInit];
	});
	return _sharedManager;
}

+ (instancetype)allocWithZone:(struct _NSZone *)zone {
	static dispatch_once_t onceToken;
	dispatch_once(&onceToken, ^{
		if (!_sharedManager) {
			_sharedManager = [super allocWithZone:zone];
		}
	});
	return _sharedManager;
}

- (id)copyWithZone:(NSZone *)zone {
	return _sharedManager;
}

- (void)commonInit {
	// 初始化属性
    [PLVVodReachability sharedReachability];
	[[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkStatusDidChange:) name:kPLVVodReachabilityChangedNotification object:nil];
    self.semaphore = dispatch_semaphore_create(1);
}

- (void)networkStatusDidChange:(NSNotification *)notification {
    PLVVodReachability *reachability =  [PLVVodReachability sharedReachability];
    if (reachability.currentReachabilityStatus != PLVVodNotReachable) {
        
        dispatch_semaphore_wait(self.semaphore, DISPATCH_TIME_FOREVER);

		while (_viewlogs.count > 0) {
			id firstViewLog = _viewlogs.firstObject;
			if (firstViewLog && [firstViewLog isKindOfClass:[NSDictionary class]]) {
				[PLVVodNetworking reportViewLogWithParams:firstViewLog];
				[_viewlogs removeObjectAtIndex:0];
			} else {
				break;
			}
		}
		while (_loadingQoses.count > 0) {
			id firstObject = _loadingQoses.firstObject;
			if (firstObject && [firstObject isKindOfClass:[NSMutableDictionary class]]) {
				NSMutableDictionary *dic = firstObject;
				NSString *pid = dic[PidKey];
				[dic removeObjectForKey:PidKey];
				NSString *vid = dic[VidKey];
				[dic removeObjectForKey:VidKey];
				NSTimeInterval time = [dic[TimeKey] doubleValue];
				[dic removeObjectForKey:TimeKey];
                NSTimeInterval time_player = [dic[TimePlayerKey] doubleValue];
                [dic removeObjectForKey:TimeKey];
                NSTimeInterval time_business = [dic[TimeBusinessKey] doubleValue];
                [dic removeObjectForKey:TimeKey];
                
				
                [PLVVodNetworking reportLoadingQosWithPid:pid vid:vid time:time time_player:time_player time_business:time_business params:dic];
				[_loadingQoses removeObjectAtIndex:0];
			} else {
				break;
			}
		}
		while (_bufferQoses.count > 0) {
			id firstObject = _bufferQoses.firstObject;
			if (firstObject && [firstObject isKindOfClass:[NSMutableDictionary class]]) {
				NSMutableDictionary *dic = firstObject;
				NSString *pid = dic[PidKey];
				[dic removeObjectForKey:PidKey];
				NSString *vid = dic[VidKey];
				[dic removeObjectForKey:VidKey];
				NSTimeInterval time = [dic[TimeKey] doubleValue];
				[dic removeObjectForKey:TimeKey];
				
				[PLVVodNetworking reportBufferQosWithPid:pid vid:vid time:time params:dic];
				[_bufferQoses removeObjectAtIndex:0];
			} else {
				break;
			}
		}
        // QOS 的error 类型，无网络时不再发送，没意义
		while (_errorQoses.count > 0) {
			id firstObject = _errorQoses.firstObject;
			if (firstObject && [firstObject isKindOfClass:[NSMutableDictionary class]]) {
				NSMutableDictionary *dic = firstObject;
				NSString *pid = dic[PidKey];
				[dic removeObjectForKey:PidKey];
				NSString *vid = dic[VidKey];
				[dic removeObjectForKey:VidKey];
				NSString *error = dic[ErrorKey];
				[dic removeObjectForKey:ErrorKey];
				NSString *requestUrl = dic[requestKey];	
				[dic removeObjectForKey:requestKey];
				NSString *responseCode = dic[responseKey];
				[dic removeObjectForKey:responseKey];
				
				[PLVVodNetworking reportErrorQosWithPid:pid 
                                                    vid:vid errorCode:error requestUri:requestUrl responseCode:responseCode extraParams:dic];

				[_errorQoses removeObjectAtIndex:0];		
			} else {
				break;
			}
		}
		while (_qosStallings.count > 0) {
			id firstObject = _qosStallings.firstObject;
			if (firstObject && [firstObject isKindOfClass:[NSMutableDictionary class]]) {
				NSMutableDictionary *dic = firstObject;
				NSString *pid = dic[PidKey];
				[dic removeObjectForKey:PidKey];
				NSString *vid = dic[VidKey];
				[dic removeObjectForKey:VidKey];
				NSTimeInterval time = [dic[TimeKey] doubleValue];
				[dic removeObjectForKey:TimeKey];
				
				[PLVVodNetworking reportQosStallingWithPid:pid vid:vid time:time params:dic];
				[_qosStallings removeObjectAtIndex:0];
			} else {
				break;
			}
		}
        
        dispatch_semaphore_signal(self.semaphore);
	}
}

#pragma mark - property
- (NSMutableArray *)viewlogs {
	if (!_viewlogs) {
		_viewlogs = [NSMutableArray array];
	}
	return _viewlogs;
}
- (NSMutableArray *)loadingQoses {
	if (!_loadingQoses) {
		_loadingQoses = [NSMutableArray array];
	}
	return _loadingQoses;
}
- (NSMutableArray *)bufferQoses {
	if (!_bufferQoses) {
		_bufferQoses = [NSMutableArray array];
	}
	return _bufferQoses;
}
- (NSMutableArray *)errorQoses {
	if (!_errorQoses) {
		_errorQoses = [NSMutableArray array];
	}
	return _errorQoses;
}
- (NSMutableArray *)qosStallings {
	if (!_qosStallings) {
		_qosStallings = [NSMutableArray array];
	}
	return _qosStallings;
}

#pragma mark - report

/// viewlog
+ (void)reportViewLogWithParams:(NSDictionary *)params {
    if ([PLVVodReachability sharedReachability].currentReachabilityStatus == PLVVodNotReachable) {
		PLVVodReportManager *manager = [PLVVodReportManager sharedManager];
		[manager.viewlogs addObject:params];
		return;
	}
	[PLVVodNetworking reportViewLogWithParams:params];
}

/// loading qos
+ (void)reportLoadingQosWithPid:(NSString *)pid vid:(NSString *)vid time:(NSTimeInterval)time time_player:(NSTimeInterval)time_player time_business:(NSTimeInterval)time_business params:(NSDictionary *)extraParams {
    if ([PLVVodReachability sharedReachability].currentReachabilityStatus == PLVVodNotReachable) {
		PLVVodReportManager *manager = [PLVVodReportManager sharedManager];
		NSMutableDictionary *params = [NSMutableDictionary dictionary];
		params[PidKey] = pid;
		params[VidKey] = vid;
		params[TimeKey] = @(time);
        params[TimePlayerKey] = @(time_player);
        params[TimeBusinessKey] = @(time_business);
		[params addEntriesFromDictionary:extraParams];
		[manager.loadingQoses addObject:params];
		return;
	}
	[PLVVodNetworking reportLoadingQosWithPid:pid vid:vid time:time time_player:time_player time_business:time_business params:extraParams];
}

/// buffer qos
+ (void)reportBufferQosWithPid:(NSString *)pid vid:(NSString *)vid time:(NSTimeInterval)time params:(NSDictionary *)extraParams {
    if ([PLVVodReachability sharedReachability].currentReachabilityStatus == PLVVodNotReachable) {
		PLVVodReportManager *manager = [PLVVodReportManager sharedManager];
		NSMutableDictionary *params = [NSMutableDictionary dictionary];
		params[PidKey] = pid;
		params[VidKey] = vid;
		params[TimeKey] = @(time);
		[params addEntriesFromDictionary:extraParams];
		[manager.bufferQoses addObject:params];
		return;
	}
	[PLVVodNetworking reportBufferQosWithPid:pid vid:vid time:time params:extraParams];
}

/// qos stalling
+ (void)reportQosStallingWithPid:(NSString *)pid vid:(NSString *)vid time:(NSTimeInterval)time params:(NSDictionary *)extraParams {
    if ([PLVVodReachability sharedReachability].currentReachabilityStatus == PLVVodNotReachable) {
        PLVVodReportManager *manager = [PLVVodReportManager sharedManager];
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        params[PidKey] = pid;
        params[VidKey] = vid;
        params[TimeKey] = @(time);
        [params addEntriesFromDictionary:extraParams];
        [manager.qosStallings addObject:params];
        return;
    }

    [PLVVodNetworking reportQosStallingWithPid:pid vid:vid time:time params:extraParams];
}

+ (void)reportErrorQosWithPid:(NSString *)pid
                          vid:(NSString *)vid
                    errorCode:(NSString *)errorCode
                   requestUrl:(NSString *)requestUrl
                 responseCode:(NSString *)responseCode
                       params:(NSDictionary *)extraParams
{
    if ([PLVVodReachability sharedReachability].currentReachabilityStatus == PLVVodNotReachable) {
        
        PLVVodReportManager *manager = [PLVVodReportManager sharedManager];
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        params[PidKey] = pid;
        params[VidKey] = vid;
        params[ErrorKey] = errorCode;
		params[requestKey] = requestUrl;
        params[responseKey] = responseCode;
        [params addEntriesFromDictionary:extraParams];
        [manager.errorQoses addObject:params];
        
        return;
    }
    
    [PLVVodNetworking reportErrorQosWithPid:pid
                                        vid:vid
                                  errorCode:errorCode
                                 requestUri:requestUrl
                               responseCode:responseCode
                                extraParams:extraParams];
}


/// 上传elog 日志
+ (void)reportElogWithElogModel:(PLVVodElogModel *)model completion:(void (^)(NSError *))completion{
    NSString *logParams = [model jsonStringFromModel];
    [PLVVodNetworking reportElogWithLogParams:logParams withVid:model.vid completion:^(NSError *error) {
        if (completion) completion(error);
    }];
}

@end
