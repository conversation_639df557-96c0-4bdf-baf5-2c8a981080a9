//
//  PLVVodM3u8Helper.h
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/12.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface PLVVodHlsHelper : NSObject

///// 日志开关
//+ (void)enableLog:(BOOL)enable;

/// ts 路径改为本地相对路径
+ (NSString *)fixTsLocallyInM3u8:(NSString *)content;

/// pts 路径改为本地相对路径
+ (NSString *)fixPtsLocallyInM3u8:(NSString *)content;

/// 修改 M3U8 文件中的 key 字段为本地相对路径
+ (NSString *)fixKeyInM3u8:(NSString *)content;

/// 修改 M3U8 文件中的 key 字段为本地相对路径  二阶段修改
+ (NSString *)fixKeyInM3u8InSecond:(NSString *)content;

/// 修正旧版m3u8，返回nil则无需修改
+ (NSString *)fixPreviousM3u8:(NSString *)content;

/// 修正tsURL，如https支持，httpDNS
+ (NSString *)fixTsUrlInM3u8:(NSString *)content;

/// 修正tsURL，如https支持，httpDNS (加密视频)
+ (NSString *)fixHttpDNSIfNeedInM3u8:(NSString *)content;


/// 使用 HttpDNS 修正请求
+ (BOOL)fixHttpDNSIfNeedAtRquest:(NSMutableURLRequest *)request;

/// 获取ts数组
+ (NSArray *)mediaListOfM3U8:(NSString *)content handler:(void (^)(NSString *tsUrl))handler;
+ (NSArray *)mediaListOfM3U8:(NSString *)content count:(int)count;

/// 加密 key
+ (NSData *)ecryptKey:(NSData *)keyData vid:(NSString *)vid;

/// 通用TS HOST
+ (NSArray *)commomTsHosts;

/// 检查m3u8内容
+ (void)checkM3u8WithURL:(NSURL *)URL completion:(void (^)(NSError *error))completion;

@end
