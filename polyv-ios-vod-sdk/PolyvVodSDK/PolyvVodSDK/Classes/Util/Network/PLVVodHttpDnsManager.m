//
//  PLVVodHttpDnsManager.m
//  _PolyvVodSDK
//
//  Created by junotang on 2022/2/28.
//  Copyright © 2022 POLYV. All rights reserved.
//

#import "PLVVodHttpDnsManager.h"
#import <AlicloudHttpDNS/AlicloudHttpDNS.h>
#import "PLVVodReachability.h"
#import "PLVVodHlsHelper.h"
#import "PLVVodUtil.h"
#import "PLVPingUtil.h"

static NSInteger const PLVVodHttpDnsAccountID = 123018;
static NSString * const PLVVodHttpDnsSecretKey = @"40cf4fac2f4d167a8d9568ffe1cbef90b30cb5cfa8c177ffc7030f622a83f0b9ab002560ee69630eeef8ca923215377b";
static NSString * const PLVVodHttpDnsSecretKeyCacheKey = @"net.polyv.sdk.vod.httpdnsSecretKey";
static NSInteger const PLVVodHttpDnsSecretKeyCacheTime = 86400; //!< 缓存时效 1 天

static NSString * const PLVVodHttpDnsSecretKeyAES_KEY = @"polyvlive7654321";
static NSString * const PLVVodHttpDnsSecretKeyAES_IV = @"****************";

@interface PLVVodHttpDnsManager ()

/// httpdns是否已经初始化（只有第一次初始化有用）
@property (nonatomic, assign) BOOL hadHttpDnsInit;

@property (nonatomic, assign) BOOL DNSOptimize;
@property (nonatomic, strong) NSMutableDictionary *DNSOptimizeCheckHostDict;
//@property (nonatomic, strong) NSMutableDictionary<NSString *host, HttpdnsResult *result> *DNSOptimizeCheckHostDict;

@property (nonatomic, strong, readwrite, nullable) NSTimer *DNSOptimizeTimer;

@end

@implementation PLVVodHttpDnsManager

#pragma mark - singleton init

static id _sharedManager = nil;

+ (instancetype)sharedManager {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _sharedManager = [[self alloc] init];
        [_sharedManager commonInit];
    });
    return _sharedManager;
}

+ (instancetype)allocWithZone:(struct _NSZone *)zone {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        if (!_sharedManager) {
            _sharedManager = [super allocWithZone:zone];
        }
    });
    return _sharedManager;
}

- (id)copyWithZone:(NSZone *)zone {
    return _sharedManager;
}

- (void)commonInit {
    self.hadHttpDnsInit = NO;
    self.DNSOptimize = NO;
    self.DNSOptimizeCheckHostDict = [NSMutableDictionary dictionary];
    
    [PLVVodReachability sharedReachability];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkStatusDidChange:) name:kPLVVodReachabilityChangedNotification object:nil];
}

- (void)dealloc {
    if (self.DNSOptimizeTimer) {
        [self.DNSOptimizeTimer invalidate];
        self.DNSOptimizeTimer = nil;
    }
}

- (NSString *)sessionid{
    return [HttpDnsService sharedInstance].getSessionId;
}

#pragma mark - [ Public Method ]

/// 配置HTTPDNS信息
/// @param key 密钥密文，为nil时将使用缓存
- (void)configHttpDNSWithSecretKey:(NSString *)key {
    if (self.hadHttpDnsInit) {
        PLVVodLogInfo(@"HttpDns已经初始化，无需重复");
        return;
    }
    // 获取key
    NSString *secretKey = key;
    if (secretKey == nil ||
        [secretKey isKindOfClass: [NSString class]] == NO ||
        secretKey.length == 0) {
        PLVVodLogInfo(@"网络HttpDnskey为空，使用本地缓存HttpDnskey");
        secretKey = [PLVVodHttpDnsManager getHttpDnsSecretKeyFromLocalCache];
    }else {
        [PLVVodHttpDnsManager setHttpDnsSecretKeyFromLocalCache:secretKey];
    }
    
    if (secretKey == nil ||
        [secretKey isKindOfClass: [NSString class]] == NO ||
        secretKey.length == 0) {
        PLVVodLogInfo(@"本地缓存HttpDnskey为空，使用固定HttpDnskey");
        secretKey = PLVVodHttpDnsSecretKey;
    }
    
    //解密key
    NSString *decryptedKey = [PLVVodHttpDnsManager decryptedHttpDnsSecretKey:secretKey];
    if (decryptedKey == nil ||
        [decryptedKey isKindOfClass: [NSString class]] == NO ||
        decryptedKey.length == 0) {
        PLVVodLogError(@"HttpDnskey解密失败");
        return;
    }
    
    /// 配置HTTPDNS账号信息
    HttpDnsService *httpdns = [[HttpDnsService alloc] initWithAccountID:PLVVodHttpDnsAccountID secretKey:decryptedKey];
    self.hadHttpDnsInit = YES;
    [httpdns setReuseExpiredIPEnabled:YES];
    [httpdns setNetworkingTimeoutInterval:3];
    [httpdns setPreResolveAfterNetworkChanged:YES];
    [httpdns setPreResolveHosts:[PLVVodHlsHelper commomTsHosts]];
    [httpdns setPersistentCacheIPEnabled:YES];
    // 允许降级，解析本地ip
    [httpdns setDegradeToLocalDNSEnabled:YES];
    // IP 优选排序
    NSDictionary *IPRankingDatasource = @{
                                     @"hls.videocc.net": @443,
                                     @"ms-ts.videocc.net": @443,
                                     @"hwab-mts.videocc.net":@443,
                                     @"ab-mts.videocc.net":@443,
                                     @"ws-mts.videocc.net":@443,
                                     @"hw-mts.videocc.net":@443
                                     };
    [httpdns setIPRankingDatasource:IPRankingDatasource];
}

/*
/// 获取域名对应格式化后的IP (针对ipv6)
/// @param host 域名
- (NSString *)getIpByHostAsyncInURLFormat:(NSString *)host {
    if (!self.hadHttpDnsInit) {
        [self configHttpDNSWithSecretKey:@""];
    }
    if (self.DNSOptimize) {
        NSMutableDictionary *hostDict = [self.DNSOptimizeCheckHostDict objectForKey:host];
        if (!hostDict) {
            hostDict = [NSMutableDictionary dictionary];
            NSDictionary *ipDict = [[HttpDnsService sharedInstance] getIPv4_v6ByHostAsync:host];
            hostDict[@"httpdns"] = ipDict;
            self.DNSOptimizeCheckHostDict[host] = hostDict;
        } else {
            if ([hostDict objectForKey:@"IPV4Array"]) {
                return hostDict[@"IPV4Array"][0];
            } else if ([hostDict objectForKey:@"IPV6Array"]) {
                return [self convertIpV6Format:hostDict[@"IPV6Array"][0]];
            }
        }
        NSDictionary *ipDict = hostDict[@"httpdns"];
        if ([ipDict objectForKey:@"ALICLOUDHDNS_IPV4"]) {
            return ipDict[@"ALICLOUDHDNS_IPV4"][0];
        } else if ([ipDict objectForKey:@"ALICLOUDHDNS_IPV6"]) {
            return [self convertIpV6Format:ipDict[@"ALICLOUDHDNS_IPV6"][0]];
        }
        return [self convertIpV6Format:[[HttpDnsService sharedInstance] getIpByHostAsyncInURLFormat:host]];
    } else {
        HttpDnsService *httpdns = [HttpDnsService sharedInstance];
        NSString *ip = [httpdns getIpByHostAsyncInURLFormat:host];
        //NSString *ip = [httpdns getIPv6ByHostAsync:host]; // only for test ipv6
        ip = [self convertIpV6Format:ip];
        return ip;
    }
}*/

- (NSString *)getIpByHostAsyncInURLFormat:(NSString *)host {
    NSString *ipAddress = nil;
    if (!self.hadHttpDnsInit) {
        // 初始化
        [self configHttpDNSWithSecretKey:@""];
    }
    HttpdnsRequest *newRequest = [[HttpdnsRequest alloc] initWithHost:host queryIpType:HttpdnsQueryIPTypeAuto];
    if (self.DNSOptimize) {
        NSMutableDictionary *hostDict = [self.DNSOptimizeCheckHostDict objectForKey:host];
        if (!hostDict) {
            hostDict = [NSMutableDictionary dictionary];
            HttpdnsResult *result = [[HttpDnsService sharedInstance] resolveHostSyncNonBlocking:newRequest];
            NSMutableDictionary *ipDict = [[NSMutableDictionary alloc] init];
            // 数据类型转化为 旧版SDK 兼容后续逻辑
            if (result.hasIpv4Address){
                [ipDict setObject:result.ips forKey:@"ALICLOUDHDNS_IPV4"];
            }
            if (result.hasIpv6Address){
                [ipDict setObject:result.ipv6s forKey:@"ALICLOUDHDNS_IPV6"];
            }
            if(ipDict.count > 0){
                hostDict[@"httpdns"] = ipDict;
                self.DNSOptimizeCheckHostDict[host] = hostDict;
            }
        } else {
            // 优选后的ip 地址
            if ([hostDict objectForKey:@"IPV4Array"]) {
                return hostDict[@"IPV4Array"][0];
            } else if ([hostDict objectForKey:@"IPV6Array"]) {
                return [self convertIpV6Format:hostDict[@"IPV6Array"][0]];
            }
        }
        NSDictionary *ipDict = hostDict[@"httpdns"];
        if ([ipDict objectForKey:@"ALICLOUDHDNS_IPV4"]) {
            return ipDict[@"ALICLOUDHDNS_IPV4"][0];
        } else if ([ipDict objectForKey:@"ALICLOUDHDNS_IPV6"]) {
            return [self convertIpV6Format:ipDict[@"ALICLOUDHDNS_IPV6"][0]];
        }
    } else {
        HttpdnsResult *result = [[HttpDnsService sharedInstance] resolveHostSyncNonBlocking:newRequest];
        if (result.hasIpv4Address){
            ipAddress = [result firstIpv4Address];
        }
        else if (result.hasIpv6Address){
            ipAddress = [result firstIpv6Address];
        }
        ipAddress = [self convertIpV6Format:ipAddress];
    }
    
    return ipAddress;
}

- (NSString *)convertIpV6Format:(NSString *)ipAddr{
    NSString *rightFormat = ipAddr;
    if (![PLVVodUtil isNilString:ipAddr]){
        if (![ipAddr hasPrefix:@"["] && [ipAddr rangeOfString:@":"].length > 0){
            rightFormat = [NSString stringWithFormat:@"[%@]", ipAddr];
        }
    }
    return rightFormat;
}

/// 获取域名对应的IP，单IP
/// @param host 域名
- (NSString *)getIpByHostAsync:(NSString *)host {
    if (!self.hadHttpDnsInit) return nil;
    
    NSString *ipAddress = nil;
    HttpdnsRequest *newRequest = [[HttpdnsRequest alloc] initWithHost:host queryIpType:HttpdnsQueryIPTypeAuto];
    HttpdnsResult *result = [[HttpDnsService sharedInstance] resolveHostSyncNonBlocking:newRequest];
    if (result.hasIpv4Address){
        ipAddress = [result firstIpv4Address];
    }
    else if (result.hasIpv6Address){
        ipAddress = [result firstIpv6Address];
    }
    ipAddress = [self convertIpV6Format:ipAddress];
    
    return ipAddress;
}

#pragma mark - [ Private Method ]

/// 从本地缓存中读取HttpDnsSecretKey密文
+ (NSString *)getHttpDnsSecretKeyFromLocalCache {
    NSDictionary *httpdnsDict = [[NSUserDefaults standardUserDefaults] dictionaryForKey:PLVVodHttpDnsSecretKeyCacheKey];
    NSTimeInterval time = [httpdnsDict[@"time"] doubleValue];
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    if (currentTime - time > PLVVodHttpDnsSecretKeyCacheTime) {
        // 缓存超时
        return @"";
    }
    NSString *secretKey = httpdnsDict[@"secretKey"];
    return secretKey;
}

/// 设置HttpDnsSecretKey密文到本地缓存中
/// @param secretKey 密钥
+ (void)setHttpDnsSecretKeyFromLocalCache:(NSString *)secretKey {
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    NSDictionary *dict = @{@"time" : @(currentTime),
                           @"secretKey" : secretKey};
    [[NSUserDefaults standardUserDefaults] setObject:dict forKey:PLVVodHttpDnsSecretKeyCacheKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

/// 解密HttpDnsSecretKey
/// @param secretKey 密文
+ (NSString *)decryptedHttpDnsSecretKey:(NSString *)secretKey {
    NSData *enAesData = [PLVVodUtil dataWithHexString:secretKey];
    NSData *deAesData = [PLVVodUtil AES128DecryptedDataWithKey:PLVVodHttpDnsSecretKeyAES_KEY iv:PLVVodHttpDnsSecretKeyAES_IV data:enAesData];
    NSData *deBase64Data = [[NSData alloc] initWithBase64EncodedData:deAesData options:0];
    NSString *deBase64String = [[NSString alloc]
      initWithData:deBase64Data encoding:NSUTF8StringEncoding];
    return deBase64String;
}

#pragma mark - [ DNSOptimize ]
- (void)enableDNSOptimize:(BOOL) on {
    if (self.DNSOptimize == on)
        return;
    self.DNSOptimize = on;
    if (self.DNSOptimize) {
        self.DNSOptimizeTimer =
            [NSTimer scheduledTimerWithTimeInterval:30.0
                                             target:self
                                           selector:@selector(refreshDelay)
                                           userInfo:nil
                                            repeats:YES];
    } else {
        if (self.DNSOptimizeTimer) {
        [self.DNSOptimizeTimer invalidate];
        self.DNSOptimizeTimer = nil;
        }
    }
}

- (void)networkStatusDidChange:(NSNotification *)notification {
    PLVVodReachability *reachability = [PLVVodReachability sharedReachability];
    if (reachability.currentReachabilityStatus == PLVVodNotReachable) {
        return;
    }
    
    PLVVodLogInfo(@"retesting DNSOptimize delay because of network status changed: %ld", (long)reachability.currentReachabilityStatus);
    [self refreshDelay];
    self.DNSOptimizeTimer.fireDate = [NSDate dateWithTimeIntervalSinceNow:self.DNSOptimizeTimer.timeInterval];
}

/*
- (void)refreshDelay {
    [_DNSOptimizeCheckHostDict enumerateKeysAndObjectsUsingBlock:^(NSString *host, NSMutableDictionary *hostDict, BOOL *stop) {
        ping(host, 1, ^(NSDictionary *extraInfo) {
            hostDict[@"localdnsPing"] = nil;
            NSString *resolveError = extraInfo[@"error"];
            if (resolveError) {
                PLVVodLogWarn(@"resolve host(%@) failed: %@", host, resolveError);
                return;
            }
            NSNumber *sequenceNumber = [[extraInfo[@"packages"] allKeys] firstObject];
            NSDictionary *firstPackage = extraInfo[@"packages"][sequenceNumber];
            if ([firstPackage objectForKey:@"failedError"]) {
                PLVVodLogWarn(@"ping host(%@) failed: %@", host, firstPackage[@"failedError"]);
                return;
            }
            if (![firstPackage objectForKey:@"elapsedTime"]) {
                PLVVodLogWarn(@"ping host(%@) timeout", host);
                return;
            }
            hostDict[@"localdnsPing"] = @{
                @"ip": extraInfo[@"ip"],
                @"delay": firstPackage[@"elapsedTime"],
            };
        });
        
        hostDict[@"IPV4Array"] = nil;
        hostDict[@"IPV6Array"] = nil;
        NSDictionary *ipDict = [[HttpDnsService sharedInstance] getIPv4_v6ByHostAsync:host];
        hostDict[@"httpdns"] = ipDict;
        if (ipDict) {
#if 0
            // 启用IP优选 https://help.aliyun.com/document_detail/435272.html#section-6v3-4k0-656
            if ([ipDict objectForKey:@"ALICLOUDHDNS_IPV4"]) {
                hostDict[@"IPV4Array"] = ipDict[@"ALICLOUDHDNS_IPV4"];
            }
            if ([ipDict objectForKey:@"ALICLOUDHDNS_IPV6"]) {
                hostDict[@"IPV6Array"] = ipDict[@"ALICLOUDHDNS_IPV6"];
            }
#else
            typedef void (^ProcessBlock)(NSArray<NSString *> *, void (^)(NSArray<NSString *> *, NSArray<NSDictionary *> *));
            ProcessBlock block =
                ^(NSArray<NSString *> *ips,
                    void (^completion)(NSArray<NSString *> *ipArray,
                                       NSArray<NSDictionary *> *pingDictArray)) {
                    NSMutableArray<NSString *> *ipArray = [NSMutableArray array];
                    NSMutableArray<NSDictionary *> *pingDictArray = [NSMutableArray array];
                    for (NSString *ip in ips) {
                        ping(ip, 1, ^(NSDictionary *extraInfo) {
                            NSString *resolveError = extraInfo[@"error"];
                            if (resolveError)
                                return;
                            NSNumber *sequenceNumber = [[extraInfo[@"packages"] allKeys] firstObject];
                            NSDictionary *firstPackage = extraInfo[@"packages"][sequenceNumber];
                            if ([firstPackage objectForKey:@"failedError"])
                                return;
                            if (![firstPackage objectForKey:@"elapsedTime"])
                                return;

                            double delay = [firstPackage[@"elapsedTime"] doubleValue];
                            NSDictionary *pingDict = @{@"ip" : ip, @"delay" : @(delay)};
                            for (int i = 0; i < pingDictArray.count; i++) {
                                if (delay < [pingDictArray[i][@"delay"] doubleValue]) {
                                    [ipArray insertObject:ip atIndex:i];
                                    [pingDictArray insertObject:pingDict atIndex:i];
                                    return;
                                }
                            }
                            [ipArray addObject:ip];
                            [pingDictArray addObject:pingDict];
                        });
                    }
                    completion([NSArray arrayWithArray:ipArray], [NSArray arrayWithArray:pingDictArray]);
              };
            if ([ipDict objectForKey:@"ALICLOUDHDNS_IPV4"]) {
                block(ipDict[@"ALICLOUDHDNS_IPV4"],
                    ^(NSArray<NSString *> *ipArray, NSArray<NSDictionary *> *pingDictArray) {
                        if (ipArray.count) {
                            hostDict[@"IPV4Array"] = ipArray;
                            hostDict[@"httpdnsIPV4Ping"] = pingDictArray;
                        }
                    });
            }
            if ([ipDict objectForKey:@"ALICLOUDHDNS_IPV6"]) {
                block(ipDict[@"ALICLOUDHDNS_IPV6"],
                    ^(NSArray<NSString *> *ipArray, NSArray<NSDictionary *> *pingDictArray) {
                        if (ipArray.count) {
                            hostDict[@"IPV6Array"] = ipArray;
                            hostDict[@"httpdnsIPV6Ping"] = pingDictArray;
                        }
                    });            
            }
#endif
        }
        
        NSArray *ipv4 = hostDict[@"IPV4Array"];
        if (ipv4.count)
            return;
        
        if (!hostDict[@"localdnsPing"] || hostDict[@"localdnsPing"][@"pingError"]) {
            return;
        }
        NSString *ip = hostDict[@"localdnsPing"][@"ip"];
        if ([ip containsString:@"."])
            hostDict[@"IPV4Array"] = @[ip];
        else
            hostDict[@"IPV6Array"] = @[ip];
        return;
    }];
    PLVVodLogInfo(@"DNSOptimize Check Host result:%@", self.DNSOptimizeCheckHostDict);
}
*/

// 定时刷新 当前30秒，获取最优ip 地址
- (void)refreshDelay {
    [_DNSOptimizeCheckHostDict enumerateKeysAndObjectsUsingBlock:^(NSString *host, NSMutableDictionary *hostDict, BOOL *stop) {
        ping(host, 1, ^(NSDictionary *extraInfo) {
            // 获取 localdns 解析的ip
            hostDict[@"localdnsPing"] = nil;
            NSString *resolveError = extraInfo[@"error"];
            if (resolveError) {
                PLVVodLogWarn(@"resolve host(%@) failed: %@", host, resolveError);
                return;
            }
            NSNumber *sequenceNumber = [[extraInfo[@"packages"] allKeys] firstObject];
            NSDictionary *firstPackage = extraInfo[@"packages"][sequenceNumber];
            if ([firstPackage objectForKey:@"failedError"]) {
                PLVVodLogWarn(@"ping host(%@) failed: %@", host, firstPackage[@"failedError"]);
                return;
            }
            if (![firstPackage objectForKey:@"elapsedTime"]) {
                PLVVodLogWarn(@"ping host(%@) timeout", host);
                return;
            }
            hostDict[@"localdnsPing"] = @{
                @"ip": extraInfo[@"ip"],
                @"delay": firstPackage[@"elapsedTime"],
            };
        });
        
        hostDict[@"IPV4Array"] = nil;
        hostDict[@"IPV6Array"] = nil;
        HttpdnsRequest *newRequest = [[HttpdnsRequest alloc] initWithHost:host queryIpType:HttpdnsQueryIPTypeAuto];
        HttpdnsResult *result = [[HttpDnsService sharedInstance] resolveHostSyncNonBlocking:newRequest];
        NSMutableDictionary *ipDict = [[NSMutableDictionary alloc] init];
        // 数据类型转化为 旧版SDK 兼容后续逻辑
        if (result.hasIpv4Address){
            [ipDict setObject:result.ips forKey:@"ALICLOUDHDNS_IPV4"];
        }
        if (result.hasIpv6Address){
            [ipDict setObject:result.ipv6s forKey:@"ALICLOUDHDNS_IPV6"];
        }
        
        hostDict[@"httpdns"] = ipDict;
        if (ipDict) {
#if 1
            // 启用IP优选 https://help.aliyun.com/document_detail/435272.html#section-6v3-4k0-656
            if ([ipDict objectForKey:@"ALICLOUDHDNS_IPV4"]) {
                hostDict[@"IPV4Array"] = ipDict[@"ALICLOUDHDNS_IPV4"];
            }
            if ([ipDict objectForKey:@"ALICLOUDHDNS_IPV6"]) {
                hostDict[@"IPV6Array"] = ipDict[@"ALICLOUDHDNS_IPV6"];
            }
#else
            typedef void (^ProcessBlock)(NSArray<NSString *> *, void (^)(NSArray<NSString *> *, NSArray<NSDictionary *> *));
            ProcessBlock block =
                ^(NSArray<NSString *> *ips,
                    void (^completion)(NSArray<NSString *> *ipArray,
                                       NSArray<NSDictionary *> *pingDictArray)) {
                    NSMutableArray<NSString *> *ipArray = [NSMutableArray array];
                    NSMutableArray<NSDictionary *> *pingDictArray = [NSMutableArray array];
                    for (NSString *ip in ips) {
                        ping(ip, 1, ^(NSDictionary *extraInfo) {
                            NSString *resolveError = extraInfo[@"error"];
                            if (resolveError)
                                return;
                            NSNumber *sequenceNumber = [[extraInfo[@"packages"] allKeys] firstObject];
                            NSDictionary *firstPackage = extraInfo[@"packages"][sequenceNumber];
                            if ([firstPackage objectForKey:@"failedError"])
                                return;
                            if (![firstPackage objectForKey:@"elapsedTime"])
                                return;

                            double delay = [firstPackage[@"elapsedTime"] doubleValue];
                            NSDictionary *pingDict = @{@"ip" : ip, @"delay" : @(delay)};
                            for (int i = 0; i < pingDictArray.count; i++) {
                                if (delay < [pingDictArray[i][@"delay"] doubleValue]) {
                                    [ipArray insertObject:ip atIndex:i];
                                    [pingDictArray insertObject:pingDict atIndex:i];
                                    return;
                                }
                            }
                            [ipArray addObject:ip];
                            [pingDictArray addObject:pingDict];
                        });
                    }
                    completion([NSArray arrayWithArray:ipArray], [NSArray arrayWithArray:pingDictArray]);
              };
            if ([ipDict objectForKey:@"ALICLOUDHDNS_IPV4"]) {
                block(ipDict[@"ALICLOUDHDNS_IPV4"],
                    ^(NSArray<NSString *> *ipArray, NSArray<NSDictionary *> *pingDictArray) {
                        if (ipArray.count) {
                            hostDict[@"IPV4Array"] = ipArray;
                            hostDict[@"httpdnsIPV4Ping"] = pingDictArray;
                        }
                    });
            }
            if ([ipDict objectForKey:@"ALICLOUDHDNS_IPV6"]) {
                block(ipDict[@"ALICLOUDHDNS_IPV6"],
                    ^(NSArray<NSString *> *ipArray, NSArray<NSDictionary *> *pingDictArray) {
                        if (ipArray.count) {
                            hostDict[@"IPV6Array"] = ipArray;
                            hostDict[@"httpdnsIPV6Ping"] = pingDictArray;
                        }
                    });
            }
#endif
        }
        
        NSArray *ipv4 = hostDict[@"IPV4Array"];
        if (ipv4.count)
            return;
        
        // httpdns 未正确解析，获取localdns 解析地址
        if (!hostDict[@"localdnsPing"] || hostDict[@"localdnsPing"][@"pingError"]) {
            return;
        }
        NSString *ip = hostDict[@"localdnsPing"][@"ip"];
        if ([ip containsString:@"."])
            hostDict[@"IPV4Array"] = @[ip];
        else
            hostDict[@"IPV6Array"] = @[ip];
        return;
    }];
    PLVVodLogInfo(@"DNSOptimize Check Host result:%@", self.DNSOptimizeCheckHostDict);
}


@end
