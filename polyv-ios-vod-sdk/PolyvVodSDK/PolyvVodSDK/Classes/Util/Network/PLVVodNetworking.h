//
//  PLVVodNetworking.h
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/10.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PLVVodVideo.h"
#import "PLVVodConstans.h"

@interface PLVVodNetworking : NSObject

+ (void)logEnable:(BOOL)enable;

#pragma mark - 工具方法

/// 增量请求
+ (NSMutableURLRequest *)risingRequestWithURL:(NSURL *)URL localPath:(NSString *)path currentSize:(NSInteger *)fileSize;

/// 检查在线 key
+ (void)checkKeyWithVid:(NSString *)vid quality:(PLVVodQuality)quality completion:(void (^)(NSError *error))completion;

/// 检查请求
+ (void)checkRequest:(NSMutableURLRequest *)request completion:(void (^)(NSError *error))completion;
+ (void)checkUrl:(NSString *)url completion:(void (^)(NSError *error))completion;

/// 设置请求头信息
+ (void)setupHTTPHeaderWithRequest:(NSMutableURLRequest *)request;

#pragma mark - 业务接口

/// 获取 videoJson 字典
+ (void)requestVideoDicWithVid:(NSString *)vid completion:(void (^)(NSDictionary *videoDic, NSError *error))completion;

/// 获取 videoJson 字典,返回原始数据
+ (void)requestVideoJsonWithVid:(NSString *)vid completion:(void (^)(NSDictionary *videoDic, NSString *videoData, NSError *error))completion;

/// 获取问答字典
+ (void)requestExamDicWithVid:(NSString *)vid completion:(void (^)(NSArray *exams, NSError *error))completion;

/// 答题统计信息上报
+ (void)saveExamStatisticsWithPid:(NSString *)pid
                              eid:(NSString *)examId
                              uid:(NSString *)userId
                        quesition:(NSString *)question
                              vid:(NSString *)vid
                          correct:(BOOL)correct
                           anwser:(NSString *)userAnswer
                       completion:(void(^)(NSError *error))completion;

/// 异步获取m3u8
+ (void)requestPdxWithUrl:(NSString *)url params:(NSDictionary *)paramDict completion:(void (^)(NSDictionary *pdxDictionary, NSError *error))completion;
+ (void)requestPdxWithUrl:(NSString *)url backHost:(NSString *)backHost completion:(void (^)(NSDictionary *, NSError *))completion;
+ (void)requestM3u8WithUrl:(NSString *)url params:(NSDictionary *)paramDict completion:(void (^)(NSString *content, NSError *error))completion;
+ (void)requestM3u8WithUrl:(NSString *)url params:(NSDictionary *)paramDic requestHandler:(void (^)(NSMutableURLRequest *request))requestHandler completion:(void (^)(NSString *content, NSError *error))completion;


/// 异步获取token
+ (void)requestKeyRquestTokenWithVid:(NSString *)vid completion:(void (^)(NSString *tokenData, NSError *error))completion;
/// 异步获取key
+ (void)requestKeyWithVid:(NSString *)vid token:(NSString*)token quality:(PLVVodQuality)quality completion:(void (^)(NSData *keyData, NSError *error))completion;
/// 组装获取key 的URLRequest
+ (NSMutableURLRequest *)keyRquestWithVid:(NSString *)vid token:(NSString*)token quality:(PLVVodQuality)quality;


/// 请求加密视频令牌
+ (void)requestVideoTokenWithVid:(NSString *)vid completion:(void (^)(NSString *videoToken, NSError *error))completion;

/// 请求加密视频令牌
+ (void)requestVideoTokenWithModel:(PLVVodVideo *)videoModel completion:(void (^)(NSString *videoToken, NSString *host, NSError *error))completion;

/// 获取投屏加密视频Key，iv
+ (void)requestCastKeyIvWithVid:(NSString *)vid constKey:(NSString *)constKey quality:(NSInteger)quality completion:(void (^)(NSString *key, NSString *iv, NSError *error))completion;

/// 获取字幕
+ (void)requestSubtitleWithUrl:(NSString *)url completion:(void(^)(NSString *subtitle, NSError *error))completion;

/// videojson 解密
+ (NSData *)AES128DecryptedDataWithBody:(NSString *)body vid:(NSString *)vid;

/// 获取pptjson
+ (void)requestPPTJsonWithVid:(NSString *)vid completion:(void (^)(NSDictionary *responseDict, NSError *error))completion;

/// 获取ppt zip url
+ (void)requestPPTZipUrlWithVid:(NSString *)vid completion:(void (^)(NSString *zipUrl, NSError *error))completion;

/// 获取最近一次播放进度
+ (void)requestLastPositionWithVid:(NSString *)vid viewerId:(NSString *)viewerId completion:(void(^)(NSTimeInterval lastPosition))completion;

@end
