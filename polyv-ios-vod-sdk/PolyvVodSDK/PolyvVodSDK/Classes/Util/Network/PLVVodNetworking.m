//
//  PLVVodNetworking.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/10.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodNetworking.h"
#import "PLVVodUtil.h"
#import "PLVVodSettings.h"
#import "PLVVodNetworkRetryManager.h"
#import "PLVVodSettings.h"
#import "PLVVodReachability.h"

//#import "PLVVodDanmu.h"

#define PLV_HM_POST @"POST"
#define PLV_HM_GET @"GET"

static BOOL PLVVodNetworkingLogEnable;
static NSTimeInterval PLVVodRequestTimeout = 10;
static NSString * const PLVVodKeepNilKey = @"PLVVodKeepNilKey";

@interface PLVVodNetworking ()

/// 异步获取数据
+ (void)requestData:(NSURLRequest *)request completion:(void (^)(NSData *data, NSError *error))completion;
+ (void)requestDictionary:(NSURLRequest *)request completion:(void (^)(NSDictionary *dic, NSError *error))completion;
+ (void)requestString:(NSURLRequest *)request completion:(void (^)(NSString *string, NSError *error))completion;

/// 快速生成Request
+ (NSMutableURLRequest *)requestWithUrl:(NSString *)url method:(NSString *)HTTPMethod params:(NSDictionary *)paramDic;

@end

@implementation PLVVodNetworking

+ (void)logEnable:(BOOL)enable {
	PLVVodNetworkingLogEnable = enable;
}

#pragma mark - 工具方法

/// 检查vid是否正确
+ (BOOL)validateVid:(NSString *)vid{
    BOOL isCorrectVid;
    if([PLVVodUtil isNilString:vid]){
        isCorrectVid = NO;
    }else{
        NSString *VID = @"^[[a-z]|[0-9]]{32}_[[a-z]|[0-9]]$";
        NSPredicate *regexVid = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",VID];
        if([regexVid evaluateWithObject:vid]){
            isCorrectVid = YES;
        }else{
            isCorrectVid = NO;
        }
    }
    return isCorrectVid;
}

/// 异步获取数据
+ (void)requestData:(NSURLRequest *)request completion:(void (^)(NSData *data, NSError *error))completion {
    if (!request) {
        NSError *plvError = PLVVodErrorMake(fetch_error, ^(NSMutableDictionary *userInfo) {
            userInfo[NSLocalizedDescriptionKey] = @"网络请求request为空";
        });
        PLVVodLogError(@"网络请求参数错误，%@", plvError);
        if (completion) completion(nil, plvError);
        return;
    }
    
    // 接口设置10 秒的请求延迟
    NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
    config.timeoutIntervalForRequest = 10.0;
    config.timeoutIntervalForResource = 10.0;
    NSURLSession *session = [NSURLSession sessionWithConfiguration:config];
    
    [[session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
		NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
		NSInteger httpStatusCode = httpResponse.statusCode;
		if (error) {
            // 网络错误
            PLVVodErrorCode error_code = network_error; // 弱网，网络超时等
            if (NSURLErrorNotConnectedToInternet == error.code){
                error_code = network_unreachable; // 无网络连接
            }
			NSError *plvError = PLVVodErrorMakeWithError(error_code, error, ^(NSMutableDictionary *userInfo) {
                // add by libl [增加服务器响应状态码,给Qos统计] 2018-12-06 start
                userInfo[PLVVodHttpStatusCodeKey] = [NSString stringWithFormat:@"%zd", httpStatusCode];
                userInfo[NSURLErrorKey] = request.URL;
                userInfo[PLVVodNSURLErrorCodeKey] = [NSString stringWithFormat:@"%d", (int)error.code];
                // add end
			});
			if (completion) completion(nil, plvError);
			PLVVodLogError(@"网络错误: %@", error);
		} else if (httpStatusCode != 200) {
            // 服务器错误
			NSString *errorMessage = [NSString stringWithFormat:@"服务器响应失败，状态码:%zd", httpResponse.statusCode];
			NSError *plvError = PLVVodErrorMake(server_error, ^(NSMutableDictionary *userInfo) {
				userInfo[NSLocalizedDescriptionKey] = errorMessage;
				userInfo[NSURLErrorKey] = response.URL;
                // add by libl [增加服务器响应状态码,给Qos统计] 2018-12-05 start
                userInfo[PLVVodHttpStatusCodeKey] = [NSString stringWithFormat:@"%zd", httpStatusCode];
                // add end
			});
			PLVVodLogError(@"服务器错误，%@", plvError);
			if (completion) completion(nil, plvError);
		} else {
			if (completion) completion(data, error);
		}
	}] resume];
}

+ (void)requestDictionary:(NSURLRequest *)request completion:(void (^)(NSDictionary *dic, NSError *error))completion {
	[self requestData:request completion:^(NSData *data, NSError *error) {
		if (error) {
			if (completion) completion(nil, error);
			return;
		}
		
		NSDictionary *responseDic = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];
		if (error){
			NSString *contentText = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
			PLVVodLogError(@"json 读取错误，%@", error.localizedDescription);
			NSError *plvError = PLVVodErrorMakeWithError(json_read_error, error, ^(NSMutableDictionary *userInfo) {
				userInfo[NSURLErrorKey] = request.URL;
                userInfo[PLVVodHttpStatusCodeKey] = @"200";
				userInfo[NSLocalizedFailureReasonErrorKey] = contentText;
			});
			if (PLVVodNetworkingLogEnable) NSLog(@"%@ - 出错文本 = \n%@", request.URL, contentText);
			if (completion) completion(nil, plvError);
			return;
		}
		if (PLVVodNetworkingLogEnable) NSLog(@"%@ - 请求结果 = %@", request.URL, responseDic);
		PLVVodNetworkingLogEnable = NO;
		if (completion) completion(responseDic, nil);
	}];
}

+ (void)requestString:(NSURLRequest *)request completion:(void (^)(NSString *string, NSError *error))completion {
	[self requestData:request completion:^(NSData *data, NSError *error) {
		if (error) {
			if (completion) completion(nil, error);
			return;
		}
		
		NSString *responseString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
		if (completion) completion(responseString, nil);
	}];
}

/// 快速生成Request
+ (NSMutableURLRequest *)requestWithUrl:(NSString *)url method:(NSString *)HTTPMethod params:(NSDictionary *)paramDic {
	NSString *urlString = url.copy;
	NSMutableURLRequest *request = [[NSMutableURLRequest alloc] init];
	if (paramDic.count) {
        NSString *parameters = [PLVVodUtil convertDictionaryToSortedString:paramDic];
		if ([PLV_HM_GET isEqualToString:HTTPMethod]) {
			urlString = [NSString stringWithFormat:@"%@?%@", urlString, parameters];
		}else if ([PLV_HM_POST isEqualToString:HTTPMethod]){
            
            NSData *bodyData = [parameters dataUsingEncoding:NSUTF8StringEncoding];
			request.HTTPBody = bodyData;
		}
	}
	
	NSURL *URL = [NSURL URLWithString:[urlString stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding]];
	request.URL = URL;
	request.HTTPMethod = HTTPMethod;
	request.timeoutInterval = PLVVodRequestTimeout;
	[self setupHTTPHeaderWithRequest:request];
	if (PLVVodNetworkingLogEnable) NSLog(@"%@ - 参数列表 = %@\t%@\n\n", url, paramDic, urlString);
	return request;
}

/// 获取文件大小
+ (NSInteger)currentSizeWithPath:(NSString *)path {
	NSDictionary *fileDict = [[NSFileManager defaultManager] attributesOfItemAtPath:path error:nil];
	return (NSInteger)fileDict.fileSize;
}

/// 设置请求头信息
+ (void)setupHTTPHeaderWithRequest:(NSMutableURLRequest *)request {
	[request setValue:[PLVVodUtil userAgent] forHTTPHeaderField:@"User-Agent"];
}

#pragma mark - public

/// 增量请求
+ (NSMutableURLRequest *)risingRequestWithURL:(NSURL *)URL localPath:(NSString *)path currentSize:(NSInteger *)fileSize {
	NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:URL];
	// 设置请求头信息
	NSInteger currentSize = [self currentSizeWithPath:path];
	*fileSize = currentSize;
	NSString *range = [NSString stringWithFormat:@"bytes=%zd-", currentSize];
	[request setValue:range forHTTPHeaderField:@"Range"];
	[self setupHTTPHeaderWithRequest:request];
	return request;
}

/// 检查在线 key
+ (void)checkKeyWithVid:(NSString *)vid quality:(PLVVodQuality)quality completion:(void (^)(NSError *error))completion {
    [self requestKeyRquestTokenWithVid:vid completion:^(NSString *tokenData, NSError *error) {
        if (error) {
            NSError *plvError = PLVVodErrorMakeWithError(network_error, error, ^(NSMutableDictionary *userInfo) {
                // userInfo[NSURLErrorKey] = request.URL;
                // userInfo[PLVVodHttpStatusCodeKey] = @"0";
                // TODO
            });
			if (completion) completion(plvError);
            return;
        }
        NSMutableURLRequest *request = [self keyRquestWithVid:vid token:tokenData quality:quality];
        [self checkRequest:request completion:completion];
    }];
}

/// 检查请求
+ (void)checkRequest:(NSMutableURLRequest *)request completion:(void (^)(NSError *error))completion {
	[request setHTTPMethod:@"HEAD"];
	NSString *userAgent = [NSString stringWithFormat:@"%@_check", [PLVVodUtil userAgent]];
	[request setValue:userAgent forHTTPHeaderField:@"User-Agent"];
	[[[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
		if (error) {
            // 网络错误（线路不通）
			NSError *plvError = PLVVodErrorMakeWithError(network_error, error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSURLErrorKey] = request.URL;
                userInfo[PLVVodHttpStatusCodeKey] = @"0";
            });
			if (completion) completion(plvError);
			error = nil;
			return;
		}
		
		NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
		NSDictionary *headerFields = httpResponse.allHeaderFields;
		NSInteger statusCode = httpResponse.statusCode;
		if (statusCode != 200) { // 服务器错误
			NSString *errorMessage = [NSString stringWithFormat:@"服务器响应失败，状态码:%zd",httpResponse.statusCode];
			NSError *plvError = PLVVodErrorMake(server_error, ^(NSMutableDictionary *userInfo) {
				userInfo[NSLocalizedDescriptionKey] = errorMessage;
				userInfo[NSURLErrorKey] = request.URL;
				userInfo[PLVVodSubErrorCodeKey] = @(statusCode);
                userInfo[PLVVodHttpStatusCodeKey] = [NSString stringWithFormat:@"%zd", statusCode];
				userInfo[NSLocalizedFailureReasonErrorKey] = headerFields;
			});
			PLVVodLogError(@"%@，服务器错误: %@", request.URL.absoluteString, plvError);
			if (completion) completion(plvError);
			return;
		}
		
		if (completion) completion(error);
	}] resume];
}

+ (void)checkUrl:(NSString *)url completion:(void (^)(NSError *error))completion {
	NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:nil];
	[self checkRequest:request completion:completion];
}

#pragma mark - 业务接口

+ (void)requestVideoDicWithVid:(NSString *)vid completion:(void (^)(NSDictionary *videoDic, NSError *error))completion {
    // vid错误
    if([self validateVid:vid] == NO){
        NSError *plvError = PLVVodErrorMake(vid_error, ^(NSMutableDictionary *userInfo) {
        });
        PLVVodLogError(@" vid错误，%@ ", plvError);
        if (completion) completion(nil, plvError);
        return;
    }
    
    NSString *mainHost = @"player.polyv.net";
    NSString *backupHost = @"v3.polyv.net";
    NSString *url = [NSString stringWithFormat:@"https://%@/secure/%@.json", mainHost, vid];
    NSString *backupUrl = [NSString stringWithFormat:@"https://%@/secure/%@.json", backupHost, vid];
    NSURL *mainURL = [NSURL URLWithString:url];
    NSURL *backURL = [NSURL URLWithString:backupUrl];
    
    NSString *dnsDomain = [PLVVodSettings sharedSettings].enableHttpDNS ? mainHost: nil;
    PLVVodNetworkRetryManager *retryManager = [[PLVVodNetworkRetryManager alloc] initWithPrimaryURL:mainURL
                                                                                          backupURL:backURL
                                                                                          dnsDomain:dnsDomain
                                                                                         httpMethod:PLV_HM_GET
                                                                                             params:nil
                                                                                      maxRetryCount:1
                                                                                       initialDelay:0];
    [retryManager sendRequestWithCompletion:^(NSData * _Nullable data, NSError * _Nullable error) {
        // 网络不可用判断
        if([PLVVodReachability sharedReachability].notReachability){
            NSError *plvError = PLVVodErrorMake(network_unreachable, ^(NSMutableDictionary *userInfo) {
                userInfo[NSURLErrorKey] = mainURL;
            });
            if (completion) completion(nil, plvError);

            return;
        }
        if (error ) {
            NSError *plvError = PLVVodErrorMakeWithError(playback_videojson_fetch_error, error, ^(NSMutableDictionary *userInfo) {
            });
            if (completion) completion(nil, plvError);
            return;
        }
        else if ([data isKindOfClass:[NSNull class]] || !data){
            NSError *plvError = PLVVodErrorMake(playback_videojson_fetch_error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSURLErrorKey] = mainURL;
            });
            if (completion) completion(nil, plvError);
            return;
        }
        
        // Parse JSON data
        NSError *jsonError = nil;
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&jsonError];
        if (error){
            NSString *contentText = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
            PLVVodLogError(@"json 读取错误，%@", error.localizedDescription);
            NSError *plvError = PLVVodErrorMakeWithError(json_read_error, error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSURLErrorKey] = mainURL;
                userInfo[PLVVodHttpStatusCodeKey] = @"200";
                userInfo[NSLocalizedFailureReasonErrorKey] = contentText;
            });
            if (completion) completion(nil, plvError);
            return;
        }
        
        // Check response code
        NSInteger code = [dic[@"code"] integerValue];
        if (code != 200) {
            NSString *message = dic[@"status"];
            PLVVodLogError(@"videoJson 请求错误，%@", message);
            NSError *plvError = PLVVodErrorMake(playback_videojson_fetch_error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSLocalizedFailureReasonErrorKey] = message;
                userInfo[PLVVodSubErrorCodeKey] = @(code);
                userInfo[PLVVodHttpStatusCodeKey] = @"200";
                userInfo[NSURLErrorKey] = mainURL;
            });
            if (completion) completion(nil, plvError);
            return;
        }
        
        // Decrypt video data
        NSString *encodedBody = dic[@"body"];
        NSData *videoJsonData = [self AES128DecryptedDataWithBody:encodedBody vid:vid];
        NSError *decodeError = nil;
        dic = [NSJSONSerialization JSONObjectWithData:videoJsonData options:NSJSONReadingMutableContainers error:&decodeError];
        if (decodeError) {
            PLVVodLogError(@"videoJson 解密失败，%@ encodedBody: \n%@", decodeError.localizedDescription, encodedBody);
            NSError *plvError = PLVVodErrorMakeWithError(json_read_error, decodeError, ^(NSMutableDictionary *userInfo) {
                userInfo[NSLocalizedFailureReasonErrorKey] = dic;
                userInfo[PLVVodHttpStatusCodeKey] = @"200";
                userInfo[NSURLErrorKey] = mainURL;
            });
            if (completion) completion(nil, plvError);
            return;
        }
        
        if (completion) completion(dic, nil);
    }];
}


+ (void)requestVideoJsonWithVid:(NSString *)vid completion:(void (^)(NSDictionary *, NSString *, NSError *))completion{
    
    // vid错误
    if([self validateVid:vid] == NO){
        NSError *plvError = PLVVodErrorMake(vid_error, ^(NSMutableDictionary *userInfo) {
        });
        PLVVodLogError(@" vid错误，%@ ", plvError);
        if (completion) completion(nil, nil, plvError);
        return;
    }
    
    void (^decodeVideoJson)(NSDictionary *dic, NSError *error) = ^(NSDictionary *dic, NSError *error) {
        if (error) {
            if (completion) completion(nil, nil, error);
            return;
        }
        NSInteger code = [dic[@"code"] integerValue];
        if (code != 200) {
            NSString *message = dic[@"status"];
            PLVVodLogError(@"视频请求错误，%@", message);
            NSError *plvError = PLVVodErrorMake(fetch_error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSLocalizedFailureReasonErrorKey] = message;
                userInfo[PLVVodSubErrorCodeKey] = @(code);
            });
            if (completion) completion(nil, nil, plvError);
            return;
        }
        
        NSString *encodedBody = dic[@"body"];
        NSData *videoJsonData = [self AES128DecryptedDataWithBody:encodedBody vid:vid];
        dic = [NSJSONSerialization JSONObjectWithData:videoJsonData options:NSJSONReadingMutableContainers error:&error];
        if (error){
            PLVVodLogError(@"videoJson 解析失败，%@ encodedBody: \n%@", error.localizedDescription, encodedBody);
            NSError *plvError = PLVVodErrorMakeWithError(json_read_error, error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSLocalizedFailureReasonErrorKey] = dic;
            });
            if (completion) completion(nil, nil, plvError);
            return;
        }
        if (completion) completion(dic, encodedBody, error);
    };
    
    NSString *mainHost = @"player.polyv.net";
    // !!!: 使用无缓存域名
    //mainHost = @"v.polyv.net";
    NSString *backupHost = @"v3.polyv.net";
    NSString *url = [NSString stringWithFormat:@"https://%@/secure/%@.json", mainHost, vid];
    NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:nil];
    [self requestDictionary:request completion:^(NSDictionary *dic, NSError *error) {
        if (error || !dic.count) {
            NSString *url = [NSString stringWithFormat:@"https://%@/secure/%@.json", backupHost, vid];
            NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:nil];
            [self requestDictionary:request completion:decodeVideoJson];
        } else {
            decodeVideoJson(dic, error);
        }
    }];
}

/// 获取问答字典
+ (void)requestExamDicWithVid:(NSString *)vid completion:(void (^)(NSArray *exams, NSError *error))completion {
    NSString *url = @"https://v.polyv.net/uc/exam/get";
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"vid"] = vid;
    NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:params];
    [self requestDictionary:request completion:^(NSDictionary *dic, NSError *error) {
        if (completion) completion((NSArray *)dic, error);
    }];
}

/// 上报答题统计
+ (void)saveExamStatisticsWithPid:(NSString *)pid eid:(NSString *)examId uid:(NSString *)userId quesition:(NSString *)question vid:(NSString *)vid correct:(BOOL)correct anwser:(NSString *)userAnswer completion:(void (^)(NSError *))completion{
    NSString *url = @"https://v.polyv.net/uc/examlog/save";
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"pid"] = pid;
    params[@"eid"] = examId;
    params[@"uid"] = userId;
    params[@"question"] = question;
    params[@"vid"] = vid;
    params[@"correct"] = [@(correct) stringValue];
    params[@"answer"] = userAnswer;
    
    NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_POST params:params];
    [self requestString:request completion:^(NSString *string, NSError *error) {
        completion(error);
    }];
}

/// 异步获取pdx
+ (void)requestPdxWithUrl:(NSString *)url params:(NSDictionary *)paramDict completion:(void (^)(NSDictionary *pdxDictionary, NSError *error))completion
{
    NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:paramDict];
    [self requestDictionary:request completion:^(NSDictionary *dic, NSError *error) {
        if (completion) completion(dic, error);
    }];
}

/// 异步获取pdx
+ (void)requestPdxWithUrl:(NSString *)url backHost:(NSString *)backHost completion:(void (^)(NSDictionary *, NSError *))completion{
    //
    NSURL *mainURL = [NSURL URLWithString:url];
    NSString *mainHost = mainURL.host;
    
    // 构造备份地址
    NSURL *backupURL = nil;
    if (backHost){
        NSURLComponents *components = [NSURLComponents componentsWithURL:[NSURL URLWithString:url] resolvingAgainstBaseURL:NO];
        components.host = backHost;
        backupURL = components.URL;
    }
    
    // 单元测试 ，修改住地址
//    url = [url stringByReplacingOccurrencesOfString:@"hls" withString:@"test"];
//    mainURL = [NSURL URLWithString:url];
    NSString *dnsDomain = [PLVVodSettings sharedSettings].enableHttpDNS ? mainHost: nil;
    PLVVodNetworkRetryManager *retryManager = [[PLVVodNetworkRetryManager alloc] initWithPrimaryURL:mainURL
                                                                                          backupURL:backupURL
                                                                                          dnsDomain:dnsDomain
                                                                                         httpMethod:PLV_HM_GET
                                                                                             params:nil
                                                                                      maxRetryCount:1
                                                                                       initialDelay:0];
    [retryManager sendRequestWithCompletion:^(NSData * _Nullable data, NSError * _Nullable error) {
        // 网络不可用判断
        if([PLVVodReachability sharedReachability].notReachability){
            NSError *plvError = PLVVodErrorMake(network_unreachable, ^(NSMutableDictionary *userInfo) {
                userInfo[NSURLErrorKey] = mainURL;
            });
            if (completion) completion(nil, plvError);
            return;
        }
        if (error ) {
            NSError *plvError = PLVVodErrorMakeWithError(pdx_fetch_error, error, ^(NSMutableDictionary *userInfo) {
            });
            if (completion) completion(nil, plvError);
            return;
        }
        else if ([data isKindOfClass:[NSNull class]] || !data){
            NSError *plvError = PLVVodErrorMake(pdx_fetch_error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSURLErrorKey] = mainURL;
            });
            if (completion) completion(nil, plvError);
            return;
        }
        
        // json解析
        NSDictionary *responseDic = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];
        if (error){
            NSString *contentText = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
            PLVVodLogError(@"json 读取错误，%@", error.localizedDescription);
            NSError *plvError = PLVVodErrorMakeWithError(json_read_error, error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSURLErrorKey] = mainURL;
                userInfo[PLVVodHttpStatusCodeKey] = @"200";
                userInfo[NSLocalizedFailureReasonErrorKey] = contentText;
            });
            if (completion) completion(nil, plvError);
            return;
        }
        
        // 返回字典
        if (completion) completion(responseDic, nil);
    }];
}

+ (void)requestM3u8WithUrl:(NSString *)url params:(NSDictionary *)paramDic completion:(void (^)(NSString *content, NSError *error))completion {
	[self requestM3u8WithUrl:url params:paramDic requestHandler:nil completion:completion];
}

+ (void)requestM3u8WithUrl:(NSString *)url params:(NSDictionary *)paramDic requestHandler:(void (^)(NSMutableURLRequest *request))requestHandler completion:(void (^)(NSString *content, NSError *error))completion {
	NSURL *URL = [NSURL URLWithString:url];
	if (URL.fileURL) {
		NSError *error = nil;
		NSString *m3u8Content = [NSString stringWithContentsOfURL:URL encoding:NSUTF8StringEncoding error:&error];
		if (completion) completion(m3u8Content, error);
	} else {
		NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:paramDic];
		if (requestHandler) requestHandler(request);
		[self requestString:request completion:^(NSString *string, NSError *error) {
			if (completion) completion(string, error);
		}];
	}
}

+ (void)requestKeyRquestTokenWithVid:(NSString *)vid completion:(void (^)(NSString *tokenData, NSError *error))completion {
    PLVVodSettings *settings = [PLVVodSettings sharedSettings];
    if (settings.requestCustomKeyTokenBlock) {
        NSString *token = settings.requestCustomKeyTokenBlock(vid);
        if ([PLVVodUtil isNilString:token]) {
            PLVVodLogError(@"自定义令牌请求错误");

             NSError *plvError = PLVVodErrorMake(token_fetch_error, ^(NSMutableDictionary *userInfo) {
                 userInfo[NSLocalizedFailureReasonErrorKey] = @"自定义令牌请求错误";
             });

            // Elog统计
            // [self reportElogWithError:plvError];
            
            if (completion)
                completion(token, plvError);
            return;
        }
        if (completion)
            completion(token, nil);
    } else {
        [self requestVideoTokenWithVid:vid completion:^(NSString *videoToken, NSError *error) {
            if (error) {
                PLVVodLogError(@"播放令牌请求错误，%@", error);
                
                if (network_unreachable != error.code){
                    NSError *plvError = PLVVodErrorMakeWithError(token_fetch_error, error, ^(NSMutableDictionary *userInfo) {
                        userInfo[PLVVodHttpStatusCodeKey] = error.userInfo[PLVVodHttpStatusCodeKey];
                    });
                    
                    // Elog统计
                    // [self reportElogWithError:plvError];
                    
                    if (completion)
                        completion(nil, plvError);
                }
            }
                
            NSString *token = videoToken;
            if (completion)
                completion(token, nil);
        }];
    }
}
/// 异步获取key
+ (void)requestKeyWithVid:(NSString *)vid token:(NSString *)token quality:(PLVVodQuality)quality completion:(void (^)(NSData *keyData, NSError *error))completion {
	NSMutableURLRequest *request = [self keyRquestWithVid:vid token:token quality:quality];
	[self requestData:request completion:^(NSData *data, NSError *error) {
		if (completion) completion(data, error);
	}];
}
+ (NSMutableURLRequest *)keyRquestWithVid:(NSString *)vid token:(NSString *)token quality:(PLVVodQuality)quality {
	PLVVodSettings *settings = [PLVVodSettings sharedSettings];
#if 1
    NSString *id = nil;
    if (settings.appid.length) {
        id = settings.appid;
    } else {
        id = [PLVVodSettings findUserIdWithVid:vid];
    }
    NSString *videoPoolId = videoPoolIdWithVid(vid);
    NSString *videoPoolIdLastChar = [videoPoolId substringFromIndex:videoPoolId.length-1];
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"token"] = token;
    NSString *url = [NSString stringWithFormat:@"https://hls.videocc.net/v3/app/%@/%@/%@_%zd.key", id, videoPoolIdLastChar, videoPoolId, quality];
    NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:params];
    return request;
#else // old api
    // mod by libl [优先判断子帐号配置] 2019-07-03 start
    if (settings.appid.length){
        if (!settings.appid.length || !settings.secretkey.length) {
            return nil;
        }
        
        NSString *videoPoolId = videoPoolIdWithVid(vid);
        if (!videoPoolId.length) {
            return nil;
        }
        
        NSString *videoPoolIdLastChar = [videoPoolId substringFromIndex:videoPoolId.length-1];
        NSString *timestamp = [PLVVodUtil timestamp];
        NSString *sign = [NSString stringWithFormat:@"%@%@%zd%@%@", settings.secretkey, videoPoolId, quality, timestamp, settings.secretkey];
        //NSLog(@"sign: %@", sign);
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        params[@"sign"] = [PLVVodUtil md5String:sign];
        params[@"ts"] = timestamp;
        NSString *url = [NSString stringWithFormat:@"https://hls.videocc.net/v2/app/%@/%@/%@_%zd.key", settings.appid, videoPoolIdLastChar, videoPoolId, quality];
        NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:params];
        return request;
    }
    else{
        NSString *userId = [PLVVodSettings findUserIdWithVid:vid];
        NSString *readtoken = [PLVVodSettings findReadTokenWithVid:vid];
        NSString *secretkey = [PLVVodSettings findSecretKeyWithVid:vid];
        if (!userId.length || !readtoken.length || !secretkey.length) {
            return nil;
        }
        
        NSString *videoPoolId = videoPoolIdWithVid(vid);
        if (!videoPoolId.length) {
            return nil;
        }
        
        NSString *videoPoolIdLastChar = [videoPoolId substringFromIndex:videoPoolId.length-1];
        NSString *timestamp = [PLVVodUtil timestamp];
        NSString *sign = [NSString stringWithFormat:@"%@%@%zd%@%@", readtoken, videoPoolId, quality, timestamp, secretkey];
        //NSLog(@"sign: %@", sign);
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        params[@"sign"] = [PLVVodUtil md5String:sign];
        params[@"ts"] = timestamp;
        NSString *url = [NSString stringWithFormat:@"https://hls.videocc.net/app/%@/%@/%@_%zd.key", userId, videoPoolIdLastChar, videoPoolId, quality];
        NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:params];
        return request;
    }
#endif
}

/// 请求加密视频令牌
+ (void)requestVideoTokenWithVid:(NSString *)vid completion:(void (^)(NSString *videoToken, NSError *error))completion {
	PLVVodSettings *settings = [PLVVodSettings sharedSettings];
	NSString *userId = @"";
    NSString *appId = settings.appid;
    
	NSString *secretkey = @"";
	NSString *timestamp = [PLVVodUtil timestamp];
	
	NSString *viewerId = settings.viewerId;
	if (!viewerId.length) viewerId = nil;
	NSString *viewerName = settings.viewerName;
	if (!viewerName.length) viewerName = nil;
	NSString *extraParams = settings.viewerTokenExtraParam;
	if (!extraParams.length) extraParams = nil;
	NSString *viewerIp = nil;
	
	NSMutableDictionary *params = [NSMutableDictionary dictionary];
//    params[@"userId"] = userId;
	params[@"videoId"] = vid;
	params[@"ts"] = timestamp;
	params[@"viewerIp"] = viewerIp;
    params[@"viewerId"] = [PLVVodUtil urlSafeBase64String:viewerId];;
    params[@"viewerName"] = [PLVVodUtil urlSafeBase64String:viewerName];
    // mod end
	params[@"extraParams"] = extraParams;
    
    // 优先配置子帐号
    NSString *url = nil;
    if (![PLVVodUtil isNilString:appId]){
        params[@"appId"] = appId;
        secretkey = settings.secretkey;
        url = @"https://hls.videocc.net/service/v2/token/create-child";
    }
    else{
        userId = [PLVVodSettings findUserIdWithVid:vid];
        params[@"userId"] = userId;
        secretkey = [PLVVodSettings findSecretKeyWithVid:vid];
        url = @"https://hls.videocc.net/service/v1/token";
    }
	
	// 产生签名
	NSArray *signKeys = params.allKeys;
	signKeys = [signKeys sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
		return [obj1 compare:obj2 options:NSCaseInsensitiveSearch];
	}];
	NSMutableString *videoSign = [NSMutableString string];
	for (int i = 0; i < signKeys.count; i ++) {
		NSString *key = signKeys[i];
		[videoSign appendFormat:@"%@%@", key, params[key]];
		if (i == signKeys.count - 1) break;
	}
	NSString *sign = [NSString stringWithFormat:@"%@%@%@", secretkey, videoSign, secretkey];
    sign = [PLVVodUtil md5String:sign].uppercaseString;
	params[@"sign"] = sign;
	
	NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_POST params:params];
	[self requestDictionary:request completion:^(NSDictionary *dic, NSError *error) {
		if (error) {
			if (completion) completion(nil, error);
			return;
		}
        
		NSInteger code = [dic[@"code"] integerValue];
		NSDictionary *dataDict = dic[@"data"];
		if (code == 200) {
			NSString *videoToken = dataDict[@"token"];
			if (completion) completion(videoToken, nil);
		} else {
			NSString *errorMessage = dataDict[@"message"];
			NSError *plvError = PLVVodErrorMake(fetch_error, ^(NSMutableDictionary *userInfo) {
				userInfo[NSLocalizedFailureReasonErrorKey] = errorMessage;
				userInfo[PLVVodSubErrorCodeKey] = @(code);
			});
			if (completion) completion(nil, plvError);
		}
	}];
}

/// 请求加密视频令牌
+ (void)requestVideoTokenWithModel:(PLVVodVideo *)videoModel completion:(void (^)(NSString *videoToken, NSString *host, NSError *error))completion {
    PLVVodSettings *settings = [PLVVodSettings sharedSettings];
    NSString *userId = @"";
    NSString *appId = settings.appid;
    
    NSString *secretkey = @"";
    NSString *timestamp = [PLVVodUtil timestamp];
    
    NSString *viewerId = settings.viewerId;
    if (!viewerId.length) viewerId = nil;
    NSString *viewerName = settings.viewerName;
    if (!viewerName.length) viewerName = nil;
    NSString *extraParams = settings.viewerTokenExtraParam;
    if (!extraParams.length) extraParams = nil;
    NSString *viewerIp = nil;
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"videoId"] = videoModel.vid;
    params[@"ts"] = timestamp;
    params[@"viewerIp"] = viewerIp;
    params[@"viewerId"] = [PLVVodUtil urlSafeBase64String:viewerId];;
    params[@"viewerName"] = [PLVVodUtil urlSafeBase64String:viewerName];
    // mod end
    params[@"extraParams"] = extraParams;
    
    // 优先配置子帐号
    NSString *mainHost = @"hls.videocc.net";
    NSString *backupHost = nil;
    if (videoModel.hlsVideos_backup.count){
        NSURL *backURL = [NSURL URLWithString:videoModel.hlsVideos_backup.firstObject];
        backupHost = backURL.host;
    }
    NSURL *mainURL = nil;
    NSURL *backURL = nil;
    NSString *url = nil;
    NSString *backupUrl = nil;
    if (![PLVVodUtil isNilString:appId]){
        params[@"appId"] = appId;
        secretkey = settings.secretkey;
        url = [NSString stringWithFormat:@"https://%@/service/v2/token/create-child", mainHost];
        mainURL = [NSURL URLWithString:url];
        if (backupHost){
            backupUrl = [NSString stringWithFormat:@"https://%@/service/v2/token/create-child", backupHost];
            backURL = [NSURL URLWithString:backupUrl];
        }
    }
    else{
        userId = [PLVVodSettings findUserIdWithVid:videoModel.vid];
        params[@"userId"] = userId;
        secretkey = [PLVVodSettings findSecretKeyWithVid:videoModel.vid];
        url = [NSString stringWithFormat:@"https://%@/service/v1/token", mainHost];
        mainURL = [NSURL URLWithString:url];
        if (backupHost){
            backupUrl = [NSString stringWithFormat:@"https://%@/service/v1/token", backupHost];
            backURL = [NSURL URLWithString:backupUrl];
        }
    }
    
    // 产生签名
    NSArray *signKeys = params.allKeys;
    signKeys = [signKeys sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
        return [obj1 compare:obj2 options:NSCaseInsensitiveSearch];
    }];
    NSMutableString *videoSign = [NSMutableString string];
    for (int i = 0; i < signKeys.count; i ++) {
        NSString *key = signKeys[i];
        [videoSign appendFormat:@"%@%@", key, params[key]];
        if (i == signKeys.count - 1) break;
    }
    NSString *sign = [NSString stringWithFormat:@"%@%@%@", secretkey, videoSign, secretkey];
    sign = [PLVVodUtil md5String:sign].uppercaseString;
    params[@"sign"] = sign;
    
    // Post 请求暂时不支持 https + ip 地址
//    NSString *dnsDomain = [PLVVodSettings sharedSettings].enableHttpDNS ? mainHost: nil;
    NSString *dnsDomain = nil;
    PLVVodNetworkRetryManager *retryManager = [[PLVVodNetworkRetryManager alloc] initWithPrimaryURL:mainURL
                                                                                          backupURL:backURL
                                                                                          dnsDomain:dnsDomain
                                                                                         httpMethod:PLV_HM_POST
                                                                                             params:params
                                                                                      maxRetryCount:1
                                                                                       initialDelay:0];
    [retryManager sendRequestWithCompletion:^(NSData * _Nullable data, NSError * _Nullable error) {
        // 网络不可用判断
        if([PLVVodReachability sharedReachability].notReachability){
            NSError *plvError = PLVVodErrorMake(network_unreachable, ^(NSMutableDictionary *userInfo) {
                userInfo[NSURLErrorKey] = mainURL;
            });
            if (completion) completion(nil, mainHost, plvError);

            return;
        }
        if (error ) {
            NSError *plvError = PLVVodErrorMakeWithError(playback_token_fetch_error, error, ^(NSMutableDictionary *userInfo) {
            });
            if (completion) completion(nil, mainHost, plvError);
            return;
        }
        else if ([data isKindOfClass:[NSNull class]] || !data){
            NSError *plvError = PLVVodErrorMake(playback_token_fetch_error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSURLErrorKey] = mainURL;
            });
            if (completion) completion(nil, mainHost, plvError);
            return;
        }
        
        // json 解析
        NSError *jsonError = nil;
        NSDictionary *responseDic = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&jsonError];
        if (jsonError){
            NSString *contentText = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
            PLVVodLogError(@"json 读取错误，%@", error.localizedDescription);
            NSError *plvError = PLVVodErrorMakeWithError(json_read_error, error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSURLErrorKey] = mainURL;
                userInfo[PLVVodHttpStatusCodeKey] = @"200";
                userInfo[NSLocalizedFailureReasonErrorKey] = contentText;
            });
            
            if (completion) completion(nil, mainHost, plvError);
            
            return;
        }
        
        // 返回结果解析
        NSInteger code = [responseDic[@"code"] integerValue];
        NSDictionary *dataDict = responseDic[@"data"];
        if (code == 200) {
            NSString *videoToken = dataDict[@"token"];
            if (completion) completion(videoToken, mainHost, nil);
        } else {
            NSString *errorMessage = dataDict[@"message"];
            NSError *plvError = PLVVodErrorMake(token_fetch_error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSLocalizedFailureReasonErrorKey] = errorMessage;
                userInfo[PLVVodSubErrorCodeKey] = @(code);
            });
            if (completion) completion(nil, mainHost, plvError);
        }
    }];
}

/// 发送log日志
+ (void)reportElogWithLogParams:(NSString *)logParams withVid:(NSString *)vid completion:(void(^)(NSError *error))completion{
    NSString *userId = [PLVVodSettings findUserIdWithVid:vid];
    if ([PLVVodUtil isNilString:vid] || userId == nil){
        userId = [PLVVodSettings sharedSettings].userid;
    }
    NSString *secretkey = @"";
    NSString *url = @"https://elog.polyv.net/v4/vod/save-elog";

    NSString *timestamp = [PLVVodUtil timestamp];
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"timestamp"] = timestamp;
    params[@"log"] = logParams;
    params[@"ltype"] = @1;
    params[@"userId"] = userId;
    
    // add by libl [特殊字符，比如+，需要编码转换，否则传输过程中出现问题，导致sign验证不通过] start
//    params[@"log"] = [self.class httpDataStr:params[@"log"]];
    // 2020.03.22 后端规则可能变动，导致签名不通过，先临时处理
//    NSString *newLog = [logParams stringByReplacingOccurrencesOfString:@"+" withString:@"-"];
//    params[@"log"] = newLog;
    // add end
    
    // 签名规则
    NSString *sign = [NSString stringWithFormat:@"polyv_sdk_api_innorltype1timestamp%@userId%@polyv_sdk_api_innor", timestamp, userId];
    params[@"sign"] = [PLVVodUtil md5String:sign].uppercaseString;
    
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] initWithURL:[NSURL URLWithString:url]];
    request.HTTPMethod = @"POST";
    request.timeoutInterval = PLVVodRequestTimeout;
    
    // 参数拼接
    NSArray *keys = params.allKeys;
    NSMutableString *paramStr = [NSMutableString string];
    for (NSString *key in keys) {
        [paramStr appendFormat:@"%@=%@&", key, params[key]];
    }
    NSString *parameters = [paramStr substringToIndex:paramStr.length - 1];
    [request setHTTPBody:[parameters dataUsingEncoding:NSUTF8StringEncoding]];
    
    [self setupHTTPHeaderWithRequest:request];
    if (PLVVodNetworkingLogEnable) NSLog(@"%@ - 参数列表 = %@\t%@\n\n", url, params, url);

    [self requestDictionary:request completion:^(NSDictionary *dic, NSError *error) {
        if (error) {
            if (completion) completion(error);
            return;
        }
        NSInteger code = [dic[@"code"] integerValue];
        if (code == 200) {
            if (completion) completion(nil);
        } else {
            NSString *errorMessage = dic[@"message"];
            NSError *plvError = PLVVodErrorMake(fetch_error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSLocalizedFailureReasonErrorKey] = errorMessage;
                userInfo[PLVVodSubErrorCodeKey] = @(code);
            });
            if (completion) completion(plvError);
        }
    }];
}

// http传输过程的数据转换，一些特殊字符无法被识别，需要编码转换
+ (NSString *)httpDataStr:(NSString *)inputStr {
    
    NSString *outputStr = (NSString *)CFBridgingRelease(CFURLCreateStringByAddingPercentEscapes(kCFAllocatorDefault,
                                                                                                (CFStringRef)inputStr,
                                                                                                NULL,
                                                                                                (CFStringRef)@"!*'();:@&=+$,/?%#[]",
                                                                                                kCFStringEncodingUTF8));
    return outputStr;
}

/// 获取投屏加密视频Key，iv
+ (void)requestCastKeyIvWithVid:(NSString *)vid
                       constKey:(NSString *)constKey
                        quality:(NSInteger)quality
                     completion:(void (^)(NSString *key, NSString *iv, NSError *error))completion{
    
    if ([PLVVodUtil isNilString:vid]) {
        PLVVodLogError(@"vid非法，投屏加密信息获取失败");
        if (completion) {
            NSError * error = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
                userInfo[NSLocalizedFailureReasonErrorKey] = @"vid非法，投屏加密信息获取失败";
            });
            completion(nil, nil, error);
        }
        return;
    }
    
    if ([PLVVodUtil isNilString:constKey]) {
        PLVVodLogError(@"constKey非法，投屏加密信息获取失败");
        if (completion) {
            NSError * error = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
                userInfo[NSLocalizedFailureReasonErrorKey] = @"constKey非法，投屏加密信息获取失败";
            });
            completion(nil, nil, error);
        }
        return;
    }
    
    NSString * userId = [vid substringToIndex:10];
    NSString * vidSign = [vid substringWithRange:NSMakeRange(vid.length - 3, 1)];
    
    NSString * bitrate = @(quality).description;
    
    NSString * vidPoolId = [vid componentsSeparatedByString:@"_"].firstObject;
    NSString * url = [NSString stringWithFormat:@"https://hls.videocc.net/sign/%@/%@/%@_%@.key",userId,vidSign,vidPoolId,bitrate];
    
    NSString * seedConst = constKey;
    NSString * ts = [PLVVodUtil timestamp];
    NSString * signBunch = [NSString stringWithFormat:@"%@%@%@%@%@",seedConst,vidPoolId,bitrate,ts,seedConst];
    NSString * sign = [PLVVodUtil md5String:signBunch];
    
    NSString * aesKey = [[PLVVodUtil md5String:sign]substringToIndex:16];
    NSString * aesIv = [[PLVVodUtil md5String:ts]substringToIndex:16];
    
    // NSLog(@"PLVVodNetworking - 加密参数检查 \n{ \n seedConst : %@ \n vidPoolId : %@ \n bitrate : %@ \n userId : %@ \n vidSign : %@ \n ts : %@ \n sign : %@ \n}",seedConst,vidPoolId,bitrate,userId,vidSign,ts,sign);
    
    // 参数
    NSMutableDictionary * params = [NSMutableDictionary dictionary];
    params[@"sign"] = sign;
    params[@"ts"] = ts;
    
    // 请求
    NSMutableURLRequest * request = [self requestWithUrl:url
                                                  method:PLV_HM_POST
                                                  params:params];
    [self requestDictionary:request completion:^(NSDictionary *dic, NSError *error) {
        
        if (error) {
            // 回调错误
            if (completion) {
                completion(nil, nil, error);
            }
        }else{
            
            NSInteger code = [(NSNumber *)dic[@"code"] integerValue];
            if (code == 200) {
                // 请求成功
                NSString * dataStr = dic[@"data"];
                if ([PLVVodUtil isNilString:dataStr]) { // 若data为空，则不需要key和iv
                    if (completion) completion(nil, nil, nil);
                }else{
                    NSData * data = [[NSData alloc] initWithBase64EncodedString:dataStr options:0];

                    NSData * encryptedData = [PLVVodUtil AES128DecryptedDataWithKey:aesKey iv:aesIv data:data];
                    NSString * aesContent = [[NSString alloc] initWithData:encryptedData encoding:NSUTF8StringEncoding];
                    
                    NSArray * arr = [aesContent componentsSeparatedByString:@"/"];
                    if (completion) completion(arr.firstObject,arr.lastObject,nil);
                }
            }else{
                // 其他情况
                if (completion) {
                    NSError * err = PLVVodErrorMake(fetch_error, ^(NSMutableDictionary *userInfo) {
                        userInfo[NSLocalizedFailureReasonErrorKey] = [NSString stringWithFormat:@"请求key iv失败 code:%zd",code];
                    });
                    completion(nil, nil, err);
                }
            }
            
        }
        
    }];
    
}

+ (void)requestLastPositionWithVid:(NSString *)vid viewerId:(NSString *)viewerId completion:(void(^)(NSTimeInterval lastPosition))completion {
    if ([PLVVodUtil isNilString:vid] || [PLVVodUtil isNilString:viewerId]) {
        if (completion){
            completion(-1);
        }
    }
    NSString *videoPoolId = videoPoolIdWithVid(vid);
    if ([PLVVodUtil isNilString:videoPoolId]) {
        if (completion){
            completion(-1);
        }
    }
    NSString *url = [NSString stringWithFormat:@"https://script.polyv.net/vod/v1/play-position/get"];
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"vid"] = videoPoolId;
    params[@"viewerId"] = viewerId;
    NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:params];
    request.timeoutInterval = 3;
    [self requestDictionary:request completion:^(NSDictionary *dic, NSError *error) {
        NSTimeInterval lastPosition = -1;
        if (error || !dic.count){
            if (completion){
                completion(lastPosition);
            }
        } else{
            lastPosition = [dic[@"cts"] integerValue];
            if (completion){
                completion (lastPosition);
            }
        }
    }];
}

#pragma mark - 汇报后台

/// viewlog
+ (void)reportViewLogWithParams:(NSDictionary *)params {
	NSString *url = @"https://prtas.videocc.net/v2/view";
	
	NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:params];
	[self requestString:request completion:^(NSString *string, NSError *error) {
		//NSLog(@"viewlog: %@", string);
	}];
}

/// loading qos
+ (void)reportLoadingQosWithPid:(NSString *)pid
                            vid:(NSString *)vid
                           time:(NSTimeInterval)time
                    time_player:(NSTimeInterval)time_player
                  time_business:(NSTimeInterval)time_business
                         params:(NSDictionary *)extraParams {
    NSString *userid = [PLVVodSettings findUserIdWithVid:vid];
	NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:extraParams];
	// pid
	params[@"pid"] = pid;
	// uid
	params[@"uid"] = userid;
	// cid
	params[@"vid"] = vid;
	// type=loading
	params[@"type"] = @"loading";
	// time=%f&
	params[@"time"] = [NSString stringWithFormat:@"%.0f",time];
    // time_player=%f&
    params[@"time_player"] = [NSString stringWithFormat:@"%.0f",time_player];
    // time_business=%f&
    params[@"time_business"] = [NSString stringWithFormat:@"%.0f",time_business];
    // pn
    params[@"pn"] = PLVVodSdkPlatform;
    // pv
    params[@"pv"] = PLVVodSdkVersion;

    // session_id 播放场次标识
    NSString *session_id = [PLVVodSettings sharedSettings].viewerId;
    if (![PLVVodUtil isNilString:session_id]){
        params[@"session_id"] = [PLVVodUtil urlSafeBase64String:session_id]; 
    }
    // param2 来自viewerName
    NSString *param2 = [PLVVodSettings sharedSettings].viewerName;
    if (![PLVVodUtil isNilString:param2]){
        params[@"param2"] = [PLVVodUtil urlSafeBase64String:param2];
    }
	
	NSString *url = @"https://prtas.videocc.net/qos";
	NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:params];
	[self requestString:request completion:^(NSString *string, NSError *error) {
		//NSLog(@"qos loading: %@", string);
	}];
}

/// buffer qos
+ (void)reportBufferQosWithPid:(NSString *)pid vid:(NSString *)vid time:(NSTimeInterval)time params:(NSDictionary *)extraParams {
	NSString *userid = [PLVVodSettings findUserIdWithVid:vid];
	NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:extraParams];
	// pid
	params[@"pid"] = pid;
	// uid
	params[@"uid"] = userid;
	// cid
	params[@"vid"] = vid;
	// type=buffer
	params[@"type"] = @"buffer";
	// time=%f&
    params[@"time"] = [NSString stringWithFormat:@"%.0f",time*1000];
    // pn
    params[@"pn"] = PLVVodSdkPlatform;
    // pv
    params[@"pv"] = PLVVodSdkVersion;

    // session_id 播放场次标识
    NSString *session_id = [PLVVodSettings sharedSettings].viewerId;
    if (![PLVVodUtil isNilString:session_id]){
        params[@"session_id"] = [PLVVodUtil urlSafeBase64String:session_id]; 
    }
    // param2 来自viewerName
    NSString *param2 = [PLVVodSettings sharedSettings].viewerName;
    if (![PLVVodUtil isNilString:param2]){
        params[@"param2"] = [PLVVodUtil urlSafeBase64String:param2];
    }
	
	NSString *url = @"https://prtas.videocc.net/qos";
	NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:params];
	[self requestString:request completion:^(NSString *string, NSError *error) {
	}];
}

/// qos stalling
+ (void)reportQosStallingWithPid:(NSString *)pid vid:(NSString *)vid time:(NSTimeInterval)time params:(NSDictionary *)extraParams {
    NSString *userid = [PLVVodSettings findUserIdWithVid:vid];
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:extraParams];
    // pid
    params[@"pid"] = pid;
    // uid
    params[@"uid"] = userid;
    // cid
    params[@"vid"] = vid;
    // type=stalling
    params[@"type"] = @"stalling";
    // time=%f&
    params[@"time"] = [NSString stringWithFormat:@"%.0f",time];
     // pn
    params[@"pn"] = PLVVodSdkPlatform;
    // pv
    params[@"pv"] = PLVVodSdkVersion;

    // session_id 播放场次标识
    NSString *session_id = [PLVVodSettings sharedSettings].viewerId;
    if (![PLVVodUtil isNilString:session_id]){
        params[@"session_id"] = [PLVVodUtil urlSafeBase64String:session_id]; 
    }
    // param2 来自viewerName
    NSString *param2 = [PLVVodSettings sharedSettings].viewerName;
    if (![PLVVodUtil isNilString:param2]){
        params[@"param2"] = [PLVVodUtil urlSafeBase64String:param2];
    }

    NSString *url = @"https://prtas.videocc.net/qos";
    NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:params];
    [self requestString:request completion:^(NSString *string, NSError *error) {
    }];
}

/// play error qos
+ (void)reportErrorQosWithPid:(NSString *)pid
                          vid:(NSString *)vid
                    errorCode:(NSString *)errorCode
                   requestUri:(NSString *)requestUri
                 responseCode:(NSString *)responseCode
                  extraParams:(NSDictionary *)extraParams{
    
    //
    NSString *userid = [PLVVodSettings findUserIdWithVid:vid];
    
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:extraParams];    
    NSString *processedPid = [PLVVodUtil isNilString:pid] ? [PLVVodUtil pid] : pid;
    params[@"pid"] = processedPid;
    // uid
    params[@"uid"] = userid;
    // cid
    params[@"vid"] = vid;
    // error
    params[@"error"] = errorCode;
    // type type=error
    params[@"type"] = @"error";
    
    // 添加播放器标识
    params[@"pn"] = PLVVodSdkPlatform;  // 播放器名称
    params[@"pv"] = PLVVodSdkVersion;   // 播放器版本
    
    // request_uri 请求资源的URI
    params[@"request_uri"] = [PLVVodUtil isNilString:requestUri] ? @"":requestUri; //
    
    // response_code 服务器响应的状态码
    params[@"response_code"] = [PLVVodUtil isNilString:responseCode] ? @"": responseCode;
    
     // session_id 播放场次标识
    NSString *session_id = [PLVVodSettings sharedSettings].viewerId;
    if (![PLVVodUtil isNilString:session_id]){
        params[@"session_id"] = [PLVVodUtil urlSafeBase64String:session_id]; 
    }
    // param2 来自viewerName
    NSString *param2 = [PLVVodSettings sharedSettings].viewerName;
    if (![PLVVodUtil isNilString:param2]){
        params[@"param2"] = [PLVVodUtil urlSafeBase64String:param2];
    }

    // 添加param1 ~param4 参数
    params[@"param1"] = @"";
    params[@"param3"] = @"";
    params[@"param4"] = @"";
    if (!params[@"param5"]){
        //
        params[@"param5"] = [PLVVodUtil userAgent];
    }
    // add end
    
    NSString *url = @"https://prtas.videocc.net/qos";
    NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:params];
    [self requestString:request completion:^(NSString *string, NSError *error) {
        //NSLog(@"qos error: %@", string);
    }];
}

+ (void)requestSubtitleWithUrl:(NSString *)url completion:(void (^)(NSString *, NSError *))completion{
    NSURLRequest *request = [NSURLRequest requestWithURL:[NSURL URLWithString:url]];
    [self requestString:request completion:^(NSString *string, NSError *error) {
        completion (string, error);
    }];
}

+ (NSData *)AES128DecryptedDataWithBody:(NSString *)body vid:(NSString *)vid {
    NSString *vidMd5 = [PLVVodUtil md5String:vid].lowercaseString;
    NSString *aesKey = [vidMd5 substringToIndex:16];
    NSString *aesIv = [vidMd5 substringFromIndex:16];
    NSData *videoJsonData = [PLVVodUtil dataWithHexString:body];
    videoJsonData = [PLVVodUtil AES128DecryptedDataWithKey:aesKey iv:aesIv data:videoJsonData];
    videoJsonData = [[NSData alloc] initWithBase64EncodedData:videoJsonData options:0];
    return videoJsonData;
}

+ (void)requestPPTJsonWithVid:(NSString *)vid completion:(void (^)(NSDictionary *responseDict, NSError *error))completion {
    NSString *url = [NSString stringWithFormat:@"https://player.polyv.net/pptjson/%@.js", vid];
    NSURLRequest *request = [NSURLRequest requestWithURL:[NSURL URLWithString:url]];
    [self requestDictionary:request completion:^(NSDictionary *dic, NSError *error) {
        completion(dic, error);
    }];
}

+ (void)requestPPTZipUrlWithVid:(NSString *)vid completion:(void (^)(NSString *, NSError *))completion{
    PLVVodSettings *settings = [PLVVodSettings sharedSettings];
    NSString *userId = [PLVVodSettings findUserIdWithVid:vid];
    NSString *secretkey = @"";
    NSString *pVid = vid;
    NSString *pTimeStamp = [PLVVodUtil timestamp];
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"ptime"] = pTimeStamp;
    params[@"vid"] = pVid;
    params[@"userid"] = userId;
    
    // 主帐号签名规则
    NSString *appId = [PLVVodSettings sharedSettings].appid;
    if ([PLVVodUtil isNilString:appId]){
        secretkey = [PLVVodSettings findSecretKeyWithVid:vid];
        NSString *parameters = [PLVVodUtil convertDictionaryToSortedString:params];
        NSString *sign = [NSString stringWithFormat:@"%@%@", parameters, secretkey];
        params[@"sign"] = [PLVVodUtil sha1String:sign].uppercaseString;
    }
    else{
        // 子账号签名规则
        // md5大写(子账号的secretkey＋签名参数＋子账号的secretkey)
        // 签名参数排序，不用= 符号
        params[@"appId"] = appId;
        
        secretkey = settings.secretkey;

        NSString *parameters = [NSString stringWithFormat:@"appId%@ptime%@userid%@vid%@", params[@"appId"], params[@"ptime"], params[@"userid"], params[@"vid"]];
        NSString *sign = [NSString stringWithFormat:@"%@%@%@", secretkey, parameters, secretkey];
        params[@"sign"] = [PLVVodUtil md5String:sign].uppercaseString;
    }
        
    NSString *url = @"https://api.polyv.net/v2/video/ppt/zip/exist";
    NSMutableURLRequest *request = [self requestWithUrl:url method:PLV_HM_GET params:params];
    [self requestDictionary:request completion:^(NSDictionary *dic, NSError *error) {
        PLVVodLogDebug(@"[pptZip] -- %@", dic);
        if (error){
            if (completion){
                completion(nil, error);
            }
        }
        else{
            //
            NSString *retCode = [[dic objectForKey:@"code"] stringValue];
            if ([retCode integerValue] == 200){
                NSDictionary *data = [dic objectForKey:@"data"];
                NSString *zipUrl = nil;
                if ([[data objectForKey:@"isExist"] isEqualToString:@"Y"]){
                    zipUrl = [data objectForKey:@"zipUrl"];
                    if (zipUrl.length){
                        zipUrl = [zipUrl stringByReplacingOccurrencesOfString:@"http:" withString:@"https:"];
                    }
                }
                if (completion){
                    completion (zipUrl, nil);
                }
            }
            else{
                if (completion){
                    completion (nil, nil);
                }
            }
        }
    }];
}

@end
