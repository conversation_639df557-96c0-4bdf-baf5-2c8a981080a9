//
//  PLVVodNetworkRetryManager.h
//  _PolyvVodSDK
//
//  Created by polyv on 2025/4/1.
//  Copyright © 2025 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^NetworkCompletion)(NSData * _Nullable data, NSError * _Nullable error);
typedef void (^DNSResolveCompletion)(NSString * _Nullable ipAddress, NSError * _Nullable error);

@interface PLVVodNetworkRetryManager : NSObject

@property (nonatomic, assign) NSInteger maxRetryCount; // 每个地址最大重试次数
@property (nonatomic, assign) NSTimeInterval initialDelay; // 初始延迟
@property (nonatomic, strong) NSURL *primaryURL; // 主用地址
@property (nonatomic, strong) NSURL *backupURL; // 备用地址
@property (nonatomic, copy) NSString *dnsDomain; // HTTPDNS域名

- (instancetype)initWithPrimaryURL:(NSURL *)primaryURL
                         backupURL:(nullable NSURL *)backupURL
                         dnsDomain:(nullable NSString *)dnsDomain
                        httpMethod:(NSString *)httpMethod
                            params:(nullable NSDictionary *)params
                     maxRetryCount:(NSInteger)maxRetryCount
                      initialDelay:(NSTimeInterval)initialDelay;

- (void)sendRequestWithCompletion:(NetworkCompletion)completion;

// 可由子类重写自定义DNS解析
- (void)resolveDNSWithDomain:(NSString *)domain
                  completion:(DNSResolveCompletion)completion;

@end

NS_ASSUME_NONNULL_END
