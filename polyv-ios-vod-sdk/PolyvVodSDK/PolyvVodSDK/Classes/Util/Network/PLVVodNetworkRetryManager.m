//
//  PLVVodNetworkRetryManager.m
//  _PolyvVodSDK
//
//  Created by polyv on 2025/4/1.
//  Copyright © 2025 POLYV. All rights reserved.
//

#import "PLVVodNetworkRetryManager.h"
#import "PLVVodHttpDnsManager.h"
#import "PLVVodNSURLProtocol.h"
#import "PLVVodUtil.h"

typedef NS_ENUM(NSInteger, PLVVodAddressType) {
    PLVVodAddressTypeHttpDNS = 1, // httpdns 解析优先
    PLVVodAddressTypePrimary,      // 主地址
    PLVVodAddressTypeBackup,      // 备用地址
};

@interface PLVVodNetworkRetryManager ()<NSURLSessionDelegate>
@property (nonatomic, strong) NSURLSession *session;
@property (nonatomic, strong) NSMutableDictionary<NSNumber *, NSNumber *> *retryCounts;
@property (nonatomic, copy) NSString *httpMethod;
@property (nonatomic, copy) NSDictionary *params;
@end

@implementation PLVVodNetworkRetryManager

- (instancetype)initWithPrimaryURL:(NSURL *)primaryURL
                         backupURL:(NSURL *)backupURL
                         dnsDomain:(NSString *)dnsDomain
                        httpMethod:(NSString *)httpMethod
                            params:(NSDictionary *)params
                     maxRetryCount:(NSInteger)maxRetryCount
                      initialDelay:(NSTimeInterval)initialDelay {
    self = [super init];
    if (self) {
        _primaryURL = primaryURL;
        _backupURL = backupURL;
        _dnsDomain = dnsDomain;
        _maxRetryCount = maxRetryCount;
        _initialDelay = initialDelay;
        _httpMethod = httpMethod;
        _params = params;
        
        _session = [self sharedUrlSession];
        _retryCounts = [NSMutableDictionary dictionary];
    }
    return self;
}

- (NSURLSession *)sharedUrlSession {
    if (!_session){
        NSURLSessionConfiguration *configuration = [NSURLSessionConfiguration defaultSessionConfiguration];

        // 为了处理SNI问题，这里替换了NSURLProtocol的实现
        NSMutableArray *protocolsArray = [NSMutableArray arrayWithArray:configuration.protocolClasses];
        [protocolsArray insertObject:[PLVVodNSURLProtocol class] atIndex:0];
        [configuration setProtocolClasses:protocolsArray];

        _session = [NSURLSession sessionWithConfiguration:configuration delegate:nil delegateQueue:nil];
    }
    
    return _session;
}

- (void)sendRequestWithCompletion:(NetworkCompletion)completion {
    [self resetRetryCounts];
    // 默认HTTPDNS 优先 如果不支持httpdns,则主地址优先
    PLVVodAddressType addressType = PLVVodAddressTypeHttpDNS;
    if (!self.dnsDomain){
        addressType = PLVVodAddressTypePrimary;
    }
    [self attemptRequestWithAddressType:addressType completion:completion];
}

- (void)attemptRequestWithAddressType:(PLVVodAddressType)addressType
                           completion:(NetworkCompletion)completion {
    
    __weak typeof(self) weakSelf = self;
    
    // 获取当前地址的URL
    [self prepareURLForAddressType:addressType completion:^(NSURL *url, NSError *dnsError) {
        if (!url) {
            [weakSelf tryNextAddressAfterError:dnsError
                                   addressType:addressType
                                    completion:completion];
            return;
        }
        
        NSInteger currentRetryCount = [weakSelf.retryCounts[@(addressType)] integerValue];
        [weakSelf performRequestWithURL:url
                            retryCount:currentRetryCount
                                 delay:[weakSelf calculateDelayForRetryCount:currentRetryCount]
                            addressType:addressType
                             completion:completion];
    }];
}

- (void)performRequestWithURL:(NSURL *)url
                  retryCount:(NSInteger)retryCount
                       delay:(NSTimeInterval)delay
                 addressType:(PLVVodAddressType)addressType
                  completion:(NetworkCompletion)completion {
    
    __weak typeof(self) weakSelf = self;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delay * NSEC_PER_SEC)), dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSMutableURLRequest *request = [self requestWithUrl:url.absoluteString method:self.httpMethod params:self.params];
        if (addressType == PLVVodAddressTypeHttpDNS){
            [request setValue:self.dnsDomain forHTTPHeaderField:@"host"];
        }
        NSURLSessionDataTask *task = [self.session dataTaskWithRequest:request
                                                    completionHandler:^(NSData *data,
                                                                       NSURLResponse *response,
                                                                       NSError *error) {
            
            NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
            if ([self shouldRetryWithError:error statusCode:httpResponse.statusCode]) {
                NSInteger newRetryCount = retryCount + 1;
                weakSelf.retryCounts[@(addressType)] = @(newRetryCount);
                
                if (newRetryCount < weakSelf.maxRetryCount) {
                    // 同地址重试
                    [weakSelf performRequestWithURL:url
                                        retryCount:newRetryCount
                                             delay:[weakSelf calculateDelayForRetryCount:newRetryCount]
                                       addressType:addressType
                                        completion:completion];
                } else {
                    // 切换到下一个地址
                    NSError *afterError = error;
                    if (!afterError){
                        afterError = [NSError errorWithDomain:PLVVodErrorDomain
                                                         code:httpResponse.statusCode
                                                     userInfo:@{NSURLErrorKey:response.URL}];
                    };
                    [weakSelf tryNextAddressAfterError:afterError
                                           addressType:addressType
                                            completion:completion];
                }
            } else {
                completion(data, error);
            }
        }];
        
        [task resume];
    });
}

#pragma mark - Helper Methods
- (NSMutableURLRequest *)requestWithUrl:(NSString *)url method:(NSString *)HTTPMethod params:(NSDictionary *)paramDic {
    NSString *urlString = url.copy;
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] init];
    if (paramDic.count) {
        NSString *parameters = [PLVVodUtil convertDictionaryToSortedString:paramDic];
        if ([@"GET" isEqualToString:HTTPMethod]) {
            urlString = [NSString stringWithFormat:@"%@?%@", urlString, parameters];
        }else if ([@"POST" isEqualToString:HTTPMethod]){
            NSData *bodyData = [parameters dataUsingEncoding:NSUTF8StringEncoding];
            request.HTTPBody = bodyData;
        }
    }
    
    NSURL *URL = [NSURL URLWithString:[urlString stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
    request.URL = URL;
    request.HTTPMethod = HTTPMethod;
    request.timeoutInterval = 10;
    [self setupHTTPHeaderWithRequest:request];
    
    return request;
}

/// 设置请求头信息
- (void)setupHTTPHeaderWithRequest:(NSMutableURLRequest *)request {
    [request setValue:[PLVVodUtil userAgent] forHTTPHeaderField:@"User-Agent"];
}

/// 上报Elog
- (void)sendElogWithError:(NSError *)error{
    // Elog 统计
}

- (void)prepareURLForAddressType:(PLVVodAddressType)addressType
                      completion:(void(^)(NSURL *url, NSError *error))completion {
    switch (addressType) {
        case PLVVodAddressTypePrimary:
            completion(self.primaryURL, nil);
            break;
        case PLVVodAddressTypeBackup:
            completion(self.backupURL, nil);
            break;
        case PLVVodAddressTypeHttpDNS:
            [self resolveDNSWithDomain:self.dnsDomain completion:^(NSString *ipAddress, NSError *error) {
                if (ipAddress) {
                    NSURLComponents *components = [NSURLComponents componentsWithURL:self.primaryURL resolvingAgainstBaseURL:NO];
                    components.host = ipAddress;
                    completion(components.URL, nil);
                } else {
                    // httpdns 解析失败，不做错误处理,返回主地址
                    completion(self.primaryURL, error);
                }
            }];
            break;
    }
}

- (void)tryNextAddressAfterError:(NSError *)error
                     addressType:(PLVVodAddressType)currentType
                      completion:(NetworkCompletion)completion {
    PLVVodAddressType nextType = PLVVodAddressTypePrimary;
    BOOL hasNext = NO;
    
    switch (currentType) {
        case PLVVodAddressTypeHttpDNS:
            nextType = PLVVodAddressTypePrimary;
            hasNext = self.primaryURL != nil;
            break;
        case PLVVodAddressTypePrimary:
            nextType = PLVVodAddressTypeBackup;
            hasNext = self.backupURL != nil;
            break;
        case PLVVodAddressTypeBackup:
            hasNext = NO;
            break;
    }
    
    if (hasNext) {
        [self attemptRequestWithAddressType:nextType completion:completion];
    } else {
        completion(nil, error);
    }
}

- (BOOL)shouldRetryWithError:(NSError *)error statusCode:(NSInteger)statusCode {
    if (error ) {
        // 上报ELOG 有网状态下，上报业务链路错误
        // 以后实现，涉及到业务参数传递
        return YES;
    }
    return (statusCode >= 400 && statusCode < 600);
}

- (NSTimeInterval)calculateDelayForRetryCount:(NSInteger)retryCount {
    NSTimeInterval delay = self.initialDelay * pow(2, retryCount);
    NSTimeInterval jitter = ((float)arc4random() / UINT32_MAX) * 0.5;
    return delay + jitter;
}

- (void)resetRetryCounts {
    [self.retryCounts removeAllObjects];
    for (NSNumber *type in @[@(PLVVodAddressTypePrimary), @(PLVVodAddressTypeBackup), @(PLVVodAddressTypeHttpDNS)]) {
        self.retryCounts[type] = @(0);
    }
}

- (void)resolveDNSWithDomain:(NSString *)domain
                  completion:(DNSResolveCompletion)completion {
    // 这里实现HTTPDNS解析逻辑
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        // 模拟DNS解析
        NSString *ipAddress =  [[PLVVodHttpDnsManager sharedManager] getIpByHostAsync:domain];
        // 实际应调用HTTPDNS服务
        completion(ipAddress, nil);
    });
}

@end

