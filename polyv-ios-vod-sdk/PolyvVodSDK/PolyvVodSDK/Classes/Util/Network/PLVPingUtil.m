//
//  PLVPingUtil.m
//  PLVVodSDK
//
//  Created by polyv on 2023/6/27.
//  Copyright © 2023 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PLVVodSimplePing.h"

#include <sys/socket.h>
#include <netdb.h>

#ifndef DEBUG
    #define NSLog(...)
#endif

static NSString * displayAddressForAddress(NSData * address) {
    int         err;
    NSString *  result;
    char        hostStr[NI_MAXHOST];
    
    result = nil;
    
    if (address != nil) {
        err = getnameinfo(address.bytes, (socklen_t) address.length, hostStr, sizeof(hostStr), NULL, 0, NI_NUMERICHOST);
        if (err == 0) {
            result = @(hostStr);
        }
    }
    
    if (result == nil) {
        result = @"?";
    }

    return result;
}

/*! Returns a short error string for the supplied error.
 *  \param error The error to render.
 *  \returns A short string representing that error.
 */

static NSString * shortErrorFromError(NSError * error) {
    NSString *      result;
    NSNumber *      failureNum;
    int             failure;
    const char *    failureStr;
    
    assert(error != nil);
    
    result = nil;
    
    // Handle DNS errors as a special case.
    
    if ( [error.domain isEqual:(NSString *)kCFErrorDomainCFNetwork] && (error.code == kCFHostErrorUnknown) ) {
        failureNum = error.userInfo[(id) kCFGetAddrInfoFailureKey];
        if ( [failureNum isKindOfClass:[NSNumber class]] ) {
            failure = failureNum.intValue;
            if (failure != 0) {
                failureStr = gai_strerror(failure);
                if (failureStr != NULL) {
                    result = @(failureStr);
                }
            }
        }
    }
    
    // Otherwise try various properties of the error object.
    
    if (result == nil) {
        result = error.localizedFailureReason;
    }
    if (result == nil) {
        result = error.localizedDescription;
    }
    assert(result != nil);
    return result;
}


#pragma mark * Main

/*! The main object for our tool.
 *  \details This exists primarily because PLVVodSimplePing requires an object to act as its delegate.
 */

@interface Main : NSObject <PLVVodSimplePingDelegate>

@property (nonatomic, strong, readwrite, nullable) PLVVodSimplePing * pinger;
@property (nonatomic, strong, readwrite, nullable) NSTimer *    sendTimer;

// TODO: public?
@property (nonatomic, assign) int maxPackagesNum;
@property (nonatomic, strong) NSMutableDictionary *result;
@property (nonatomic, copy) void (^completion)(NSDictionary *extraInfo);

@end

NSString *const packagesKey = @"packages";

@implementation Main

- (instancetype)init {
	if (self = [super init]) {
		[self commonInit];
	}
	return self;
}
- (void)commonInit {
    _maxPackagesNum = 4;
    _result = [NSMutableDictionary dictionary];
    _result[packagesKey] = [NSMutableDictionary dictionary];
}

- (void)dealloc {
    [self->_pinger stop];
    [self->_sendTimer invalidate];
}

/*! The Objective-C 'main' for this program.
 *  \details This creates a PLVVodSimplePing object, configures it, and then runs the run loop 
 *      sending pings and printing the results.
 *  \param hostName The host to ping.
 */

- (void)runWithHostName:(NSString *)hostName completion:(void (^)(NSDictionary *extraInfo))completion {
    self.completion = completion;
    
    assert(self.pinger == nil);
    
    self.pinger = [[PLVVodSimplePing alloc] initWithHostName:hostName];
    assert(self.pinger != nil);
    
    // By default we use the first IP address we get back from host resolution (.Any) 
    // but these flags let the user override that.
    
    self.pinger.delegate = self;
    [self.pinger start];
    
    _result[@"hostName"] = hostName;
    
    // TODO: in application
     do {
         [[NSRunLoop currentRunLoop] runMode:NSDefaultRunLoopMode beforeDate:[NSDate distantFuture]];
     } while (self.pinger != nil);
}

/*! Sends a ping.
 *  \details Called to send a ping, both directly (as soon as the PLVVodSimplePing object starts up) 
 *      and via a timer (to continue sending pings periodically).
 */

- (void)sendPing {
    id totalSendValue = [_result objectForKey:@"totalSend"];
    if ([totalSendValue integerValue] >= _maxPackagesNum) {
        if (self.completion)
            self.completion(_result);
        
        // 目前使用 sendTimer 间隔时间作为包超时时间
        [self.sendTimer invalidate];
        self.sendTimer = nil;

        self.pinger = nil;
        return;
    }
    
    assert(self.pinger != nil);
    [self.pinger sendPingWithData:nil];
    
    if (!totalSendValue) {
        _result[@"totalSend"] = @1;
    } else {
        _result[@"totalSend"] = @([totalSendValue integerValue]+1);
    }
}

- (void)PLVVodSimplePing:(PLVVodSimplePing *)pinger didStartWithAddress:(NSData *)address {
    #pragma unused(pinger)
    assert(pinger == self.pinger);
    assert(address != nil);

    NSString *addressString = displayAddressForAddress(address);
    NSLog(@"pinging %@", addressString);
    _result[@"ip"] = addressString;

    // Send the first ping straight away.
    
    [self sendPing];

    // And start a timer to send the subsequent pings.
    
    assert(self.sendTimer == nil);
    self.sendTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(sendPing) userInfo:nil repeats:YES];
}

- (void)PLVVodSimplePing:(PLVVodSimplePing *)pinger didFailWithError:(NSError *)error {
    #pragma unused(pinger)
    assert(pinger == self.pinger);
    NSString *errorString = shortErrorFromError(error);
    NSLog(@"failed: %@", errorString);
    _result[@"error"] = errorString;

    [self.sendTimer invalidate];
    self.sendTimer = nil;

    // No need to call -stop.  The pinger will stop itself in this case.
    // We do however want to nil out pinger so that the runloop stops.

    self.pinger = nil;
}

- (void)PLVVodSimplePing:(PLVVodSimplePing *)pinger didSendPacket:(NSData *)packet sequenceNumber:(uint16_t)sequenceNumber {
    #pragma unused(pinger)
    assert(pinger == self.pinger);
    #pragma unused(packet)
    NSLog(@"#%u sent", (unsigned int) sequenceNumber);
    
    NSTimeInterval start = [[NSDate date] timeIntervalSince1970];
    _result[packagesKey][@(sequenceNumber)] = [NSMutableDictionary dictionaryWithObject:@(start) forKey:@"startTime"];
}

- (void)PLVVodSimplePing:(PLVVodSimplePing *)pinger didFailToSendPacket:(NSData *)packet sequenceNumber:(uint16_t)sequenceNumber error:(NSError *)error {
    #pragma unused(pinger)
    assert(pinger == self.pinger);
    #pragma unused(packet)
    NSLog(@"#%u send failed: %@", (unsigned int) sequenceNumber, shortErrorFromError(error));
    
    if (_result[packagesKey][@(sequenceNumber)]) {
        _result[packagesKey][@(sequenceNumber)][@"failedError"] = error;
    }
}

- (void)PLVVodSimplePing:(PLVVodSimplePing *)pinger didReceivePingResponsePacket:(NSData *)packet sequenceNumber:(uint16_t)sequenceNumber {
    #pragma unused(pinger)
    assert(pinger == self.pinger);
    #pragma unused(packet)
    NSLog(@"#%u received, size=%zu", (unsigned int) sequenceNumber, (size_t) packet.length);
    
    id dict = _result[packagesKey][@(sequenceNumber)];
    if (dict) {
        NSDate *start = [NSDate dateWithTimeIntervalSince1970:[dict[@"startTime"] doubleValue]];
        NSDate *end = [NSDate date];
        NSTimeInterval elapsedTime = [end timeIntervalSinceDate:start]*1000;
        dict[@"endTime"] = @([end timeIntervalSince1970]);
        dict[@"elapsedTime"] = @(elapsedTime);
        dict[@"receivedBytes"] = @(packet.length);
        NSLog(@"elapsedTime: %f", elapsedTime);
    }
}

@end

#pragma Public

void ping(NSString *host, int testPackagesNum, void (^completion)(NSDictionary *extraInfo)) {
    Main *mainObj = [[Main alloc] init];
    mainObj.maxPackagesNum = testPackagesNum;
    [mainObj runWithHostName:host completion:completion];
}
