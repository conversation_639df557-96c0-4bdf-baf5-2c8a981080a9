//
//  PLVVodHttpDnsManager.h
//  _PolyvVodSDK
//
//  Created by junotang on 2022/2/28.
//  Copyright © 2022 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// HttpDns工具类
@interface PLVVodHttpDnsManager : NSObject

/// sessionid 记录本次解析回话，用于问题排查
@property (nonatomic, strong, readonly) NSString *sessionid;

#pragma mark - [ 方法 ]

+ (instancetype)sharedManager;

/// 配置HTTPDNS信息
/// @param key 密钥，为nil时将使用缓存
- (void)configHttpDNSWithSecretKey:(NSString *)key;

/// 获取域名对应格式化后的IP (针对ipv6)
/// @param host 域名
- (NSString *)getIpByHostAsyncInURLFormat:(NSString *)host;

/// 获取域名对应的IP，单IP
/// @param host 域名
- (NSString *)getIpByHostAsync:(NSString *)host;

/// DNS 优选
/// @param on 开关
- (void)enableDNSOptimize:(BOOL) on;

@end

NS_ASSUME_NONNULL_END
