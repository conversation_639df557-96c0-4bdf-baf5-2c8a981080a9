//
//  PLVVodReportManager.h
//  _PolyvVodSDK
//
//  Created by <PERSON><PERSON> <PERSON> on 2018/3/13.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>
@class PLVVodElogModel;

@interface PLVVodReportManager : NSObject

/// viewlog
+ (void)reportViewLogWithParams:(NSDictionary *)params;


/// loading qos
+ (void)reportLoadingQosWithPid:(NSString *)pid
                            vid:(NSString *)vid
                           time:(NSTimeInterval)time
                    time_player:(NSTimeInterval)time_player
                  time_business:(NSTimeInterval)time_business
                         params:(NSDictionary *)extraParams;

/// buffer qos
+ (void)reportBufferQosWithPid:(NSString *)pid
                           vid:(NSString *)vid
                          time:(NSTimeInterval)time
                        params:(NSDictionary *)extraParams;

/// qos stalling
+ (void)reportQosStallingWithPid:(NSString *)pid 
                             vid:(NSString *)vid
                            time:(NSTimeInterval)time
                          params:(NSDictionary *)extraParams;

/// play error qos
+ (void)reportErrorQosWithPid:(NSString *)pid
                          vid:(NSString *)vid
                    errorCode:(NSString *)errorCode
                   requestUrl:(NSString *)requestUrl
                 responseCode:(NSString *)responseCode
                       params:(NSDictionary *)extraParams;


/// 发送elog
+ (void)reportElogWithElogModel:(PLVVodElogModel *)model
                     completion:(void(^)(NSError *error))completion;

@end
