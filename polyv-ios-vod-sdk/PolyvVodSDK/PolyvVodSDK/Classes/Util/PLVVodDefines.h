//
//  PLVVodDefines.h
//  PolyvVodSDK
//
//  Created by mac on 2018/12/5.
//  Copyright © 2018 POLYV. All rights reserved.
//

#ifndef PLVVodDefines_h
#define PLVVodDefines_h

typedef NS_ENUM(NSUInteger, PLVVodQosErrorType) {
    ePLVVodQosErrorLoadVideoFail ,       // 视频加载失败
    ePLVVodQosErrorLoadVideoJsonFail,    // videojson 加载失败
    ePLVVodQosErrorLoadVideoTokenFail,   // 加载播放令牌失败
    ePLVVodQosErrorOther                 // 其他错误
};

#define PLVVodQosErrorLoadVideoFail          @"load_video_failure"
#define PLVVodQosErrorLoadVideoJsonFail      @"load_videojson_failure"
#define PLVVodQosErrorOther                  @"other_error"
#define PLVVodQosErrorLoadVideoTokenFail     @"load_videotoken_failure"

#define PLVSubtitleDir                       @"subtitles"     // 字幕存储相对路径
#define PLVExamsDir                          @"exams"         // 问答存储相对路径
#define PLVPPTDir                       @"PPT"           // ppt 课件相对路径
#define PLVMultiAccountIdDefault             @"0"             // 多账号体系，默认用户Id


#endif /* PLVVodDefines_h */
