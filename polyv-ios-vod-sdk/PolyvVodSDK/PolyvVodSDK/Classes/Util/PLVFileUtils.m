//
//  PLVFileUtils.m
//  PolyvVodSDK
//
//  Created by mac on 2019/4/9.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import "PLVFileUtils.h"

@implementation PLVFileUtils

+ (NSString *)homeDir{
    return NSHomeDirectory();
}

+ (NSString *)documentsDir{
    NSArray *docArray = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    return [docArray firstObject];
}

+ (NSString *)libraryDir{
    NSArray *libArray = NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, YES);
    return [libArray firstObject];
}

+ (NSString *)cacheDir{
    NSArray *cacheArray = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    return [cacheArray firstObject];
}

+ (NSString *)getCacheDirectoryWithAccountId:(NSString *)accountId{
    NSString *cachePath = [NSString stringWithFormat:@"PolyvCache_%@", accountId];
    NSString *cacheDirectory = [[self.class documentsDir] stringByAppendingPathComponent:cachePath];
    
    return cacheDirectory;
}

+ (NSString *)getVideoDirectoryWithAccoutId:(NSString *)accountId{
    NSString *dbPath = [[self.class getCacheDirectoryWithAccountId:accountId] stringByAppendingPathComponent:@"VideoCache"];
    
    return dbPath;
}

+ (NSString *)getDbDirectoryWithAccountId:(NSString *)accountId{
    NSString *dbPath = [[self.class getCacheDirectoryWithAccountId:accountId] stringByAppendingPathComponent:@"DBCache"];
    
    return dbPath;
}

@end
