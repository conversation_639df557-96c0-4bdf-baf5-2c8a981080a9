//
//  PLVVodElogSeekModel.h
//  PLVVodSDK
//
//  Created by mac on 2019/11/12.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PLVVodJsonModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface PLVVodElogSeekModel : PLVVodJsonModel

@property (nonatomic, copy) NSString *seek_count;
@property (nonatomic, copy) NSString *seek_time_ago;
@property (nonatomic, copy) NSString *seek_time_later;
@property (nonatomic, copy) NSString *play_time_duration;
@property (nonatomic, copy) NSString *stay_time_duration;
@property (nonatomic, copy) NSString *flow;

- (void)reset;

- (NSString *)uteSeek;
- (NSString *)uteSeekDes;

@end

NS_ASSUME_NONNULL_END
