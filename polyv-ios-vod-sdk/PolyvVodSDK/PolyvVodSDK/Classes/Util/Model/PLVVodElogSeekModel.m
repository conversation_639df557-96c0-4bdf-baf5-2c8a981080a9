//
//  PLVVodElogSeekModel.m
//  PLVVodSDK
//
//  Created by mac on 2019/11/12.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import "PLVVodElogSeekModel.h"

@implementation PLVVodElogSeekModel

- (void)reset{
    self.play_time_duration = @"";
    self.stay_time_duration = @"";
    self.seek_count = @"";
    self.seek_time_later = @"";
    self.seek_time_ago = @"";
    self.flow = @"";
}

- (NSString *)uteSeek{
    NSString *seekStr = @"";
    if ([self.seek_count integerValue] > 0){
        if ([self.seek_time_ago integerValue] > [self.seek_time_later integerValue]){
            seekStr = @"skbw";
        }
        else{
            seekStr = @"skfw";
        }
    }
    
    return seekStr;
}

- (NSString *)uteSeekDes{
    NSString *seekDes = @"";
    if ([self.seek_count integerValue] > 0){
        seekDes = [NSString stringWithFormat:@"seek_count:%@", self.seek_count];
    }
    
    return seekDes;
}

@end
