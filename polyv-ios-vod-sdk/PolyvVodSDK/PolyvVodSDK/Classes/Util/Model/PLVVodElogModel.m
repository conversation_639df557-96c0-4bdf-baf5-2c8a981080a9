//
//  PLVVodElogModel.m
//  PolyvVodSDK
//
//  Created by mac on 2019/4/30.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import "PLVVodElogModel.h"
#import "PLVVodSettings.h"
#import "PLVVodUtil+Device.h"
#import "PLVVodUtil.h"

// elog
@implementation PLVVodElogModel

- (instancetype)init{
    self = [super init];
    if (self){
        _viewerId = [PLVVodSettings sharedSettings].viewerId;
        _deviceId = [PLVVodUtil getDeviceId];
        _version2 = [PLVVodUtil userAgent];
        
        _platform2 = @"iOS";
        
        // 时间戳
        NSTimeInterval timestamp = [NSDate date].timeIntervalSince1970*1000;
        _time = [NSString stringWithFormat:@"%.0f", timestamp];

        _userAgent = [NSString stringWithFormat:@"%@ / %@ / %@", [PLVVodUtil getDeviceName], [PLVVodUtil getSystemVersion], _version2];
    }
    
    return self;
}

@end

// logFile
@implementation PLVVodElogLogfileModel

- (instancetype)init{
    self = [super init];
    if (self){
        // 线程调用堆栈
//        _systemLog = [self threadCallStack];
        _systemLog = @"";
    }
    
    return self;
}

- (NSString *)threadCallStack{
    NSMutableString *str = [[NSMutableString alloc] init];
    [[NSThread callStackSymbols] enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [str appendString:obj];
    }];
    
    return [self removeSpaceAndNewline:str];
}

- (NSString *)removeSpaceAndNewline:(NSString *)str{
    NSString *temp = [str stringByReplacingOccurrencesOfString:@"  " withString:@""];
//    temp = [temp stringByReplacingOccurrencesOfString:@"\r" withString:@""];
//    temp = [temp stringByReplacingOccurrencesOfString:@"\n" withString:@""];
    return temp;
}

@end

// playerItem
@implementation PLVVodElogPlayerPramaItem

@end
