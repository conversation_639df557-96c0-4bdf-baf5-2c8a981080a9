//
//  PLVVodElogModel.h
//  PolyvVodSDK
//
//  Created by mac on 2019/4/30.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PLVVodJsonModel.h"

NS_ASSUME_NONNULL_BEGIN

/// 定义业务功能模块，暂时定义上传/下载/播放模块
static NSString *PLV_Busi_Download_Module = @"download";
static NSString *PLV_Busi_Play_Module = @"play";
static NSString *PLV_Busi_Upload_Module = @"upload";

@class PLVVodElogLogfileModel;
@class PLVVodElogPlayerPramaItem;

@interface PLVVodElogModel : PLVVodJsonModel

@property (nonatomic, copy) NSString *vid;
@property (nonatomic, copy) NSString *videoId;
@property (nonatomic, copy) NSString *userId2;
@property (nonatomic, copy) NSString *playId;
@property (nonatomic, copy) NSString *viewerId;
@property (nonatomic, copy) NSString *event;
@property (nonatomic, copy) NSString *errorCode;
@property (nonatomic, copy) NSString *module;
@property (nonatomic, copy) NSString *version2; // 
@property (nonatomic, copy) NSString *deviceId;
@property (nonatomic, copy) NSString *userAgent;
@property (nonatomic, copy) NSString *time;
@property (nonatomic, copy) NSString *platform2;

@property (nonatomic, strong) PLVVodElogLogfileModel *logFile;

@end

@interface PLVVodElogLogfileModel : PLVVodJsonModel

@property (nonatomic, copy) NSString *systemLog ; // app日志，系统日志
@property (nonatomic, copy) NSString *infomation ; // 普通信息，自定义的信息
@property (nonatomic, copy) NSString *exception ; // 异常信息，程序异常
@property (nonatomic, copy) NSArray<PLVVodElogPlayerPramaItem *> *playerParam ; // ijkplayer播放器参数

@end

@interface PLVVodElogPlayerPramaItem : PLVVodJsonModel

@property (nonatomic, copy) NSString *type;  // 播放参数类型
@property (nonatomic, copy) NSString *name;  // 参数名称
@property (nonatomic, copy) NSString *value; // 参数值

@end

/** log
 
vid:""
userId:""
playId:""
viewerId:""//学员ID
logfile:"" //详细的日志内容
event:"" //事件类型
errorCode:""//错误码
module:"" //所属模块名
version:"" // SDK版本
deviceId:"" //设备标识
userAgent:""//https://www.scientiamobile.com/correctly-form-user-agents-for-mobile-apps/
time:"" // 毫秒单位的时间戳
*/

NS_ASSUME_NONNULL_END
