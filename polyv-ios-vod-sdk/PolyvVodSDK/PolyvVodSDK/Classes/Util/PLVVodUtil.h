//
//  PLVVodUtil.h
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/9.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PLVVodConstans.h"
#import "PLVVodVideo.h"
#import <CommonCrypto/CommonCryptor.h>
#import "PLVVodSettings.h"
#import "PLVVodDefines.h"

/// vid -> videoPoolId
NSString *videoPoolIdWithVid(NSString *vid);

/// videoPoolId -> vid
NSString *vidWithVideoPoolId(NSString *videoPoolId);

/// 方向
NSString *NSStringFromUIDeviceOrientation(UIDeviceOrientation orientation);

/// 创建错误
NSError *PLVVodErrorWithCode(PLVVodErrorCode code);
NSError *PLVVodErrorMake(PLVVodErrorCode code, void (^userInfoHandler)(NSMutableDictionary *userInfo));
NSError *PLVVodErrorMakeWithError(PLVVodErrorCode code, NSError *error, void (^userInfoHandler)(NSMutableDictionary *userInfo));

/// 日志输出
#define LOG_ERROR	([PLVVodSettings sharedSettings].logLevel & PLVVodLogLevelError)
#define LOG_WARN	([PLVVodSettings sharedSettings].logLevel & PLVVodLogLevelWarn)
#define LOG_INFO	([PLVVodSettings sharedSettings].logLevel & PLVVodLogLevelInfo)
#define LOG_DEBUG	([PLVVodSettings sharedSettings].logLevel & PLVVodLogLevelDebug)

#define PLVVodLogError(format, ...)	if (LOG_ERROR)	{NSLog((@"[PLVVODSDK_ERROR] " format), ##__VA_ARGS__);}
#define PLVVodLogWarn(format, ...)	if (LOG_WARN)	{NSLog((@"[PLVVODSDK_WARN] " format), ##__VA_ARGS__);}
#define PLVVodLogInfo(format, ...)	if (LOG_INFO)	{NSLog((@"[PLVVODSDK_INFO] " format), ##__VA_ARGS__);}
#define PLVVodLogDebug(format, ...)	if (LOG_DEBUG)	{NSLog((@"[PLVVODSDK_DEBUG] " format), ##__VA_ARGS__);}

@interface PLVVodUtil : NSObject

/// 时间转字符串
+ (NSString *)timeStringWithSeconds:(NSTimeInterval)seconds;

/// 字符串转时间
+ (NSTimeInterval)secondsWithtimeString:(NSString *)timeString;

/// 获取签名
+ (NSString *)signWithTimestamp:(NSString *)timestamp videoPoolId:(NSString *)videoPoolId;

/// 时间戳
+ (NSString *)timestamp;

/// 获取当前时间
+ (NSTimeInterval)currentSeconds;

/// 计算时间间隔
+ (NSTimeInterval)timeIntervalSinceSeconds:(NSTimeInterval)seconds;

/// pid
+ (NSString *)pid;

/// 随机数 [from, to]
+ (NSInteger)randomIntegerFrom:(int)fromValue to:(int)toValue;
+ (double)randomDoubleFrom:(double)fromValue to:(double)toValue accuracy:(NSInteger)accuracy;

/// userAgent 值
+ (NSString *)userAgent;

/// ijkplayer使用的userAgent 值
+ (NSString *)ijkplayerUserAgent;

/// 十六进制字符串转NSData
+ (NSData *)dataWithHexString:(NSString *)hexString;

/// 十六进制转颜色
+ (UIColor *)colorWithHex:(NSUInteger)hexValue;
+ (UIColor *)colorWithHex:(NSUInteger)hexValue alpha:(CGFloat)alpha;

#pragma mark - 文件处理

/// 创建不存在的目录
+ (void)createDirIfNeed:(NSString *)dir error:(NSError **)error;

/// 移动文件
+ (BOOL)movePath:(NSString *)fromPath toPath:(NSString *)toPath error:(NSError **)error;

/// 兼容 1.0 SDK 离线视频
+ (void)compatibleWithPreviousDownloadDir:(NSString *)previousDownloadDir;

#pragma mark - 加密/解密

/// Base64 编码
+ (NSString *)base64String:(NSString *)inputString;

/// url safe Base64 编码
+ (NSString *)urlSafeBase64String:(NSString *)inputString;

/// SHA1 加密
+ (NSString *)sha1String:(NSString *)inputString;

/// MD5 加密
+ (NSString *)md5String:(NSString *)inputString;

/// AES-128 加密，不使用加密向量
+ (NSData *)AES128EncryptedDataWithKey:(NSString *)key data:(NSData *)data;

/// AES-128 解密，不使用加密向量
+ (NSData *)AES128DecryptedDataWithKey:(NSString *)key data:(NSData *)data;

/// AES-128 加密
+ (NSData *)AES128EncryptedDataWithKey:(NSString *)key iv:(NSString *)iv data:(NSData *)data;

/// AES-128 解密
+ (NSData *)AES128DecryptedDataWithKey:(NSString *)key iv:(NSString *)iv data:(NSData *)data;

/// AES-128
+ (NSData *)AES128Operation:(CCOperation)operation key:(NSData *)keyData iv:(NSData *)ivData data:(NSData *)contentData;

+ (NSUInteger)preferredIndexWithArray:(NSArray *)array index:(NSInteger)index;

/// 拼接参数
+ (NSString *)url:(NSString *)url appendParams:(NSDictionary *)paramDict;

+ (NSString *)convertDictionaryToSortedString:(NSDictionary *)params;

/// NSData -> NSDictionary
+ (NSDictionary *)dictionaryWithData:(NSData *)data;

/// NSDictionary -> NSData
+ (NSData *)dataWithDictionary:(NSDictionary *)dic;

/// 判断是否为空字符串
+ (BOOL)isNilString:(NSString *)origStr;

/// 根据url 获取域名
+ (NSString *)getHostWithUrl:(NSString *)urlStr;

/// 根据Qos错误类型返回具体的错误码
+ (NSString *)getQosErrorCodeWithType:(PLVVodQosErrorType )type;

/// 检查vid是否正确
+ (BOOL)validateVid:(NSString *)vid;


/// 当前系统语言是否中文语言
+ (BOOL)isChineseSystemLanguage;

@end
