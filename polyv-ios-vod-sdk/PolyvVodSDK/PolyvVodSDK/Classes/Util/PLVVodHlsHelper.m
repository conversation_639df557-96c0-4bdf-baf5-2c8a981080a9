//
//  PLVVodM3u8Helper.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/12.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodHlsHelper.h"
#import "PLVVodSettings.h"
#import "PLVVodHttpDnsManager.h"
#import "PLVVodUtil.h"
#import "PLVVodNetworking.h"

/// 标记 M3U8 文件开始
static NSString * const M3U8TagEXTM3U = @"#EXTM3U";
/// 标记每个片段时长
static NSString * const M3U8TagEXTINF = @"#EXTINF:";
/// 标记换行
static NSString * const M3U8TagLF = @"\n";

@implementation PLVVodHlsHelper

/// 修正旧版m3u8
+ (NSString *)fixPreviousM3u8:(NSString *)content {
	if (!content.length) {
		return nil;
	}
	NSString *modifiedString = content;
	
	// 修正key链接
	modifiedString = [self fixKeyInM3u8:modifiedString];
	
	// 修正旧版ts链接
	NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"^[/](.*)([^/]*\\.ts)"
																		   options:NSRegularExpressionCaseInsensitive | NSRegularExpressionAnchorsMatchLines
																			 error:nil];
	
	// 不匹配
	NSUInteger matchNumber = [regex numberOfMatchesInString:modifiedString options:0 range:NSMakeRange(0, modifiedString.length)];
	if (!matchNumber) {
		return nil;
	}
	
	NSString *template = [NSString stringWithFormat:@"$1$2"];
	modifiedString = [regex stringByReplacingMatchesInString:modifiedString
													 options:0
													   range:NSMakeRange(0, modifiedString.length)
												withTemplate:template];
	return modifiedString;
}

/// ts 路径改为本地相对路径
+ (NSString *)fixTsLocallyInM3u8:(NSString *)content {
	if (!content.length) {
		return nil;
	}
	NSString *modifiedString = content;
	{ // 修正在线ts链接
		NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"https*://(([^\\n#\"]*/)*)([^/]*\\.ts)"
																			   options:NSRegularExpressionCaseInsensitive
																				 error:nil];
		NSString *template = [NSString stringWithFormat:@"$3"];
		modifiedString = [regex stringByReplacingMatchesInString:modifiedString
																   options:0
																	 range:NSMakeRange(0, modifiedString.length)
															  withTemplate:template];
	}
	return modifiedString;
}

/// pts 路径改为本地相对路径
+ (NSString *)fixPtsLocallyInM3u8:(NSString *)content {
    if (!content.length) {
        return nil;
    }
    NSString *modifiedString = content;
    { // 修正在线ts链接
        NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"https*://(([^\\n#\"]*/)*)([^/]*\\.pts)"
                                                                               options:NSRegularExpressionCaseInsensitive
                                                                                 error:nil];
        NSString *template = [NSString stringWithFormat:@"$3"];
        modifiedString = [regex stringByReplacingMatchesInString:modifiedString
                                                                   options:0
                                                                     range:NSMakeRange(0, modifiedString.length)
                                                              withTemplate:template];
    }
    return modifiedString;
}

/// 修改 M3U8 文件中的 key 字段为本地相对路径
+ (NSString *)fixKeyInM3u8:(NSString *)content {
	if (!content.length) return nil;
	NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"\".*/.*\\..*\""
																		   options:NSRegularExpressionCaseInsensitive
																			 error:nil];
	
	NSRange matchRange = [regex rangeOfFirstMatchInString:content options:0 range:NSMakeRange(0, content.length)];
	if (matchRange.location == NSNotFound) {
		return content;
	}
	NSString *matchString = [content substringWithRange:matchRange];
	matchString = [matchString stringByReplacingOccurrencesOfString:@"\"" withString:@""];
	matchString = matchString.lastPathComponent;
	matchString = [NSString stringWithFormat:@"\"%@\"", matchString];
	NSString *modifiedString = [content stringByReplacingCharactersInRange:matchRange withString:matchString];
	//NSLog(@"%@", content);
    
    // 二次路径修正 web加密，ab流视频，key路径url 会带参数返回
    modifiedString = [self fixKeyInM3u8InSecond:modifiedString];
    
	return modifiedString;
}

/// 修改 M3U8 文件中的 key 字段为本地相对路径  二阶段修改
+ (NSString *)fixKeyInM3u8InSecond:(NSString *)content{
    if (!content.length) return nil;
    if (![content containsString:@".key?"]){
        return content;
    }
    NSString *patternStr = @"\\?([^\"?]+)\"";
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:patternStr
                                                                           options:NSRegularExpressionCaseInsensitive
                                                                             error:nil];
    
    NSRange matchRange = [regex rangeOfFirstMatchInString:content options:0 range:NSMakeRange(0, content.length)];
    if (matchRange.location == NSNotFound) {
        return content;
    }
    if (matchRange.length > 0){
        // 不包含‘ “ ’
        matchRange = NSMakeRange(matchRange.location, matchRange.length -1);
    }
    NSString *matchString = [content substringWithRange:matchRange];
    NSString *modifiedString = [content stringByReplacingOccurrencesOfString:matchString withString:@""];
    //NSLog(@"%@", content);
    return modifiedString;
}

/// 获取ts数组
+ (NSArray *)mediaListOfM3U8:(NSString *)content handler:(void (^)(NSString *tsUrl))handler {
	return [self mediaListOfM3U8:content count:0 handler:handler];
}

+ (NSArray *)mediaListOfM3U8:(NSString *)content count:(int)count {
	return [self mediaListOfM3U8:content count:count handler:nil];
}

+ (NSArray *)mediaListOfM3U8:(NSString *)content count:(int)count handler:(void (^)(NSString *tsUrl))handler {
	if (content.length == 0) return nil;
	
	NSRange rangeOfEXTM3U = [content rangeOfString:M3U8TagEXTM3U];
	if (rangeOfEXTM3U.location == NSNotFound || rangeOfEXTM3U.location != 0) return nil;
	
	NSMutableArray *mediaList = [NSMutableArray array];
	
	// 片段时长标签
	NSRange durationTagRange = [content rangeOfString:M3U8TagEXTINF];
	
	// 剩余内容初始化，为以 M3U8 头标记到文件尾的文本
	NSString *remainingContent = content;
	
	while (NSNotFound != durationTagRange.location) {
		/// 读取 EXTINF 与内容之间的时间，即 EXTINF 值
		
		/// 忽略 EXTINF 标签行
		// 更新剩余文本，为片段时长标签到结尾
		remainingContent = [remainingContent substringFromIndex:durationTagRange.location];
		NSRange extinfoLFRange = [remainingContent rangeOfString:M3U8TagLF];
		// 更新剩余文本，为片段时长标签下一行（片段URL）到结尾
		remainingContent = [remainingContent substringFromIndex:extinfoLFRange.location + 1];
		
		// 读取媒体 URL，并且忽略片段时长标签行和空白行
		while (YES) {
			/// 获取媒体 URL，并去除空格
			NSRange lfRange = [remainingContent rangeOfString:M3U8TagLF];
			NSString *line = [remainingContent substringWithRange:NSMakeRange(0, lfRange.location)];
			line = [line stringByReplacingOccurrencesOfString:@" " withString:@""];
			
			// 更新剩余文本，为换行符结尾到结尾
			remainingContent = [remainingContent substringFromIndex:NSMaxRange(lfRange)]; // lfRange.location + 1 -> NSMaxRange(lfRange)
			
			// 去除空行
			if ([line characterAtIndex:0] != '#' && 0 != line.length) { // 非标签行，非空行
				// 移除CR换行
				char lastChar = [line characterAtIndex:line.length - 1];
				if (lastChar == '\r') line = [line substringToIndex:line.length - 1];
				// M3U8KeyMediaURL
				if (handler) handler(line);
				[mediaList addObject:line];
				break;
			}
		}
		
		// 更新剩余文本，为下一片段时长标签到结尾
		durationTagRange = [remainingContent rangeOfString:M3U8TagEXTINF];
		if (mediaList.count >= count && count != 0) break;
	}
	return mediaList;
}

+ (NSString *)fixTsUrlInM3u8:(NSString *)content {
    if ([PLVVodUtil isNilString:content])
        return nil;
    
    // 后台可能返回http https 两种ts链接
    NSString *newm3u8 = [content stringByReplacingOccurrencesOfString:@"http://" withString:@"https://"];
    
    return [self m3u8FromContent:newm3u8];
}

/// 根据设置修改为 httpDNS ts链接
+ (NSString *)fixHttpDNSIfNeedInM3u8:(NSString *)content {
    if (!content.length) {
        return nil;
    }
    return [self m3u8FromContent:content];
}

+ (NSString *)m3u8FromContent:(NSString *)content {
    NSString *newm3u8 = content;
    if ([PLVVodSettings sharedSettings].enableHttpDNS == NO || [[UIDevice currentDevice].systemVersion floatValue] < 10.0 || [PLVVodSettings sharedSettings].enableIPV6 == YES) {
        // [ATS默认禁止http访问，httpdns 方案在ios9 系统上，经测试下载链接被禁止]
        return newm3u8;
    }
    
    NSString *tsUrlPattern = @"http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?ts";
    NSString *firstTsUrl;
    @try {
        firstTsUrl = [newm3u8 substringWithRange:[newm3u8 rangeOfString:tsUrlPattern options:NSRegularExpressionSearch]];
    }
    @catch (NSException *exception) {
        return newm3u8;
    }
    firstTsUrl = [firstTsUrl stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    NSURL *firstTSURL = [NSURL URLWithString:firstTsUrl];
    
    // 获取ip
    NSString *host = firstTSURL.host;
    NSString *hostIP = [[PLVVodHttpDnsManager sharedManager]getIpByHostAsync:host];
    //NSLog(@"host = %@", hostIP);
    if (hostIP.length) {
        newm3u8 = [newm3u8 stringByReplacingOccurrencesOfString:host withString:[hostIP stringByAppendingFormat:@"/%@", host]];
        newm3u8 = [newm3u8 stringByReplacingOccurrencesOfString:@"https" withString:@"http"];
        PLVVodLogDebug(@"使用 ip：%@，替换 host：%@。", hostIP, host);
    }else{
        PLVVodLogDebug(@"初次启动 HttpDNS，将使用源地址访问。");
    }
    
    return newm3u8;
}

/// 使用 HttpDNS 修正请求
+ (BOOL)fixHttpDNSIfNeedAtRquest:(NSMutableURLRequest *)request {
	// TODO: 过滤host为ip的URL
	if (![PLVVodSettings sharedSettings].enableHttpDNS) {
		return NO;
	}
    
    // add by libl [ATS默认禁止http访问，httpdns 方案在ios9 系统上，经测试下载链接被禁止]
    if ([[UIDevice currentDevice].systemVersion floatValue] < 10.0){
        return NO;
    }
    // add end
    
	// 获取ip
	NSString *host = request.URL.host;
    NSString *hostIP = [[PLVVodHttpDnsManager sharedManager]getIpByHostAsync:host];
	
	if (!hostIP.length) {
		PLVVodLogDebug(@"初次启动 HttpDNS，将使用源地址访问。");
		return NO;
	}
	NSString *originalUrl = request.URL.absoluteString;
	NSString *newUrl = [originalUrl stringByReplacingOccurrencesOfString:host withString:hostIP];
	newUrl = [newUrl stringByReplacingOccurrencesOfString:@"https" withString:@"http"];
	//NSLog(@"new url: %@", newUrl);
	request.URL = [NSURL URLWithString:newUrl];
	[request setValue:host forHTTPHeaderField:@"host"];
	PLVVodLogDebug(@"使用 ip：%@，替换 host：%@。", hostIP, host);
	
	return YES;
}

/// 加密 key
+ (NSData *)ecryptKey:(NSData *)keyData vid:(NSString *)vid {
	//NSLog(@"data origin: %zd", data.length);
	NSString *aeskey = [PLVVodUtil md5String:videoPoolIdWithVid(vid)];
	aeskey = [aeskey substringToIndex:kCCKeySizeAES128];
	Byte ivs[] = {7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7};
	NSData *ivData = [NSData dataWithBytes:ivs length:kCCKeySizeAES128];
	NSData *ecryptedKey = [PLVVodUtil AES128Operation:kCCEncrypt key:[aeskey dataUsingEncoding:NSUTF8StringEncoding] iv:ivData data:keyData];
	//NSLog(@"data ecrypted: %zd", endata.length);
	return ecryptedKey;
}

/// 通用TS HOST
+ (NSArray *)commomTsHosts {
    
    // ab-mts.videocc.net,ws-mts.videocc.net,kw-mts.videocc.net
	return @[
			 @"ab-mts.videocc.net",
             @"ws-mts.videocc.net",
             @"kw-mts.videocc.net",
             @"hw-mts.videocc.net",
             @"hls.videocc.net",    // m3u8 域名
			 ];
}

/// 检查m3u8内容
+ (void)checkM3u8WithURL:(NSURL *)URL completion:(void (^)(NSError *error))completion {
	// 本地获取的m3u8才校验本地的ts
	BOOL local = URL.fileURL;
	NSString *hlsDir = nil;
	if (local) {
		hlsDir = [URL path].stringByDeletingLastPathComponent;
	}
	[PLVVodNetworking requestM3u8WithUrl:URL.absoluteString params:nil requestHandler:^(NSMutableURLRequest *request) {
		NSString *ua = [NSString stringWithFormat:@"%@_check", [PLVVodUtil userAgent]];
		[request setValue:@"User-Agent" forHTTPHeaderField:ua];
	} completion:^(NSString *content, NSError *error) {
		if (!content.length) {
			PLVVodLogError(@"m3u8请求错误，%@", error);
			NSError *plvError = PLVVodErrorMakeWithError(m3u8_fetch_error, error, ^(NSMutableDictionary *userInfo) {
                if (!local){
                    userInfo[NSURLErrorKey] = URL;
                    userInfo[PLVVodHttpStatusCodeKey] = error.userInfo[PLVVodHttpStatusCodeKey];
                }
			});
			if (completion) completion(plvError);
			return;
		}
		
		NSFileManager *fileManager = [NSFileManager defaultManager];
		NSArray *tsUrls = [self mediaListOfM3U8:content count:0];
		for (NSString *tsUrl in tsUrls) {
			BOOL onlineTs = [tsUrl hasPrefix:@"http"];
            if (local == NO) {
                [PLVVodNetworking checkUrl:tsUrl completion:completion];
                break;
            }
			
            if (onlineTs) {
                PLVVodLogWarn(@"本地视频索引了在线资源");
            } else {
                BOOL isDir = NO;
                if (![fileManager fileExistsAtPath:hlsDir isDirectory:&isDir] || !isDir) {
                    PLVVodLogError(@"hls目录索引失败，%@", hlsDir);
                    NSError *plvError = PLVVodErrorMake(hls_dir_not_found, ^(NSMutableDictionary *userInfo) {
                        
                    });
                    if (completion) completion(plvError);
                    break;
                }
                [fileManager changeCurrentDirectoryPath:hlsDir];
                if (![fileManager fileExistsAtPath:tsUrl isDirectory:&isDir] || isDir) {
                    PLVVodLogError(@"切片索引失败，%@", tsUrl);
                    NSError *plvError = PLVVodErrorMake(ts_not_found, ^(NSMutableDictionary *userInfo) {
                        userInfo[NSFilePathErrorKey] = tsUrl;
                    });
                    if (completion) completion(plvError);
                    break;
                }
            }
		}
		
		if (completion) completion(error);
	}];
}


@end
