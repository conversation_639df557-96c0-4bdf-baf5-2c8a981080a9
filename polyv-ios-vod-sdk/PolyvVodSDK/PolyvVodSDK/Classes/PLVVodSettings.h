//
//  PLVVodSettings.h
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/9.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PLVVodConstans.h"

/// SDK 版本
extern NSString * const PLVVodSdkVersion;
/// SDK 平台标识
extern NSString * const PLVVodSdkPlatform;
/// hls私有加密版本
extern NSString * const PLVHlsPrivateVersion;
/// hls解密key版本
extern NSString * const PLVNativeKeyVersion;

/// viewlog 参数设置
@interface PLVVodViewerInfo : NSObject

/// 终端用户id
@property (nonatomic, copy) NSString *viewerId;

/// 终端用户昵称
@property (nonatomic, copy) NSString *viewerName;

/// 终端用户头像
@property (nonatomic, copy) NSString *viewerAvatar;

/// viewlog 自定义参数，param1 ,param2 对用户不可见
@property (nonatomic, copy) NSString *viewerExtraInfo1; // 对应 viewlog param3
@property (nonatomic, copy) NSString *viewerExtraInfo2; // 对应 viewlog param4
@property (nonatomic, copy) NSString *viewerExtraInfo3; // 对应 viewlog param5

@end

@interface PLVVodSettings : NSObject

/// userid
@property (nonatomic, copy, readonly) NSString *userid;

/// readtoken
@property (nonatomic, copy, readonly) NSString *readtoken;

/// writetoken
@property (nonatomic, copy, readonly) NSString *writetoken;

/// secretkey
@property (nonatomic, copy, readonly) NSString *secretkey;

/// 子账号appid
@property (nonatomic, copy, readonly) NSString *appid;

/// 请求自定义keyToken。
/// 下载加密视频时，若设置此block将优先使用block获取的值来解密视频；没有设置此block则sdk自动处理解密；
@property (nonatomic, copy) NSString *(^requestCustomKeyTokenBlock)(NSString *vid);

/// 终端用户id
@property (nonatomic, copy) NSString *viewerId;

/// 终端用户昵称
@property (nonatomic, copy) NSString *viewerName;

/// 终端用户头像
@property (nonatomic, copy) NSString *viewerAvatar;

/// viewlog 参数设置
@property (nonatomic, strong) PLVVodViewerInfo *viewerInfos;

/// 终端用户播放令牌额外参数
@property (nonatomic, copy) NSString *viewerTokenExtraParam;

/// 是否启用多账户，默认 NO。开启后可播放、下载除了配置的账号以外的其他账号的非加密视频。
@property (nonatomic, assign, getter=isMutilAccount) BOOL mutilAccount;

/// 日志输出等级，默认 PLVVodLogLevelWithoutDebug
@property (nonatomic, assign) PLVVodLogLevel logLevel;

/// 是否启动 HTTPDNS，默认 YES. 优先级低于远端的httpDnsMode属性
@property (nonatomic, assign) BOOL enableHttpDNS;
/// 是否启用 IPV6，如果启用，将自动选择IP，取消HTTPDNS
@property (nonatomic, assign) BOOL enableIPV6;
/// 延迟启动 HttpDNS，enableHttpDNS 为 YES 时生效，默认 NO
@property (class, nonatomic, assign) BOOL delayHttpDNS DEPRECATED_MSG_ATTRIBUTE("HttpDNS将在获取视频信息的时候启动，此属性已废弃");
/// 延迟启动 HttpDNS 的时间，delayHttpDNS 为 YES 时生效，默认 2（单位：秒）
@property (class, nonatomic, assign) NSInteger delayHttpDNSTime DEPRECATED_MSG_ATTRIBUTE("HttpDNS将在获取视频信息的时候启动，此属性已废弃");

/// 错误回调
@property (nonatomic, copy) void (^settingErrorHandler)(NSError *error);

/**
 获取 PLVVodSettings 静态对象

 @return PLVVodSettings 静态对象
 */
+ (instancetype)sharedSettings;

/**
 通过 userid、readtoken、writetoken、secretkey 配置账号

 @param userid userid
 @param readtoken readtoken
 @param writetoken writetoken
 @param secretkey secretkey
 @return 新的 PLVVodSettings 静态对象
 */
+ (instancetype)settingsWithUserid:(NSString *)userid readtoken:(NSString *)readtoken writetoken:(NSString *)writetoken secretkey:(NSString *)secretkey;

/**
 使用加密串配置账号

 @param configString 加密串
 @param error 解密、配置过程的错误
 @return 新的 PLVVodSettings 静态对象
 */
+ (instancetype)settingsWithConfigString:(NSString *)configString error:(NSError **)error ;

/**
 使用加密串、加密秘钥、加密向量配置账号

 @param configString 加密串
 @param key 加密秘钥
 @param iv 加密向量
 @param error 解密、配置过程的错误
 @return 新的 PLVVodSettings 静态对象
 */
+ (instancetype)settingsWithConfigString:(NSString *)configString key:(NSString *)key iv:(NSString *)iv error:(NSError **)error;

/**
 
 通过appid，secretkey，总帐号userid 配置子帐号
 
 @param appId       子帐号appid
 @param secretKey   子帐号加密key
 @param userId      总帐号userid
 
 */
+ (instancetype)settingsWithAppId:(NSString *)appId secretKey:(NSString *)secretKey userId:(NSString *)userId;


#pragma mark -- 多账号相关 --
/// 多账号就是在特殊场景下，可以同时播放和下载来自不同账号的视频。

/// 通过 userid、readtoken、writetoken、secretkey 添加多账号，请先使用主账号初始化SDK后再调用
/// @param userid userid
/// @param readtoken readtoken
/// @param writetoken writetoken
/// @param secretkey secretkey
/// @param error 解密、配置过程的错误
+ (instancetype)addAccountWithUserid:(NSString *)userid readtoken:(NSString *)readtoken writetoken:(NSString *)writetoken secretkey:(NSString *)secretkey error:(NSError **)error;

/// 使用加密串添加账号，请先使用主账号初始化SDK后再调用
/// @param configString 加密串
/// @param error 解密、配置过程的错误
+ (instancetype)addAccountWithConfigString:(NSString *)configString error:(NSError **)error;

/// 使用加密串、加密秘钥、加密向量添加账号，请先使用主账号初始化SDK后再调用
/// @param configString 加密串
/// @param key 加密秘钥
/// @param iv 加密向量
/// @param error 解密、配置过程的错误
+ (instancetype)addAccountWithConfigString:(NSString *)configString key:(NSString *)key iv:(NSString *)iv error:(NSError **)error;

/// 移除多账号中的账号信息（即：UserId、ReadToken、SecretKey）
/// @param userId 多账号的userid
+ (void)removeAccountWithUserId:(NSString *)userId;

/// 在多账号中查找vid归属账号的userid
/// @param vid 视频id
/// @return userid，为nil则表示该账号暂未添加
+ (NSString *)findUserIdWithVid:(NSString *)vid;

/// 在多账号中查找vid归属账号的readtoken
/// @param vid 视频id
/// @return readtoken，为nil则表示该账号暂未添加
+ (NSString *)findReadTokenWithVid:(NSString *)vid;

/// 在多账号中查找vid归属账号的secretkey
/// @param vid 视频id
/// @return secretkey，为nil则表示该账号暂未添加
+ (NSString *)findSecretKeyWithVid:(NSString *)vid;


@end
