//
//  PLVVodSingleDownloader.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/15.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodSingleDownloader.h"
#import "PLVVodUtil.h"
#import "PLVVodDownloadManager.h"
#import "PLVVodNetworking.h"

//单文件下载器，不支持后台下载
@interface PLVVodSingleDownloader ()

/// 输出流
@property (nonatomic, strong) NSOutputStream *outputStream;
/// 文件总大小
@property (nonatomic, assign) NSInteger totalSize;
/// 已下载的文件大小
@property (nonatomic, assign) NSInteger currentSize;
/// 下载任务
@property (nonatomic, strong) NSURLSessionTask *downloadTask;

/// 临时文件扩展名
@property (nonatomic, copy) NSString *tempPathExtension;
/// 要下载的文件名（包含扩展名）
@property (nonatomic, copy) NSString *fileName;

@end

@implementation PLVVodSingleDownloader

+ (instancetype)downloaderWithVideo:(PLVVodVideo *)video quality:(PLVVodQuality)quality {
	PLVVodQuality processedQuality = [PLVVodUtil preferredIndexWithArray:video.plainVideos index:quality-1] + 1;
	PLVVodSingleDownloader *downloader = [[self alloc] init];
	downloader.downloadInfo = [[PLVVodDownloadInfo alloc] initWithVideo:video quality:processedQuality];
	[downloader commonInit];
	return downloader;
}

- (void)commonInit {
	self.tempPathExtension = @"bin";
	self.downloadDir = [PLVVodDownloadManager sharedManager].downloadDir;
}

#pragma mark - property

- (NSURLSessionTask *)downloadTask {
	if (!_downloadTask) {
		NSURL *URL = [NSURL URLWithString:self.video.plainVideos[self.quality - 1]];
		self.fileName = URL.lastPathComponent;
		NSString *destinationPath = [self pathForURL:URL];
		if ([[NSFileManager defaultManager] fileExistsAtPath:destinationPath]) {
			self.state = PLVVodDownloadStateSuccess;
			PLVVodLogWarn(@"%@ 已下载", self.vid);
			return nil;
		}
		
		// 创建任务，强制前台
		_downloadTask = [self.session dataTaskWithRequest:[PLVVodNetworking risingRequestWithURL:URL localPath:[self tempPath] currentSize:&_currentSize]];
	}
	return _downloadTask;
}

#pragma mark - private

- (NSString *)tempPath {
	return [[self.downloadDir stringByAppendingPathComponent:self.rootName] stringByAppendingPathExtension:self.tempPathExtension];
}

#pragma mark - rewrite

- (void)startDownload {
	[super startDownload];
	__weak typeof(self) weakSelf = self;
	[self prepareWithCompletion:^{
		[weakSelf.downloadTask resume];
	}];
}

- (void)prepareWithCompletion:(void (^)(void))completion {
	self.state = PLVVodDownloadStatePreparing;
	// 创建下载任务，并装载数组
	NSURLSessionTask *downloadTask = self.downloadTask;
	if (!downloadTask) {
		return;
	}
	self.downloadTasks = @[downloadTask];
	
	self.state = PLVVodDownloadStateReady;
	self.prepared = YES;
	if (completion) completion();
}

- (void)stopDownload {
	if (self.state == PLVVodDownloadStateReady) { // 任务只就绪未开始
		PLVVodLogWarn(@"队列未开始下载");
		return;
	}
	[super stopDownload];
	[self.downloadTask cancel];
	self.downloadTask = nil;
	self.state = PLVVodDownloadStateStopped;
}

#pragma mark - URLSessionDataDelegate

/// 接收到服务器响应
- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveResponse:(NSURLResponse *)response completionHandler:(void (^)(NSURLSessionResponseDisposition disposition))completionHandler {
	// 得到文件总大小
	self.totalSize = self.currentSize + (NSInteger)response.expectedContentLength;
	
	// 拼接文件路径
	NSString *filePath = [self tempPath];
	
	// 创建并打开输出流
	self.outputStream = [[NSOutputStream alloc] initToFileAtPath:filePath append:YES];
	[self.outputStream open];
	
	// 接受服务器响应
	completionHandler(NSURLSessionResponseAllow);
	
	self.state = PLVVodDownloadStateRunning;
}

/// 接收到服务器返回的数据
- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveData:(NSData *)data {
	[self.outputStream write:data.bytes maxLength:data.length];
	
	// 累加当前已下载的文件大小
	self.currentSize += data.length;
	
	double progress = 1.0 * self.currentSize / self.totalSize;
	self.downloadInfo.progress = progress;
	
	// 累计下载长度
	self.bytesAccumulator += data.length;
}

/// 下载完成
- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(NSError *)error {
	// 关闭输出流
	[self.outputStream close];
	self.outputStream = nil;
	
	if (error.code == -999) { // 任务被取消
		return;
	}
	if (error) {
		PLVVodLogError(@"[download] -- 下载错误，%@", error.localizedDescription);
		NSError *plvError = PLVVodErrorMakeWithError(download_error, error, nil);
		[self reportError:plvError downloader:self];
		return;
	}
    
	NSString *filePath = [self.downloadDir stringByAppendingPathComponent:self.fileName];
	NSString *location = [self tempPath];
	[PLVVodUtil movePath:location toPath:filePath error:&error];
	if (error) {
		PLVVodLogError(@"[download] -- 文件移动错误");
		[self reportError:error downloader:self];
	}
	self.state = PLVVodDownloadStateSuccess;
}
@end
