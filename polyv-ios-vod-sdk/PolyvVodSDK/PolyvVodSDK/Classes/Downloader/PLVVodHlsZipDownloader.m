//
//  PLVVodHlsZipDownload.m
//  des: ts zip download
//
//  Created by mac on 2018/8/6.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import "PLVVodHlsZipDownloader.h"
#import "PLVVodHlsHelper.h"
#import "PLVVodUtil.h"
#import "PLVVodNetworking.h"
#import "PLVVodLocalVideo.h"
#import "PLVVodDownloadManager.h"
#import "ZipArchive.h"
#import "PLVVodAttachMgr.h"

@interface PLVVodHlsZipDownloader ()<NSURLSessionDownloadDelegate>

/// M3U8文件内容
@property (nonatomic, copy) NSString *m3u8Content;

/// 下载后未处理的 key data
@property (nonatomic, strong) NSData *tokenData;
@property (nonatomic, strong) NSData *keyData;

/// ts打包下载任务
@property (nonatomic, strong) NSURLSessionDownloadTask *tsPackageDownloadTask;

/// resumeData pathExtension
@property (nonatomic, copy) NSString *resumePathExtension;

@property (nonatomic, strong) NSData *resumeData;

@property (nonatomic, copy) void (^requestM3u8IfNeed)(void (^completion)(void));
@property (nonatomic, copy) void (^requestKeyIfNeed)(void (^completion)(void));

@property (nonatomic, copy) NSString *constKey;
@property (nonatomic, assign) NSInteger hlsPrivateVersion;    //!< 私有加密版本

@end

@implementation PLVVodHlsZipDownloader

+ (instancetype)downloaderWithVideo:(PLVVodVideo *)video quality:(PLVVodQuality)quality {
    PLVVodQuality processedQuality = [PLVVodUtil preferredIndexWithArray:video.tsPackages index:quality-1] + 1;
    PLVVodHlsZipDownloader *downloader = [[self alloc] init];
    downloader.constKey = video.constKey;
    downloader.hlsPrivateVersion = video.hlsPrivateVersion;
    downloader.downloadInfo = [[PLVVodDownloadInfo alloc] initWithVideo:video quality:processedQuality];
    [downloader commonInit];
    return downloader;
}

- (void)commonInit {
    self.resumePathExtension = @"bin";
    self.downloadProgress.kind = NSProgressKindFile;
    [self.downloadProgress setUserInfoObject:self.vid forKey:@"vid"];
}

#pragma mark - property

- (NSData *)resumeData {
    if (!_resumeData) {
        _resumeData = [NSData dataWithContentsOfFile:[self resumePath]];
        if (!_resumeData.length) _resumeData = nil;
    }
    return _resumeData;
}

#pragma mark - download

- (void)prepareWithCompletion:(void (^)(void))completion {
    self.state = PLVVodDownloadStatePreparing;
    
    if (!self.prepared) {
        // 若本地已存在该视频则不进行下载
        NSString *downloadDir = [PLVVodDownloadManager sharedManager].downloadDir;
        if (!downloadDir.length) {
            PLVVodLogError(@"[download] -- 下载目录为空");
            NSError *plvError = PLVVodErrorMake(download_dir_not_found, ^(NSMutableDictionary *userInfo) {
                userInfo[PLVVodErrorSelectorKey] = [NSString stringWithUTF8String:__FUNCTION__];
            });
            [self reportError:plvError downloader:self];
            return;
        }
        
        if ( 0 < [PLVVodDownloadManager videoExist:self.vid]){
            self.state = PLVVodDownloadStateSuccess;
            return;
        }
        // end
       
        __weak typeof(self) weakSelf = self;
        // 下载 m3u8
        self.requestM3u8IfNeed = ^(void (^completion)(void)) {
            if (weakSelf.m3u8Content.length) {
                if (completion) completion();
                return;
            }
            NSString *m3u8Url = weakSelf.video.hlsVideos[weakSelf.quality - 1];
            NSString *appId = [PLVVodSettings sharedSettings].appid;
            NSString *did = weakSelf.downloadInfo.did;
            if (![PLVVodUtil isNilString:did]){
                NSDictionary *params = @{PLVDownlaodDid:did};
                m3u8Url = [PLVVodUtil url:m3u8Url appendParams:params];
            }
            
            if (![PLVVodUtil isNilString:appId]){
                NSDictionary *params = @{PLVDownlaodAppId: appId};
                m3u8Url = [PLVVodUtil url:m3u8Url appendParams:params];
                PLVVodLogDebug(@"[downlaod] -- 子账号下载地址：%@ \n", m3u8Url)
            }
            
            if (weakSelf.tokenData){
                //
                NSString *token = [[NSString alloc] initWithData:weakSelf.tokenData encoding:NSUTF8StringEncoding];
                if (![PLVVodUtil isNilString:token]){
                    NSDictionary *params = @{PLVDownlaodToken: token};
                    m3u8Url = [PLVVodUtil url:m3u8Url appendParams:params];
                    PLVVodLogDebug(@"[downlaod] -- 加密视频下载地址：%@ \n", m3u8Url)
                }
            }
            if (weakSelf.hlsPrivateVersion >= 1) {
                [PLVVodNetworking requestPdxWithUrl:m3u8Url params:nil completion:^(NSDictionary *pdxDictionary, NSError *error) {
                    NSString *bodyString = pdxDictionary[@"body"];
                    if (error || !bodyString.length) {
                        PLVVodLogError(@"[download] -- 加密视频，下载m3u8请求错误，%@", error);
                        NSError *plvError = PLVVodErrorMakeWithError(m3u8_fetch_error, error, nil);
                        [weakSelf reportError:plvError downloader:weakSelf];
                        return;
                    }
                    weakSelf.m3u8Content = [weakSelf decryptPdxToM3u8:bodyString];
                    if (completion) completion();
                }];
            }else {
                [PLVVodNetworking requestM3u8WithUrl:m3u8Url params:nil completion:^(NSString *content, NSError *error) {
                    if (error || !content.length) {
                        PLVVodLogError(@"[download] -- 加密视频，下载m3u8请求错误，%@", error);
                        NSError *plvError = PLVVodErrorMakeWithError(m3u8_fetch_error, error, nil);
                        [weakSelf reportError:plvError downloader:weakSelf];
                        return;
                    }
                    weakSelf.m3u8Content = content;
                    if (completion) completion();
                }];
            }
        };
        
        //self.requestM3u8IfNeed(nil);
        
        // 下载 key
        self.requestKeyIfNeed = ^(void (^completion)(void)) {
            if (weakSelf.keyData.length) {
                if (completion) completion();
                return;
            }
            [PLVVodNetworking requestKeyRquestTokenWithVid:weakSelf.vid completion:^(NSString *tokenData, NSError *error) {
                if (error) {
                    PLVVodLogError(@"[download] -- 播放令牌请求错误，%@", error);
                    NSError *plvError = PLVVodErrorMakeWithError(token_fetch_error, error, nil);
                    [weakSelf reportError:plvError downloader:weakSelf];
                    return;
                }
                weakSelf.tokenData = [tokenData dataUsingEncoding:NSUTF8StringEncoding];
                [PLVVodNetworking requestKeyWithVid:weakSelf.vid token:tokenData quality:weakSelf.quality completion:^(NSData *keyData, NSError *error) {
                    if (error) {
                        if (error.code == 20102 && [error.localizedDescription containsString:@"404"]) {
                            if (completion) {
                                // 2019-07-22 Lincal
                                // completion不为nil，代表需要请求key，此时404需要报错打印及回调downloadErrorHandler
                                PLVVodLogError(@"播放秘钥请求错误，%@", error);
                                NSError *plvError = PLVVodErrorMakeWithError(key_fetch_error, error, nil);
                                [weakSelf reportError:plvError downloader:weakSelf];
                                return;
                            }
                            // 2019-07-22 Lincal
                            // 404的报错，可能是不需请求Key造成。completion为nil，代表是第一次与m3u8同时的请求，本次请求报错暂不理会，因还无法判断是否需要Key。
                            return;
                        }
                        
                        PLVVodLogError(@"[download] -- 播放秘钥请求错误，%@", error);
                        NSError *plvError = PLVVodErrorMakeWithError(key_fetch_error, error, nil);
                        [weakSelf reportError:plvError downloader:weakSelf];
                        return;
                    }
                    
                    weakSelf.keyData = keyData;
                    if (completion) completion();
                }];
            }];
        };
        
        // self.requestKeyIfNeed(nil);
        // 优先请求key，再请求m3u8
        self.requestKeyIfNeed(^{
            
            // 加密视频，带token请求m3u8
            weakSelf.requestM3u8IfNeed(nil);
        });
        
        // add by libl 添加字幕下载功能 19-04-02
        [PLVVodAttachMgr downloadSubtitlesWithVideo:self.video];
        
        // add by libl 添加问答下载功能 19-08-01
        [PLVVodAttachMgr downloadExamsWithVideo:self.video];
        
        // 设置下载路径
        self.downloadDir = [downloadDir stringByAppendingPathComponent:[self rootName]];
        
        // 只操作一次
        self.prepared = YES;
    }
    
    /* zip 文件损坏，不能走此流程
    // add by libl [如果存在本地zip 文件，则执行解压流程] 2018-09-15 start
    if ([[NSFileManager defaultManager] fileExistsAtPath:[self zipFilePath]]){
        
        PLVVodLogDebug(@"+++++ start unizp file in %@ ++++++", NSStringFromSelector(_cmd));
        [self tsDownloadCompletionWithPath:[self zipFilePath]];
        
        return;
    }
    // add end
    */
    
    // 创建下载任务
    if (self.tsPackageDownloadTask) {
        PLVVodLogWarn(@"下载任务已存在");
        
        // add by libl [取消前一个下载任务，否则存在多个任务同时下载] 2018-09-15 start
        [self.tsPackageDownloadTask cancel];
        self.tsPackageDownloadTask = nil;
        // add end
    }
    NSError *error = nil;
    
    if (self.resumeData && ![self.resumeData isKindOfClass:[NSNull class]]) {
        @synchronized (self.session){
            PLVVodLogDebug(@"[download] -- 开启断点下载");
            self.tsPackageDownloadTask = [self.session downloadTaskWithResumeData:self.resumeData];
        }
        // mod end
    } else {
        // !!!: zip URL
        NSString *tsPackageUrl = self.video.tsPackages[self.quality-1];
        
        // mod by libl [添加同步锁，解决下载过程中覆盖安装，再次下载失败的问题] 2018-09-17 start
        @synchronized (self.session){
            NSURLSessionDownloadTask *tsPackageDownloadTask = [self downloadTaskWithURL:[NSURL URLWithString:tsPackageUrl] error:&error];
            self.tsPackageDownloadTask = tsPackageDownloadTask;
        }
        // mod end
    }
    if (!self.tsPackageDownloadTask) {
        PLVVodLogError(@"[downlaod] -- 创建下载任务失败，%@", error);
        [self reportError:error downloader:self];
    }
    self.downloadTasks = @[self.tsPackageDownloadTask];
    self.state = PLVVodDownloadStateReady;
    
    if (completion) completion();
}

/// 解密pdx为M3u8字符串
/// @param pdxBody pdx的body字段内容
-(NSString *)decryptPdxToM3u8:(NSString *)pdxBody {
    NSString *bodyBase64String = [[pdxBody componentsSeparatedByCharactersInSet:[NSCharacterSet newlineCharacterSet]] componentsJoinedByString:@""];
    NSData *bodyData = [[NSData alloc] initWithBase64EncodedString:bodyBase64String options:0];
    
    NSData *ivData;
    NSString *salt = @"";
    if (self.hlsPrivateVersion == 1) {
        salt = @"NTQ1ZjhmY2QtMzk3OS00NWZhLTkxNjktYzk3NTlhNDNhNTQ4#";
        Byte ivArray[] = {1, 1, 2, 3, 5, 8, 13, 21, 34, 21, 13, 8, 5, 3, 2, 1};
        ivData = [NSData dataWithBytes:ivArray length:sizeof(ivArray)];
    }else if (self.hlsPrivateVersion == 2) {
        salt = @"OWtjN9xcDcc2cwXKxECpRgKw7piD4RwCdfOUlyNHFdSV0gHi=";
        Byte ivArray[] = {13, 22, 8, 12, 7, 6, 13, 1, 50, 11, 12, 8, 5, 16, 4, 1};
        ivData = [NSData dataWithBytes:ivArray length:sizeof(ivArray)];
    }
    NSString *seedConstWithSalt = [NSString stringWithFormat:@"%@%@", salt, self.constKey];
    
    NSString *md5Hex = [[PLVVodUtil md5String:seedConstWithSalt] substringWithRange:NSMakeRange(1, 16)];
    NSData *aesKeyData = [md5Hex dataUsingEncoding:NSUTF8StringEncoding];
    
    NSData *aesDecryptData = [PLVVodUtil AES128Operation:kCCDecrypt key:aesKeyData iv:ivData data:bodyData];
    NSString *aesDecryptString = [[NSString alloc]initWithData:aesDecryptData encoding:NSUTF8StringEncoding];
    
    return aesDecryptString;
}

- (NSURLSessionDownloadTask *)downloadTaskWithRequest:(NSMutableURLRequest *)request error:(NSError *__autoreleasing *)error {
    [PLVVodHlsHelper fixHttpDNSIfNeedAtRquest:request];
    NSURLSessionDownloadTask *downloadTask = [super downloadTaskWithRequest:request error:error];
    return downloadTask;
}

- (void)startDownload {
    [super startDownload];
    __weak typeof(self) weakSelf = self;
    [self prepareWithCompletion:^{
        
        [weakSelf.tsPackageDownloadTask resume];
        weakSelf.state = PLVVodDownloadStateRunning;
    }];
}

- (void)stopDownload {
    if (self.state == PLVVodDownloadStateReady) { // 任务只就绪未开始
        PLVVodLogWarn(@"[download] -- 下载任务未开始,不需停止");
        return;
    }
    [super stopDownload];
    [self.tsPackageDownloadTask cancelByProducingResumeData:^(NSData * _Nullable resumeData) {
        if (!resumeData.length) {
            PLVVodLogWarn(@"[download] -- 停止下载，获取断点数据失败");
            return;
        }
    }];
}

- (void)handleStopTaskWithResumeData:(NSData *)resumeData{
    [self saveResumeData:resumeData];
    
    self.tsPackageDownloadTask = nil;
    self.state = PLVVodDownloadStateStopped;
}

- (void)saveResumeData:(NSData *)resumeData{
    
    if (!resumeData || [resumeData length] <= 0) return;

    NSData *processedResumeData = resumeData;
    if ([UIDevice currentDevice].systemVersion.integerValue == 11) {
        // ios 11 系统bug，需要移除
        // 移除 NSURLSessionResumeByteRange
        NSMutableDictionary *resumeDic = [PLVVodUtil dictionaryWithData:resumeData].mutableCopy;
        [resumeDic removeObjectForKey:@"NSURLSessionResumeByteRange"];
        processedResumeData = [PLVVodUtil dataWithDictionary:resumeDic];
    }
    
    self.resumeData = processedResumeData;
    [self writeResumeData:self.resumeData];
}

- (void)reset {
    _resumeData = nil;
    [self.tsPackageDownloadTask cancel];
    self.tsPackageDownloadTask = nil;
}

- (void)writeResumeData:(NSData *)resumeData {
    NSString *resumePath = [self resumePath];
    [resumeData writeToFile:resumePath atomically:YES];
}

- (NSString *)resumePath {
    return [[self.downloadDir stringByAppendingPathComponent:self.rootName] stringByAppendingPathExtension:self.resumePathExtension];
}

- (NSString *)zipFilePath {
    return [[self.downloadDir stringByAppendingPathComponent:self.rootName] stringByAppendingPathExtension:@"zip"];
}


/// ts 下载完成
- (void)tsDownloadCompletionWithPath:(NSString *)packagePath {
    // 维护变量
    [[NSFileManager defaultManager] removeItemAtPath:[self resumePath] error:nil];
    // 停止计时器
    [self stopSpeedTimer];
    
    PLVVodLogDebug(@"[download] -- 开始解压 :%@",packagePath);
    NSDate *startDate = [NSDate date];
    
    // 解压ts包
    __weak typeof(self) weakSelf = self;
    NSString *destinationPath = packagePath.stringByDeletingLastPathComponent;
    [SSZipArchive unzipFileAtPath:packagePath toDestination:destinationPath overwrite:YES password:nil progressHandler:^(NSString * _Nonnull entry, unz_file_info zipInfo, long entryNumber, long total) {
        
        // add by libl [添加解压进度回调] 19-03-18 start
        if (total > 0 && weakSelf.downloadInfo.unzipProgressDidChangeBlock) {
            weakSelf.downloadInfo.unzipProgress =entryNumber*1.0/total;
        }
        PLVVodLogDebug(@"[download] -- 解压进度 entryNumber %d: total: %d ", (int)entryNumber, (int)total);
        
        // add end
        
    } completionHandler:^(NSString * _Nonnull path, BOOL succeeded, NSError * _Nullable error) {
        if (!succeeded) {
            PLVVodLogError(@"[download] -- 解压错误，%@", error);
            NSError *plvError = PLVVodErrorMakeWithError(unzip_error, error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSFilePathErrorKey] = packagePath;
            });
            [weakSelf reportError:plvError downloader:weakSelf];
            return;
        }
        NSTimeInterval timeCost = [[NSDate date] timeIntervalSinceDate:startDate];
        weakSelf.downloadInfo.unzipProgress = 1.0;
        PLVVodLogDebug(@"[download] -- 解压成功，%@: need time:%f ", self.vid, timeCost);
        
        // 移除包
        [[NSFileManager defaultManager] removeItemAtPath:packagePath error:nil];
        
        // 处理 key 和 m3u8
        weakSelf.requestM3u8IfNeed(^{

            // 2019-07-22 lincal
            // 原因：部分15年老视频的seed字段是1，按原逻辑会下载key，但实际上并非加密视频，继续下载key会404报错。
            // 解决：应根据m3u8中'EXT-X-KEY'字段判断是否有key需下载
            if ([weakSelf.m3u8Content containsString:@"EXT-X-KEY"]) {
                weakSelf.requestKeyIfNeed(^{
                    // 处理 key
                    [weakSelf writeToken]; // TODO: check failed?
                    [weakSelf writeKey];
                    [weakSelf handleM3u8Content];
                });
            }else{
                [weakSelf handleM3u8Content];
            }
        });
    }];
    
    // 解压同步执行？？
    PLVVodLogInfo(@"[download] -- 解压结束");
}

- (void)handleM3u8Content{
    // 处理M3U8
    NSString *m3u8Content;
    if (self.hlsPrivateVersion >= 1) {
        m3u8Content = [PLVVodHlsHelper fixPtsLocallyInM3u8:self.m3u8Content];
    }else {
        m3u8Content = [PLVVodHlsHelper fixTsLocallyInM3u8:self.m3u8Content];
    }
    m3u8Content = [PLVVodHlsHelper fixKeyInM3u8:m3u8Content];
    
    if (m3u8Content) {
        NSString *m3u8Path = [self.rootName stringByAppendingPathExtension:@"m3u8"];
        m3u8Path = [self.downloadDir stringByAppendingPathComponent:m3u8Path];
        NSError *error = nil;
        [m3u8Content writeToFile:m3u8Path atomically:YES encoding:NSUTF8StringEncoding error:&error];
        if (error) {
            PLVVodLogError(@"[download] -- m3u8写入错误，%@", error.localizedDescription);
            NSError *plvError = PLVVodErrorMake(m3u8_write_error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSFilePathErrorKey] = m3u8Path;
            });
            [self reportError:plvError downloader:self];
        } else { // 写入成功
            // 汇报进度，仅增加一个单位进度
            self.downloadProgress.completedUnitCount ++;
            // 更新状态
            self.state = PLVVodDownloadStateSuccess;
        }
    }
}

- (void)writeKey {
    if (!self.keyData) {
        PLVVodLogError(@"[download] 无法获取播放秘钥: %s", __FUNCTION__);
        NSError *plvError = PLVVodErrorWithCode(key_fetch_error);
        [self reportError:plvError downloader:self];
        return;
    }
    NSError *error = nil;
    NSString *keyPath = [self.downloadDir stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.key", self.rootName]];
    if ([self fileExistForURL:[NSURL URLWithString:keyPath]]){
        return;
    }
    
    NSData *keyData = [PLVVodHlsHelper ecryptKey:self.keyData vid:self.vid];
    [keyData writeToFile:keyPath options:NSDataWritingAtomic error:&error];
    if (error) {
        PLVVodLogError(@"[download] -- 播放秘钥写入错误，%@", error.localizedDescription);
        NSError *plvError = PLVVodErrorMakeWithError(key_write_error, error, ^(NSMutableDictionary *userInfo) {
            userInfo[NSFilePathErrorKey] = keyPath;
        });
        [self reportError:plvError downloader:self];
        error = nil;
    }
}

- (void)writeToken {
    if (!self.tokenData) {
        PLVVodLogError(@"[download] 无法获取播放令牌: %s", __FUNCTION__);
        NSError *plvError = PLVVodErrorWithCode(token_fetch_error);
        [self reportError:plvError downloader:self];
        return;
    }
    
    NSError *error = nil;
    NSString *tokenPath = [self.downloadDir stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.token", self.rootName]];
    if ([self fileExistForURL:[NSURL URLWithString:tokenPath]]){
        return;
    }
    
    // NSData *tokenData = [PLVVodHlsHelper ecryptKey:self.tokenData vid:self.downloadInfo.video.vid];
    NSData *tokenData = self.tokenData;
    [tokenData writeToFile:tokenPath options:NSDataWritingAtomic error:&error];
    if (error) {
        PLVVodLogError(@"[download] -- 播放令牌写入失败，%@", error.localizedDescription);
        NSError *plvError = PLVVodErrorMakeWithError(token_write_error, error, ^(NSMutableDictionary *userInfo) {
            userInfo[NSFilePathErrorKey] = tokenPath;
        });
        [self reportError:plvError downloader:self];
        error = nil;
    }
}

#pragma mark - NSURLSessionDownloadDelegate

// 每次获取数据
- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didWriteData:(int64_t)bytesWritten totalBytesWritten:(int64_t)totalBytesWritten totalBytesExpectedToWrite:(int64_t)totalBytesExpectedToWrite {
    self.downloadProgress.totalUnitCount = totalBytesExpectedToWrite+1;
    self.downloadProgress.completedUnitCount = totalBytesWritten;
    
    // 累计下载长度
    self.bytesAccumulator += bytesWritten;
    
    // 用于取消下载任务
    if (!self.tsPackageDownloadTask || self.tsPackageDownloadTask.taskIdentifier != downloadTask.taskIdentifier){
        self.tsPackageDownloadTask = downloadTask;
    }
    
    // add by libl [断网后再联网，会自动开始下载，需要更正下载状态] 2018-09-15 start
    if (self.state != PLVVodDownloadStateRunning){
        self.state = PLVVodDownloadStateRunning;
    }
}

/// 下载成功，此方法内完成文件移动
- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didFinishDownloadingToURL:(NSURL *)location {
    NSError *error;
    NSString *fileName = downloadTask.response.suggestedFilename;
    if (!fileName.length) {
        PLVVodLogError(@"[download] -- 无法获取文件名");
        NSError *plvError = PLVVodErrorMake(filename_not_found, ^(NSMutableDictionary *userInfo) {
            userInfo[NSURLErrorKey] = downloadTask.response.URL;
        });
        [self reportError:plvError downloader:self];
        return;
    }
    NSString *destinationPath = [self.downloadDir stringByAppendingPathComponent:fileName];
    [PLVVodUtil movePath:location.path toPath:destinationPath error:&error];
    if (error) {
        PLVVodLogError(@"[download] -- 移动错误，%@", error);
        [self reportError:error downloader:self];
    } else {
        PLVVodLogDebug(@"[download] -- 移动成功，%@", self.vid);
        // mod by libl [开启新线程解压，否则其他下载中的视频，数据接收回调不执行] 19-04-11 start
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            [self tsDownloadCompletionWithPath:destinationPath];
        });
        // mod end
    }
}

- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didResumeAtOffset:(int64_t)fileOffset expectedTotalBytes:(int64_t)expectedTotalBytes {
    
}

#pragma mark - URLSessionTaskDelegate
/// 任务完成
- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(NSError *)error {
    if (error.code == NSURLErrorCancelled) {
        // 任务主动取消
        NSData *resumeData = error.userInfo[NSURLSessionDownloadTaskResumeData];
        if (resumeData && [resumeData length] > 0) {
            
            [self handleStopTaskWithResumeData:resumeData];
            PLVVodLogDebug(@"[downlaod] -- 任务被取消，保存断点数据");
        }
        else{
            //
            PLVVodLogWarn(@"[download] -- 任务取消，无法获取断点数据: %@", NSStringFromSelector(_cmd));
            self.tsPackageDownloadTask = nil;
            self.state = PLVVodDownloadStateStopped;
        }
        
        return;
    }
    
    if (error.code == NSURLErrorBackgroundSessionWasDisconnected) {
        // 失去后台服务
        // add by libl [需要取消，断网场景下载失败，后网络可用，会自动重启下载] 2018-09-15 start
        NSData *resumeData = error.userInfo[NSURLSessionDownloadTaskResumeData];
        if (resumeData && [resumeData length] > 0) {
            [self saveResumeData:resumeData];
        }
        else{
            PLVVodLogWarn(@"[download] -- 无法获取断点数据: %@ errorCode:%d", NSStringFromSelector(_cmd), (int)error.code);
        }
        
        NSError *plvError = PLVVodErrorMakeWithError(download_error, error, nil);
        [self reportError:plvError downloader:self];
        
        return;
    }
    
    /* 暂时屏蔽该bug 修复，下个版本严格测试后上线
     
    if (error.code == 2){
        // 取消所有任务,因为出现这种场景，所有都不能下载
        [self.session getTasksWithCompletionHandler:^(NSArray<NSURLSessionDataTask *> * _Nonnull dataTasks, NSArray<NSURLSessionUploadTask *> * _Nonnull uploadTasks, NSArray<NSURLSessionDownloadTask *> * _Nonnull downloadTasks) {
            //
            [downloadTasks enumerateObjectsUsingBlock:^(NSURLSessionDownloadTask * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                [obj cancel];
                
                PLVVodLogDebug(@"++++++++++ need cancel : %@ url: %@++++++++++", obj, obj.originalRequest.URL);
            }];
            
            NSError *plvError = PLVVodErrorMakeWithError(download_error, error, nil);
            [self reportError:plvError downloader:self];
        }];
        
        return;
    }
    */
    
    // add by libl [删除断点数据文件，否则无法再次下载] 2018-11-06
    if (error.code == NSURLErrorCannotWriteToFile || error.code == 2){
        // 删除
        NSString *resumeFile = [self resumePath];
        if ([[NSFileManager defaultManager] fileExistsAtPath:resumeFile]){
            [[NSFileManager defaultManager] removeItemAtPath:resumeFile error:nil];
            PLVVodLogDebug(@"[download] -- 下载失败，删除断点数据 %@", resumeFile);
        }

        [self reset];

        NSError *plvError = PLVVodErrorMakeWithError(download_error, error, nil);
        [self reportError:plvError downloader:self];
        return;
    }
    // add end
    
    if (error) {
        PLVVodLogError(@"[download] -- 下载错误，%@", error);
        // add by libl [需要保存断点数据，注意resumeData 失效场景] 2018-09-15 start
        NSData *resumeData = error.userInfo[NSURLSessionDownloadTaskResumeData];
        if (resumeData && [resumeData length] > 0) {
            [self saveResumeData:resumeData];
            PLVVodLogDebug(@"[download] -- 下载错误,保存断点数据");
        }
        else{
            PLVVodLogWarn(@"[download] -- 下载失败，无法获取断点数据: %@ errorCode: %d", NSStringFromSelector(_cmd), (int)error.code);
        }
        
        NSError *plvError = PLVVodErrorMakeWithError(download_error, error, nil);
        [self reportError:plvError downloader:self];
        return;
    }
}

@end

