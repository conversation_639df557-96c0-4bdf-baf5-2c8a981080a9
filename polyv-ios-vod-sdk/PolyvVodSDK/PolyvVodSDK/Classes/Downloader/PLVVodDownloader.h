//
//  PLVVodDownloader.h
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/12.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PLVVodDownloadInfo.h"
#import "PLVTimer.h"
#import "PLVKVOController.h"
#import "PLVVodConstans.h"

// 子账号,下载地址带上appId
#define PLVDownlaodAppId @"appId"

// 请求cdn资源时的唯一标志
#define PLVDownlaodDid    @"did"

// 下载加密视频带token
#define PLVDownlaodToken    @"token"


@interface PLVVodDownloadInfo ()

/// PLVVodVideo 对象
@property (nonatomic, strong) PLVVodVideo *video;

/// 清晰度
@property (nonatomic, assign) PLVVodQuality quality;

/// 下载状态
@property (nonatomic, assign) PLVVodDownloadState state;

/// 下载速率（单位：byte/s）
@property (nonatomic, assign) double bytesPerSeconds;

/// 下载进度（0-1）
@property (nonatomic, assign) double progress;

/// 解压进度 (0-1)
@property (nonatomic, assign) double unzipProgress;

/// 下载错误
@property (nonatomic, strong) NSError *error;

/// 队列ID
@property (nonatomic, assign) NSInteger downloadId;

/// 文件类型
@property (nonatomic, assign) PLVDownloadFileType fileType;

/// 请求cdn资源时的唯一标志
@property (nonatomic, copy) NSString *did;


- (instancetype)initWithVideo:(PLVVodVideo *)video quality:(PLVVodQuality)quality;
- (instancetype)initWithVideo:(PLVVodVideo *)video fileType:(PLVDownloadFileType)fileType quality:(PLVVodQuality)quality;

@end

@interface PLVVodDownloader : NSOperation

/// 下载目录
@property (nonatomic, copy) NSString *downloadDir;

/// 是否就绪
@property (nonatomic, assign) BOOL prepared;

/// 下载会话
@property (nonatomic, strong) NSURLSession *session;

/// 产生的下载任务
@property (nonatomic, strong) NSArray<NSURLSessionTask *> *downloadTasks;

/***** add by libl [后台下载优化] 2018-07-02 start *********/
/// m3u8 文件下载
@property (nonatomic, strong) NSURLSessionTask *m3u8DownloadTask;
/// key 文件下载
@property (nonatomic, strong) NSURLSessionTask *keyDownloadTask;
/***** add by libl end *********/


/// 下载模型
@property (nonatomic, strong) PLVVodDownloadInfo *downloadInfo;

/// 下载进度
@property (nonatomic, strong) NSProgress *downloadProgress;

/// 用于刷下载速度的定时器
@property (nonatomic, strong) PLVTimer *speedTimer;
/// 下载字节数累计器
@property (nonatomic, assign) int64_t bytesAccumulator;

// 便利属性
@property (nonatomic, strong, readonly) PLVVodVideo *video;
@property (nonatomic, copy, readonly) NSString *vid;
@property (nonatomic, assign, readonly) PLVVodQuality quality;
@property (nonatomic, assign) PLVVodDownloadState state;

/// 下载错误回调
@property (nonatomic, copy) void (^downloadErrorHandler)(PLVVodDownloader *downloader, NSError *error);

// 监听属性
@property (nonatomic, strong) PLVKVOController *KVOController;
@property (nonatomic, strong) PLVKVOController *KVOControllerNonRetaining;


/// 初始化，该基类已完成参数的校验，子类实现可跳过这些校验
+ (instancetype)downloaderWithVideo:(PLVVodVideo *)video quality:(PLVVodQuality)quality createTaskMode:(PLVTaskCreateMode )taskMode;

/// 初始化，接口定义，必须在子类实现
+ (instancetype)downloaderWithVideo:(PLVVodVideo *)video quality:(PLVVodQuality)quality;

/// 初始化，创建音频下载器
+ (instancetype)audioDownloaderWithVideo:(PLVVodVideo *)video;

/// 初始化，创建PPT下载器
+ (instancetype)pptDownloaderWithVideo:(PLVVodVideo *)video;

/// 开始下载
- (void)prepareWithCompletion:(void (^)(void))completion;
- (void)startDownload;

/// 停止下载
- (void)stopDownload;

/// 停止下载速度计算定时器
- (void)stopSpeedTimer;

/// 创建下载速度计算定时器
- (PLVTimer *)createSpeedTimer;

/// 统一汇报错误
- (void)reportError:(NSError *)error downloader:(PLVVodDownloader *)downloader;

#pragma mark - 工具方法

- (NSString *)rootName;

/// 对应的下载路径
- (NSString *)pathForURL:(NSURL *)URL;

/// 指定URL对应TS是否已下载
- (BOOL)fileExistForURL:(NSURL *)URL;

/// 创建下载任务
- (NSURLSessionDownloadTask *)downloadTaskWithURL:(NSURL *)URL error:(NSError **)error;
- (NSURLSessionDownloadTask *)downloadTaskWithRequest:(NSMutableURLRequest *)request error:(NSError **)error;

#pragma mark NSURLSessionDownloadDelegate
- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didResumeAtOffset:(int64_t)fileOffset expectedTotalBytes:(int64_t)expectedTotalBytes;

- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didWriteData:(int64_t)bytesWritten totalBytesWritten:(int64_t)totalBytesWritten totalBytesExpectedToWrite:(int64_t)totalBytesExpectedToWrite;

- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didFinishDownloadingToURL:(NSURL *)location;

#pragma mark NSURLSessionDataDelegate
- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveResponse:(NSURLResponse *)response completionHandler:(void (^)(NSURLSessionResponseDisposition disposition))completionHandler;

- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveData:(NSData *)data;

#pragma mark NSURLSessionTaskDelegate
- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(NSError *)error;

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didReceiveChallenge:(NSURLAuthenticationChallenge *)challenge completionHandler:(void (^)(NSURLSessionAuthChallengeDisposition, NSURLCredential *))completionHandler ;

@end
