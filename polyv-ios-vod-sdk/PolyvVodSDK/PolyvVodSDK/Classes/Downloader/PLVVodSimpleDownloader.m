//
//  PLVVodSimpleDownloader.m
//  _PolyvVodSDK
//
//  Created by mac on 2018/8/13.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import "PLVVodSimpleDownloader.h"

#import "PLVVodUtil.h"
#import "PLVVodDownloadManager.h"
#import "PLVVodNetworking.h"
#import "PLVVodAttachMgr.h"

// 单文件下载器，支持后台下载
@interface PLVVodSimpleDownloader ()

/// 下载任务
@property (nonatomic, strong) NSURLSessionDownloadTask *downloadTask;

/// 要下载的文件名（包含扩展名）
@property (nonatomic, copy) NSString *fileName;

@property (nonatomic, copy) NSString *resumePathExtension;
@property (nonatomic, strong) NSData *resumeData;

@end

@implementation PLVVodSimpleDownloader

+ (instancetype)downloaderWithVideo:(PLVVodVideo *)video quality:(PLVVodQuality)quality {
    PLVVodQuality processedQuality = [PLVVodUtil preferredIndexWithArray:video.plainVideos index:quality-1] + 1;
    PLVVodSimpleDownloader *downloader = [[self alloc] init];
    downloader.downloadInfo = [[PLVVodDownloadInfo alloc] initWithVideo:video quality:processedQuality];
    [downloader commonInit];
    return downloader;
}

- (void)commonInit {
    // mod by libl [bin已经作为前台下载临时文件的后缀，为兼容以前版本，后台下载的断点文件后缀不能为bin] 2018-08-18
    // 后缀中也不能包含 m / v 等字符，取缓存文件时m / v 会用到。
//    self.resumePathExtension = @"bin";
    self.resumePathExtension = @"res";
    // mod end
    self.downloadDir = [PLVVodDownloadManager sharedManager].downloadDir;
}

#pragma mark - property
- (NSData *)resumeData {
    if (!_resumeData) {
        _resumeData = [NSData dataWithContentsOfFile:[self resumePath]];
        if (!_resumeData.length) _resumeData = nil;
    }
    return _resumeData;
}

- (NSURLSessionTask *)downloadTask {
    if (!_downloadTask) {
        NSURL *URL = [NSURL URLWithString:self.video.plainVideos[self.quality - 1]];
        self.fileName = URL.lastPathComponent;
        
        // 检查本地是否已经下载
        NSString *destinationPath = [self pathForURL:URL];
        
        if ([[NSFileManager defaultManager] fileExistsAtPath:destinationPath]) {
            self.state = PLVVodDownloadStateSuccess;
            PLVVodLogWarn(@"[download] -- %@ 已下载", self.vid);
            return nil;
        }
        
        // 创建下载任务
        NSError *error = nil;
        NSData *resumeData = self.resumeData;
        
        if (resumeData && ![resumeData isKindOfClass:[NSNull class]]) {
            @synchronized (self.session){
                _downloadTask = [self.session downloadTaskWithResumeData:resumeData];
            }
        } else {
            // !!!: zip URL
            NSString *videoUrl = self.video.plainVideos[self.quality-1];
            NSString *appId = [PLVVodSettings sharedSettings].appid;
            NSString *did = self.downloadInfo.did;
            if (![PLVVodUtil isNilString:did]){
                NSDictionary *params = @{PLVDownlaodDid: self.downloadInfo.did};
                videoUrl = [PLVVodUtil url:videoUrl appendParams:params];
            }
            
            if (![PLVVodUtil isNilString:appId]){
                NSDictionary *params = @{PLVDownlaodAppId: appId};
                videoUrl = [PLVVodUtil url:videoUrl appendParams:params];
                PLVVodLogDebug(@"[downlaod] -- 子账号下载地址：%@ \n", videoUrl)
            }
            
            // mod by libl [添加同步锁，解决下载过程中覆盖安装，再次下载失败的问题] 2018-09-17 start
            @synchronized (self.session){
                NSURLSessionDownloadTask *simpleDownloadTast = [self downloadTaskWithURL:[NSURL URLWithString:videoUrl] error:&error];
                _downloadTask = simpleDownloadTast;
            }
            // mod end
        }
        
        if (!_downloadTask) {
            PLVVodLogError(@"[downlown] -- 不能创建下载任务，%@", error);
            [self reportError:error downloader:self];
            
            return nil;
        }
        
        self.downloadTasks = @[_downloadTask];
    }
    
    return _downloadTask;
}

#pragma mark - private

#pragma mark - rewrite

- (void)startDownload {
    [super startDownload];
    __weak typeof(self) weakSelf = self;
    [self prepareWithCompletion:^{
        
        [weakSelf.downloadTask resume];
        self.state = PLVVodDownloadStateRunning;
    }];
}

- (void)prepareWithCompletion:(void (^)(void))completion {
    self.state = PLVVodDownloadStatePreparing;
    
    // 创建下载任务，并装载数组
    NSURLSessionTask *downloadTask = self.downloadTask;
    if (!downloadTask) {
        return;
    }
    self.downloadTasks = @[downloadTask];
    
    self.state = PLVVodDownloadStateReady;
    self.prepared = YES;
    
    if (completion) completion();
    
    // add by libl 添加字幕下载功能 19-04-02
    [PLVVodAttachMgr downloadSubtitlesWithVideo:self.video];
   
    // add by libl 添加问答下载功能 19-08-01
    [PLVVodAttachMgr downloadExamsWithVideo:self.video];
}

- (void)stopDownload {
    if (self.state == PLVVodDownloadStateReady) { // 任务只就绪未开始
        PLVVodLogWarn(@"下载任务未开始");
        return;
    }
    [super stopDownload];
    __weak typeof(self) weakSelf = self;
    [self.downloadTask cancelByProducingResumeData:^(NSData * _Nullable resumeData) {
        if (!resumeData.length) {
            PLVVodLogWarn(@"[download mp4] 停止下载，无法获取断点数据");
        }
    }];
    
    weakSelf.downloadTask = nil;
}

- (void)handleStopTaskWithResumeData:(NSData *)resumeData{
    [self saveResumeData:resumeData];
    
    self.downloadTask = nil;
    self.state = PLVVodDownloadStateStopped;
}

- (void)saveResumeData:(NSData *)resumeData{
    
    if (!resumeData || [resumeData length] <= 0) return;
    
    NSData *processedResumeData = resumeData;
    if ([UIDevice currentDevice].systemVersion.integerValue == 11) {
        // ios 11 系统bug，需要移除
        // 移除 NSURLSessionResumeByteRange
        NSMutableDictionary *resumeDic = [PLVVodUtil dictionaryWithData:resumeData].mutableCopy;
        [resumeDic removeObjectForKey:@"NSURLSessionResumeByteRange"];
        processedResumeData = [PLVVodUtil dataWithDictionary:resumeDic];
    }
    
    self.resumeData = processedResumeData;
    [self writeResumeData:self.resumeData];
}


- (void)reset {
    self.resumeData = nil;
    [self.downloadTask cancel];
    self.downloadTask = nil;
}

- (void)writeResumeData:(NSData *)resumeData {
    NSString *resumePath = [self resumePath];
    [resumeData writeToFile:resumePath atomically:YES];
}

- (NSString *)resumePath {
    return [[self.downloadDir stringByAppendingPathComponent:self.rootName] stringByAppendingPathExtension:self.resumePathExtension];
}

#pragma mark - URLSessionDataDelegate

// 每次获取数据
- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didWriteData:(int64_t)bytesWritten totalBytesWritten:(int64_t)totalBytesWritten totalBytesExpectedToWrite:(int64_t)totalBytesExpectedToWrite {
    self.downloadProgress.totalUnitCount = totalBytesExpectedToWrite;
    self.downloadProgress.completedUnitCount = totalBytesWritten;
    
    // 累计下载长度
    self.bytesAccumulator += bytesWritten;
    
    // add by libl [断网后再联网，会自动开始下载，需要更正下载状态] 2018-09-15 start
    if (self.state != PLVVodDownloadStateRunning){
        self.state = PLVVodDownloadStateRunning;
    }
    
    // 用于取消下载任务
    if (!self.downloadTask || self.downloadTask.taskIdentifier != downloadTask.taskIdentifier){
        self.downloadTask = downloadTask;
    }
    // end
}

/// 下载成功，此方法内完成文件移动
- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didFinishDownloadingToURL:(NSURL *)location {
    NSError *error;
    NSString *destinationPath = [self.downloadDir stringByAppendingPathComponent:self.fileName];
    PLVVodLogDebug(@"[download] -- 下载完成，目标路径: %@", destinationPath);
    
    [PLVVodUtil movePath:location.path toPath:destinationPath error:&error];
    if (error) {
        PLVVodLogError(@"[download] -- 移动错误，%@", error);
        [self reportError:error downloader:self];
    } else {
        PLVVodLogDebug(@"[download] -- 移动成功，%@", self.vid);
        [self simpleDownloadComplete];
    }
}

/// ts 下载完成
- (void)simpleDownloadComplete {
    // 维护变量,删除断点数据
    [[NSFileManager defaultManager] removeItemAtPath:[self resumePath] error:nil];
    // 停止计时器
    [self stopSpeedTimer];
    
    // 设置下载成功状态
    self.state = PLVVodDownloadStateSuccess;
}

- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didResumeAtOffset:(int64_t)fileOffset expectedTotalBytes:(int64_t)expectedTotalBytes {
    
}

#pragma mark - URLSessionTaskDelegate
/// 任务完成
- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(NSError *)error {
    if (error.code == NSURLErrorCancelled) {
        // 任务主动取消
        NSData *resumeData = error.userInfo[NSURLSessionDownloadTaskResumeData];
        if (resumeData && [resumeData length] > 0) {
            
            [self handleStopTaskWithResumeData:resumeData];
            PLVVodLogDebug(@"[download mp4] -- 任务被取消,保存断点数据");
        }
        else{
            //
            PLVVodLogWarn(@"[download mp4] -- 任务取消，无法获取断点数据");
            self.downloadTask = nil;
            self.state = PLVVodDownloadStateStopped;
        }
        return;
    }
    
    if (error.code == NSURLErrorBackgroundSessionWasDisconnected) {
        // 失去后台服务
        NSData *resumeData = error.userInfo[NSURLSessionDownloadTaskResumeData];
        if (resumeData && [resumeData length] > 0) {
            [self saveResumeData:resumeData];
        }
        else{
            //
            PLVVodLogWarn(@"[download] -- 失去后台服务，无法获取断点数据");
        }
        NSError *plvError = PLVVodErrorMakeWithError(download_error, error, nil);
        [self reportError:plvError downloader:self];
        return;
    }
    
    // add by libl [删除断点数据文件，否则无法再次下载] 2018-11-06
    if (error.code == NSURLErrorCannotWriteToFile || error.code == 2){
        // 删除
        NSString *resumeFile = [self resumePath];
        if ([[NSFileManager defaultManager] fileExistsAtPath:resumeFile]){
            [[NSFileManager defaultManager] removeItemAtPath:resumeFile error:nil];
            PLVVodLogDebug(@"[downlaod] -- 下载失败，并删除断点数据");
        }

        [self reset];
        
        NSError *plvError = PLVVodErrorMakeWithError(download_error, error, nil);
        [self reportError:plvError downloader:self];
        return;
    }
    // add end
    
    if (error) {
        PLVVodLogError(@"[download] -- 下载错误，%@", error);
        NSData *resumeData = error.userInfo[NSURLSessionDownloadTaskResumeData];
        if (resumeData && [resumeData length] > 0) {
            [self saveResumeData:resumeData];
        }
        else{
            PLVVodLogWarn(@"[download] -- 下载失败，无法获取断点数据");
        }
        
        NSError *plvError = PLVVodErrorMakeWithError(download_error, error, nil);
        [self reportError:plvError downloader:self];
        return;
    }
}

@end
