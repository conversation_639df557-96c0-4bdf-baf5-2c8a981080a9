//
//  PLVVodPPTMgr.h
//  _PolyvVodSDK
//
//  Created by mac on 2019/8/16.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PLVVodDownloadInfo.h"
#import "PLVVodVideo.h"

NS_ASSUME_NONNULL_BEGIN

@interface PLVVodPPTMgr : NSObject

+ (instancetype)shareManager;

/**
 
 下载PPT 文件
 
 @param video PLVVodVideo 视频对象
 
 */
- (void)downloadPPTWithVideo:(PLVVodVideo *)video completion:(void(^)(PLVVodDownloadInfo *info))completion;

/**
 删除PPT 本地文件
 
 @param vid 视频id
 */
+ (void)removePPTWithVid:(NSString *)vid error:(NSError **)error;


@end

NS_ASSUME_NONNULL_END
