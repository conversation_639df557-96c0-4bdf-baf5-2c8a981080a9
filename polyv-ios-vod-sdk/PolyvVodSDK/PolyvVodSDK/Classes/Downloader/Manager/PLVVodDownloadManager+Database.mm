//
//  PLVVodDownloadManager+Database.m
//  PolyvVodSDK
//
//  Created by mac on 2018/10/13.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import "PLVVodDownloadManager+Database.h"
#import "PLVVodDownloadInfoManager.h"
#import "PLVExtendDownloadInfo.h"

@implementation PLVVodDownloadManager (Database)

+ (NSString *)sdkDBPath{
    return [[PLVVodDownloadInfoManager sharedManager] dbPath];
}

+ (BOOL)createExtendTableWithClass:(Class)classObj{
    PLVFDatabase *database = [[PLVVodDownloadInfoManager sharedManager] shareDB];
    NSString *tableName = NSStringFromClass(classObj);
    return [database createTable:tableName withClass:classObj];
}

+ (BOOL)insertOrUpdateWithExtendInfo:(NSObject *)extendInfo{
    PLVFDatabase *database = [[PLVVodDownloadInfoManager sharedManager] shareDB];

    Class saveInfo = [extendInfo class];
    NSString *tableName = NSStringFromClass(saveInfo);

    [database beginTransaction];

    BOOL result = [database insertOrReplaceFromClass:saveInfo object:extendInfo table:tableName where:nil];
//    BOOL result = [database insertOrReplaceObject:extendInfo onProperties:[saveInfo allProperties] intoTable:tableName];

    if (result) {
        [database commitOrRollbackTransaction];
    } else {
        [database rollbackTransaction];
    }
    return result;
}

+ (NSArray /* <NSObject *> */ *)getExtendInfoWithClass:(Class)classObj condition:(NSString *)condition{
    PLVFDatabase *database = [[PLVVodDownloadInfoManager sharedManager] shareDB];
    NSString *tableName = NSStringFromClass(classObj);
    NSArray *array = [database objectsFromClass:classObj table:tableName where:condition];

    return array;
}

+ (NSArray /* <NSObject *> */ *)getAllExtendInfoWithClass:(Class)classObj{
    PLVFDatabase *database = [[PLVVodDownloadInfoManager sharedManager] shareDB];
    NSString *tableName = NSStringFromClass(classObj);
    NSArray *array = [database objectsFromClass:classObj table:tableName where:nil];

    return array;
}

+ (BOOL)deleteExtendInfoWithClass:(Class)classObj condition:(NSString *)condition{
    PLVFDatabase *database = [[PLVVodDownloadInfoManager sharedManager] shareDB];
    NSString *tableName = NSStringFromClass(classObj);

    return [database deleteTable:tableName where:condition];
}

+ (BOOL)deleteAllExtendInfoWithClass:(Class)classObj{
    PLVFDatabase *database = [[PLVVodDownloadInfoManager sharedManager] shareDB];
    NSString *tableName = NSStringFromClass(classObj);

    return [database deleteTable:tableName where:nil];
}

+ (BOOL)updateExtendInfo:(NSObject *)extendInfo condition:(NSString *)condition{
    Class saveInfo = [extendInfo class];
    NSString *tableName = NSStringFromClass(saveInfo);
    
    PLVFDatabase *database = [[PLVVodDownloadInfoManager sharedManager] shareDB];
    return [database insertOrReplaceFromClass:saveInfo object:extendInfo table:tableName where:condition];
}

@end
