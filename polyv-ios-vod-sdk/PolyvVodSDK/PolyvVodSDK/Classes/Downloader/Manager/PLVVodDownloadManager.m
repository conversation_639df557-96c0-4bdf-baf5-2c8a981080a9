//
//  PLVVodDownloadManager.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/12.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodDownloadManager.h"
#import "PLVVodUtil.h"
#import "PLVKVOController.h"
#import "PLVVodDownloader.h"
#import "PLVVodDownloadInfoManager.h"
#import "PLVVodReportManager.h"
#import "PLVVodHlsTsDownloader.h"
#import "PLVVodLocalVideo+Private.h"
#import "PLVFileUtils.h"
#import "PLVVodElogModel.h"
#import "PLVVodAttachMgr.h"
#import "PLVVodPPTMgr.h"
#import "PLVVodSettings.h"

#import "PLVVodReachability.h"

#define SYSTEM_VERSION_LESS_THAN(v) ([[[UIDevice currentDevice] systemVersion] compare:v options:NSNumericSearch] == NSOrderedAscending)

static NSString * const PLVURLSessionManagerLockName = @"net.polyv.networking.session.manager.lock";
static NSString * const PLVBGDownloadSessionId = @"net.polyv.sdk.download.bgsession";

const NSInteger PLVBackgroundSessionMaxConnection = 3;
const NSTimeInterval PLVBackgroundSessionResourceTimeout = 1200*3*3; // 请求超时3小时
const NSTimeInterval PLVBackgroundSessionRequestTimeout = 30;

const NSInteger PLVForegroundSessionMaxConnection = 1;
const NSTimeInterval PLVForegroundSessionResourceTimeout = 1200;
const NSTimeInterval PLVForegroundSessionRequestTimeout = 120;

const NSInteger PLVMinRuningCount = 1;
const NSInteger PLVMaxRuningCount = 3;
const NSInteger PLVAudioMaxRuningCount = 5;

NSString * const kHasMigrateCacheVideoWithSecretKey = @"kHasMigrateCacheVideoWithSecretKey";
NSString * const kHasExecuteSetDownloadDir = @"kHasExecuteSetDownloadDir";

#define SPEED_REFRESH_INTERVAL 0.3

@interface PLVVodDownloadManager ()<NSURLSessionDownloadDelegate>

@property (nonatomic, strong) PLVKVOController *KVOController;
@property (nonatomic, strong) PLVKVOController *KVOControllerNonRetaining;

/// 下载会话
@property (nonatomic, strong) NSURLSession *session;

/// 下载队列
@property (nonatomic, strong) NSMutableArray<PLVVodDownloader *> *downloaderList;
@property (nonatomic, strong) NSMutableDictionary *taskDownloaderDic;
@property (nonatomic, strong) NSMutableArray<PLVVodDownloader *> *runingQueue;

/// 下载信息
@property (nonatomic, strong) NSMutableArray<PLVVodDownloadInfo *> *downloadInfos;

/// 回调队列
@property (nonatomic, strong) NSOperationQueue *sessionCallbackQueue;

@property (nonatomic, copy) NSString *pid;

@property (nonatomic, strong) NSLock *lock;

@property (nonatomic, assign) BOOL isAppBackground; // 后台模式

@property (nonatomic, assign) BOOL isStopDownload;  // 手动停止下载，默认NO

@property (nonatomic, assign) UIBackgroundTaskIdentifier bgTask;

@property (nonatomic, copy) NSString *accountId;    // 多账号下载时，当前帐号ID

@end

@implementation PLVVodDownloadManager

#pragma mark - singleton init

static id _sharedManager = nil;

+ (instancetype)sharedManager {
	static dispatch_once_t onceToken;
	dispatch_once(&onceToken, ^{
		_sharedManager = [[self alloc] init];
		[_sharedManager commonInit];
	});
	return _sharedManager;
}

+ (instancetype)allocWithZone:(struct _NSZone *)zone {
	static dispatch_once_t onceToken;
	dispatch_once(&onceToken, ^{
		if (!_sharedManager) {
			_sharedManager = [super allocWithZone:zone];
		}
	});
	return _sharedManager;
}

- (id)copyWithZone:(NSZone *)zone {
	return _sharedManager;
}

- (void)commonInit {
	// 初始化属性
	_downloadDir = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES).lastObject stringByAppendingPathComponent:@"PolyvVodCache"];
	_lock = [[NSLock alloc] init];
	_lock.name = PLVURLSessionManagerLockName;
	_taskDownloaderDic = [NSMutableDictionary dictionary];
	_allowsCellularAccess = YES;
	_pid = [PLVVodUtil pid];
	if ([UIDevice currentDevice].systemVersion.integerValue >= 8) {
		_enableBackgroundDownload = YES;
	}
    
    _isAppBackground = NO;
    _isStopDownload = NO;
    
	[PLVVodDownloadInfoManager sharedManager];
    [PLVVodReachability sharedReachability];
	[[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkStatusDidChange:) name:kPLVVodReachabilityChangedNotification object:nil];
    
    _maxRuningCount = PLVMinRuningCount; // 默认为1
    _runingQueue = [NSMutableArray arrayWithCapacity:3];
    
    //
    _downloadType = PLVDownloadModeTypeVideo;
    _isMultiAccount = NO;
    
    //
    _taskCreateMode = PLVTaskCreateModeCreateAllResumeOne;
    
    [self addNotifications];
}

- (void)addNotifications{
    // 进程终止通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(applicationWillTerminateInternal)
                                                 name:UIApplicationWillTerminateNotification
                                               object:nil];
    
    // 应用回到前台
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(applicationWillEnterForegroundInternal)
                                                 name:UIApplicationWillEnterForegroundNotification
                                               object:nil];
    
    // 应用回到后台
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(applicationDidEnterBackgroundInternal)
                                                 name:UIApplicationDidEnterBackgroundNotification
                                               object:nil];
}

- (void)removeNotifications{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)networkStatusDidChange:(NSNotification *)notification {
	
    if ([PLVVodReachability sharedReachability].currentReachabilityStatus == PLVVodNotReachable) {
		[self stopDownloadLoop];
		PLVVodLogError(@"网络中断，已暂停下载");
	}
}

#pragma mark - property

- (PLVKVOController *)KVOController {
	if (!_KVOController) {
		_KVOController = [PLVKVOController controllerWithObserver:self];
	}
	return _KVOController;
}
- (PLVKVOController *)KVOControllerNonRetaining {
	if (!_KVOControllerNonRetaining) {
		_KVOControllerNonRetaining = [[PLVKVOController alloc] initWithObserver:self retainObserved:NO];
	}
	return _KVOControllerNonRetaining;
}

- (void)setDownloadDir:(NSString *)downloadDir {
	_downloadDir = downloadDir;
	if (!downloadDir.length) {
		PLVVodLogError(@"%s - 参数为空", __FUNCTION__);
		NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
		NSError *plvError = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
			userInfo[PLVVodErrorSelectorKey] = selector;
		});
		[self reportError:plvError video:nil];
        return;
	}
	[PLVVodUtil createDirIfNeed:downloadDir error:nil];
    
    // 本地加密视频播放，移除token，兼容旧版本缓存视频
    NSString *secretKey = [PLVVodSettings sharedSettings].secretkey;
    if ([PLVVodUtil isNilString:secretKey]){
        return;
    }
    else{
        BOOL isExecute = [[NSUserDefaults standardUserDefaults] objectForKey:kHasExecuteSetDownloadDir];
        if (!isExecute){
            // 只执行一次
            [[NSUserDefaults standardUserDefaults] setBool:YES forKey:kHasExecuteSetDownloadDir];
            [[NSUserDefaults standardUserDefaults] synchronize];

            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self migrateLocalVideoPlaylist:secretKey];
            });
        }
    }
}

- (NSMutableArray<PLVVodDownloader *> *)downloaderList {
	if (!_downloaderList) {
		_downloaderList = [NSMutableArray array];
	}
	return _downloaderList;
}

- (NSMutableArray<PLVVodDownloadInfo *> *)downloadInfos {
	if (!_downloadInfos) {
		_downloadInfos = [NSMutableArray array];
	}
	return _downloadInfos;
}

- (void)setMaxRuningCount:(NSUInteger)maxRuningCount{
    if (self.downloadType == PLVDownloadModeTypeAudio){
        // 音频文件下载
        if (maxRuningCount > PLVAudioMaxRuningCount){
            _maxRuningCount =PLVAudioMaxRuningCount;
        }
        else if (maxRuningCount < PLVMinRuningCount){
            _maxRuningCount = PLVMinRuningCount;
        }
        else {
            _maxRuningCount = maxRuningCount;
        }
    }
    else{
        // 视频文件下载
        if (maxRuningCount > PLVMaxRuningCount){
            _maxRuningCount =PLVMaxRuningCount;
        }
        else if (maxRuningCount < PLVMinRuningCount){
            _maxRuningCount = PLVMinRuningCount;
        }
        else {
            _maxRuningCount = maxRuningCount;
        }
    }
}

#pragma mark - public property
- (BOOL)isDownloading{
    
    __block BOOL downloading = NO;
    [self.downloaderList enumerateObjectsUsingBlock:^(PLVVodDownloader * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        
        PLVVodDownloadInfo *info = obj.downloadInfo;
        if (info.state == PLVVodDownloadStateRunning ||
            info.state == PLVVodDownloadStateReady ||
            info.state == PLVVodDownloadStatePreparingStart){
            downloading = YES;
            
            *stop = YES;
        }
    }];
    
    return downloading;
}

#pragma mark - private method

- (void)updateDownloadListToDatabase {
	PLVVodDownloadInfoManager *infoManager = [PLVVodDownloadInfoManager sharedManager];
	[self requestDownloadInfosWithCompletion:^(NSArray<PLVVodDownloadInfo *> *downloadInfos) {
		for (PLVVodDownloadInfo *info in downloadInfos) {
			[infoManager insertOrUpdateDatabaseWithDownloadInfo:info];
		}
	}];
}

// 插入数据or更新数据
- (BOOL)insertOrUpdateDatabaseWithDownloadInfo:(PLVVodDownloadInfo *)downloadInfo{
    //
    PLVVodDownloadInfoManager *infoManager = [PLVVodDownloadInfoManager sharedManager];
    return [infoManager insertOrUpdateDatabaseWithDownloadInfo:downloadInfo];
}

- (BOOL)updateDatabaseWithDownloadInfo:(PLVVodDownloadInfo *)downloadInfo{
    PLVVodDownloadInfoManager *infoManager = [PLVVodDownloadInfoManager sharedManager];
//    return [infoManager insertOrUpdateDatabaseWithDownloadInfo:downloadInfo];
    return [infoManager updateDatabaseWithDownloadInfo:downloadInfo];
}

// 修改并且保存视频下载状态
- (void)updateDownloadListToDatabaseWhenTerminate{
    PLVVodDownloadInfoManager *infoManager = [PLVVodDownloadInfoManager sharedManager];

    [self.downloadInfos enumerateObjectsUsingBlock:^(PLVVodDownloadInfo * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        
        if (obj.state <= PLVVodDownloadStateReady || obj.state == PLVVodDownloadStatePreparingStart){
            
            obj.state = PLVVodDownloadStatePreparing;
            [infoManager insertOrUpdateDatabaseWithDownloadInfo:obj];
        }
        else if (obj.state <= PLVVodDownloadStateStopped){
            obj.state = PLVVodDownloadStateStopped;
            [infoManager insertOrUpdateDatabaseWithDownloadInfo:obj];
        }
    }];
}

- (void)stopDownloadWhenTerminate{
    // 如果是加密的hls视频，调用stopdownload，支持断点下载
    [self.downloaderList enumerateObjectsUsingBlock:^(PLVVodDownloader * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        PLVVodDownloadInfo *info = obj.downloadInfo;
        
        // == PLVVodDownloadStateStopped ,上一步已经重置状态
        if ((info.state == PLVVodDownloadStateStopped) && (info.video.isHls && !info.video.isPlain)){
            [obj stopDownload];
        }
    }];
    
    // add libl [非加密mp4 视频也需要保存断点数据,停止下载] 19.12.02
    [self.downloaderList enumerateObjectsUsingBlock:^(PLVVodDownloader * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        PLVVodDownloadInfo *info = obj.downloadInfo;
        
        // == PLVVodDownloadStateStopped ,上一步已经重置状态
        if ((!info.video.isHls && info.video.isPlain)){
            [obj stopDownload];
        }
    }];
    // add end
    
    // add by [最后停止源文件hls 视频的下载，因为耗时间较长放在最后] 19.12.02
    [self.downloaderList enumerateObjectsUsingBlock:^(PLVVodDownloader * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        PLVVodDownloadInfo *info = obj.downloadInfo;
        
        // == PLVVodDownloadStateStopped ,上一步已经重置状态
        if ((info.video.isHls && info.video.isPlain)){
            [obj stopDownload];
        }
    }];
    // add end
    
    [NSThread sleepForTimeInterval:1.0];
    
    // 结束session
    if (self.session){
        [self.session invalidateAndCancel];
    }
}

#pragma mark session

/// 创建会话
- (NSURLSession *)createSession {
	NSURLSession *session = nil;
	if (self.enableBackgroundDownload) {
		session = [self backgroundSession];
	} else {
		session = [self foregroundSession];
	}
	return session;
}
/// 前台会话
- (NSURLSession *)foregroundSession {
	NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
	config.HTTPMaximumConnectionsPerHost = PLVForegroundSessionMaxConnection;
	config.timeoutIntervalForResource = PLVForegroundSessionResourceTimeout;
	config.timeoutIntervalForRequest = PLVForegroundSessionRequestTimeout;
	config.allowsCellularAccess = self.allowsCellularAccess;
	NSOperationQueue *sessionQueue = [[NSOperationQueue alloc] init];
	sessionQueue.maxConcurrentOperationCount = 1;
	self.sessionCallbackQueue = sessionQueue;
	return [NSURLSession sessionWithConfiguration:config delegate:self delegateQueue:sessionQueue];
}
/// 后台会话
- (NSURLSession *)backgroundSession {
//    NSString *bgsessionID = [NSString stringWithFormat:@"net.polyv.sdk.download.bgsession"];
    NSString *bgsessionID = PLVBGDownloadSessionId;
	//[PolyvUtil writeDocumentWithValue:bgsessionID];
	NSURLSessionConfiguration *config = [NSURLSessionConfiguration backgroundSessionConfigurationWithIdentifier:bgsessionID];
	//PLVDebugLog(@"create background session id:%@", bgsessionID);
	config.HTTPMaximumConnectionsPerHost = PLVBackgroundSessionMaxConnection;	//一个host最大并发连接数
	config.timeoutIntervalForResource = PLVBackgroundSessionResourceTimeout;	//限制整个资源的请求时长
	config.timeoutIntervalForRequest = PLVBackgroundSessionRequestTimeout;		//每次有新的data请求到会被重置
	config.allowsCellularAccess = self.allowsCellularAccess;
	config.discretionary = NO;
	config.sessionSendsLaunchEvents = YES;
    
	NSOperationQueue *sessionQueue = [[NSOperationQueue alloc] init];
	sessionQueue.maxConcurrentOperationCount = 1;
	self.sessionCallbackQueue = sessionQueue;
	return [NSURLSession sessionWithConfiguration:config delegate:self delegateQueue:sessionQueue];
}

#pragma mark other

/// 汇报错误
- (void)reportError:(NSError *)error video:(PLVVodVideo *)video {
	if (self.downloadErrorHandler) self.downloadErrorHandler(video, error);
    
    // mod by libl [后台模式下不要发送网络请求] 2018-07-02
    if (!self.isAppBackground){
        PLVVodElogModel *logModel = [[PLVVodElogModel alloc] init];
        logModel.userId2 = [PLVVodSettings findUserIdWithVid:video.vid];
        logModel.module = PLV_Busi_Download_Module;
        logModel.vid = video.vid;
        logModel.errorCode = [@(error.code) stringValue];
        
        // logFile 设置
        PLVVodElogLogfileModel *logfileModel = [[PLVVodElogLogfileModel alloc] init];
        logfileModel.exception = [error description];
        logModel.logFile = logfileModel;
        
        [PLVVodReportManager reportElogWithElogModel:logModel completion:nil];
    }
    // end
}

#pragma mark download

- (PLVVodDownloader *)downloaderForTask:(NSURLSessionTask *)task {
	NSParameterAssert(task);
	
	PLVVodDownloader *downloader = nil;
	[self.lock lock];
	downloader = self.taskDownloaderDic[@(task.taskIdentifier)];
	[self.lock unlock];
	
	return downloader;
}
- (void)setDownloader:(PLVVodDownloader *)downloader forTask:(NSURLSessionTask *)task {
	NSParameterAssert(task);
	NSParameterAssert(downloader);
	[self.lock lock];
	self.taskDownloaderDic[@(task.taskIdentifier)] = downloader;
	[self.lock unlock];
}
- (void)removeDownloaderForTask:(NSURLSessionTask *)task {
	NSParameterAssert(task);
	
	[self.lock lock];
	[self.taskDownloaderDic removeObjectForKey:@(task.taskIdentifier)];
	[self.lock unlock];
}

#pragma mark - public method

- (BOOL)setDownloadDir:(NSString *)downloadDir skipBackup:(BOOL)skipBackup error:(NSError *__autoreleasing *)error {
	self.downloadDir = downloadDir;
	BOOL success = [[NSURL fileURLWithPath:downloadDir] setResourceValue:@(YES) forKey:NSURLIsExcludedFromBackupKey error:error];
	return success;
}

- (BOOL)moveDownloadVideoFromSourceDir:(NSString *)sourceDir toDestDir:(NSString *)destDir{
    if ([PLVVodUtil isNilString:sourceDir]){
        PLVVodLogWarn(@"[download] -- sourceDir 为空");
        return NO;
    }
    
    if ([PLVVodUtil isNilString:destDir]){
        PLVVodLogWarn(@"[download] -- destinationDir 为空");
        return NO;
    }
    
    if (![[NSFileManager defaultManager] fileExistsAtPath:sourceDir]){
        PLVVodLogWarn(@"[download] -- %@ 路径不存在", sourceDir);
        return NO;
    }
    
    // destDir 必须不存在
    if ([[NSFileManager defaultManager] fileExistsAtPath:destDir]){
        PLVVodLogWarn(@"[download] -- %@ 路径已经存在", destDir);
        return NO;
    }
        
    NSError *error;
    [[NSFileManager defaultManager] moveItemAtPath:sourceDir toPath:destDir error:&error];
    if (!error){
        PLVVodLogInfo(@"[download] -- 迁移成功");
        return YES;
    }
    else{
        PLVVodLogInfo(@"[download] -- 迁移失败")
        return NO;
    }
}

#pragma mark download

- (PLVVodDownloadInfo *)downloadVideo:(PLVVodVideo *)video {
	return [self downloadVideo:video quality:video.preferredQuality];
}

- (void)requstDownloadProcessingListWithCompletion:(void (^)(NSArray<PLVVodDownloadInfo *> *))completion{
    
    if (self.downloadInfos.count) {
        
        NSMutableArray<PLVVodDownloadInfo *> *processingArray = [NSMutableArray arrayWithCapacity:0];
        for (PLVVodDownloadInfo *downInfo in self.downloadInfos) {
            if (downInfo.state != PLVVodDownloadStateSuccess) {
                [processingArray addObject:downInfo];
            }
        }
        
        if (completion) completion(processingArray);
        return;
    }
    
    NSArray *dbInfos = [[PLVVodDownloadInfoManager sharedManager] downloadProcessingListFromDatabase];
    
    NSArray *dbInfosFilter = [self fixCompleteDbData:dbInfos];

    if (dbInfosFilter.count){
        [self recoveryDownloadListWithInfos:dbInfosFilter returnAllDownload:NO complete:completion];
    }
    else{
        if (completion)
            completion (nil);
    }
}


/// 修复实际上下载完了，但是数据库没有更新的情况，去除下载完的dbinfo，更新数据库
/// @param dbInfos 数据库获取的下载任务列表
- (NSArray<PLVVodDownloadInfo *> *)fixCompleteDbData:(NSArray<PLVVodDownloadInfo *> *)dbInfos
{
    NSMutableArray *dealResult = [NSMutableArray array];
    for (PLVVodDownloadInfo *dbInfo in dbInfos) {
        if (dbInfo.fileType == PLVDownloadFileTypeAudio){
            BOOL isExist = [self.class audioExist:dbInfo.vid];
            if (isExist) {
                dbInfo.state = PLVVodDownloadStateSuccess;
                dbInfo.progress = 1;
                dbInfo.unzipProgress = 1;
                [self updateDatabaseWithDownloadInfo:dbInfo];
            }else {
                [dealResult addObject:dbInfo];
            }
        }
        else if (dbInfo.fileType == PLVDownloadFileTypeVideo){
            PLVVodQuality videoQuality = [self.class videoExist:dbInfo.vid];
            if (videoQuality > PLVVodQualityAuto){
                //视频实际上已经下载完了
                dbInfo.state = PLVVodDownloadStateSuccess;
                dbInfo.progress = 1;
                dbInfo.unzipProgress = 1;
                [self updateDatabaseWithDownloadInfo:dbInfo];
            }else {
                [dealResult addObject:dbInfo];
            }
        }
    }

    return (NSArray<PLVVodDownloadInfo *> *)dealResult;
}

- (NSArray<PLVVodDownloadInfo *> *)requestDownloadCompleteList{
    NSArray *dbInfos = [[PLVVodDownloadInfoManager sharedManager] downloadCompleteListFromDatabase];

    return dbInfos.copy;
}

- (PLVVodDownloadInfo *)requestDownloadInfoWithVid:(NSString *)vid{
    
    if (vid == nil || [vid isKindOfClass:[NSNull class]]){
        return nil;
    }
    
    __block PLVVodDownloadInfo *info = nil;
    [self.downloadInfos enumerateObjectsUsingBlock:^(PLVVodDownloadInfo * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([vid isEqualToString:obj.vid]){
            info = obj;
            *stop = YES;
        }
    }];
    
    if (nil == info){
        info = [[PLVVodDownloadInfoManager sharedManager] downloadInfoWithVid:vid];
    }
    
    return info;
}

- (PLVVodDownloadInfo *)requestDownloadInfoWithVideoParams:(PLVVodVideoParams *)videoParams{
    if (videoParams.vid == nil || [videoParams.vid isKindOfClass:[NSNull class]]){
        return nil;
    }
    
    __block PLVVodDownloadInfo *info = nil;
    [self.downloadInfos enumerateObjectsUsingBlock:^(PLVVodDownloadInfo * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([videoParams.vid isEqualToString:obj.vid] && videoParams.fileType == obj.fileType){
            info = obj;
            *stop = YES;
        }
    }];
    
    if (nil == info){
        info = [[PLVVodDownloadInfoManager sharedManager] downloadInfoWithVideoParams:videoParams];
    }
    
    return info;
}

/// 短时间，先不动大体流程与逻辑，只修复明显bug
/// 理由：对于具体的业务流程需求还不清晰，很容易改出大问题
/// 请求下载回调信息
- (void)requestDownloadInfosWithCompletion:(void (^)(NSArray<PLVVodDownloadInfo *> *downloadInfos))completion {
	NSArray *dbInfos = [[PLVVodDownloadInfoManager sharedManager] downloadListFromDatabase];
    [self recoveryDownloadListWithInfos:dbInfos returnAllDownload:YES complete:completion];
}

- (void)recoveryDownloadListWithInfos:(NSArray<PLVVodDownloadInfo *> *)dbInfos
                    returnAllDownload:(BOOL)all
                             complete:(void (^)(NSArray<PLVVodDownloadInfo *> *downloadInfos))completion{
    
    __weak typeof(self) weakSelf = self;
    __block NSMutableArray *downloadInfos = [NSMutableArray array];
    __block NSMutableDictionary<NSString *, PLVVodVideo *> *videoDics = [NSMutableDictionary dictionary];
    
    // 获取所有video后
    void (^requestAllVideosHandler)(void) = ^(){
        BOOL autoStart = weakSelf.autoStart;
        weakSelf.autoStart = NO;
        
        for (PLVVodDownloadInfo *dbInfo in dbInfos) {
            PLVVodVideo *video = videoDics[dbInfo.identifier];
            if (!video) continue;
            
            
            // 将本地数据赋给新对象,并且创建download 下载器，加入下载队列
            PLVVodDownloadInfo *downloadInfo = nil;
            if (dbInfo.fileType == PLVDownloadFileTypeAudio){
                downloadInfo = [weakSelf downloadAudio:video];
                PLVVodLogInfo(@"[download manager] -- %@_%d，音频恢复下载。", video.vid,(int) dbInfo.fileType);
            }
            else if (dbInfo.fileType == PLVDownloadFileTypeVideo){
                downloadInfo = [weakSelf downloadVideo:video quality:dbInfo.quality];
                PLVVodLogInfo(@"[download manager] %@_%d，视频恢复下载。", video.vid,(int) dbInfo.fileType);
            }
            // mod end
            
            if (all && !downloadInfo) { // 返回全部任务，包括已下载任务
                downloadInfo = [[PLVVodDownloadManager sharedManager] requestDownloadInfoWithVid:video.vid];
            }
            
            if (!downloadInfo) {
                PLVVodLogWarn(@"[download manager] -- %@_%d，无法恢复下载。", video.vid,(int) dbInfo.fileType);
                continue;
            }
            [downloadInfos addObject:downloadInfo];
            
            // mod by libl [从数据库恢复时，需要调整视频下载状态] 2018-07-02 start
            downloadInfo.state = dbInfo.state;
            if (dbInfo.state <= PLVVodDownloadStateReady){
                downloadInfo.state = PLVVodDownloadStatePreparing;
            }
            else if (dbInfo.state <= PLVVodDownloadStateStopped){
                downloadInfo.state = PLVVodDownloadStateStopped;
            }
            // end
            downloadInfo.progress = dbInfo.progress;
        }
        
        weakSelf.downloadInfos = downloadInfos;
        weakSelf.autoStart = autoStart;
        if (autoStart) [weakSelf startDownloadLoop];
        
        // mod by libl [回调处理,如果没有请求到网络数据，传递数据库数据] 2018-07-07 start
        if (downloadInfos.count == 0){
            //
            // 从数据库恢复时，需要调整视频下载状态
            [dbInfos enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                PLVVodDownloadInfo *dbDownInfo = obj;
                if (dbDownInfo.state <= PLVVodDownloadStateReady){
                    dbDownInfo.state = PLVVodDownloadStatePreparing;
                }
                else if (dbDownInfo.state <= PLVVodDownloadStateStopped){
                    dbDownInfo.state = PLVVodDownloadStateStopped;
                }
            }];
            
            [downloadInfos addObjectsFromArray:dbInfos];
        }
        
        if (completion) completion(downloadInfos.copy);
        // mod end
    };
    
    __block NSInteger dbInfosCount = dbInfos.count;
    if (dbInfosCount == 0){
        if (completion) {
            completion (nil);
        }
    }
    else{
        for (PLVVodDownloadInfo *info in dbInfos) {
            
            // mod by libl [性能优化，优先从本地读取] 2018-09-10 start
            [PLVVodVideo requestVideoPriorityCacheWithVid:info.vid completion:^(PLVVodVideo *video, NSError *error) {
                // mod end
                PLVVodLogInfo(@"%@", video);
                if (error) {
                    PLVVodLogError(@"[download manager] -- 视频对象请求错误，%@", error.localizedDescription);
                    dbInfosCount -= 1;
                    
                    // 填充完毕
                    if (videoDics.count == dbInfosCount) {
                        requestAllVideosHandler();
                    }
                }
                else {
                    
                    if (video.available) {
                        [videoDics setObject:video forKey:info.identifier];
                    } else {
                        dbInfosCount -= 1;
                        [[PLVVodDownloadInfoManager sharedManager] removeFromdatabseWithVid:video.vid];
                    }
                    
                    // 填充完毕
                    if (videoDics.count == dbInfosCount) {
                        requestAllVideosHandler();
                    }
                }
            }];
        }
    }
}

- (void)createDownloadingListWithCompletion:(void(^)(void *))completion{
    //
    __weak typeof (self) weakSelf = self;
    __block NSMutableArray<PLVVodDownloadInfo *> *needArray = [NSMutableArray arrayWithCapacity:0];
    [self.downloadInfos enumerateObjectsUsingBlock:^(PLVVodDownloadInfo * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ((obj.state != PLVVodDownloadStateSuccess) && (obj.video == nil)){
            [needArray addObject:obj];
        }
    }];
    
    if (!needArray.count){
        if (completion){
            completion(nil);
            return;
        }
    }
    
    __block NSMutableArray<PLVVodVideo *> *downloadArray = [NSMutableArray new];
    __block NSUInteger resVideoCount = needArray.count;
    [needArray enumerateObjectsUsingBlock:^(PLVVodDownloadInfo * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        
        // mod by libl [性能优化，优先从本地读取数据] 2018-09-10 start
        [PLVVodVideo requestVideoPriorityCacheWithVid:obj.vid completion:^(PLVVodVideo *video, NSError *error) {
            if (error) {
                PLVVodLogError(@"[download manager] -- 视频对象请求错误，%@", error.localizedDescription);
                // mod by libl [请求失败而已，不应该过滤，减少计数即可] 2018-07-06 start
                resVideoCount -= 1;
                // add end
                
                // 请求完毕
                if (downloadArray.count == resVideoCount) {
                    [weakSelf handleCreateDownloadingList:downloadArray withComplete:completion];
                }
                // mod end
            }
            else {
                
                if (video.available) {
                    [downloadArray addObject:video];
                } else {
                    resVideoCount -= 1;
                }
                
                // 请求完毕
                if (downloadArray.count == resVideoCount) {
                    [weakSelf handleCreateDownloadingList:downloadArray withComplete:completion];
                }
            }
        }];
    }];
}

- (void)handleCreateDownloadingList:(NSArray<PLVVodVideo *> *)downloadArray withComplete:(void(^)(void *))completion{
    
    if (!downloadArray.count){
        if (completion){
            completion (nil);
            return;
        }
    }
    
    BOOL autoStart = self.autoStart;
    
    // 创建任务不要开启下载
    self.autoStart = NO;
    [downloadArray enumerateObjectsUsingBlock:^(PLVVodVideo * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        
        PLVVodVideo *curVideo = obj;
        // 先删除数据，再加入，不然重复添加队列
        [self.downloadInfos enumerateObjectsUsingBlock:^(PLVVodDownloadInfo * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            PLVVodDownloadInfo *curInfo = obj;
            
            if ([curInfo.vid isEqualToString:curVideo.vid]){
                
                *stop = YES;
                
                [self.downloadInfos removeObject:curInfo];
            }
        }];
        
        // 将本地数据赋给新对象,并且创建download 下载器，加入下载队列
        PLVVodDownloadInfo *downloadInfo = [self downloadVideo:obj];
        if (!downloadInfo) {
            PLVVodLogWarn(@"[download manager] -- %@，创建下载器失败", obj.vid);
        }
        
    }];
    
    self.autoStart = autoStart;
    
    if (completion){
        completion (nil);
    }
}

/// 音频下载
- (PLVVodDownloadInfo *)downloadAudio:(PLVVodVideo *)video{
    //
    if (!video) {
        PLVVodLogError(@"[download manager] -- %s - 参数为空", __FUNCTION__);
        NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
        NSError *plvError = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
            userInfo[PLVVodErrorSelectorKey] = selector;
        });
        [self reportError:plvError video:video];
        return nil;
    }
    
    if (!video.available) {
        NSError *plvError = video.error ? video.error : PLVVodErrorWithCode(account_video_illegal);
        [self reportError:plvError video:video];
        return nil;
    }
    
    //
    if (![video canSwithPlaybackMode]){
        PLVVodLogWarn(@"[download manager] -- %@ ，不支持下载音频", video.vid);
        return nil;
    }
    
    // 是否已经加入队列
    NSArray *array = [NSArray arrayWithArray:self.downloadInfos];
    for (PLVVodDownloadInfo *info in array) {
        if ([info.vid isEqualToString:video.vid] && info.fileType == PLVDownloadFileTypeAudio) {
            PLVVodLogWarn(@"[download manager] -- %@ ，重复加入队列", video.vid);
            return nil;
        }
    }
    
    // add by libl [添加音频已经缓存的判断条件] 2018-07-26 start
    // 是否已经下载完成
    BOOL isExist = [self.class audioExist:video.vid];
    if (isExist){
        PLVVodLogWarn(@"[download manager] -- %@ , 音频文件已经下载完成", video.vid);
        return nil;
    }
    // add end
    
    if (!self.session) {
        self.session = [self createSession];
    }
    PLVVodDownloader *downloader = [PLVVodDownloader audioDownloaderWithVideo:video];
    if (!downloader) {
        PLVVodLogError(@"[download manager] -- 音频下载器创建失败");
        NSError *plvError = PLVVodErrorWithCode(downloader_create_error);
        [self reportError:plvError video:video];
        return nil;
    }
    
    downloader.session = self.session;
    __weak typeof(self) weakSelf = self;
    downloader.downloadErrorHandler = ^(PLVVodDownloader *downloader, NSError *error) {
        [weakSelf reportError:error video:downloader.video];
    };
    
    [self.downloaderList addObject:downloader];
    if (self.autoStart){
        [self startDownloadLoop];
    }
    
    PLVVodDownloadInfo *downloadInfo = downloader.downloadInfo;
    if (downloadInfo) {
        downloadInfo.downloadId = self.downloadInfos.lastObject.downloadId + 1;
        [self.downloadInfos addObject:downloadInfo];
        
        // add by libl [添加进下载队列立即保存数据库，否则App 退出后再进入，下载列表丢失] 2018-09-19 start
        [self insertOrUpdateDatabaseWithDownloadInfo:downloadInfo];
        // add end
    }
    
    return downloadInfo;
}

- (void)downloadPPTWithVideo:(PLVVodVideo *)video completion:(void (^)(PLVVodDownloadInfo *))completion{
    [[PLVVodPPTMgr shareManager] downloadPPTWithVideo:video completion:^(PLVVodDownloadInfo * _Nullable info) {
        if (completion){
            completion (info);
        }
    }];
}

- (PLVVodDownloadInfo *)downloadVideo:(PLVVodVideo *)video quality:(PLVVodQuality)quality {
	if (!video) {
		PLVVodLogError(@"%s - 参数为空", __FUNCTION__);
		NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
		NSError *plvError = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
			userInfo[PLVVodErrorSelectorKey] = selector;
		});
		[self reportError:plvError video:video];
		return nil;
	}
    
	if (!video.available) {
		NSError *plvError = video.error ? video.error : PLVVodErrorWithCode(account_video_illegal);
		[self reportError:plvError video:video];
		return nil;
	}
    
    // 是否已经加入队列
    NSArray *array = [NSArray arrayWithArray:self.downloadInfos];
	for (PLVVodDownloadInfo *info in array) {
		if ([info.vid isEqualToString:video.vid] && info.fileType == PLVDownloadFileTypeVideo) {
			PLVVodLogWarn(@"[download manager] -- %@ - %@，重复加入队列", video.vid, @(quality));
			return nil;
		}
	}
    
    // add by libl [添加视频已经缓存的判断条件] 2018-07-26 start
    // 是否已经下载完成
    PLVVodQuality videoQuality = [self.class videoExist:video.vid];
    if (videoQuality > PLVVodQualityAuto){
        PLVVodLogWarn(@"[download manager] -- %@ - %@ , 视频已经下载完成", video.vid, @(quality));
        
        // add by libl [如果是1.0版本升级到2.0版本，需要重建下载记录] 19-04-03 start
        PLVVodDownloadInfo *downloadInfo = [[PLVVodDownloadManager sharedManager] requestDownloadInfoWithVid:video.vid];
        if (!downloadInfo){
            PLVVodDownloadInfo *updateInfo = [[PLVVodDownloadInfo alloc] initWithVideo:video quality:quality];
            updateInfo.state = PLVVodDownloadStateSuccess;
            BOOL success = [self insertOrUpdateDatabaseWithDownloadInfo:updateInfo];
            if (success){
                PLVVodLogDebug(@"[download manager] -- 1.0升级2.0 为已下载视频创建下载记录");
            }
        }
        // add end
        return nil;
    }
    // add end
    
	if (!self.session) {
		self.session = [self createSession];
	}
    PLVVodDownloader *downloader = [PLVVodDownloader downloaderWithVideo:video quality:quality createTaskMode:self.taskCreateMode];
	if (!downloader) {
		PLVVodLogError(@"[download manager] -- 视频下载器创建失败");
		NSError *plvError = PLVVodErrorWithCode(downloader_create_error);
		[self reportError:plvError video:video];
		return nil;
	}
    
	downloader.session = self.session;
	__weak typeof(self) weakSelf = self;
	downloader.downloadErrorHandler = ^(PLVVodDownloader *downloader, NSError *error) {
		[weakSelf reportError:error video:downloader.video];
	};
	
	[self.downloaderList addObject:downloader];
    if (self.autoStart){
        [self startDownloadLoop];
    }
    
	PLVVodDownloadInfo *downloadInfo = downloader.downloadInfo;
	if (downloadInfo) {
		downloadInfo.downloadId = self.downloadInfos.lastObject.downloadId + 1;
		[self.downloadInfos addObject:downloadInfo];
        
        // add by libl [添加进下载队列立即保存数据库，否则App 退出后再进入，下载列表丢失] 2018-09-19 start
        BOOL success =  [self insertOrUpdateDatabaseWithDownloadInfo:downloadInfo];
        if (success){
        }
	}
    
    // 如果存在ppt，需要下载ppt
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [self downloadPPTWithVideo:video completion:nil];
    });
    
	return downloadInfo;
}

// 队列没有下载任务则立即开始任务，否则暂停，排队
- (void)startDownload {
    _isStopDownload = NO;
    
#warning -- TODO: 以后继续优化
   
    // 1 创建下载器，并且添加到下载队列
    // 此场景检测PLVDownloadInfo中video 为空的场景,直接返回了数据库数据
    __weak typeof (self) weakSelf = self;
    [self createDownloadingListWithCompletion:^(void *param) {
        
        // 2 更改队列中下载状态：停止/失败状态重置为准备状态
        [weakSelf.downloaderList enumerateObjectsUsingBlock:^(PLVVodDownloader * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.downloadInfo.state == PLVVodDownloadStateStopping ||
                obj.downloadInfo.state == PLVVodDownloadStateStopped ||
                obj.downloadInfo.state == PLVVodDownloadStateFailed){
                
                obj.downloadInfo.state = PLVVodDownloadStatePreparing;
            }
        }];
        
        // 3 开启下载循环
        [weakSelf startDownloadLoop];
    }];
}

// 从指定视频开始下载，外部方法
- (void)startDownloadWithVid:(NSString *)vid{
    // 1 重置停止开关
    _isStopDownload = NO;

    // 2 开启指定视频的下载
    // 首先创建完整的下载队列
    __weak typeof (self) weakSelf = self;
    PLVVodVideoParams *params = [PLVVodVideoParams videoParamsWithVid:vid fileType:PLVDownloadFileTypeVideo];
    [self createDownloadingListWithCompletion:^(void *param) {
        
        // 从指定视频开始下载
        [weakSelf startDownloadLoopWithParams:params];
    }];
}

//
- (void)startDownloadWithVideoParams:(PLVVodVideoParams *)videoParams{
    // 1 重置停止开关
    _isStopDownload = NO;
    
    // 2 开启指定视频的下载
    // 首先创建完整的下载队列
    __weak typeof (self) weakSelf = self;
    [self createDownloadingListWithCompletion:^(void *param) {
        
        // 从指定视频开始下载
        [weakSelf startDownloadLoopWithParams:videoParams];
    }];
}

- (BOOL)hasReachMaxRuning{
    BOOL reachMax = NO;
    if (self.runingQueue.count >= self.maxRuningCount){
        reachMax = YES;
    }
    
    return reachMax;
}

// 开始下载队列，内部方法
- (void)startDownloadLoop{
    
    // add by libl [UI 层手动点击了全部停止，不再下载] 2018-07-05
    if (self.isStopDownload) return;
    // end
    
    if (!self.downloaderList.count) {
        PLVVodLogWarn(@"[download manager] -- 当前没有可启动的下载任务");
        return;
    }
    
    if ([self hasReachMaxRuning]){
        PLVVodLogWarn(@"[download manager] -- 已经达到最大并发数");
        return;
    }

    [self.downloaderList enumerateObjectsUsingBlock:^(PLVVodDownloader * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (obj.downloadInfo.state == PLVVodDownloadStatePreparing){
            
            if ([self hasReachMaxRuning]){
                *stop = YES;
            }
            [self handleStartDownloadLoop:obj];
        }
    }];
}

// 从指定视频开始下载，内部方法
- (void)startDownloadLoopWithParams:(PLVVodVideoParams *)videoParams{
    // add by libl [UI 层手动点击了停止下载，不再下载] 2018-07-05
    if (self.isStopDownload) return;
    // add end
    
    if (!self.downloaderList.count) {
        PLVVodLogWarn(@"[download manager] -- 当前没有可启动的下载任务");
        return;
    }
    
    for (int i = 0; i < self.downloaderList.count; i++) {
        PLVVodDownloader *downloader = self.downloaderList[i];
        if ([downloader.downloadInfo.vid isEqualToString:videoParams.vid] &&
            downloader.downloadInfo.fileType == videoParams.fileType)
        {
            
            if (downloader.downloadInfo.state == PLVVodDownloadStateRunning) {
                PLVVodLogWarn(@"[download manager] -- 该视频已处于下载中状态，vid：%@",videoParams.vid);
                return;
            }
            
            downloader.downloadInfo.state = PLVVodDownloadStatePreparing;
            
            // 如果已经达到并发数限制，先移除最先下载任务
            if ([self hasReachMaxRuning]){
                PLVVodDownloader *needStopDownload =  [self.runingQueue firstObject];
                [needStopDownload stopDownload];
                [self.runingQueue removeObject:needStopDownload];
            }
            
            [self handleStartDownloadLoop:downloader];

            break;
        }
    }
    
    // mod end
}

// 开始某个vid视频下载，若达到并发数限制，则处于Preparing准备状态
- (void)startDownloadWithVid:(NSString *)vid highPriority:(BOOL)priority{

    // 1 重置停止开关
    _isStopDownload = NO;

    // 2 开启指定视频的下载
    // 首先创建完整的下载队列
    __weak typeof (self) weakSelf = self;
    PLVVodVideoParams *params = [PLVVodVideoParams videoParamsWithVid:vid fileType:PLVDownloadFileTypeVideo];
    [self createDownloadingListWithCompletion:^(void *param) {
        
        if (priority){
            // 优先下载此视频
            [weakSelf startDownloadLoopWithParams:params];
        }
        else{
            if (self.isStopDownload) return;
            
            if (!self.downloaderList.count) {
                PLVVodLogWarn(@"[download manager] -- FIFO 当前没有可启动的下载任务");
                return;
            }
            
            for (int i = 0; i < self.downloaderList.count; i++) {
                PLVVodDownloader *downloader = self.downloaderList[i];
                if ([downloader.downloadInfo.vid isEqualToString:vid] &&
                    downloader.downloadInfo.fileType == params.fileType) {
                    
                    if (downloader.downloadInfo.state == PLVVodDownloadStateRunning) {
                        PLVVodLogWarn(@"[download manager] -- 该视频已处于下载中状态，vid：%@",vid);
                        return;
                    }
                    
                    downloader.downloadInfo.state = PLVVodDownloadStatePreparing;
                    
                    if ([self hasReachMaxRuning]){
                        // 已经达到并发数限制，则仅仅修改为‘Preparing’状态，等runingQueue的视频下载完后，自动开启这个Preparing的视频下载
                        // 不做任何逻辑...
                    }else{
                        // 没有达到并发数限制，则正常开启下载
                        [self handleStartDownloadLoop:downloader];
                    }
                    
                    break;
                }
            }
        }
    }];
}

// 配置下载回调，监听事件，并开始下载
- (void)handleStartDownloadLoop:(PLVVodDownloader *)downloaderObj{
    
    // add by libl [超出最大并发数，返回] 2018-11-09 start
    if ([self hasReachMaxRuning]){
        return;
    }
    // add end
    
    PLVVodDownloader *downloader = downloaderObj;
    PLVVodDownloadInfo *info = downloader.downloadInfo;
    
    __weak typeof(self) weakSelf = self;
    [self.KVOController observe:info keyPath:@"state" options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSKeyValueChangeKey,id> * _Nonnull change) {
        PLVVodDownloadInfo *info = object;
        
        [weakSelf updateDatabaseWithDownloadInfo:info];
        if (info.state == PLVVodDownloadStateSuccess) {
            
            // 从队列中移除
            [weakSelf.downloaderList removeObject:downloader];
            [weakSelf.downloadInfos removeObject:downloader.downloadInfo];
            [weakSelf.runingQueue removeObject:downloader];
            
            // add by lib [添加单个视频下载完成回调] 2018-07-26 start
            if (weakSelf.downloadCompleteBlock){
                weakSelf.downloadCompleteBlock(info);
            }
            // add end
            
            if (weakSelf.downloaderList.count) {
                // 为什么调用多次 ？
                PLVVodLogDebug(@"[download manager] -- info.state == PLVVodDownloadStateSuccess");
                [weakSelf startDownloadLoop];
            } else {
                // 若数组为空，则下载完成
                PLVVodLogDebug(@"[download manager] -- 所有视频下载完成");
                if (self.taskDownloaderDic.count){
                    [self.taskDownloaderDic enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
                        PLVVodLogDebug(@"[download manager] -- taskId %@ downloader: %@", key, obj);
                    }];
                    
                    [self.taskDownloaderDic removeAllObjects];
                }
                
                if (weakSelf.completeBlock) weakSelf.completeBlock();
            }
        }
        else if (info.state == PLVVodDownloadStateFailed){
            
            // add by libl [自动开启下一个视频下载，会不会造成死循环？] 2018-07-03 start
            PLVVodLogInfo(@"[download manager] -- 视频下载失败 %@ ", info.vid);
            [weakSelf.downloaderList removeObject:downloader];
            // 已经下载过，添加到队尾
            [weakSelf.downloaderList addObject:downloader];
            
            [weakSelf.runingQueue removeObject:downloader];
            PLVVodLogDebug(@"[download manager] -- runingQueue 移除：%@ ", downloader);
                    
            [weakSelf startDownloadNextIfFailed];
        }
        else if (info.state == PLVVodDownloadStateStopping || info.state == PLVVodDownloadStateStopped){
            //
            [weakSelf.runingQueue removeObject:downloader];
            
            if (info.state == PLVVodDownloadStateStopping ){
                PLVVodLogDebug(@"[download manager] -- PLVVodDownloadStateStopping");
                PLVVodLogDebug(@"[download manager] -- runingQueue 移除：%@ ", downloader);
            }
            else{
                PLVVodLogDebug(@"[download manager] -- PLVVodDownloadStateStopped");
                PLVVodLogDebug(@"[download manager] -- runingQueue 移除：%@ ", downloader);
            }
        }
    }];
    
    // 建立download 下载器与 ts task 的关联，用户代理回调
    [self.KVOController observe:downloader keyPath:@"downloadTasks" options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSKeyValueChangeKey,id> * _Nonnull change) {
        PLVVodDownloader *downloader = object;
        if (!downloader.downloadTasks.count) return;
        for (NSURLSessionTask *task in downloader.downloadTasks) {
            [weakSelf setDownloader:downloader forTask:task];
        }
    }];
    
    // add by libl [建立download 下载器与task 的关联，m3u8DownloadTask, keyDownloadTask] 2018-07-02 start
    [self.KVOController observe:downloader keyPath:@"m3u8DownloadTask" options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSKeyValueChangeKey,id> * _Nonnull change) {
        PLVVodDownloader *downloader = object;
        if (!downloader.m3u8DownloadTask) return;
        
        [weakSelf setDownloader:downloader forTask:downloader.m3u8DownloadTask];
    }];
    
    [self.KVOController observe:downloader keyPath:@"keyDownloadTask" options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSKeyValueChangeKey,id> * _Nonnull change) {
        PLVVodDownloader *downloader = object;
        if (!downloader.keyDownloadTask) return;
        
        [weakSelf setDownloader:downloader forTask:downloader.keyDownloadTask];
    }];
    // end
    
    // 开始下载
    [downloader startDownload];
    
    // add by libl [加入运行中队列] 2018-11-09 start
    [self.runingQueue addObject:downloader];
    PLVVodLogInfo(@"[download manager] -- 正在运行任务数: %d ", (int)self.runingQueue.count);
}

// 当前下载失败后，自动开启下一个视频下载
- (void)startDownloadNextIfFailed{
    
    if (self.isStopDownload) return;
    
    // 什么条件开启？防止死循环下载
    // 1 出错后，会添加到队尾
    // 2 如果所有下载器都成功或者失败，不再下载
    // 3 对于初始化就为fail 的下载器，正常流程还是可以得到机会下载
    [self.downloaderList enumerateObjectsUsingBlock:^(PLVVodDownloader * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        
        if (obj.state < PLVVodDownloadStateSuccess){
            PLVVodLogInfo(@"[download manager] -- 下载失败，开启下一个视频下载");
            [self startDownloadLoop];
        }
        
        *stop = YES;
    }];
}

// 手动停止下载队列，外部方法
- (void)stopDownload {
    
    // 设置标志，防止自动循环下载
    _isStopDownload = YES;
    
    [self stopDownloadLoop];
}

// 停止指定视频下载
- (void)stopDownloadWithVid:(NSString *)vid{
    // 1 参数检查
    if ((vid==nil) || [vid isKindOfClass:[NSNull class]] || ([vid length]==0)){
        
        PLVVodLogWarn(@"[download manager] -- 视频ID 不合法 %@", vid);
        return;
    }
    
    if (!self.downloaderList.count) {
        PLVVodLogWarn(@"[download manager] -- 当前没有可停止的下载任务");
        return;
    }
    
    // 2 检测指定视频是否处于下载状态
    __block PLVVodDownloader *findDownload = nil;
    [self.downloaderList enumerateObjectsUsingBlock:^(PLVVodDownloader * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj.vid isEqualToString:vid]){
            
            findDownload = obj;
            // 2019-03-07
            // 原因：新增的FIFO方法会将任务置为Preparing状态，但stopDownloadWithVid并不会停止Preparing状态的任务，导致调用该方法后，任务反而会被启动下载
            // 解决：暂时Preparing也会被停止，等同于取消排队。
            if (obj.downloadInfo.state == PLVVodDownloadStateReady ||
                obj.downloadInfo.state == PLVVodDownloadStatePreparing ||
                obj.downloadInfo.state == PLVVodDownloadStatePreparingStart ||
                obj.downloadInfo.state == PLVVodDownloadStateRunning){
                [obj stopDownload];
            }
            else{
                PLVVodLogWarn(@"[download manager] -- 视频未处于下载状态 %@ state %ld", vid,(long)obj.downloadInfo.state);
                return;
            }

            *stop = YES;
        }
    }];
    
    if (findDownload == nil){
        PLVVodLogWarn(@"[download manager] -- 视频未处于下载状态 %@", vid);
        return;
    }
    
    // 3 开启下载下一个视频
    // 失败/成功状态才会自动开启下一个视频的下载，这里需要手动调用
    [self startDownloadLoop];
}

- (void)stopDownloadWithVid:(NSString *)vid autoNext:(BOOL)autoNext{
    // 1 参数检查
    if ((vid==nil) || [vid isKindOfClass:[NSNull class]] || ([vid length]==0)){
        
        PLVVodLogWarn(@"[download manager] -- 视频ID 不合法 %@", vid);
        return;
    }
    
    if (!self.downloaderList.count) {
        PLVVodLogWarn(@"[download manager] -- 当前没有可停止的下载任务");
        return;
    }
    
    // 2 检测指定视频是否处于下载状态
    __block PLVVodDownloader *findDownload = nil;
    [self.downloaderList enumerateObjectsUsingBlock:^(PLVVodDownloader * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj.vid isEqualToString:vid]){
            
            findDownload = obj;
            if (obj.downloadInfo.state == PLVVodDownloadStateReady ||
                obj.downloadInfo.state == PLVVodDownloadStateRunning){
                
                [obj stopDownload];
            }
            else{
                PLVVodLogWarn(@"[download manager] -- 视频未处于下载状态 %@", vid);
                // add by libl [需要将状态设置为停止状态] 2019-06-03 start
                obj.downloadInfo.state = PLVVodDownloadStateStopped;
                // add end
                return;
            }
            
            *stop = YES;
        }
    }];
    
    if (findDownload == nil){
        PLVVodLogWarn(@"[download manager] -- 视频未处于下载状态 %@", vid);
        return;
    }
    
    // 3 开启下载下一个视频
    // 失败/成功状态才会自动开启下一个视频的下载，这里需要手动调用
    if (autoNext){
        [self startDownloadLoop];
    }
}

// 停止下载指定音频/视频文件
- (void)stopDownloadWithVideoParams:(PLVVodVideoParams *)videoParams{
    // 1 参数检查
    if ((videoParams.vid==nil) || [videoParams.vid isKindOfClass:[NSNull class]] || ([videoParams.vid length]==0)){
        
        PLVVodLogWarn(@"[download manager] -- 视频ID 不合法 %@", videoParams.vid);
        return;
    }
    
    if (!self.downloaderList.count) {
        PLVVodLogWarn(@"[download manager] -- 当前没有可停止的下载任务");
        return;
    }
    
    // 2 检测指定视频是否处于下载状态
    __block PLVVodDownloader *findDownload = nil;
    [self.downloaderList enumerateObjectsUsingBlock:^(PLVVodDownloader * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj.vid isEqualToString:videoParams.vid] && obj.downloadInfo.fileType == videoParams.fileType){
            
            findDownload = obj;
            if (obj.downloadInfo.state == PLVVodDownloadStateReady ||
                obj.downloadInfo.state == PLVVodDownloadStateRunning)
            {
                [obj stopDownload];
            }
            else{
                PLVVodLogWarn(@"[download manager] -- 视频未处于下载状态 %@", videoParams.vid);
                return;
            }
            
            *stop = YES;
        }
    }];
    
    if (findDownload == nil){
        PLVVodLogWarn(@"[download manager] -- 视频未处于下载状态 %@", videoParams.vid);
        return;
    }
    
    // 3 开启下载下一个视频
    // 失败/成功状态才会自动开启下一个视频的下载，这里需要手动调用
    [self startDownloadLoop];
}

// 停止下载队列，内部方法
- (void)stopDownloadLoop{
    
    if (!self.downloaderList.count) {
        PLVVodLogWarn(@"[download manager] -- 当前没有可停止的下载任务");
        return;
    }
    
    for (int i = 0; i < self.downloaderList.count; i++) {
        PLVVodDownloader *downloader = self.downloaderList[i];
        // TODO: 是否需要改为 != PLVVodDownloadStateSuccess
        if (downloader.downloadInfo.state == PLVVodDownloadStateRunning ||
            downloader.downloadInfo.state == PLVVodDownloadStateReady ||
            downloader.downloadInfo.state == PLVVodDownloadStatePreparing ||
            downloader.downloadInfo.state == PLVVodDownloadStatePreparingStart) {
            
            // ts 格式下载特殊处理，需要调用stopdown，因为task 任务创建耗时
            if (downloader.downloadInfo.state == PLVVodDownloadStatePreparing){
                if ([downloader isKindOfClass:[PLVVodHlsTsDownloader class]]){
                    [downloader stopDownload];
                }else {
                    downloader.downloadInfo.state = PLVVodDownloadStateStopped;
                }
            }
            else{
                [downloader stopDownload];
            }
        }
    }
}

// 移除下载任务
- (void)removeDownloadWithVid:(NSString *)vid error:(NSError **)error {
	for (int i = 0; i < self.downloaderList.count; i++) {
		PLVVodDownloader *downloader = self.downloaderList[i];
		if ([vid isEqualToString:downloader.vid]) {
			[self.downloaderList removeObjectAtIndex:i];
			PLVVodDownloadState state = downloader.state;
            [downloader stopDownload];
			if (state == PLVVodDownloadStateRunning) {
                [self startDownloadLoop];
			}
			break;
		}
	}
	for (int i = 0; i < self.downloadInfos.count; i++) {
		if ([vid isEqualToString:self.downloadInfos[i].video.vid] ||
            [vid isEqualToString:self.downloadInfos[i].vid]) {
			[self.downloadInfos removeObjectAtIndex:i];
			break;
		}
	}
	[self.class removeVideoWithVid:vid error:error];

	[[PLVVodDownloadInfoManager sharedManager] removeFromdatabseWithVid:vid];
}

// 移除指定的视频/音频下载任务
- (void)removeDownloadWithVideoParams:(PLVVodVideoParams *)videoParams error:(NSError **)error{
    for (int i = 0; i < self.downloaderList.count; i++) {
        PLVVodDownloader *downloader = self.downloaderList[i];
        if ([videoParams.vid isEqualToString:downloader.vid] && videoParams.fileType == downloader.downloadInfo.fileType) {
            [self.downloaderList removeObjectAtIndex:i];
            PLVVodDownloadState state = downloader.state;
            [downloader stopDownload];
            if (state == PLVVodDownloadStateRunning) {
                [self startDownloadLoop];
            }
            break;
        }
    }
    for (int i = 0; i < self.downloadInfos.count; i++) {
        PLVVodDownloadInfo *info = self.downloadInfos[i];
        if (([videoParams.vid isEqualToString:info.video.vid] || [videoParams.vid isEqualToString:info.vid])
            && videoParams.fileType == info.fileType)
        {
            [self.downloadInfos removeObjectAtIndex:i];
            break;
        }
    }
    
    [self.class removeVideoWithVideoParams:videoParams error:error];
    
    // 移除数据库记录
    [[PLVVodDownloadInfoManager sharedManager] removeFromdatabseWithVideoParams:videoParams];
}


- (void)removeAllDownloadWithComplete:(void (^)(void *))completion{
    [self stopDownload];
    
    [self.downloaderList removeAllObjects];
    [self.taskDownloaderDic removeAllObjects];

    NSError *error;
    NSArray *array = [NSArray arrayWithArray:self.downloadInfos];
    for (PLVVodDownloadInfo *obj in array) {
        
        PLVVodVideoParams *params = [PLVVodVideoParams videoParamsWithVid:obj.vid fileType:obj.fileType];
        [self.class removeVideoWithVideoParams:params error:&error];
        [[PLVVodDownloadInfoManager sharedManager] removeFromdatabseWithVideoParams:params];
    }
   
    [self.downloadInfos removeAllObjects];
    
    // 2019-03-06 lincal
    // 原因：[self stopDownload]会将_isStopDownload置为YES，而使得后续客户调用downloadVideo时，内含的“判断autoStart为YES时，自动开始下载”逻辑失效
    // 解决：在所有操作完成之后，在回调之前（避免客户要在回调中，调用下载相关方法），重置_isStopDownload为NO
    _isStopDownload = NO;
    
    if (completion){
        completion(nil);
    }
}

#pragma mark file manage

/// 删除视频
+ (void)removeVideoWithVid:(NSString *)vid error:(NSError **)error {
	NSString *videoPoolId = videoPoolIdWithVid(vid);
	if (!videoPoolId.length) {
		PLVVodLogError(@"[download manager] -- vid错误");
		NSError *plvError = PLVVodErrorWithCode(video_not_found);
        if (error) *error = plvError;
		return;
	}
    
	NSString *dir = [[self sharedManager] downloadDir];
	NSArray *files = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:dir error:nil];
	for (NSString *file in files) {
		if ([file containsString:videoPoolId]) {
			NSString *pathToRemove = [dir stringByAppendingPathComponent:file];
			NSError *err = nil;
			[[NSFileManager defaultManager] removeItemAtPath:pathToRemove error:&err];
			if (err) {
				PLVVodLogError(@"[download manager] -- 视频移除错误，%@", err);
				NSError *plvError = PLVVodErrorMakeWithError(video_remove_error, err, ^(NSMutableDictionary *userInfo) {
					
				});
                if (error) *error = plvError;
			}
		}
	}
    
    // 移除数据库记录
    [[PLVVodDownloadInfoManager sharedManager] removeFromdatabseWithVid:vid];
}

/// 删除PPT
+ (void)removePPTWithVid:(NSString *)vid error:(NSError **)error{
    //
    if ([PLVVodUtil isNilString:vid]){
        PLVVodLogError(@"[download manager] -- 删除ppt本地文件，vid 参数不正确 %@", vid);
        return;
    }
    [PLVVodPPTMgr removePPTWithVid:vid error:error];
}

/// 删除音频
+ (void)removeAudioWithVid:(NSString *)vid error:(NSError **)error{
    NSString *videoPoolId = videoPoolIdWithVid(vid);
    if (!videoPoolId.length) {
        PLVVodLogError(@"[download manager] -- vid错误");
        NSError *plvError = PLVVodErrorWithCode(video_not_found);
        if (error) *error = plvError;
        return;
    }
    
    NSString *dir = [[[self sharedManager] downloadDir] stringByAppendingPathComponent:@"Audio"];
    NSArray *files = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:dir error:nil];
    for (NSString *file in files) {
        if ([file containsString:videoPoolId]) {
            NSString *pathToRemove = [dir stringByAppendingPathComponent:file];
            NSError *err = nil;
            [[NSFileManager defaultManager] removeItemAtPath:pathToRemove error:&err];
            if (err) {
                PLVVodLogError(@"[download manager] -- 音频移除错误，%@", err);
                NSError *plvError = PLVVodErrorMakeWithError(video_remove_error, err, ^(NSMutableDictionary *userInfo) {
                    
                });
                if (error) *error = plvError;
            }
        }
    }
    
    // 移除数据库记录
    PLVVodVideoParams *params = [PLVVodVideoParams videoParamsWithVid:vid fileType:PLVDownloadFileTypeAudio];
    [[PLVVodDownloadInfoManager sharedManager] removeFromdatabseWithVideoParams:params];
}

/// 删除指定视频/音频文件
+ (void)removeVideoWithVideoParams:(PLVVodVideoParams *)videoParams error:(NSError **)error{
    if (videoParams.fileType == PLVDownloadFileTypeVideo){
        [self.class removeVideoWithVid:videoParams.vid error:error];
    }
    else {
        [self.class removeAudioWithVid:videoParams.vid error:error];
    }
}

/// 删除所有视频文件
+ (void)removeAllVideoWithError:(NSError **)error {
	NSString *dir = [[self sharedManager] downloadDir];
	if (!dir.length || ![[NSFileManager defaultManager] fileExistsAtPath:dir]) {
		PLVVodLogError(@"[download manager] -- 下载目录不存在，%s", __FUNCTION__);
		NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
		NSError *plvError = PLVVodErrorMake(download_dir_not_found, ^(NSMutableDictionary *userInfo) {
			userInfo[PLVVodErrorSelectorKey] = selector;
			userInfo[NSFilePathErrorKey] = dir;
		});
        if (error) *error = plvError;
		return;
	}
	NSError *err = nil;
	[[NSFileManager defaultManager] removeItemAtPath:dir error:&err];
	if (err) {
		PLVVodLogError(@"[download manager] -- 视频移除错误%@", err);
		NSError *plvError = PLVVodErrorMakeWithError(video_remove_error, err, ^(NSMutableDictionary *userInfo) {
			
		});
        if (error) *error = plvError;
		return;
	}
	[PLVVodUtil createDirIfNeed:dir error:nil];
    
    // 移除数据库记录20191016lh
    [[PLVVodDownloadInfoManager sharedManager] removeAllFromDatabase];
}

/// 删除所有音频文件
+ (void)removeAllAudioWithError:(NSError **)error{
    NSString *dir = [[[self sharedManager] downloadDir] stringByAppendingPathComponent:@"Audio"];
    if (!dir.length || ![[NSFileManager defaultManager] fileExistsAtPath:dir]) {
        PLVVodLogError(@"[download manager] -- 下载目录不存在，%s", __FUNCTION__);
        NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
        NSError *plvError = PLVVodErrorMake(download_dir_not_found, ^(NSMutableDictionary *userInfo) {
            userInfo[PLVVodErrorSelectorKey] = selector;
            userInfo[NSFilePathErrorKey] = dir;
        });
        if (error) *error = plvError;
        return;
    }
    NSError *err = nil;
    [[NSFileManager defaultManager] removeItemAtPath:dir error:&err];
    if (err) {
        PLVVodLogError(@"[download manager] -- 音频全部移除错误%@", err);
        NSError *plvError = PLVVodErrorMakeWithError(video_remove_error, err, ^(NSMutableDictionary *userInfo) {
            
        });
        if (error) *error = plvError;
        return;
    }
    [PLVVodUtil createDirIfNeed:dir error:nil];
    
    // 移除数据库记录20191016lh
    // TODO: 需要删除音频表
//    [[PLVVodDownloadInfoManager sharedManager] removeAllFromDatabase];
}

/// 已下载的本地视频
- (NSArray<PLVVodLocalVideo *> *)localVideos {
	return [PLVVodLocalVideo localVideosWithDir:self.downloadDir];
}

/// 指定vid视频是否已下载，返回指定 vid 的清晰度，返回0则不存在该视频
+ (PLVVodQuality)videoExist:(NSString *)vid {
	PLVVodDownloadManager *downloadManager = [self sharedManager];
	NSString *dir = downloadManager.downloadDir;
	if (!dir.length || ![[NSFileManager defaultManager] fileExistsAtPath:dir]) {
        PLVVodLogDebug(@"[download manager] -- %s -- 下载目录还未创建", __FUNCTION__);
		return 0;
	}
    
	PLVVodLocalVideo *localVideo = [PLVVodLocalVideo localVideoWithVid:vid dir:dir];
    if ([PLVVodUtil isNilString:localVideo.path]){
        return 0;
    }
    
	return localVideo.quality;
}

/// 指定vid 的音频是否已经下载
+ (BOOL)audioExist:(NSString *)vid{
    
    BOOL isExist = NO;
    PLVVodDownloadManager *downloadManager = [self sharedManager];
    NSString *dir = downloadManager.downloadDir;
    if (!dir.length || ![[NSFileManager defaultManager] fileExistsAtPath:dir]) {
        
        // del by libl [无需报错，下载目录可能还未创建] 19-05-09 start
        PLVVodLogWarn(@"[download manager] -- 下载目录不存在，%s", __FUNCTION__);
        return isExist;
    }
    
    PLVVodLocalVideo *localAudio = [PLVVodLocalVideo localAudioWithVid:vid dir:dir];
    if (localAudio){
        isExist = YES;
        PLVVodLogDebug(@"本地音频路径: %@", localAudio.audioPath);
    }
    
    return isExist;
}

///  兼容 1.x.x 版本的离线视频
- (void)compatibleWithPreviousVideos {
	[PLVVodUtil compatibleWithPreviousDownloadDir:self.downloadDir];
}

#pragma mark - session delegate alias

#pragma mark NSURLSessionDownloadDelegate

- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didWriteData:(int64_t)bytesWritten totalBytesWritten:(int64_t)totalBytesWritten totalBytesExpectedToWrite:(int64_t)totalBytesExpectedToWrite {
	PLVVodDownloader *downloader = [self downloaderForTask:downloadTask];
	if (downloader) {
		[downloader URLSession:session downloadTask:downloadTask didWriteData:bytesWritten totalBytesWritten:totalBytesWritten totalBytesExpectedToWrite:totalBytesExpectedToWrite];
	}
}

- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didResumeAtOffset:(int64_t)fileOffset expectedTotalBytes:(int64_t)expectedTotalBytes {
	PLVVodDownloader *downloader = [self downloaderForTask:downloadTask];
	if (downloader) {
		[downloader URLSession:session downloadTask:downloadTask didResumeAtOffset:fileOffset expectedTotalBytes:expectedTotalBytes];
	}
}

- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didFinishDownloadingToURL:(NSURL *)location {
	PLVVodDownloader *downloader = [self downloaderForTask:downloadTask];
	if (downloader) {
		[downloader URLSession:session downloadTask:downloadTask didFinishDownloadingToURL:location];
	}
    else{
//        PLVVodLogDebug(@"-------- can't find download: lost taskid = %ld  func:%@", downloadTask.taskIdentifier, NSStringFromSelector(_cmd));
//        [self moveFileInLauchMode:downloadTask hasDownloadingToPath:location];
    }
}

#pragma mark NSURLSessionDataDelegate

- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveResponse:(NSURLResponse *)response completionHandler:(void (^)(NSURLSessionResponseDisposition disposition))completionHandler {
	PLVVodDownloader *downloader = [self downloaderForTask:dataTask];
	if (downloader) {
		[downloader URLSession:session dataTask:dataTask didReceiveResponse:response completionHandler:completionHandler];
	}
    else{
    }
}

- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveData:(NSData *)data {
	PLVVodDownloader *downloader = [self downloaderForTask:dataTask];
	if (downloader) {
		[downloader URLSession:session dataTask:dataTask didReceiveData:data];
	}
    else{
    }
}

#pragma mark NSURLSessionTaskDelegate

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(NSError *)error {
	PLVVodDownloader *downloader = [self downloaderForTask:task];
	if (downloader) {
		[downloader URLSession:session task:task didCompleteWithError:error];
		[self removeDownloaderForTask:task];
	}
    else{
//        PLVVodLogInfo(@"-------- can't find download: lost taskid = %ld  func:%@", task.taskIdentifier, NSStringFromSelector(_cmd));
    }
}

// 实现则需完整处理逻辑
//- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didReceiveChallenge:(NSURLAuthenticationChallenge *)challenge completionHandler:(void (^)(NSURLSessionAuthChallengeDisposition, NSURLCredential * _Nullable))completionHandler {
//	PLVVodDownloader *downloader = [self downloaderForTask:task];
//	if (downloader) {
//		//[downloader URLSession:session task:task didReceiveChallenge:challenge completionHandler:completionHandler];
//	}
//}

#pragma mark - background download delegate

- (void)URLSession:(NSURLSession *)session didBecomeInvalidWithError:(NSError *)error{
    PLVVodLogInfo(@"[download manager] -- didBecomeInvalidWithError");
}

- (void)URLSessionDidFinishEventsForBackgroundURLSession:(NSURLSession *)session {
    PLVVodLogInfo(@"[download manager] -- %@", NSStringFromSelector(_cmd));
    
    NSUInteger delayTime = 3; // 前台执行耗时操作，切换到后台，争取时间在后台执行任务
    if (self.isAppBackground){
        // 这个根据具体情况再调节
        // 文件解压属于同步操作，这里减为15s
        delayTime = 15;
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (self.backgroundCompletionHandler) {
            void (^completionHandler)(void) = self.backgroundCompletionHandler;
            self.backgroundCompletionHandler = nil;
            
            completionHandler();
            
            PLVVodLogInfo(@"[download manager] -- 后台下载完成");
        }
    });
}

#pragma mark -- UIApplication Terminate
- (void)applicationWillTerminate{
}

- (void)applicationWillTerminateInternal{
    PLVVodLogInfo(@"[download manager] -- %@", NSStringFromSelector(_cmd));
    [self updateDownloadListToDatabaseWhenTerminate];
    
    [self stopDownloadWhenTerminate];
    
    [self removeNotifications];
}

- (void)applicationWillEnterForeground{
}

- (void)applicationWillEnterForegroundInternal{
    PLVVodLogInfo(@"[download manager] -- %@", NSStringFromSelector(_cmd));

    _isAppBackground = NO;
}

- (void)applicationDidEnterBackground{
}

- (void)applicationDidEnterBackgroundInternal{
    PLVVodLogInfo(@"[download manager] -- %@", NSStringFromSelector(_cmd));
    _isAppBackground = YES;
}

- (void)handleEventsForBackgroundURLSession:(NSString *)identifier completionHandler:(void (^)(void))completionHandler{
    if ([identifier isEqualToString:PLVBGDownloadSessionId]){
        self.backgroundCompletionHandler = completionHandler;
        
        // 如果app退出后被wake 的场景，需要重建session
        if (!self.session){
            self.session = [self createSession];
        }
    }
}

// 多账号下载时，切换下载帐号
- (void)switchDownloadAccount:(NSString *)accountId{
    if (!self.isMultiAccount){
        //
        PLVVodLogWarn(@"%s -- 不支持多账号下载模式", __FUNCTION__);
        return;
    }
    
    // 参数检查
    if ([PLVVodUtil isNilString:accountId]){
        PLVVodLogError(@"%s -- 帐号id不能为空",__FUNCTION__);
        return;
    }
    
    if ([self.accountId isEqualToString:accountId]){
        PLVVodLogWarn(@"%s -- 同一个帐号，暂不切换", __FUNCTION__);
        return;
    }
    
    // 清除当前视频缓存信息
    // 停止下载
    [self stopDownload];
    
    // 清理内存缓存数据
    [self clearMemoryCache];
    
    // 延时切换
    int delayTime = 0.5;
    if (self.downloaderList.count){
        delayTime = 2.0;
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        
        // TODO: 数据库操作是否会有问题？停止下载过程中，数据库不能得到有效更新？
        // 是否需要多个数据库实例 ？
        
        self.accountId = accountId;

        // 切换视频文件存储路径
        [self switchDownloadDirWithAccountId:accountId];
        
        // 切换数据库路径
        [[PLVVodDownloadInfoManager sharedManager] switchDbWithAccountId:accountId];
        
        // 如果是2.19.0 版本以下升级上来，加密视频需要做解密token 的迁移
        NSString *secretKey = [PLVVodSettings sharedSettings].secretkey;
        if (![PLVVodUtil isNilString:secretKey]){
            [self migrateLocalVideoPlaylist:secretKey];
        }
    });
}

// 切换存储路径
- (void)switchDownloadDirWithAccountId:(NSString *)accountId{
    //
    if (!accountId.length) {
        PLVVodLogError(@"%s - 参数为空", __FUNCTION__);
        NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
        NSError *plvError = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
            userInfo[PLVVodErrorSelectorKey] = selector;
        });
        [self reportError:plvError video:nil];
    }
    
    NSString *downloadDir = [PLVFileUtils getVideoDirectoryWithAccoutId:accountId];
    _downloadDir = downloadDir;
    
    if ([accountId isEqualToString:PLVMultiAccountIdDefault]){
        // 多账号体系下的默认账户，无需迁移单帐号数据
        [PLVVodUtil createDirIfNeed:self.downloadDir error:nil];

        return;
    }
    
    // 是否需要将单账户的视频迁移到第一个帐号。
    // 1 当前帐号下载目录已经创建，说明已经迁移过
    // /Documents/PolyvCache_AccountId/VideoCache
    if (![[NSFileManager defaultManager] fileExistsAtPath:_downloadDir])
    {
        // 创建/Documents/PolyvCache_AccountId 目录
        NSString *cacheDir = [PLVFileUtils getCacheDirectoryWithAccountId:accountId];
        [PLVVodUtil createDirIfNeed:cacheDir error:nil];

        if (self.previousDownloadDir != nil && [[NSFileManager defaultManager] fileExistsAtPath:self.previousDownloadDir]){
            // 已缓存视频迁移
            NSError *error = nil;
            [[NSFileManager defaultManager] moveItemAtPath:self.previousDownloadDir toPath:self.downloadDir error:&error];
            if (!error){
                PLVVodLogDebug(@"%s -- 视频迁移成功", __FUNCTION__);
            }
            else{
                PLVVodLogError(@"%s -- 视频迁移失败", __FUNCTION__);
            }
        }
        else{
            // 新用户，或着已经迁移过，无需再迁移
            [PLVVodUtil createDirIfNeed:self.downloadDir error:nil];
        }
    }
}

// 设置多账号开关，并初始化默认账户
- (void)setIsMultiAccount:(BOOL)isMultiAccount{
    _isMultiAccount = isMultiAccount;
    
    if (isMultiAccount){
        // 设置多账号体系下的默认用户下载目录
        self.accountId = PLVMultiAccountIdDefault;
        
        // 切换视频文件存储路径
        [self switchDownloadDirWithAccountId:self.accountId];
        
        // 切换数据库路径
        [[PLVVodDownloadInfoManager sharedManager] switchDbWithAccountId:self.accountId];
    }
}

// 登出当前下载帐号
- (void)logoutMultiAccount{
    if (!self.isMultiAccount){
        //
        PLVVodLogWarn(@"%s -- 不支持多账号下载模式", __FUNCTION__);
        return;
    }
    
    // 停止下载
    [self stopDownload];
    
    // 清理内存缓存数据
    [self clearMemoryCache];
    
    // 延时切换
    int delayTime = 0.5;
    if (self.downloaderList.count){
        delayTime = 2.0;
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        
        // 切换到默认帐号
        // TODO: 数据库操作是否会有问题？停止下载过程中，数据库不能得到有效更新？
        // 是否需要多个数据库实例 ？
        [self switchDownloadAccount:PLVMultiAccountIdDefault];
    });
}

// 清理当前帐号缓存数据
- (void)clearMemoryCache{
    
    //
    [self.downloaderList removeAllObjects];
    [self.downloadInfos removeAllObjects];
    [self.taskDownloaderDic removeAllObjects];
    [self.runingQueue removeAllObjects];
}

- (NSArray *)migrateLocalVideoPlaylist:(NSString*)secretKey {
    NSArray *array;
    NSString *successMigrateKey = kHasMigrateCacheVideoWithSecretKey;
    // 支持多账号迁移
    if (self.isMultiAccount && ![PLVVodUtil isNilString:self.accountId]){
        successMigrateKey = [NSString stringWithFormat:@"%@_%@",successMigrateKey, self.accountId];
    }
    BOOL hasMigrate = [[NSUserDefaults standardUserDefaults] objectForKey:successMigrateKey];
    if (hasMigrate){
        return array;
    }
    else{
        array = [PLVVodLocalVideo migrateLocalVideoPlaylist:self.downloadDir secretKey:secretKey];
        if (0 == array.count){
            return array;
        }
        // 迁移完成
        [[NSUserDefaults standardUserDefaults] setBool:YES forKey:successMigrateKey];
        [[NSUserDefaults standardUserDefaults] synchronize];
        
        NSString *videoVid = [array objectAtIndex:0];
        // 上报elog
        if (![PLVVodUtil isNilString:videoVid]){
            PLVVodElogModel *logModel = [[PLVVodElogModel alloc] init];
            logModel.userId2 = [PLVVodSettings sharedSettings].userid;
            logModel.module = PLV_Busi_Download_Module;
            logModel.event = @"localVideoMigrate2.19";
            logModel.vid = videoVid;
            // logFile 设置
            PLVVodElogLogfileModel *logfileModel = [[PLVVodElogLogfileModel alloc] init];
            logfileModel.infomation = [array componentsJoinedByString:@","];
            logModel.logFile = logfileModel;
            
            [PLVVodReportManager reportElogWithElogModel:logModel completion:^(NSError *error) {
                if (error == nil){
                    NSLog(@"视频迁移日志上报成功");
                }
            }];
        }
    }
    
    return array;
}

@end
