//
//  PLVVodPPTMgr.m
//  _PolyvVodSDK
//
//  Created by mac on 2019/8/16.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import "PLVVodPPTMgr.h"
#import "PLVVodAttachMgr.h"
#import "PLVVodDownloader.h"
#import "PLVKVOController.h"
#import "PLVVodUtil.h"
#import "PLVVodPPT.h"

const NSInteger PLVPPTBackgroundSessionMaxConnection = 3;
const NSTimeInterval PLVPPTBackgroundSessionResourceTimeout = 1200*3*3; // 请求超时3小时
const NSTimeInterval PLVPPTBackgroundSessionRequestTimeout = 30;
static NSString * const PLVPPTBGDownloadSessionId = @"net.polyv.sdk.download.bgsession.ppt";
static NSString * const PLVPPTURLSessionManagerLockName = @"net.polyv.networking.session.manager.lock.ppt";


static PLVVodPPTMgr *instance = nil;

@interface PLVVodPPTMgr()<NSURLSessionDownloadDelegate>

@property (nonatomic, strong) NSURLSession *session;
@property (nonatomic, strong) NSMutableArray<PLVVodDownloader *> *downloaderList;
@property (nonatomic, strong) NSMutableArray<PLVVodDownloader *> *runingQueue;
@property (nonatomic, strong) NSMutableArray<PLVVodDownloadInfo *> *downloadInfos;
@property (nonatomic, strong) NSMutableDictionary<NSNumber *, PLVVodDownloader *> *taskDownloaderDic;

@property (nonatomic, strong) NSLock *lock;
@property (nonatomic, strong) PLVKVOController *KVOController;


@end

@implementation PLVVodPPTMgr

+ (instancetype)shareManager{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[PLVVodPPTMgr alloc] init];
        [instance commonInit];
    });
    
    return instance;
}

- (NSURLSession *)session{
    if (!_session){
        _session = [self backgroundSession];
    }
    
    return _session;
}

- (NSMutableArray<PLVVodDownloader *> *)downloaderList{
    if (!_downloaderList){
        _downloaderList = [NSMutableArray arrayWithCapacity:0];
    }
    
    return _downloaderList;
}

- (NSMutableArray<PLVVodDownloadInfo *> *)downloadInfos{
    if (!_downloadInfos){
        _downloadInfos = [NSMutableArray arrayWithCapacity:0];
    }
    
    return _downloadInfos;
}

- (NSMutableDictionary<NSNumber *, PLVVodDownloader *> *)taskDownloaderDic{
    if (!_taskDownloaderDic){
        _taskDownloaderDic = [[NSMutableDictionary alloc] init];
    }
    
    return _taskDownloaderDic;
}

- (PLVKVOController *)KVOController{
    if (!_KVOController){
        _KVOController = [[PLVKVOController alloc] initWithObserver:self retainObserved:YES];
    }
    return _KVOController;
}

- (NSMutableArray<PLVVodDownloader *> *)runingQueue{
    if (!_runingQueue){
        _runingQueue = [NSMutableArray arrayWithCapacity:0];
    }
    return _runingQueue;
}

/// 初始化
- (void)commonInit{
    //
    _lock = [[NSLock alloc] init];
    _lock.name = PLVPPTURLSessionManagerLockName;
}

/// 后台会话
- (NSURLSession *)backgroundSession {
    NSString *bgsessionID = PLVPPTBGDownloadSessionId;
    NSURLSessionConfiguration *config = [NSURLSessionConfiguration backgroundSessionConfigurationWithIdentifier:bgsessionID];
    config.HTTPMaximumConnectionsPerHost = PLVPPTBackgroundSessionMaxConnection;    //一个host最大并发连接数
    config.timeoutIntervalForResource = PLVPPTBackgroundSessionResourceTimeout;     //限制整个资源的请求时长
    config.timeoutIntervalForRequest = PLVPPTBackgroundSessionRequestTimeout;       //每次有新的data请求到会被重置
//    config.allowsCellularAccess = self.allowsCellularAccess;
    config.discretionary = NO;
    config.sessionSendsLaunchEvents = YES;
    
    NSOperationQueue *sessionQueue = [[NSOperationQueue alloc] init];
    sessionQueue.maxConcurrentOperationCount = 1;
//    self.sessionCallbackQueue = sessionQueue;
    return [NSURLSession sessionWithConfiguration:config delegate:self delegateQueue:sessionQueue];
}

#pragma mark -- public
- (void)downloadPPTWithVideo:(PLVVodVideo *)video completion:(void (^)(PLVVodDownloadInfo *))completion{
    if (video.hasPPT){
        // 是否已经加入队列
        for (PLVVodDownloadInfo *info in self.downloadInfos) {
            if ([info.vid isEqualToString:video.vid]) {
                PLVVodLogWarn(@"[ppt download] %@ ，重复加入队列", video.vid);
                if (completion){
                    completion (info);
                }
                return;
            }
        }
        
        // 是否已经下载成功
        if ([PLVVodPPT isExistWithVid:video.vid]){
            PLVVodLogInfo(@"[ppt download] -- 已经下载成功 %@", video.vid);
            if (completion){
                completion (nil);
            }
            return;
        }
        
        [PLVVodAttachMgr getPPTZipUrl:video.vid completion:^(NSString * pptUrl) {
            if (pptUrl){
                [video setValue:pptUrl forKey:@"ppt_link"];
                
                PLVVodDownloadInfo *info = [self downloadPPT:video];
                if (info && completion){
                    completion (info);
                }
                else if (completion){
                    completion (nil);
                }
            }
        }];
    }
    else{
        PLVVodLogInfo(@"[ppt download] -- 不支持ppt课件")
        if (completion){
            completion (nil);
        }
    }
}

/// PPT 下载
- (PLVVodDownloadInfo *)downloadPPT:(PLVVodVideo *)video{
    if (!video) {
        PLVVodLogError(@"%s - 参数为空", __FUNCTION__);
        return nil;
    }
    
    if (!video.available) {
//        NSError *plvError = video.error ? video.error : PLVVodErrorWithCode(account_video_illegal);
//        [self reportError:plvError video:video];
        return nil;
    }
    
    PLVVodDownloader *downloader = [PLVVodDownloader pptDownloaderWithVideo:video];
    
    if (!downloader) {
        PLVVodLogError(@"[download] -- PPT下载器创建失败");
//        NSError *plvError = PLVVodErrorWithCode(downloader_create_error);
//        [self reportError:plvError video:video];
        return nil;
    }
    
    downloader.session = self.session;
    
    [self.downloaderList addObject:downloader];
    [self configDownloadBlock:downloader];
    
    PLVVodDownloadInfo *downloadInfo = downloader.downloadInfo;
    if (downloadInfo) {
        downloadInfo.downloadId = self.downloadInfos.lastObject.downloadId + 1;
        [self.downloadInfos addObject:downloadInfo];
    }
    
    return downloadInfo;
}

/// 删除ppt
+ (void)removePPTWithVid:(NSString *)vid error:(NSError **)error{
    if ([PLVVodUtil isNilString:vid]){
        PLVVodLogError(@"[ppt] -- 删除ppt本地文件，vid 参数不正确 %@", vid);
        return;
    }
    
    NSString *pptDir = [PLVVodAttachMgr getLocalPPTPathWithVid:vid];
    if (pptDir.length && [[NSFileManager defaultManager] fileExistsAtPath:pptDir]){
        // 删除目录下所有文件
        NSArray *pptArr = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:pptDir error:error];
        [pptArr enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            PLVVodLogDebug(@"[ppt] -- file list %@", obj);
        }];
        
        [[NSFileManager defaultManager] removeItemAtPath:pptDir error:error];
        
        if (![[NSFileManager defaultManager] fileExistsAtPath:pptDir]){
            PLVVodLogInfo(@"[ppt] -- ppt 删除成功 %@:", pptDir);
        }
    }
}


#pragma -- private
// 配置下载回调，监听事件，并开始下载
- (void)configDownloadBlock:(PLVVodDownloader *)downloaderObj{
    if ([self hasReachMaxRuning]){
        return;
    }
    
    PLVVodDownloader *downloader = downloaderObj;
    PLVVodDownloadInfo *info = downloader.downloadInfo;
    
    __weak typeof(self) weakSelf = self;
    [self.KVOController observe:info keyPath:@"state" options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSKeyValueChangeKey,id> * _Nonnull change) {
        PLVVodDownloadInfo *info = object;
        
        if (info.state == PLVVodDownloadStateSuccess) {
            // 从队列中移除
            [weakSelf.downloaderList removeObject:downloader];
            [weakSelf.downloadInfos removeObject:downloader.downloadInfo];
            [weakSelf.runingQueue removeObject:downloader];
            
            if (weakSelf.downloaderList.count) {
                PLVVodLogDebug(@"[ppt download] -- ppt 下载完成 %@", info.vid);
                [weakSelf startDownloadLoop];
            } else {
                // 若数组为空，则下载完成
                PLVVodLogDebug(@"[ppt download] -- 所有ppt下载完成");
                if (self.taskDownloaderDic.count){
                    [self.taskDownloaderDic enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
                        PLVVodLogDebug(@"[ppt download] -- taskId %@ :: downloader: %@", key, obj);
                    }];
                    [self.taskDownloaderDic removeAllObjects];
                }
            }
        }
        else if (info.state == PLVVodDownloadStateFailed){
            PLVVodLogDebug(@"[ppt download] -- 下载失败");

            [weakSelf.downloaderList removeObject:downloader];
            // 已经下载过，添加到队尾
            [weakSelf.downloaderList addObject:downloader];
            
            [weakSelf.runingQueue removeObject:downloader];
            
            [weakSelf startDownloadNextIfFailed];
        }
        else if (info.state == PLVVodDownloadStateStopping || info.state == PLVVodDownloadStateStopped){
            PLVVodLogDebug(@"[ppt download] -- 停止下载： %zd", info.state);
            [weakSelf.runingQueue removeObject:downloader];
        }
    }];
    
    // 建立download 下载器与 ts task 的关联，用户代理回调
    [self.KVOController observe:downloader keyPath:@"downloadTasks" options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSKeyValueChangeKey,id> * _Nonnull change) {
        PLVVodDownloader *downloader = object;
        if (!downloader.downloadTasks.count) return;
        for (NSURLSessionTask *task in downloader.downloadTasks) {
            [weakSelf setDownloader:downloader forTask:task];
        }
    }];
    
    // 开始下载
    [downloader startDownload];
    
    [self.runingQueue addObject:downloader];
    PLVVodLogDebug(@"[ppt download] -- 下载中任务数: %d", (int)self.runingQueue.count);
}

- (BOOL)hasReachMaxRuning{
    BOOL reachMax = NO;
    if (self.runingQueue.count >= 10){
        reachMax = YES;
    }
    
    return reachMax;
}

// 开始下载队列，内部方法
- (void)startDownloadLoop{
//    if (self.isStopDownload) return;
    
    if (!self.downloaderList.count) {
        PLVVodLogWarn(@"[ppt download] -- 当前没有可启动的下载任务");
        return;
    }
    
    if ([self hasReachMaxRuning]){
        PLVVodLogWarn(@"[ppt download] -- 已经达到最大并发数");
        return;
    }
    
    [self.downloaderList enumerateObjectsUsingBlock:^(PLVVodDownloader * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (obj.downloadInfo.state == PLVVodDownloadStatePreparing){
            if ([self hasReachMaxRuning]){
                *stop = YES;
            }
            [self configDownloadBlock:obj];
        }
    }];
}

// 当前下载失败后，自动开启下一个视频下载
- (void)startDownloadNextIfFailed{
//    if (self.isStopDownload) return;
    
    // 什么条件开启？防止死循环下载
    // 1 出错后，会添加到队尾
    // 2 如果所有下载器都成功或者失败，不再下载
    // 3 对于初始化就为fail 的下载器，正常流程还是可以得到机会下载
    [self.downloaderList enumerateObjectsUsingBlock:^(PLVVodDownloader * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        
        if (obj.state < PLVVodDownloadStateSuccess){
            
            PLVVodLogInfo(@"[ppt download] -- 下载失败，开启下一个ppt 下载 : %@", obj.downloadInfo.vid);
            [self startDownloadLoop];
        }
        
        *stop = YES;
    }];
}

#pragma mark download
- (PLVVodDownloader *)downloaderForTask:(NSURLSessionTask *)task {
    NSParameterAssert(task);
    
    PLVVodDownloader *downloader = nil;
    [self.lock lock];
    downloader = self.taskDownloaderDic[@(task.taskIdentifier)];
    [self.lock unlock];
    
    return downloader;
}

- (void)setDownloader:(PLVVodDownloader *)downloader forTask:(NSURLSessionTask *)task {
    NSParameterAssert(task);
    NSParameterAssert(downloader);
    [self.lock lock];
    self.taskDownloaderDic[@(task.taskIdentifier)] = downloader;
    [self.lock unlock];
}

- (void)removeDownloaderForTask:(NSURLSessionTask *)task {
    NSParameterAssert(task);
    
    [self.lock lock];
    [self.taskDownloaderDic removeObjectForKey:@(task.taskIdentifier)];
    [self.lock unlock];
}

#pragma mark NSURLSessionDownloadDelegate
- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didWriteData:(int64_t)bytesWritten totalBytesWritten:(int64_t)totalBytesWritten totalBytesExpectedToWrite:(int64_t)totalBytesExpectedToWrite {
    PLVVodDownloader *downloader = [self downloaderForTask:downloadTask];
    if (downloader) {
        [downloader URLSession:session downloadTask:downloadTask didWriteData:bytesWritten totalBytesWritten:totalBytesWritten totalBytesExpectedToWrite:totalBytesExpectedToWrite];
    }
}

- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didResumeAtOffset:(int64_t)fileOffset expectedTotalBytes:(int64_t)expectedTotalBytes {
    PLVVodDownloader *downloader = [self downloaderForTask:downloadTask];
    if (downloader) {
        [downloader URLSession:session downloadTask:downloadTask didResumeAtOffset:fileOffset expectedTotalBytes:expectedTotalBytes];
    }
}

- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didFinishDownloadingToURL:(NSURL *)location {
    PLVVodDownloader *downloader = [self downloaderForTask:downloadTask];
    if (downloader) {
        [downloader URLSession:session downloadTask:downloadTask didFinishDownloadingToURL:location];
    }
}

#pragma mark NSURLSessionDataDelegate
- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveResponse:(NSURLResponse *)response completionHandler:(void (^)(NSURLSessionResponseDisposition disposition))completionHandler {
    PLVVodDownloader *downloader = [self downloaderForTask:dataTask];
    if (downloader) {
        [downloader URLSession:session dataTask:dataTask didReceiveResponse:response completionHandler:completionHandler];
    }
}

- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveData:(NSData *)data {
    PLVVodDownloader *downloader = [self downloaderForTask:dataTask];
    if (downloader) {
        [downloader URLSession:session dataTask:dataTask didReceiveData:data];
    }
}



@end
