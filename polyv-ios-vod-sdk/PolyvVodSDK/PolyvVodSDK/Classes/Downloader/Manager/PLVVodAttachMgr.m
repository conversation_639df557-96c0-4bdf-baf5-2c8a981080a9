//
//  PLVVodAttachMgr.m
//  DES: 附件（字幕，问答）下载管理
//
//  Created by mac on 2019/8/2.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import "PLVVodAttachMgr.h"
#import "PLVVodDownloadManager.h"
#import "PLVVodUtil.h"
#import "PLVVodNetworking.h"

@implementation PLVVodAttachMgr

+ (void)downloadSubtitlesWithVideo:(PLVVodVideo *)video{
    // 参数检查
    if (!video || [PLVVodUtil isNilString:video.vid]){
        PLVVodLogWarn(@"[download] -- video 参数为nil");
        return;
    }
    
    // 下载字幕
    if (video.srts.count){
        NSString *vid = video.vid;
        NSString *downloadDir = [PLVVodDownloadManager sharedManager].downloadDir;
        NSString *subtitleDir = [[downloadDir stringByAppendingPathComponent:PLVSubtitleDir] stringByAppendingPathComponent:vid];
        [PLVVodUtil createDirIfNeed:subtitleDir error:nil];
        
        [video.srts enumerateObjectsUsingBlock:^(PLVVodVideoSubtitleItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            //
            NSString *rtsUrl = obj.url;
            // 保存文件
            NSString *fileName = [rtsUrl lastPathComponent];
            NSString *filePath = [subtitleDir stringByAppendingPathComponent:fileName];
            if (![[NSFileManager defaultManager] fileExistsAtPath:filePath]){
                [PLVVodNetworking requestSubtitleWithUrl:rtsUrl completion:^(NSString *subtitle, NSError *error) {
                    NSError *writeErr = nil;
                    [subtitle writeToFile:filePath atomically:YES encoding:NSUTF8StringEncoding error:&writeErr];
                    if (!writeErr){
                        PLVVodLogDebug(@"[download] -- 字幕文件路径: %@", filePath);
                    }
                    else{
                        PLVVodLogError(@"[download] -- 字幕文件下载错误: %@", writeErr);
                    }
                }];
            }
        }];
    }
}

+ (void)downloadExamsWithVideo:(PLVVodVideo *)video{
    // 参数检查
    if (!video || [PLVVodUtil isNilString:video.vid]){
        PLVVodLogWarn(@"[download] -- video 参数为nil");
        return;
    }
    // add by libl 添加问答功能
    if (video.interactive){
        NSString *vid = video.vid;
        NSString *rootDownloadDir = [PLVVodDownloadManager sharedManager].downloadDir;
        NSString *examsDir = [[rootDownloadDir stringByAppendingPathComponent:PLVExamsDir] stringByAppendingPathComponent:vid];
        [PLVVodUtil createDirIfNeed:examsDir error:nil];
        
        NSString *filePath = [examsDir stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.json", vid]];
        if (![[NSFileManager defaultManager] fileExistsAtPath:filePath]){
            [PLVVodNetworking requestExamDicWithVid:video.vid completion:^(NSArray *exams, NSError *error) {
                // 保存文件
                NSData *jsonData = [NSJSONSerialization dataWithJSONObject:exams options:NSJSONWritingPrettyPrinted error:nil];
                NSError *writeErr = nil;
                [jsonData writeToFile:filePath options:NSDataWritingAtomic error:&writeErr];
                if (!writeErr){
                    PLVVodLogDebug(@"[download] -- 问答文件下载路径: %@", filePath);
                }
                else{
                    PLVVodLogError(@"[download] -- 问答文件下载错误: %@", writeErr);
                }
            }];
        }
    }
}

/// ppt zip url
+ (void)getPPTZipUrl:(NSString *)videoId completion:(void (^)(NSString * ))completion{
    [PLVVodNetworking requestPPTZipUrlWithVid:videoId completion:^(NSString *zipUrl, NSError *error) {
        if (completion){
            completion (zipUrl);
        }
    }];
}

/// ppt json 本地文件路径
+ (NSString *)getLocalPPTJsonPathWithVid:(NSString *)vid{
    NSString *rootDir = [PLVVodDownloadManager sharedManager].downloadDir;
    NSString *pptDir = [[rootDir stringByAppendingPathComponent:PLVPPTDir] stringByAppendingPathComponent:vid];
    NSString *jsonFile = [pptDir stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.json", vid]];
    
    return jsonFile;
}

/// 获取某个视频PPT 文件存储路径
+ (NSString *)getLocalPPTPathWithVid:(NSString *)vid{
    NSString *rootDir = [PLVVodDownloadManager sharedManager].downloadDir;
    NSString *pptDir = [[rootDir stringByAppendingPathComponent:PLVPPTDir] stringByAppendingPathComponent:vid];
    
    return pptDir;
}

@end
