//
//  PLVVodAttachMgr.h
//  _PolyvVodSDK
//
//  Created by mac on 2019/8/2.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PLVVodVideo.h"

NS_ASSUME_NONNULL_BEGIN

@interface PLVVodAttachMgr : NSObject

/**
 字幕下载
 
 @param video videojson 解析模型
 */

+ (void)downloadSubtitlesWithVideo:(PLVVodVideo *)video;

/**
 问答下载
 
 @param video videojson 解析模型
 
 */
+ (void)downloadExamsWithVideo:(PLVVodVideo *)video;

/**
 ppt zip 包url获取
 
 */
+ (void)getPPTZipUrl:(NSString *)videoId completion:(void (^)(NSString *pptUrl))completion;

/**
 获取ppt json 本地文件路径
 */
+ (NSString *)getLocalPPTJsonPathWithVid:(NSString *)vid;

/**
 获取某个视频 ppt 文件存储路径
 */
+ (NSString *)getLocalPPTPathWithVid:(NSString *)vid;

@end

NS_ASSUME_NONNULL_END
