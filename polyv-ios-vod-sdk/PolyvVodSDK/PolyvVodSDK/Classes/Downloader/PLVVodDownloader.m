//
//  PLVVodDownloader.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/12.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodDownloader.h"
#import "PLVVodUtil.h"
#import "PLVTimer.h"
#import <UIKit/UIKit.h>
#import "PLVVodHlsTsDownloader.h"
#import "PLVVodHlsZipDownloader.h"
#import "PLVVodSimpleDownloader.h"
#import "PLVVodAudioDownloader.h"
#import "PLVVodPPTDownloader.h"
#import "PLVVodDownloadInfoManager.h"
#import "PLVVodTsOneByOneDownloader.h"

const double PLVDownloadSpeedRefreshInterval = 0.3;

@interface PLVVodDownloader ()

@end

@implementation PLVVodDownloader

- (void)dealloc{
}

+ (instancetype)downloaderWithVideo:(PLVVodVideo *)video quality:(PLVVodQuality)quality createTaskMode:(PLVTaskCreateMode)taskMode{
	if (!video) return nil;
	if (!video.available) {
		return nil;
	}
	PLVVodDownloader *downloader = nil;
	if (video.isHls) {
		if (video.keepSource) {
            // 源文件地址，用play_source_url 地址下载
			if (![PLVVodUtil isNilString:video.play_source_url] && ![@"m3u8" isEqualToString:video.play_source_url.pathExtension.lowercaseString]) {
                // 非hls视频
				return nil;
			}
			// 这种情况只有直播转存视频，只下一个码率
            if (PLVTaskCreateModeCreateOneResumeOne == taskMode){
                // 文都定制，解决直播回放下载不稳定问题 06.09
                PLVVodLogDebug(@"[download] 创建下载器，下载模式CreateOneResumeOne ，TS 片下载: %@ ", video.vid);
                downloader = [PLVVodTsOneByOneDownloader downloaderWithVideo:video quality:PLVVodQualityStandard];
            }
            else{
                PLVVodLogDebug(@"[download] 创建下载器，下载模式CreateAllResumeOne， TS 片下载: %@ ", video.vid);
                downloader = [PLVVodHlsTsDownloader downloaderWithVideo:video quality:PLVVodQualityStandard];
            }
		} else {
            // 加密hls视频
            PLVVodLogDebug(@"[download] 创建下载器，ZIP 下载: %@ ", video.vid);
            downloader = [PLVVodHlsZipDownloader downloaderWithVideo:video quality:quality];
		}
	} else {
        NSString *url = video.plainVideos[0];
        PLVVodLogDebug(@"[download] 创建下载器，MP4 下载: %@ ", url);
        downloader = [PLVVodSimpleDownloader downloaderWithVideo:video quality:quality];
	}
	return downloader;
}

// 接口定义，必须子类实现
+ (instancetype)downloaderWithVideo:(PLVVodVideo *)video quality:(PLVVodQuality)quality{
    PLVVodLogError(@"[downloader] -- 必须子类实现 %s", __FUNCTION__);
    return nil;
}

// 创建音频下载器
+ (instancetype)audioDownloaderWithVideo:(PLVVodVideo *)video{
    if (!video) return nil;
    if (!video.available) {
        return nil;
    }
    PLVVodDownloader *downloader = nil;
    downloader = [PLVVodAudioDownloader audioDownloaderWithVideo:video];
    return downloader;
}

// 创建PPT下载器
+ (instancetype)pptDownloaderWithVideo:(PLVVodVideo *)video{
    if (!video) return nil;
    if (!video.available) {
        return nil;
    }
    PLVVodDownloader *downloader = nil;
    downloader = [PLVVodPPTDownloader pptDownloaderWithVideo:video];
    return downloader;
}

#pragma mark - property

- (PLVKVOController *)KVOController {
	if (!_KVOController) {
		_KVOController = [PLVKVOController controllerWithObserver:self];
	}
	return _KVOController;
}
- (PLVKVOController *)KVOControllerNonRetaining {
	if (!_KVOControllerNonRetaining) {
		_KVOControllerNonRetaining = [[PLVKVOController alloc] initWithObserver:self retainObserved:NO];
	}
	return _KVOControllerNonRetaining;
}

#pragma mark 便利属性

- (NSString *)vid {
	return self.video.vid;
}
- (PLVVodVideo *)video {
	return self.downloadInfo.video;
}
- (PLVVodQuality)quality {
	return self.downloadInfo.quality;
}
- (PLVVodDownloadState)state {
	return self.downloadInfo.state;
}
- (void)setState:(PLVVodDownloadState)state {
	self.downloadInfo.state = state;
	if (state == PLVVodDownloadStateSuccess) {
		self.downloadInfo.progress = 1.0;
	}
}

- (NSString *)createDid{
    PLVVodDownloadInfo *info = [[PLVVodDownloadInfoManager sharedManager] downloadInfoWithVid:self.vid];
    if ([PLVVodUtil isNilString:info.did]){
        return [PLVVodUtil pid];
    }
    
    return info.did;
}

#pragma mark 业务属性

- (void)setDownloadDir:(NSString *)downloadDir {
	_downloadDir = downloadDir;
	[PLVVodUtil createDirIfNeed:downloadDir error:nil];
}

- (void)setDownloadInfo:(PLVVodDownloadInfo *)downloadInfo {
	_downloadInfo = downloadInfo;
    _downloadInfo.did = [self createDid];
	
	if (_downloadInfo) {
		__weak typeof(self) weakSelf = self;
		[self.KVOController observe:_downloadInfo keyPath:@"state" options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSKeyValueChangeKey,id> * _Nonnull change) {
			switch (weakSelf.downloadInfo.state) {
				case PLVVodDownloadStatePreparing:{
					
				}break;
				case PLVVodDownloadStateReady:{
					
				}break;
				case PLVVodDownloadStateRunning:{
					
				}break;
				case PLVVodDownloadStateStopping:{
					
				}break;
				case PLVVodDownloadStateStopped:{
					//[weakSelf setValue:@(YES) forKey:@"cancelled"];
				}break;
				case PLVVodDownloadStateSuccess:{
					//[weakSelf setValue:@(YES) forKey:@"finished"];
				}break;
				case PLVVodDownloadStateFailed:{
					//[weakSelf setValue:@(YES) forKey:@"finished"];
				}break;
				default:{}break;
			}
		}];
	}
}

#pragma mark - NSOperation

- (instancetype)init {
	if (self = [super init]) {
		self.downloadProgress = [[NSProgress alloc] initWithParent:nil userInfo:nil];
		__weak typeof(self) weakSelf = self;
		[self.KVOController observe:self.downloadProgress keyPath:@"fractionCompleted" options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSKeyValueChangeKey,id> * _Nonnull change) {
			NSProgress *progress = object;
			if (!progress) return;
            if (!weakSelf.downloadInfo) return;
			weakSelf.downloadInfo.progress = progress.fractionCompleted;
		}];
	}
	return self;
}

- (BOOL)isAsynchronous {
	return YES;
}

- (void)start {
	
}

- (void)cancel {
	
}

#pragma mark - public method

- (void)prepareWithCompletion:(void (^)(void))completion {
	self.state = PLVVodDownloadStatePreparing;
	if (self.prepared) {
		if (completion) completion();
		return;
	}
	self.prepared = YES;
	self.state = PLVVodDownloadStateReady;
	if (completion) completion();
	/// 实际逻辑在子类
}

- (void)startDownload {
	if (self.downloadInfo.state == PLVVodDownloadStateRunning) {
		PLVVodLogWarn(@"[downlaod] %@ 正在下载，退出", self.vid);
		return;
	}
	// 开启计时器
    self.speedTimer = [self createSpeedTimer];
	NSString *startTimerLog = [NSString stringWithFormat:@"开始计时 %@", self.video.vid];
	startTimerLog = nil;
	/// 实际逻辑在子类
}

- (void)stopDownload {
	self.state = PLVVodDownloadStateStopping;
	// 停止计时器
	[self stopSpeedTimer];
	/// 实际逻辑在子类
}

/// 统一汇报错误
- (void)reportError:(NSError *)error downloader:(PLVVodDownloader *)downloader {
    // add by libl [停止定时器] 2018-07-02 start
    [self stopSpeedTimer];
    // end
    
	self.downloadInfo.error = error;
	self.downloadInfo.state = PLVVodDownloadStateFailed;
	if (self.downloadErrorHandler) self.downloadErrorHandler(downloader, error);
}

#pragma mark - private method

/// 停止下载速度计算定时器
- (void)stopSpeedTimer {
	if (!self.speedTimer) return;
	[self.speedTimer cancel];
	self.speedTimer = nil;
	[self.downloadInfo setValue:@(0.0) forKey:@"bytesPerSeconds"];
}

/// 创建下载速度计算定时器
- (PLVTimer *)createSpeedTimer {
	__block NSDate *lastTime = [NSDate date];
	__block int64_t lastBytesAccumulated = 0;
	__weak typeof(self) weakSelf = self;
	self.bytesAccumulator = 0;
	return [PLVTimer repeatWithInterval:PLVDownloadSpeedRefreshInterval repeatBlock:^{
		dispatch_async(dispatch_get_main_queue(), ^{
            
            BOOL isBackground = [UIApplication sharedApplication].applicationState == UIApplicationStateBackground;
            if (isBackground) return;
			// 获取当前时间，与上一次时间做对比，大于1秒则计算速度
			NSTimeInterval timeDiff = [[NSDate date] timeIntervalSinceDate:lastTime];
			if (timeDiff >= 1){
				if (lastBytesAccumulated == weakSelf.bytesAccumulator){
					weakSelf.bytesAccumulator = 0;
					[weakSelf.downloadInfo setValue:@(0.0) forKey:@"bytesPerSeconds"];
				}
				double speed = 1.0 * weakSelf.bytesAccumulator / timeDiff;
				// 小于 1 k 则不汇报
				if (speed >= 1024) {
					[weakSelf.downloadInfo setValue:@(speed) forKey:@"bytesPerSeconds"];
				}
				// 维护变量
				weakSelf.bytesAccumulator = 0;
				lastTime = [NSDate date];
				lastBytesAccumulated = weakSelf.bytesAccumulator;
			}
		});
	}];
}

#pragma mark - tool method

- (NSString *)rootName {
	NSString *videoPoolId = videoPoolIdWithVid(self.video.vid);
	NSString *rootName = [NSString stringWithFormat:@"%@_%zd", videoPoolId, (long)self.quality];
	return rootName;
}

/// 对应的下载路径，兼容加密/非加密
- (NSString *)pathForURL:(NSURL *)URL {
	NSString *fileName = URL.lastPathComponent;
	NSString *path = [self.downloadDir stringByAppendingPathComponent:fileName];

	return path;
}

/// 指定URL对应的文件是否已下载
- (BOOL)fileExistForURL:(NSURL *)URL {
	NSString *path = [self pathForURL:URL];
	if (!path || !path.length) {
		return NO;
	}
	BOOL isDir = NO;
	BOOL exist = [[NSFileManager defaultManager] fileExistsAtPath:path isDirectory:&isDir];
	if (isDir) {
		PLVVodLogError(@"[download] -- 无法检索文件，目标存在同名目录");
		NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
		NSError *plvError = PLVVodErrorMake(target_file_is_dir, ^(NSMutableDictionary *userInfo) {
			userInfo[NSFilePathErrorKey] = path;
			userInfo[PLVVodErrorSelectorKey] = selector;
		});
		[self reportError:plvError downloader:self];
	}
	return exist && !isDir;
}

/// 创建下载任务
- (NSURLSessionDownloadTask *)downloadTaskWithURL:(NSURL *)URL error:(NSError **)error {
	return [self downloadTaskWithRequest:[NSMutableURLRequest requestWithURL:URL] error:error];
}

- (NSURLSessionDownloadTask *)downloadTaskWithRequest:(NSMutableURLRequest *)request error:(NSError **)error {
	// 创建 downloadTask
	__block NSURLSessionDownloadTask *downloadTask = nil;
	@try {
		//[PolyvSettings configUserAgentWithRequest:request];
		downloadTask = [self.session downloadTaskWithRequest:request];
	}
	@catch (NSException *exception) {
		[self cancelSession];
		// 会话创建任务出错
		PLVVodLogError(@"[download] -- %@ 下载任务创建失败，%@", request.URL.lastPathComponent, exception.reason);
		NSError *plvError = PLVVodErrorMake(download_task_create_error, ^(NSMutableDictionary *userInfo) {
			userInfo[NSURLErrorKey] = request.URL;
			userInfo[NSLocalizedFailureReasonErrorKey] = exception.reason;
		});
        if (error) *error = plvError;
		downloadTask = nil;
	}
	return downloadTask;
}

- (void)cancelSession {
	// 在此种设计中，似乎不需要销毁会话的操作
}

/// 增量请求
//- (NSMutableURLRequest *)risingRequestWithURL:(NSURL *)URL {
//	return [self risingRequestWithURL:URL currentSize:nil];
//}
//- (NSMutableURLRequest *)risingRequestWithURL:(NSURL *)URL currentSize:(NSInteger *)fileSize{
//	return [self.class risingRequestWithURL:URL localPath:[self pathForURL:URL] currentSize:fileSize];
//}

#pragma mark - subclass implementation

#pragma mark NSURLSessionDownloadDelegate
- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didResumeAtOffset:(int64_t)fileOffset expectedTotalBytes:(int64_t)expectedTotalBytes {
	PLVVodLogError(@"需由子类实现，%s - %@", __FUNCTION__, [NSThread currentThread]);
}

- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didWriteData:(int64_t)bytesWritten totalBytesWritten:(int64_t)totalBytesWritten totalBytesExpectedToWrite:(int64_t)totalBytesExpectedToWrite {
	PLVVodLogError(@"需由子类实现，%s - %@", __FUNCTION__, [NSThread currentThread]);
}

- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didFinishDownloadingToURL:(NSURL *)location {
	PLVVodLogError(@"需由子类实现，%s - %@", __FUNCTION__, [NSThread currentThread]);
}

#pragma mark NSURLSessionDataDelegate
- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveResponse:(NSURLResponse *)response completionHandler:(void (^)(NSURLSessionResponseDisposition disposition))completionHandler {
	PLVVodLogError(@"需由子类实现，%s - %@", __FUNCTION__, [NSThread currentThread]);
}

- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveData:(NSData *)data {
	PLVVodLogError(@"需由子类实现，%s - %@", __FUNCTION__, [NSThread currentThread]);
}

#pragma mark NSURLSessionTaskDelegate
- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(NSError *)error {
	PLVVodLogError(@"需由子类实现，%s - %@", __FUNCTION__, [NSThread currentThread]);
}

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didReceiveChallenge:(NSURLAuthenticationChallenge *)challenge completionHandler:(void (^)(NSURLSessionAuthChallengeDisposition, NSURLCredential *))completionHandler {
	PLVVodLogError(@"需由子类实现，%s - %@", __FUNCTION__, [NSThread currentThread]);
}

@end
