//
//  PLVVodTsDownloadTask.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/16.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodTsDownloadTask.h"

@implementation PLVVodTsDownloadTask

+ (instancetype)tsTaskWithURL:(NSURL *)URL{
	return [[self alloc] initWithURL:URL];
}

- (instancetype)initWithURL:(NSURL *)URL{
	if (self = [super init]) {
		_URL = URL;
		_fileName = [self fileNameWithURL:_URL];
		_downloadState = PLVVodDownloadStateReady;
	}
	return self;
}

- (NSString *)fileNameWithURL:(NSURL *)URL{
	return URL.lastPathComponent;
}

- (NSString *)description {
	NSMutableString *description = [super.description stringByAppendingString:@":\n"].mutableCopy;
	[description appendFormat:@" URL: %@;\n", _URL];
	[description appendFormat:@" fileName: %@;\n", _fileName];
	[description appendFormat:@" downloadState: %zd;\n", _downloadState];
	[description appendFormat:@" downloadTask: %@;\n", _downloadTask];
	[description appendFormat:@" tsDownloadProgress: %@;\n", _tsDownloadProgress];
	[description appendFormat:@" outputStream: %@;\n", _outputStream];
	return description;
}

@end
