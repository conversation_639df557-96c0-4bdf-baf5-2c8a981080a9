//
//  PLVVodTsDownloadTask.h
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/16.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PLVVodDownloadInfo.h"

@interface PLVVodTsDownloadTask : NSObject

/// URL
@property (nonatomic, copy) NSURL *URL;

/// 文件名
@property (nonatomic, copy) NSString *fileName;

/// 下载状态
@property (nonatomic, assign) PLVVodDownloadState downloadState;

/// 对应的下载任务
//@property (nonatomic, assign) NSInteger taskIdentifier;
@property (nonatomic, strong) NSURLSessionTask *downloadTask;

/// ts下载进度
@property (nonatomic, strong) NSProgress *tsDownloadProgress;

/// 输出流（仅用于 dataTask）
@property (nonatomic, strong) NSOutputStream *outputStream;


/// 初始化
+ (instancetype)tsTaskWithURL:(NSURL *)URL;

@end
