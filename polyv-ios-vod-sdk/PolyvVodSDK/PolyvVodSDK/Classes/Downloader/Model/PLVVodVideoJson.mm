//
//  PLVVodVideoJson.mm
//  PolyvVodSDK
//
//  Created by mac on 2018/9/10.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import <PLVFDB/PLVFDatabase.h>
#import "PLVVodVideoJson.h"

@interface PLVVodVideoJson () <PLVFDatabaseProtocol>

@end

@implementation PLVVodVideoJson

+ (NSString *)tableName{
    return @"plv_video_json";
}

#pragma mark - PLVFDatabaseProtocol

+ (nonnull NSString *)primaryKey {
    return @"vid";
}

+ (nonnull NSArray<NSString *> *)propertyKeys {
    return @[
        @"vid",
        @"modifyDate",
        @"dataBody"
    ];
}

@end
