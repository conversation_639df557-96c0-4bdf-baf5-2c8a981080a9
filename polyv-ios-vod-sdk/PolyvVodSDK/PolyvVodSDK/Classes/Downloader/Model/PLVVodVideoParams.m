//
//  PLVVodVideoParams.m
//  PolyvVodSDK
//
//  Created by mac on 2019/3/26.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import "PLVVodVideoParams.h"

@implementation PLVVodVideoParams

- (instancetype)init{
    if (self = [super init]){
        
        // 默认视频文件
        _fileType = PLVDownloadFileTypeVideo;
    }
    
    return self;
}

+ (PLVVodVideoParams *)videoParamsWithVid:(NSString *)vid fileType:(PLVDownloadFileType)fileType{
    PLVVodVideoParams *params = [[self alloc] init];
    
    params.vid = vid;
    params.fileType = fileType;
    
    return params;
}

@end
