//
//  PLVVodLocalVideo.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/12.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodLocalVideo.h"
#import "PLVVodUtil.h"
#import <PLVIJKPlayer/PLVIJKPlayer.h>

NSString *vidWithFileName(NSString *fileName) {
	NSRange range = [fileName rangeOfString:@"_"];
	if (range.location == NSNotFound) {
		return nil;
	}
	NSString *videoPoolId = [fileName substringToIndex:range.location];
	return vidWithVideoPoolId(videoPoolId);
}

@interface PLVVodLocalVideo ()

@property (nonatomic, copy) NSString *vid;

/// 清晰度
@property (nonatomic, assign) PLVVodQuality quality;

/// 本地路径，hls为m3u8路径
@property (nonatomic, copy) NSString *path;

/// 音频文件路径
@property (nonatomic, copy) NSString *tokenPath;

/// 文件大小
@property (nonatomic, assign) NSInteger filesize;

/// 是否为非加密视频
@property (nonatomic, assign) BOOL isPlain;

/// 是否为 m3u8 视频
@property (nonatomic, assign) BOOL isHls;

/// 音频文件路径
@property (nonatomic, copy) NSString *audioPath;

@end

@implementation PLVVodLocalVideo
{
	NSString *_vid;
}
@synthesize vid = _vid;

+ (instancetype)localVideoWithVideo:(PLVVodVideo *)video dir:(NSString *)dir {
	PLVVodLocalVideo *localVideo = [[self alloc] initWithVid:video.vid dir:dir];
	if (!localVideo) {
		//PLVVodLogWarn(@"无法找到对应的本地视频");
		return nil;
	}
	
    [localVideo setupWithVideo:video];
	return localVideo;
}

+ (instancetype)localVideoWithVid:(NSString *)vid dir:(NSString *)dir {
	return [[self alloc] initWithVid:vid dir:dir];
}

+ (NSArray<PLVVodLocalVideo *> *)localVideosWithDir:(NSString *)dir {
	BOOL isDir = YES;
	if (!dir.length) {
		//PLVVodLogError(@"%s - 参数为空", __FUNCTION__);
		return nil;
	}
	if (![[NSFileManager defaultManager] fileExistsAtPath:dir isDirectory:&isDir] || !isDir) {
		//PLVVodLogError(@"%s - 目录不存在", __FUNCTION__);
		return nil;
	}
	
	NSArray *subpaths  = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:dir error:nil];
	NSMutableDictionary<NSString *, PLVVodLocalVideo *> *localVideoDic = [NSMutableDictionary dictionary];
	
	// 遍历目录下路径
	for (NSString *subpath in subpaths) {
		NSString *fileName = subpath.stringByDeletingPathExtension;
		NSRange underlineRange = [fileName rangeOfString:@"_"];
		if (underlineRange.location == NSNotFound) continue;
		
		// 获取清晰度
		PLVVodQuality quality = [[fileName substringFromIndex:NSMaxRange(underlineRange)] intValue];
		if (quality < PLVVodQualityStandard || quality > PLVVodQualityUltra) continue;
		
		// 获取vid
		NSString *vid = vidWithVideoPoolId([fileName substringToIndex:underlineRange.location]);
		
		// 判断是否为文件夹
		BOOL isDir = NO;
		[[NSFileManager defaultManager] fileExistsAtPath:[dir stringByAppendingPathComponent:subpath] isDirectory:&isDir];
		
		NSString *pathExtension = subpath.pathExtension.lowercaseString;
		BOOL isHls = [@"m3u8" isEqualToString:pathExtension] && !isDir;
		BOOL isKey = [@"key" isEqualToString:pathExtension] && !isDir;
		BOOL isToken = [@"token" isEqualToString:pathExtension] && !isDir;
		BOOL isOtherFile = pathExtension.length > 0 && !isDir && !isHls && !isKey && !isToken &&
		([pathExtension containsString:@"m"] || [pathExtension containsString:@"v"]);
		
		PLVVodLocalVideo *localVideo = nil;
		
		// 目录分为两种情况，一种为旧版的ts目录，一种为新版的hls视频目录
        // 旧版ts目录，结构为m3u8 key tlsdir/ts
        // dir 为新版hls 视频目录，ts/key/m3u8 存储在同一目录
		if (isDir) {
            
            // 如果是旧版ts目录可以直接过滤，如果是新版本hls目录，已经处理过，也可以跳过
			if (localVideoDic[vid]) continue;
            
			NSString *hlsDir = [dir stringByAppendingPathComponent:subpath];
			NSArray *hlsFiles  = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:hlsDir error:nil];
			BOOL containM3u8 = NO;
			BOOL containKey = NO;
			NSString *path = nil;
			for (NSString *hlsFile in hlsFiles) {
				if ([@"key" isEqualToString:hlsFile.pathExtension.lowercaseString]) {
					containKey = YES;
				} else if ([@"m3u8" isEqualToString:hlsFile.pathExtension.lowercaseString]) {
					containM3u8 = YES;
					path = [hlsDir stringByAppendingPathComponent:hlsFile];
				} else if ([@"token" isEqualToString:hlsFile.pathExtension.lowercaseString]) {
                    localVideo.tokenPath = [hlsDir stringByAppendingPathComponent:hlsFile];
				} 
			}
            
            // 此处只存在bin 文件也会导致空数据
			localVideo = [[PLVVodLocalVideo alloc] init];
			localVideo.isHls = containM3u8;
			localVideo.isPlain = !containKey;
            
            if (localVideo.isHls){
                // TODO: path 为空的场景
                // 旧版ts目录，优先遍历了ts 目录文件便会出现这种情况
                localVideo.path = path;
            }
		}

		if (isToken) {
            localVideo.tokenPath = subpath;
        }
		if (isKey) {
			if (localVideoDic[vid]) { // 先遍历 m3u8
				localVideoDic[vid].isPlain = NO;
				continue;
			} else {
				localVideo = [[PLVVodLocalVideo alloc] init];
				localVideo.isPlain = NO;
			}
		}
		if (isHls) {
			if (localVideoDic[vid]) { // 先遍历 key
				localVideoDic[vid].isHls = YES;
                
                // add by libl [需要设置m3u8 文件路径] 2018-10-17 start
                localVideoDic[vid].path = [dir stringByAppendingPathComponent:subpath];
                // add end
				continue;
			} else {
				localVideo = [[PLVVodLocalVideo alloc] init];
				localVideo.isHls = YES;
                // add by libl [需要设置m3u8 文件路径] 2018-10-17 start
                localVideo.path = [dir stringByAppendingPathComponent:subpath];
                // add end
			}
		}
		
		// 如果是非 hls，非 key 文件，则一定为非加密非hls视频
        // 其他格式视频文件
        
		if (isOtherFile) {
			localVideo = [[PLVVodLocalVideo alloc] init];
			localVideo.isHls = NO;
			localVideo.isPlain = YES;
            
            // add by libl [需要设置文件路径] 2018-10-17 start
            localVideo.path = [dir stringByAppendingPathComponent:subpath];
            // add end
        }
		
		if (localVideo) {
            
            // del by libl [不要在此处设置路径，此文件不知具体类型] 2018-10-17 start
//            if (!localVideo.path)
//                localVideo.path = [dir stringByAppendingPathComponent:subpath];
            // del end
            
			localVideo.vid = vid;
			localVideo.quality = quality;
			localVideoDic[localVideo.vid] = localVideo;
		}
	}// end
    
	
	// 排除非 hls 但加密的模型
	NSMutableArray *tempLocalVideos = localVideoDic.allValues.mutableCopy;
	NSMutableArray *localVideos = [NSMutableArray array];
	for (PLVVodLocalVideo *localVideo in tempLocalVideos) {
        
        PLVVodLogDebug(@"+++++local path: %@ ++++++++", localVideo.path);
        if (!localVideo.path || localVideo.path <= 0){
            continue;
        }
        
        // 排除非 hls 但加密的模型
#warning -- TODO:暂时不清楚为什么要过滤
		if (!localVideo.isHls && !localVideo.isPlain) {
			continue;
		}
        
        // add by libl [如果path 为目录，排除，文件还未下载完成] 2018-08-18 start
        BOOL pathIsDir = NO;
        [[NSFileManager defaultManager] fileExistsAtPath:localVideo.path isDirectory:&pathIsDir];
        if (pathIsDir){
            continue;
        }
        // add end
        
        NSString *pathExtension = localVideo.path.pathExtension.lowercaseString;
        PLVVodLogDebug(@"+++++localVideo: pathextension: %@ +++++++", pathExtension);
        
        // add by libl [如果path 后缀为bin,zip 中间文件，未下载完成] 2018-08-18 start
        // 已经过滤
        
        // add end

		[localVideos addObject:localVideo];
	}
	// TODO: 计算文件大小
	return localVideos;
}

/// 通过vid获取对应dir的资源
- (instancetype)initWithVid:(NSString *)vid dir:(NSString *)dir {
    self = [self init];
    
    self.vid = vid;
    
    BOOL isDir = YES;
    if (!vid.length || !dir.length) {
        //PLVVodLogError(@"%s - 参数为空", __FUNCTION__);
        return nil;
    }
    if (![[NSFileManager defaultManager] fileExistsAtPath:dir isDirectory:&isDir] || !isDir) {
        //PLVVodLogError(@"%s - 目录不存在", __FUNCTION__);
        return nil;
    }
    
    NSArray *subpaths  = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:dir error:nil];
    for (NSString *subpath in subpaths) {
        NSString *fileName = subpath.stringByDeletingPathExtension;
        NSString *subFileName = [NSString stringWithFormat:@"%@_", videoPoolIdWithVid(vid)];
        if (![fileName containsString:subFileName]) continue;
        
        // 总共是遍历 key/m3u8/ts 目录
        // 2.0 存储结构 vid_qualty 单个视频目录        ts文件 key m3u8
        // 1.0 存储结构 vid_qualty ts文件夹           key m3u8
        
        NSRange underlineRange = [fileName rangeOfString:@"_"];
        
        // 获取清晰度
        PLVVodQuality quality = [[fileName substringFromIndex:NSMaxRange(underlineRange)] intValue];
        if (quality < PLVVodQualityStandard || quality > PLVVodQualityUltra) continue;
        self.quality = quality;
        
        // 判断是否为文件夹
        BOOL isDir = NO;
        [[NSFileManager defaultManager] fileExistsAtPath:[dir stringByAppendingPathComponent:subpath] isDirectory:&isDir];
        
        NSString *pathExtension = subpath.pathExtension.lowercaseString;
        BOOL isHls = [@"m3u8" isEqualToString:pathExtension] && !isDir;
        BOOL isKey = [@"key" isEqualToString:pathExtension] && !isDir;
        BOOL isToken = [@"token" isEqualToString:pathExtension] && !isDir;
        BOOL isOtherFile = pathExtension.length > 0 &&
        !isDir && !isHls && !isKey && !isToken &&
        ([pathExtension containsString:@"m"] || [pathExtension containsString:@"v"]);
        
        // 目录分为两种情况，一种为旧版的ts目录，一种为新版的hls视频目录
        if (isDir) {
            
            NSString *hlsDir = [dir stringByAppendingPathComponent:subpath];
            NSArray *hlsFiles  = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:hlsDir error:nil];
            BOOL containM3u8 = NO;
            BOOL containKey = NO;
            NSString *path = nil;
            for (NSString *hlsFile in hlsFiles) {
                if ([@"key" isEqualToString:hlsFile.pathExtension.lowercaseString]) {
                    containKey = YES;
                } else if ([@"m3u8" isEqualToString:hlsFile.pathExtension.lowercaseString]) {
                    containM3u8 = YES;
                    path = [hlsDir stringByAppendingPathComponent:hlsFile];
                } else if ([@"token" isEqualToString:hlsFile.pathExtension.lowercaseString]) {
                    self.tokenPath = [hlsDir stringByAppendingPathComponent:hlsFile];
				} 
            }
            
            // mod by libl [如果是旧版ts 目录，这里不能获取到path 路径值] 2018-10-18 start
            //            self.isHls = containM3u8;
            //            self.isPlain = !containKey;
            //            self.path = path;
            //            return self;
            
            if (containM3u8){
                self.isHls = containM3u8;
                self.isPlain = !containKey;
                self.path = path;
                return self;
            }
            else{
                // 旧版ts 目录,过滤，继续遍历
                continue;
            }
            // mod end
        }
        
        // 以下逻辑兼容1.0 缓存视频
        
        if (isKey) {
            self.isPlain = NO;
        }
        
        if (isHls) {
            
            // TODO: 校验ts个数，此处可用递归。P.S. ts不完整也可以播放。
            _path = [dir stringByAppendingPathComponent:subpath];
            _quality = quality;
            _isHls = YES;
        }
        
        // 如果是非 hls，非 key 文件，则一定为非加密非hls视频
        if (isOtherFile) {
            self.isHls = NO;
            self.isPlain = YES;
            self.path = [dir stringByAppendingPathComponent:subpath];
            _filesize = (NSInteger)[[NSFileManager defaultManager] attributesOfItemAtPath:_path error:nil].fileSize;
            
            return self;
        }
    }
    
    if (_path!= nil && ![_path isKindOfClass:[NSNull class]]){
        return self;
    }
    
    return nil;
}


+ (NSInteger)fileSizeForDir:(NSString *)dir {
	NSInteger fileSize = 0;
	NSFileManager *fileManager = [NSFileManager defaultManager];
	NSArray *files = [fileManager contentsOfDirectoryAtPath:dir error:nil];
	for (NSString *fileName in files) {
		NSString *subPath = [dir stringByAppendingPathComponent:fileName];
		BOOL subPathIsDir = NO;
		[fileManager fileExistsAtPath:subPath isDirectory:&subPathIsDir];
		if (subPathIsDir) {
			fileSize += [self fileSizeForDir:subPath];
		} else {
			fileSize += [fileManager attributesOfItemAtPath:subPath error:nil].fileSize;
		}
	}
	return fileSize;
}

- (instancetype)init {
	if (self = [super init]) {
		_isPlain = YES;
		_isHls = NO;
	}
	return self;
}

- (NSString *)description {
	NSMutableString *description = [NSMutableString stringWithFormat:@"<PLVVodLocalVideo: %p>:\n", self];
	[description appendFormat:@" vid: %@;\n", _vid];
	[description appendFormat:@" quality: %zd;\n", _quality];
	[description appendFormat:@" path: %@;\n", _path.lastPathComponent];
	[description appendFormat:@" filesize: %zd;\n", _filesize];
	[description appendFormat:@" isPlain: %s;\n", _isPlain?"YES":"NO"];
	[description appendFormat:@" isHls: %s;\n", _isHls?"YES":"NO"];
	return description;
}

/// 获取本地字幕信息
+ (NSDictionary<NSString *,NSString *> *)localSubtitlesWithVideo:(PLVVodVideo *)video dir:(NSString *)dir{
    //
    BOOL isDir = YES;
    if (!video || !dir.length) {
        PLVVodLogWarn(@"%s - 参数为空", __FUNCTION__);
        return nil;
    }
    if (![[NSFileManager defaultManager] fileExistsAtPath:dir isDirectory:&isDir] || !isDir) {
        PLVVodLogWarn(@"%s - 目录不存在", __FUNCTION__);
        return nil;
    }
    
    //
    if (!video.srts.count){
        return nil;
    }
    
    __block NSMutableDictionary *dicts = [[NSMutableDictionary alloc] init];
    [video.srts enumerateObjectsUsingBlock:^(PLVVodVideoSubtitleItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NSString *key = obj.title;
        NSString *url = obj.url;
        NSString *fileName = [url lastPathComponent];
        NSString *subtitleDir = [[dir stringByAppendingPathComponent:PLVSubtitleDir] stringByAppendingPathComponent:video.vid];
        NSString *filePath = [subtitleDir stringByAppendingPathComponent:fileName];
        
        if ([[NSFileManager defaultManager] fileExistsAtPath:filePath]){
            [dicts setObject:filePath forKey:key];
        }
    }];
    
    return dicts;
}

- (void)setupWithVideo:(PLVVodVideo *)video {
    
    self.vid = video.vid;
    //[self setValue:video.vid forKey:@"vid"];
    [self setValue:@(video.qualityCount) forKey:@"qualityCount"];
    //self.qualityCount = video.qualityCount;
    [self setValue:@(video.preferredQuality) forKey:@"preferredQuality"];
    //self.preferredQuality = video.preferredQuality;
    [self setValue:video.snapshot.copy forKey:@"snapshot"];
    //self.snapshot = video.snapshot;
    [self setValue:@(video.duration) forKey:@"duration"];
    //self.duration = video.duration;
    [self setValue:video.filesizes.copy forKey:@"filesizes"];
    //self.filesizes = video.filesizes.copy;
    [self setValue:@(video.interactive) forKey:@"interactive"];
    //localVideo.interactive = video.interactive;
    [self setValue:video.title.copy forKey:@"title"];
    //self.title = video.title;
    [self setValue:video.srts.mutableCopy forKey:@"srts"];
    //self.srts = video.srts.copy;
    [self setValue:video.ads.mutableCopy forKey:@"ads"];
    //self.ads = video.ads.copy;
    [self setValue:@(video.isPlain) forKey:@"isPlain"];
    //self.isPlain = video.isPlain;
    [self setValue:@(video.isHls) forKey:@"isHls"];
    [self setValue:@(video.isHls302) forKey:@"isHls302"];
    //self.isHls = video.isHls;
    [self setValue:@(video.available) forKey:@"available"];
    [self setValue:video.teaser.copy forKey:@"teaser"];
    //self.teaser = video.teaser;
    [self setValue:@(video.teaserDuration) forKey:@"teaserDuration"];
    //self.teaserDuration = video.teaserDuration;
    [self setValue:@(video.teaserShow) forKey:@"teaserShow"];
    //self.teaserShow = video.teaserShow;
    [self setValue:@(video.status) forKey:@"status"];
    //self.status = video.status;
    [self setValue:@(video.outflow) forKey:@"outflow"];
    //self.outflow = video.outflow;
    [self setValue:@(video.timeoutflow) forKey:@"timeoutflow"];
    //self.timeoutflow = video.timeoutflow;
    [self setValue:video.hlsIndex.copy forKey:@"hlsIndex"];
    //self.hlsIndex = video.hlsIndex.copy;
    [self setValue:video.hlsVideos.mutableCopy forKey:@"hlsVideos"];
    //self.hlsVideos = video.hlsVideos.copy;
    [self setValue:video.hlsIndex2.copy forKey:@"hlsIndex2"];
    [self setValue:video.hlsVideos2.mutableCopy forKey:@"hlsVideos2"];
    [self setValue:video.plainVideos.mutableCopy forKey:@"plainVideos"];
    //self.plainVideos = video.plainVideos.copy;
    [self setValue:video.tsPackages.mutableCopy forKey:@"tsPackages"];
    //self.tsPackages = video.tsPackages.copy;
    [self setValue:video.constKey.copy forKey:@"constKey"];
    //self.constKey = video.constKey;
    [self setValue:@(video.keepSource) forKey:@"keepSource"];
    //self.keepSource = video.keepSource;
    [self setValue:video.categoryId forKey:@"categoryId"];
    //self.categoryId = video.categoryId;
    [self setValue:video.categoryTree.copy forKey:@"categoryTree"];
    //self.categoryTree = video.categoryTree.copy;
    [self setValue:video.availableRouteLines.copy forKey:@"availableRouteLines"];
    [self setValue:video.videokeyframes forKey:@"videokeyframes"];
    [self setValue:video.aac_link forKey:@"aac_link"];
    //self.availableRouteLines = video.availableRouteLines.copy;
}

+ (NSArray *)migrateLocalVideoPlaylist:(NSString *)downloadDir secretKey:(NSString*)secretKey {
    // NSString *secretKey = [PLVVodSettings sharedSettings].secretkey;
    NSMutableArray *videos = [[NSMutableArray alloc] init];
    const char* secretKeyC = [secretKey UTF8String];

    NSArray<PLVVodLocalVideo *> *array = [PLVVodLocalVideo localVideosWithDir:downloadDir];
    for (PLVVodLocalVideo *one in array) {        
        if (!one.isHls || one.isPlain)
            continue;
        
        NSString *tokenPath = [one.path stringByReplacingOccurrencesOfString:@"m3u8" withString:@"token"];
        if ([[NSFileManager defaultManager] fileExistsAtPath:tokenPath])
            continue;
        
        const char* path = [[one.path stringByReplacingOccurrencesOfString:@"m3u8" withString:@"key"] UTF8String];
        const char* videoPoolId = [videoPoolIdWithVid(one.vid) UTF8String];

        char token[64] = {0};
        int result = IJKFFMigrateLocalVideoPlaylist(path, secretKeyC, videoPoolId, 0, token);
        if (result != 1) {
            PLVVodLogWarn(@"迁移本地视频失败 %@", one.path);
            continue;
        }
        // NSString *tokenStr = [[NSString alloc] initWithUTF8String:token];
        NSData *data = [NSData dataWithBytes:token length:strlen(token)];
        if (![data writeToFile:tokenPath atomically:YES]) {
            PLVVodLogWarn(@"迁移本地视频失败：写入令牌失败 %@", one.path);
        }
        else{
            if(![PLVVodUtil isNilString:one.vid]){
                [videos addObject:one.vid];
            }
        }
    }
    
    return videos;
}

@end
