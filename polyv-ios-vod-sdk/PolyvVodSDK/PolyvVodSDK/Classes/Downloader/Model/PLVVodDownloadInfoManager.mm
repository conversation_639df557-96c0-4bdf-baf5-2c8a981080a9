//
//  PLVVodDownloadInfoManager.m
//  _PolyvVodSDK
//
//  Created by <PERSON><PERSON> <PERSON> on 2018/1/18.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import "PLVVodDownloadInfoManager.h"
#import "PLVVodDownloadInfo.h"
#import "PLVVodVideoJson.h"
//#import "PLVVodDownloadManager.h"
#import "PLVExtendDownloadInfo.h"
#import "PLVVodVideoParams.h"
#import "PLVFileUtils.h"
#import "PLVVodDefines.h"
#import <PLVFDB/PLVFDatabase.h>

static NSString * const DbName = @"vod_cache";
static NSString * const DbTableName = @"download_list";
static NSString * const DbVidKey = @"vid";
static NSString * const DbQueueIdKey = @"queue_id";
static NSString * const DbStateKey = @"state";
static NSString * const DbProgressKey = @"progress";
static NSString * const DbTableName_Audio = @"download_list_audio";

@interface PLVVodDownloadInfoManager ()

@property (nonatomic, strong) PLVFDatabase *database;
@property (nonatomic, copy) NSString *dbPath;

@end

@implementation PLVVodDownloadInfoManager

static id _sharedInstance = nil;

+ (instancetype)sharedManager {
	static dispatch_once_t onceToken;
	dispatch_once(&onceToken, ^{
		_sharedInstance = [[self alloc] init];
		[_sharedInstance commonInit];
	});
	return _sharedInstance;
}

+ (instancetype)allocWithZone:(struct _NSZone *)zone {
	static dispatch_once_t onceToken;
	dispatch_once(&onceToken, ^{
		if (!_sharedInstance) {
			_sharedInstance = [super allocWithZone:zone];
		}
	});
	return _sharedInstance;
}

- (id)copyWithZone:(NSZone *)zone {
	return _sharedInstance;
}

- (void)commonInit {
	[self database];
}

- (PLVFDatabase *)shareDB{
    return _database;
}

#pragma mark - property
// TODO: 数据库错误汇报
- (PLVFDatabase *)database {
	if (!_database) {
        
//        NSString *dirName = @"net.polyv.vod";
//        NSString *dir = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES).lastObject stringByAppendingPathComponent:dirName];
//        NSString *dbPath = [dir stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.db", DbName]];
//        _database = [[WCTDatabase alloc] initWithPath:dbPath];
        
        // 默认单账号模式，需要迁移数据库位置
        // 文件不存在无需移动，默认移动成功
        BOOL moveSuccess = YES;
        
        if ([[NSFileManager defaultManager] fileExistsAtPath:[self lastDbPath]]){
            // 移动数据库到新的默认位置
            if (![[NSFileManager defaultManager] fileExistsAtPath:[self defaultDbPath]]){
                //
                NSError *error = nil;
                // 需要文件夹整体移动，否则数据库文件没有数据
                // /Library/Cache/net.polyv.vod -> /Documents/net.polyv.vod.db
                NSString *toPathDir = [[self defaultDbPath] stringByDeletingLastPathComponent];;
                NSString *fromPathDir = [[self lastDbPath] stringByDeletingLastPathComponent];
                [[NSFileManager defaultManager] moveItemAtPath:fromPathDir toPath:toPathDir error:&error];
                if (error){
                    moveSuccess = NO;
                }
                else{
                    NSLog(@"---------- move db success! -----------");
                }
            }
        }
        
        if (moveSuccess){
            self.dbPath = [self defaultDbPath];
        }
        else{
            self.dbPath = [self lastDbPath];
        }
        
        _database = [PLVFDatabase databaseWithPath:self.dbPath];
        
        NSLog(@"\n%@\n", self.dbPath);
        // add by libl [可以重复调用，底层自动判断是否已经存在，还可以动态增加字段] 2018-07-08 start
        // 视频下载表
        [_database createTable:DbTableName withClass:[PLVVodDownloadInfo class]];
        
        // videojson 表
        [_database createTable:[PLVVodVideoJson tableName] withClass:[PLVVodVideoJson class]];
        
        // 创建音频文件下载表
        [_database createTable:DbTableName_Audio withClass:[PLVVodDownloadInfo class]];
        
	}
    
	return _database;
}

- (void)initDatabase{
    _database = [PLVFDatabase databaseWithPath:self.dbPath];
    
    NSLog(@"\n%@\n", self.dbPath);
    // add by libl [可以重复调用，底层自动判断是否已经存在，还可以动态增加字段] 2018-07-08 start
    // 视频下载表
    [_database createTable:DbTableName withClass:[PLVVodDownloadInfo class]];
    
    // videojson 表
    [_database createTable:[PLVVodVideoJson tableName] withClass:[PLVVodVideoJson class]];
    
    // 创建音频文件下载表
    [_database createTable:DbTableName_Audio withClass:[PLVVodDownloadInfo class]];
}

- (void)switchDbWithAccountId:(NSString *)accountId{
   
    NSString *dbName = [NSString stringWithFormat:@"%@.db", DbName];
    NSString *dbFile = [[PLVFileUtils getDbDirectoryWithAccountId:accountId] stringByAppendingPathComponent:dbName];
    if (dbFile != nil && [dbFile isEqualToString:self.dbPath]){
        NSLog(@"同一个数据库路径，无需切换");
        return;
    }
    
    // 是否需要迁移数据
    // 如果当前帐号数据库文件已经存在，不需要再迁移
    // 多账户体系默认帐号，不需要迁移数据库
    if (![[NSFileManager defaultManager] fileExistsAtPath:dbFile] && ![accountId isEqualToString:PLVMultiAccountIdDefault]){
        
        // 数据库迁移
        [_database close];
        _database = nil;
        
        [self moveDbFile:[self defaultDbPath] toPath:dbFile];
    }
    
    [_database close];
    _database = nil;
    
    // 设置数据库路径
    self.dbPath = dbFile;
    
    [self initDatabase];
}

//
- (void)moveDbFile:(NSString *)fromPath toPath:(NSString *)toPath{
    NSError *error = nil;
    // 需要文件夹整体移动，否则数据库文件没有数据
    // /Documents/net.polyv.vod.db -> /Documents/PolyvCache_AccountId/DBCache
    NSString *toPathDir = [toPath stringByDeletingLastPathComponent];;
    NSString *fromPathDir = [fromPath stringByDeletingLastPathComponent];
    [[NSFileManager defaultManager] moveItemAtPath:fromPathDir toPath:toPathDir error:&error];
    if (error){
    }
    else{
        NSLog(@"---------- move db success! -----------");
    }
}

// 上个版本默认db 路径
- (NSString *)lastDbPath{
    NSString *dirName = @"net.polyv.vod";
    NSString *dir = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES).lastObject stringByAppendingPathComponent:dirName];
    NSString *dbPath = [dir stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.db", DbName]];
    
    return dbPath;
}

// 当前默认DB 路径
// /Documents/net.polyv.vod.db
- (NSString *)defaultDbPath{
    NSString *dirName = @"net.polyv.vod.db";
    NSString *dir = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).lastObject stringByAppendingPathComponent:dirName];
    NSString *dbPath = [dir stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.db", DbName]];
    
    return dbPath;
}

#pragma mark - database

- (BOOL)insertOrUpdateDatabaseWithDownloadInfo:(PLVVodDownloadInfo *)downloadInfo {
	[self.database beginTransaction];
	
    NSString *tableName = DbTableName;
    if (downloadInfo.fileType == PLVDownloadFileTypeAudio)
        tableName = DbTableName_Audio;
    
    BOOL result = [self.database insertOrReplaceFromClass:[PLVVodDownloadInfo class] object:downloadInfo table:tableName where:[NSString stringWithFormat:@"where vid = '%@'", downloadInfo.vid]];
	
	if (result) {
		[self.database commitOrRollbackTransaction];
	} else {
		[self.database rollbackTransaction];
	}
	return result;
}

- (BOOL)updateDatabaseWithDownloadInfo:(PLVVodDownloadInfo *)downloadInfo{
    [self.database beginTransaction];
    
    NSString *tableName = DbTableName;
    if (downloadInfo.fileType == PLVDownloadFileTypeAudio)
        tableName = DbTableName_Audio;
    
    BOOL result = [self.database insertOrReplaceFromClass:[PLVVodDownloadInfo class] object:downloadInfo table:tableName where:[NSString stringWithFormat:@"where vid = '%@'", downloadInfo.vid]];
    
    if (result) {
        [self.database commitOrRollbackTransaction];
    } else {
        [self.database rollbackTransaction];
    }
    return result;
}

- (BOOL)removeFromdatabseWithVid:(NSString *)vid {
    return [self.database deleteTable:DbTableName where:[NSString stringWithFormat:@"where vid = '%@'",vid]];
}

- (BOOL)removeFromdatabseWithVideoParams:(PLVVodVideoParams *)videoParams{
    NSString *strTable = DbTableName;
    if (videoParams.fileType == PLVDownloadFileTypeAudio){
        strTable = DbTableName_Audio;
    }
    return [self.database deleteTable:strTable where:[NSString stringWithFormat:@"where vid = '%@'",videoParams.vid]];
}

- (BOOL)removeAllFromDatabase{
    return [self.database deleteTable:DbTableName where:nil];
}

- (NSArray<PLVVodDownloadInfo *> *)downloadListFromDatabase {
    NSArray *dbDownloadList = [self.database objectsFromClass:[PLVVodDownloadInfo class] table:DbTableName];
	dbDownloadList = [dbDownloadList sortedArrayUsingComparator:^NSComparisonResult(PLVVodDownloadInfo *obj1, PLVVodDownloadInfo *obj2) {
		return [@(obj1.downloadId) compare:@(obj2.downloadId)];
	}];
	return dbDownloadList;
}

- (PLVVodDownloadInfo *)downloadInfoWithVid:(NSString *)vid{
    NSArray<PLVVodDownloadInfo *> *dbDownloadList = [self.database objectsFromClass:[PLVVodDownloadInfo class] table:DbTableName where:[NSString stringWithFormat:@"where vid = '%@'", vid]];
    
    PLVVodDownloadInfo *info = [dbDownloadList firstObject];
    
    return info;
}

- (PLVVodDownloadInfo *)downloadInfoWithVideoParams:(PLVVodVideoParams *)videoParams{
    
    NSString *strTable = DbTableName;
    if (videoParams.fileType == PLVDownloadFileTypeAudio){
        strTable = DbTableName_Audio;
    }
    NSArray<PLVVodDownloadInfo *> *dbDownloadList = [self.database objectsFromClass:[PLVVodDownloadInfo class] table:strTable where:[NSString stringWithFormat:@"where vid = '%@'", videoParams.vid]];
    
    PLVVodDownloadInfo *info = [dbDownloadList firstObject];
    
    return info;
}

- (NSArray<PLVVodDownloadInfo *> *)downloadCompleteListFromDatabase{
    // 从视频表读取
    NSArray<PLVVodDownloadInfo *> *videoDownloadList = [self.database objectsFromClass:[PLVVodDownloadInfo class] table:DbTableName where:[NSString stringWithFormat:@"where state = %ld", PLVVodDownloadStateSuccess]];
    videoDownloadList = [videoDownloadList sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
        PLVVodDownloadInfo *objOne = obj1;
        PLVVodDownloadInfo *objTwo = obj2;
        
        return [@(objOne.downloadId) compare:@(objTwo.downloadId)];
    }];
    
    // 从音频表读取
    NSArray<PLVVodDownloadInfo *> *audioDownloadList = [self.database objectsFromClass:[PLVVodDownloadInfo class] table:DbTableName_Audio where:[NSString stringWithFormat:@"where state = %ld", PLVVodDownloadStateSuccess]];
    audioDownloadList = [audioDownloadList sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
        PLVVodDownloadInfo *objOne = obj1;
        PLVVodDownloadInfo *objTwo = obj2;
        
        return [@(objOne.downloadId) compare:@(objTwo.downloadId)];
    }];
    
    NSMutableArray *dbDownloadList = [NSMutableArray array];
    [dbDownloadList addObjectsFromArray:videoDownloadList];
    [dbDownloadList addObjectsFromArray:audioDownloadList];
    
    return dbDownloadList;
}

- (NSArray<PLVVodDownloadInfo *> *)downloadProcessingListFromDatabase{
    
    // 读取视频列表
    NSArray<PLVVodDownloadInfo *> *videoDownloadList = [self.database objectsFromClass:[PLVVodDownloadInfo class] table:DbTableName where:[NSString stringWithFormat:@"where state != %ld", PLVVodDownloadStateSuccess]];
    videoDownloadList = [videoDownloadList sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
        PLVVodDownloadInfo *objOne = obj1;
        PLVVodDownloadInfo *objTwo = obj2;
        
        return [@(objOne.downloadId) compare:@(objTwo.downloadId)];
    }];
    
    // 读取音频列表
    NSArray<PLVVodDownloadInfo *> *audioDownloadList = [self.database objectsFromClass:[PLVVodDownloadInfo class] table:DbTableName_Audio where:[NSString stringWithFormat:@"where state != %ld", PLVVodDownloadStateSuccess]];
    audioDownloadList = [audioDownloadList sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
        PLVVodDownloadInfo *objOne = obj1;
        PLVVodDownloadInfo *objTwo = obj2;
        
        return [@(objOne.downloadId) compare:@(objTwo.downloadId)];
    }];
    
    NSMutableArray *dbDownloadList = [NSMutableArray array];
    [dbDownloadList addObjectsFromArray:videoDownloadList];
    [dbDownloadList addObjectsFromArray:audioDownloadList];
    
    return dbDownloadList;
}

#pragma mark -- table plv_video_json
- (BOOL)updateDatabaseWithVideoJson:(PLVVodVideoJson *)videoJson{
    [self.database beginTransaction];
    
    BOOL result = [self.database insertOrReplaceFromClass:[PLVVodVideoJson class] object:videoJson table:[PLVVodVideoJson tableName] where:[NSString stringWithFormat:@"where vid = '%@'",videoJson.vid]];
    
    if (result) {
        [self.database commitOrRollbackTransaction];
    } else {
        [self.database rollbackTransaction];
    }
    return result;
}

- (PLVVodVideoJson *)videoJsonWithVid:(NSString *)vid{
    NSArray<PLVVodVideoJson *> *dbDownloadList = [self.database objectsFromClass:[PLVVodVideoJson class] table:[PLVVodVideoJson tableName] where:[NSString stringWithFormat:@"where vid = '%@'",vid]];
    PLVVodVideoJson *videoJson = [dbDownloadList firstObject];
    
    return videoJson;
}

@end
