//
//  PLVVodDownloadInfoManager.h
//  _PolyvVodSDK
//
//  Created by <PERSON><PERSON> <PERSON> on 2018/1/18.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>
@class PLVVodDownloadInfo;
@class PLVVodVideoJson;
@class PLVFDatabase;
@class PLVVodVideoParams;

@interface PLVVodDownloadInfoManager : NSObject

+ (instancetype)sharedManager;

- (PLVFDatabase *)shareDB;

- (NSString *)dbPath;

// 切换数据库
- (void)switchDbWithAccountId:(NSString *)accountId;

// table PLVVodDownloadInfo
- (NSArray<PLVVodDownloadInfo *> *)downloadListFromDatabase;
- (NSArray<PLVVodDownloadInfo *> *)downloadCompleteListFromDatabase;
- (NSArray<PLVVodDownloadInfo *> *)downloadProcessingListFromDatabase;
- (PLVVodDownloadInfo *)downloadInfoWithVid:(NSString *)vid;
- (PLVVodDownloadInfo *)downloadInfoWithVideoParams:(PLVVodVideoParams *)videoParams;
- (BOOL)insertOrUpdateDatabaseWithDownloadInfo:(PLVVodDownloadInfo *)downloadInfo;
- (BOOL)updateDatabaseWithDownloadInfo:(PLVVodDownloadInfo *)downloadInfo;
- (BOOL)removeFromdatabseWithVid:(NSString *)vid;
- (BOOL)removeFromdatabseWithVideoParams:(PLVVodVideoParams *)videoParams;
- (BOOL)removeAllFromDatabase;

// table PLVVodVideoJson
- (BOOL)updateDatabaseWithVideoJson:(PLVVodVideoJson *)videoJson;
- (PLVVodVideoJson *)videoJsonWithVid:(NSString *)vid;


@end
