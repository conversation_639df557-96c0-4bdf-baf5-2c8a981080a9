//
//  PLVVodLocalVideo+Private.m
//  PolyvVodSDK
//
//  Created by mac on 2019/3/26.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import "PLVVodLocalVideo+Private.h"
#import "PLVVodUtil.h"

@implementation PLVVodLocalVideo (Private)

+ (instancetype)localAudioWithVid:(NSString *)vid dir:(NSString *)dir{
    
    BOOL isDir = YES;
    if (!vid.length || !dir.length) {
        //PLVVodLogError(@"%s - 参数为空", __FUNCTION__);
        return nil;
    }
    
    NSString *audioDir = [dir stringByAppendingPathComponent:@"Audio"];
    if (![[NSFileManager defaultManager] fileExistsAtPath:audioDir isDirectory:&isDir] || !isDir) {
        //PLVVodLogError(@"%s - 目录不存在", __FUNCTION__);
        return nil;
    }
    
    NSArray *subpaths  = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:audioDir error:nil];
    NSString *audioPath = nil;
    for (NSString *subpath in subpaths) {
        NSString *fileName = subpath.stringByDeletingPathExtension;
        NSString *subFileName = [NSString stringWithFormat:@"%@_", videoPoolIdWithVid(vid)];
        if (![fileName containsString:subFileName]) continue;
        
        // audio
        // audio/vid.mp3
        // add by libl [屏蔽断点临时文件] 19-04-26 start
        if ([[subpath pathExtension] isEqualToString:@"res"]){
            continue;
        }
        // add end

        audioPath = subpath;
        break;
    }
        
    if (audioPath){
        PLVVodLocalVideo *localVideo = [[PLVVodLocalVideo alloc] init];
        [localVideo setValue:vid forKey:@"vid"];
        [localVideo setValue:audioPath forKey:@"audioPath"];

        return localVideo;
    }
    
    return nil;
}

// 创建离线音频对象
+ (instancetype)localAudioWithVideo:(PLVVodVideo *)video dir:(NSString *)dir{
    if (!video || !dir.length) {
        PLVVodLogError(@"%s - 参数为空",__FUNCTION__);
        return nil;
    }
    BOOL isDir = NO;
    if (![[NSFileManager defaultManager] fileExistsAtPath:dir isDirectory:&isDir] || !isDir) {
        PLVVodLogError(@"%s - 目录不存在", __FUNCTION__);
        return nil;
    }
    
    if (![video canSwithPlaybackMode]){
        PLVVodLogWarn(@"%s - 不支持视频转音频服务",__FUNCTION__);
        return nil;
    }
    
    NSString *fileName = video.aac_link.lastPathComponent;
    NSString *audioDocPath = [dir stringByAppendingPathComponent:@"Audio"];
    NSString *audioFilePath = [audioDocPath stringByAppendingPathComponent:fileName];
    
    if (![[NSFileManager defaultManager] fileExistsAtPath:audioFilePath]){
        PLVVodLogWarn(@"%s - 本地音频文件不存在",__FUNCTION__);
        return nil;
    }
    
    PLVVodLogDebug(@"%@ -- 音频文件路径", audioFilePath);
    
    PLVVodLocalVideo *localVideo = [[self alloc] init];
    [localVideo setValue:audioFilePath forKey:@"audioPath"];
    [localVideo setupPrivateWithVideo:video];
    return localVideo;
}

- (void)setupPrivateWithVideo:(PLVVodVideo *)video {
    
//    self.vid = video.vid;
    [self setValue:video.vid forKey:@"vid"];
    [self setValue:@(video.qualityCount) forKey:@"qualityCount"];
    //self.qualityCount = video.qualityCount;
    [self setValue:@(video.preferredQuality) forKey:@"preferredQuality"];
    //self.preferredQuality = video.preferredQuality;
    [self setValue:video.snapshot.copy forKey:@"snapshot"];
    //self.snapshot = video.snapshot;
    [self setValue:@(video.duration) forKey:@"duration"];
    //self.duration = video.duration;
    [self setValue:video.filesizes.copy forKey:@"filesizes"];
    //self.filesizes = video.filesizes.copy;
    [self setValue:@(video.interactive) forKey:@"interactive"];
    //localVideo.interactive = video.interactive;
    [self setValue:video.title.copy forKey:@"title"];
    //self.title = video.title;
    [self setValue:video.srts.mutableCopy forKey:@"srts"];
    //self.srts = video.srts.copy;
    [self setValue:video.ads.mutableCopy forKey:@"ads"];
    //self.ads = video.ads.copy;
    [self setValue:@(video.isPlain) forKey:@"isPlain"];
//    self.isPlain = video.isPlain;
    [self setValue:@(video.isHls) forKey:@"isHls"];
    //self.isHls = video.isHls;
    [self setValue:@(video.isHls302) forKey:@"isHls302"];
    [self setValue:@(video.available) forKey:@"available"];
    [self setValue:video.teaser.copy forKey:@"teaser"];
    //self.teaser = video.teaser;
    [self setValue:@(video.teaserDuration) forKey:@"teaserDuration"];
    //self.teaserDuration = video.teaserDuration;
    [self setValue:@(video.teaserShow) forKey:@"teaserShow"];
    //self.teaserShow = video.teaserShow;
    [self setValue:@(video.status) forKey:@"status"];
    //self.status = video.status;
    [self setValue:@(video.outflow) forKey:@"outflow"];
    //self.outflow = video.outflow;
    [self setValue:@(video.timeoutflow) forKey:@"timeoutflow"];
    //self.timeoutflow = video.timeoutflow;
    [self setValue:video.hlsIndex.copy forKey:@"hlsIndex"];
    //self.hlsIndex = video.hlsIndex.copy;
    [self setValue:video.hlsVideos.mutableCopy forKey:@"hlsVideos"];
    //self.hlsVideos = video.hlsVideos.copy;
    [self setValue:video.hlsIndex2.copy forKey:@"hlsIndex2"];
    [self setValue:video.hlsVideos2.mutableCopy forKey:@"hlsVideos2"];
    [self setValue:video.plainVideos.mutableCopy forKey:@"plainVideos"];
    //self.plainVideos = video.plainVideos.copy;
    [self setValue:video.tsPackages.mutableCopy forKey:@"tsPackages"];
    //self.tsPackages = video.tsPackages.copy;
    [self setValue:video.constKey.copy forKey:@"constKey"];
    //self.constKey = video.constKey;
    [self setValue:@(video.keepSource) forKey:@"keepSource"];
    //self.keepSource = video.keepSource;
    [self setValue:video.categoryId forKey:@"categoryId"];
    //self.categoryId = video.categoryId;
    [self setValue:video.categoryTree.copy forKey:@"categoryTree"];
    //self.categoryTree = video.categoryTree.copy;
    [self setValue:video.availableRouteLines.copy forKey:@"availableRouteLines"];
    [self setValue:video.videokeyframes forKey:@"videokeyframes"];
    [self setValue:video.aac_link forKey:@"aac_link"];
    //self.availableRouteLines = video.availableRouteLines.copy;
}


- (void)test {
    [self isHls];
    
    NSLog(@"%@ -%@", self.audioPath, self.audioPath);
}

@end
