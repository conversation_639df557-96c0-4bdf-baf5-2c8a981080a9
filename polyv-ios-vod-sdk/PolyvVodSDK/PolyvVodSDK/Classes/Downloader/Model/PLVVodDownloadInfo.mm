//
//  PLVVodDownloadInfo.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/12.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodDownloadInfo.h"
#import <PLVFDB/PLVFDatabase.h>

@interface PLVVodDownloadInfo () <PLVFDatabaseProtocol>

/// PLVVodVideo 对象
@property (nonatomic, strong) PLVVodVideo *video;
@property (nonatomic, copy) NSString *vid;

/// 清晰度
@property (nonatomic, assign) PLVVodQuality quality;

/// 下载状态
@property (nonatomic, assign) PLVVodDownloadState state;

/// 下载速率（单位：byte/s）
@property (nonatomic, assign) double bytesPerSeconds;

/// 下载进度（0-1）
@property (nonatomic, assign) double progress;

/// 解压进度
@property (nonatomic, assign) double unzipProgress;

/// 下载错误
@property (nonatomic, strong) NSError *error;

/// 队列ID
@property (nonatomic, assign) NSInteger downloadId;

/// 文件类型
@property (nonatomic, assign) PLVDownloadFileType fileType;

/// 请求cdn资源时的唯一标志
@property (nonatomic, copy) NSString *did;

/// UI展示
@property (nonatomic, copy) NSString *snapshot; // 封面
@property (nonatomic, copy) NSString *title;    // 视频名称
@property (nonatomic, assign) NSUInteger filesize; // 文件大小
@property (nonatomic, assign) NSUInteger duration; // 视频时长

@end

@implementation PLVVodDownloadInfo

- (instancetype)initWithVideo:(PLVVodVideo *)video quality:(PLVVodQuality)quality {
	if (self = [super init]) {
		_video = video;
		_vid = video.vid;
		_quality = quality;
        _fileType = PLVDownloadFileTypeVideo; // 默认视频文件
        // add by libl [给info 赋值，用于UI层展示] 2018-07-07 start
        _snapshot = video.snapshot;
        _title = video.title;
        _filesize = 0;
        if (video.filesizes.count){
            if (PLVVodQualityAuto < quality && quality <= PLVVodQualityUltra){
                _filesize = [video.filesizes[quality-1] integerValue];
            }
            else{
                _filesize = [video.filesizes[PLVVodQualityAuto] integerValue];
            }
        }
        _duration = video.duration;
        // end
	}
	return self;
}

- (instancetype)initWithVideo:(PLVVodVideo *)video fileType:(PLVDownloadFileType)fileType quality:(PLVVodQuality)quality{
    if (self = [self initWithVideo:video quality:quality]){
        _fileType = fileType;
        
        if (_fileType == PLVDownloadFileTypeAudio){
            _filesize = video.aac_filesize;
        }
    }
    
    return self;
}

#pragma mark - property

- (void)setState:(PLVVodDownloadState)state {
	_state = state;
	if (self.stateDidChangeBlock) self.stateDidChangeBlock(self);
}

- (void)setBytesPerSeconds:(double)bytesPerSeconds {
	_bytesPerSeconds = bytesPerSeconds;
	if (self.bytesPerSecondsDidChangeBlock) self.bytesPerSecondsDidChangeBlock(self);
}

- (void)setProgress:(double)progress {
    _progress = progress;
    if (self.progressDidChangeBlock) self.progressDidChangeBlock(self);
}

- (void)setStateDidChangeBlock:(void (^)(PLVVodDownloadInfo *))stateDidChangeBlock {
	_stateDidChangeBlock = stateDidChangeBlock;
	if (stateDidChangeBlock) stateDidChangeBlock(self);
}

- (void)setBytesPerSecondsDidChangeBlock:(void (^)(PLVVodDownloadInfo *))bytesPerSecondsDidChangeBlock {
	_bytesPerSecondsDidChangeBlock = bytesPerSecondsDidChangeBlock;
	if (bytesPerSecondsDidChangeBlock) bytesPerSecondsDidChangeBlock(self);
}

- (void)setProgressDidChangeBlock:(void (^)(PLVVodDownloadInfo *))progressDidChangeBlock {
	_progressDidChangeBlock = progressDidChangeBlock;
	if (progressDidChangeBlock) progressDidChangeBlock(self);
}

- (void)setUnzipProgress:(double)unzipProgress{
    _unzipProgress = unzipProgress;
    if (self.unzipProgressDidChangeBlock) self.unzipProgressDidChangeBlock(self);
}

/// 解压进度回调
- (void)setUnzipProgressDidChangeBlock:(void (^)(PLVVodDownloadInfo *))unzipProgressDidChangeBlock{
    _unzipProgressDidChangeBlock = unzipProgressDidChangeBlock;
    if (unzipProgressDidChangeBlock) unzipProgressDidChangeBlock (self);
}

- (NSString *)identifier{
    return [NSString stringWithFormat:@"%@_%d", _vid, (int)_fileType];
}

- (NSString *)description {
	NSMutableString *description = [super.description stringByAppendingString:@":\n"].mutableCopy;
	[description appendFormat:@" vid: %@;\n", _vid];
	[description appendFormat:@" quality: %@;\n", NSStringFromPLVVodQuality(_quality)];
	[description appendFormat:@" state: %@;\n", NSStringFromPLVVodDownloadState(_state)];
	[description appendFormat:@" progress: %f;\n", _progress];
	[description appendFormat:@" downloadId: %@;\n", @(_downloadId)];
	return description;
}

// TODO: PLVFDB 支持设置默认值
//WCDB_DEFAULT(fileType, PLVDownloadFileTypeVideo)
#pragma mark - PLVFDatabaseProtocol

+ (nonnull NSString *)primaryKey {
    return @"vid";
}

+ (nonnull NSArray<NSString *> *)propertyKeys {
    return @[
        @"vid",
        @"quality",
        @"progress",
        @"state",
        @"downloadId",
        @"fileType",
        @"did",
// add by libl [增加字段用于UI展示] 2018-07-07 start
        @"snapshot",
        @"title",
        @"filesize",
        @"duration"
// end
    ];
}

@end
