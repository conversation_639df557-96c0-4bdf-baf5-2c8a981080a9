//
//  PLVVodLocalVideo+Private.h
//  PolyvVodSDK
//
//  Created by mac on 2019/3/26.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import <PLVVodSDK/PLVVodSDK.h>

NS_ASSUME_NONNULL_BEGIN

@interface PLVVodLocalVideo (Private)

/**
 获取对应目录的离线音频对象

 @param video videojson 模型
 
 */
+ (instancetype)localAudioWithVideo:(PLVVodVideo *)video dir:(NSString *)dir;

/**
 获取对应目录的离线音频对象

 @param vid 视频vid
 
 */
+ (instancetype)localAudioWithVid:(NSString *)vid dir:(NSString *)dir;
 

@end

NS_ASSUME_NONNULL_END
