//
//  PLVVodHlsTsDownload.m
//  _PolyvVodSDK
//
//  Created by mac on 2018/7/2.
//  Copyright © 2018年 POLYV. All rights reserved.
//
// 使用切片单片下载方式来进行下载
// 目前仅适用于直播转存视频，因此，下载源也有所不同

#import "PLVVodHlsTsDownloader.h"
#import "PLVVodUtil.h"
#import "PLVVodHlsHelper.h"
#import "PLVVodNetworking.h"
#import "PLVVodDownloadManager.h"
#import "PLVVodTsDownloadTask.h"
#import "PLVVodAttachMgr.h"

#define PLVTaskDescribeKey       @"key"
#define PLVTaskDescribeM3u8      @"m3u8"

@interface PLVVodHlsTsDownloader ()<NSURLSessionDelegate>

/// M3U8文件内容
@property (nonatomic, copy) NSString *m3u8Content;

/// 下载后未处理的 key data
@property (nonatomic, strong) NSData *tokenData;
@property (nonatomic, strong) NSData *keyData;
@property (nonatomic, copy) void (^requestKeyIfNeed)(void (^completion)(void));

/// ts任务数组(包括已经下载，未下载)
@property (nonatomic, strong) NSArray<PLVVodTsDownloadTask *> *tsTasks;

/// ts任务字典，通过 NSURLSessionTask 索引 PLVVodTsDownloadTask
@property (nonatomic, strong) NSMutableDictionary<id, PLVVodTsDownloadTask *> *tsTaskDict;

/// ts 总数
@property (nonatomic, assign) NSUInteger tsCount;

/// 已下载的ts数量
@property (nonatomic, assign) NSUInteger tsDownloadedCount;

/// 下载回调次数统计
@property (nonatomic, assign) NSUInteger tsCompletedCount;

/// 介于准备和准备就绪状态之间，正在创建任务
@property (nonatomic, assign) BOOL isCreateTask;

@end

@implementation PLVVodHlsTsDownloader
{
    BOOL _shouldStop;
}

- (void)dealloc{
    
    PLVVodLogDebug(@"[dealloc] -- %s", __FUNCTION__);
}

+ (instancetype)downloaderWithVideo:(PLVVodVideo *)video quality:(PLVVodQuality)quality {
    PLVVodHlsTsDownloader *downloader = [[self alloc] init];
    downloader.downloadInfo = [[PLVVodDownloadInfo alloc] initWithVideo:video quality:quality];
    return downloader;
}

#pragma mark - property

- (NSMutableDictionary *)tsTaskDict{
    if (!_tsTaskDict) {
        _tsTaskDict = [NSMutableDictionary dictionary];
    }
    return _tsTaskDict;
}

- (void)setTsDownloadedCount:(NSUInteger)tsDownloadedCount {
    if (!tsDownloadedCount) { // 设为0时，不记录进度
        _tsDownloadedCount = tsDownloadedCount;
        return;
    }
    if (tsDownloadedCount < _tsDownloadedCount) {
        PLVVodLogWarn(@"[download] -- ts片丢失: %zd \n", (long)tsDownloadedCount);
    }
    _tsDownloadedCount = tsDownloadedCount;
    // 记录完成进度
    self.downloadProgress.completedUnitCount = tsDownloadedCount;
}

#pragma mark - download

/// 下载前就绪
- (void)prepareWithCompletion:(void (^)(void))completion {
    // mod by libl [增加一种准备中状态，ts 单片下载创建任务花费时间长] 2018-11-15 start
    self.state = PLVVodDownloadStatePreparingStart;
    
    __weak typeof(self) weakSelf = self;
    
    if (!self.prepared) {
        // 若本地已存在改视频则不进行下载
        NSString *downloadDir = [PLVVodDownloadManager sharedManager].downloadDir;
        if (!downloadDir.length) {
            PLVVodLogError(@"[download] -- 下载目录为空");
            NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
            NSError *plvError = PLVVodErrorMake(download_dir_not_found, ^(NSMutableDictionary *userInfo) {
                userInfo[PLVVodErrorSelectorKey] = selector;
            });
            [self reportError:plvError downloader:self];
            return;
        }
        
        if ( 0 < [PLVVodDownloadManager videoExist:self.vid]){
            
            // manager 键值观察，回调
#warning -- TODO:后续完善相关逻辑
            // TODO: 还不完善，需要检查m3u8 文件，key文件（加密视频）
            self.state = PLVVodDownloadStateSuccess;
            return;
        }
        
        // 设置下载目录
        downloadDir = [downloadDir stringByAppendingPathComponent:[weakSelf rootName]];
        weakSelf.downloadDir = downloadDir;
        
        // 1. 请求M3U8
        [self createRequestForM3u8];
        
        // add by libl 添加字幕下载功能 19-04-02
        [PLVVodAttachMgr downloadSubtitlesWithVideo:self.video];
        
        // add by libl 添加问答下载功能 19-08-01
        [PLVVodAttachMgr downloadExamsWithVideo:self.video];
        
    } else {
        
        // 失败或停止下载后，需要重建任务，否则很多未知错误
        if (self.m3u8Content){
            PLVVodLogInfo(@"[download] -- createDownloadTasksWithComplete() line:%d vid: %@", __LINE__ ,self.vid);
            [self createDownloadTasksWithComplete:completion];
        }
        else{
            // 1. 请求M3U8
            PLVVodLogDebug(@"[downlaod] -- 再次请求m3u8 vid: %@",self.vid);
            [self createRequestForM3u8];
        }
    }
}

#pragma mark -- 网络请求m3u8 文件 --
- (void)createRequestForM3u8{
    
    NSError *error = nil;
    NSString *m3u8UrlStr = nil;
    if (self.video.isPlain){
        if (self.video.keepSource){
            m3u8UrlStr = self.video.play_source_url;
        }
        else{
            m3u8UrlStr = [self.video.plainVideos firstObject];
        }
    }
    else{
        m3u8UrlStr = self.video.hlsVideos[self.quality - 1];
    }
    
    NSString *did = self.downloadInfo.did;
    if (![PLVVodUtil isNilString:did]){
        NSDictionary *params = @{PLVDownlaodDid: self.downloadInfo.did};
        m3u8UrlStr = [PLVVodUtil url:m3u8UrlStr appendParams:params];
    }
    
    NSString *appId = [PLVVodSettings sharedSettings].appid;
    if (![PLVVodUtil isNilString:appId]){
        NSDictionary *params = @{PLVDownlaodAppId: appId};
        m3u8UrlStr = [PLVVodUtil url:m3u8UrlStr appendParams:params];
        PLVVodLogDebug(@"[downlaod] -- 子账号下载地址：%@ \n", m3u8UrlStr)
    }
    NSURLSessionDownloadTask *downloadTask = [self downloadTaskWithURL:[NSURL URLWithString:m3u8UrlStr] error:&error];
    
    if (!downloadTask) {
        PLVVodLogError(@"[download] -- m3u8下载任务创建异常，%@", error);
        [self reportError:error downloader:self];
        return;
    }
    
    downloadTask.taskDescription = PLVTaskDescribeM3u8;
    [downloadTask resume];
    
    // manager 键值观察， 建立download 与 task 的匹配
    self.m3u8DownloadTask = downloadTask;
}

- (void)handleResponseForM3u8:(NSString *)content error:(NSError *)error {
    if (error || !content.length) {
        PLVVodLogError(@"[download] -- m3u8获取失败");
        NSError *plvError = PLVVodErrorMakeWithError(m3u8_fetch_error, error, nil);
        [self reportError:plvError downloader:self];
        return;
    }
    
    // 1. 保存原始的 m3u8 文本到属性
    self.m3u8Content = content;
    
    // 2. httpDNS 处理 m3u8 文本
    if (self.video.isPlain){
        content = [PLVVodHlsHelper fixTsUrlInM3u8:content];
    }
    else{
        content = [PLVVodHlsHelper fixHttpDNSIfNeedInM3u8:content];
    }
    
    // 3. 读取文本获得 tsURLs 数组
    NSArray *tsUrls = [PLVVodHlsHelper mediaListOfM3U8:content handler:nil];
    
    // 4. 赋值属性：tsCount
    self.tsCount = tsUrls.count;
    if (self.tsCount <= 0) {
        PLVVodLogError(@"[download] -- 无法获取任何切片URL");
        NSError *plvError = PLVVodErrorMake(ts_not_found, ^(NSMutableDictionary *userInfo) {
            
        });
        [self reportError:plvError downloader:self];
        return;
    }
    self.downloadProgress.totalUnitCount = self.tsCount;
    self.downloadProgress.totalUnitCount += 1;
    
    // 5. 遍历 tsURLs，创建 tsTasks 模型数组，并存储到属性
    NSString *appId = [PLVVodSettings sharedSettings].appid;
    NSMutableArray *tsTasks = [NSMutableArray array];
    NSString *newUrl = nil;
    for (NSString *tsUrl in tsUrls) {
        newUrl = tsUrl;
        if (![PLVVodUtil isNilString:appId]){
            NSDictionary *params = @{PLVDownlaodAppId: appId};
            newUrl = [PLVVodUtil url:tsUrl appendParams:params];
            PLVVodLogDebug(@"[downlaod] -- ts下载地址：%@ \n", newUrl);
        }
        NSURL *tsURL = [NSURL URLWithString:newUrl];
        PLVVodTsDownloadTask *tsTask = [PLVVodTsDownloadTask tsTaskWithURL:tsURL];
        // 初始化子进度
        [self.downloadProgress becomeCurrentWithPendingUnitCount:1];
        tsTask.tsDownloadProgress = [NSProgress progressWithTotalUnitCount:-1];
        tsTask.tsDownloadProgress.kind = NSProgressKindFile;
        [tsTask.tsDownloadProgress setUserInfoObject:tsTask.fileName forKey:@"file"];
        [self.downloadProgress resignCurrent];
        [tsTasks addObject:tsTask];
    }
    self.tsTasks = tsTasks;
    
    __weak typeof (self) weakSelf = self;
    // 6. 下载 key，并赋值属性
    self.requestKeyIfNeed = ^(void (^completion)(void)) {
        __strong typeof (weakSelf) strongSelf = weakSelf;
        
        if (strongSelf.video.isPlain) return;
        if (strongSelf.keyData.length) {
            if (completion) completion();
            return;
        }

        [PLVVodNetworking
            requestKeyRquestTokenWithVid:strongSelf.vid
                              completion:^(NSString *tokenData,
                                           NSError *error) {
                                if (error) {
                                    PLVVodLogError(@"[download] -- 下载 token "
                                                    @"任务异常，%@",
                                                    error);
                                    [strongSelf tsDownloadFailed];
                                    [strongSelf reportError:error downloader:strongSelf];
                                    return;
                                }

                                strongSelf.tokenData = [tokenData dataUsingEncoding:NSUTF8StringEncoding];
                                [strongSelf createRequestForKey:tokenData];
                              }];
    };
    
    self.requestKeyIfNeed(nil);
    
    // 只操作一次
    self.prepared = YES;
    
    // 创建下载任务
    [self createDownloadTasksWithComplete:^{
        [weakSelf resumeNextTask];
    }];
}

- (void)createRequestForKey:(NSString*)token {
    
    NSError *error;
    NSMutableURLRequest *request = [PLVVodNetworking keyRquestWithVid:self.vid token:token quality:self.quality];
    NSURLSessionDownloadTask *downloadTask = [self downloadTaskWithRequest:request error:&error];
    if (!downloadTask) {
        PLVVodLogError(@"[download] -- key 下载任务创建异常，%@", error);
        [self tsDownloadFailed];
        [self reportError:error downloader:self];
        return;
    }

    downloadTask.taskDescription = PLVTaskDescribeKey;
    [downloadTask resume];
    
    // manager 键值观察， 建立download 与 task 的匹配
    self.keyDownloadTask = downloadTask;
}

- (void)handleResponseForKey:(NSData *)data error:(NSError *)error{
    
    if (error) {
        PLVVodLogError(@"[download] -- 播放秘钥请求错误，%@", error);
        NSError *plvError = PLVVodErrorMakeWithError(key_fetch_error, error, ^(NSMutableDictionary *userInfo) {

        });
        [self reportError:plvError downloader:self];
        return;
    }
    
    self.keyData = data;
    PLVVodLogDebug(@"[download] -- 播放秘钥已获取，%@", self.vid);
}

- (void)createDownloadTasksWithComplete:(void(^)(void))completion{
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSDate *start = [NSDate date];
        
        // add by libl [创建任务中标志] 2018-09-14 start
        _isCreateTask = YES;
        // add end
        
        // 维护变量
        self.tsDownloadedCount = 0;
        self.tsCompletedCount = 0;
        
        // 用于统计已下载的切片个数
        int tsDownloadedCount = 0;
        // 用于装载任务数组
        NSMutableArray *downloadTasks = [NSMutableArray array];
        for (NSInteger i = 0; i < self.tsTasks.count; i++) {
            PLVVodTsDownloadTask *tsTask = self.tsTasks[i];
            NSURL *tsURL = tsTask.URL;
            
            // 1. 检查对应的 ts 文件
            if ([self fileExistForURL:tsURL]) {
                tsDownloadedCount ++;
                tsTask.downloadState = PLVVodDownloadStateSuccess;
                continue;
            }
            
            // 2. 创建下载任务
            NSError *error = nil;
            NSURLSessionDownloadTask *downloadTask = [self downloadTaskWithURL:tsURL error:&error];
            if (!downloadTask) {
                PLVVodLogError(@"[download] -- 下载任务创建异常，%@", error);
                [self reportError:error downloader:self];
                return;
            }
            
            // 3. 用户调用停止下载则跳出循环
            if (_shouldStop) {
                PLVVodLogWarn(@"%s - %@ - should stop", __FUNCTION__, [NSThread currentThread]);
                return;
            }
            
            self.tsTaskDict[@(downloadTask.taskIdentifier)] = tsTask;
            tsTask.downloadTask = downloadTask;
            tsTask.downloadState = PLVVodDownloadStateReady;
            
            [downloadTasks addObject:downloadTask];
        }
        
        // 任务创建完成
        _isCreateTask = NO;
        
        // 更新已下载切片数量
        if (self.tsDownloadedCount < tsDownloadedCount) {
            self.tsDownloadedCount = tsDownloadedCount;
        }
        
        // 是否已全部下载完
        if (self.tsCount == tsDownloadedCount && self.tsCount > 0) {
            [self tsDownloadCompletion];
            return;
        }
        
        if (_shouldStop) {
            PLVVodLogWarn(@"%s - %@ - should stop", __FUNCTION__, [NSThread currentThread]);
            return;
        }
        
        // manager 键值观察，建立download 与 taskID map 关系
        self.downloadTasks = downloadTasks;
        
        if (_shouldStop) {
            PLVVodLogWarn(@"%s - %@ - should stop", __FUNCTION__, [NSThread currentThread]);
            return;
        }
        // 更新下载状态，准备完成
        self.state = PLVVodDownloadStateReady;
        
        if (completion) completion();
        
        NSTimeInterval spendTime =[[NSDate date] timeIntervalSinceDate:start];
        PLVVodLogDebug(@"[download] -- createDownloadTasksWithComplete spendTime: %f ", spendTime);
    });
}

- (void)startDownload {
    if (self.state == PLVVodDownloadStateReady) {
        
        // 这里是否需要调用 resumeNextTask ？？？
        [self resumeNextTask];
        return;
    }
    [super startDownload];
    __weak typeof(self) weakSelf = self;
    
    // add by libl [开始下载] 2018-07-02 start
    _shouldStop = NO;
    // end
    
    // add by libl [正在创建任务标识] 2018-09-14 start
    _isCreateTask = NO;
    // add end
   
    [self prepareWithCompletion:^{
        
        [weakSelf resumeNextTask];
    }];
}

- (void)stopDownload {
    [super stopDownload];
    _shouldStop = YES;
    
    // mod by libl [停止下载，清除所有task，不然会有很多未知错误]
    [[self.tsTaskDict allValues] enumerateObjectsUsingBlock:^(PLVVodTsDownloadTask * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        PLVVodTsDownloadTask *tsTask = obj;
        
        // 后台下载，可能存在已经完成的task，但还未经Delegate 回调处理
        if (tsTask.downloadTask.state != NSURLSessionTaskStateCompleted){
            tsTask.downloadState = PLVVodDownloadStateStopped;
            [tsTask.downloadTask cancel];
            tsTask.downloadTask = nil;
        }
    }];
    [self.tsTaskDict removeAllObjects];
    
    // add by libl [] 2018-09-14 start
    // 如果当前是准备下载状态，不用修改状态
    if (self.state == PLVVodDownloadStatePreparing)
        return;
    // add end
    
    self.state = PLVVodDownloadStateStopped;
}

- (void)tsDownloadFailed{
    
    _shouldStop = YES;
    
    [[self.tsTaskDict allValues] enumerateObjectsUsingBlock:^(PLVVodTsDownloadTask * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        PLVVodTsDownloadTask *tsTask = obj;
        
        // 后台下载，可能存在已经完成的task，但还未经Delegate 回调处理
        if (tsTask.downloadTask.state != NSURLSessionTaskStateCompleted){
            tsTask.downloadState = PLVVodDownloadStateStopped;
            [tsTask.downloadTask cancel];
            tsTask.downloadTask = nil;
        }
    }];
    
    [self.tsTaskDict removeAllObjects];
    self.tsTaskDict = nil;
}

- (void)resumeNextTask {
    // > 调用了 `-stopDownload` 方法则跳出方法，不继续下载
    if (_shouldStop && self.downloadInfo.state == PLVVodDownloadStateStopped) return;
    
    // 已经成功下载，不再调用
    if (PLVVodDownloadStateSuccess == self.state) return;
    
    int tsDownloadedCount = 0;
    // 使用 tsTasks 数组来获取 downloadTask
    for (PLVVodTsDownloadTask *tsTask in self.tsTasks) {
        
        if (tsTask.downloadState == PLVVodDownloadStateSuccess || [self fileExistForURL:tsTask.URL]) {
            // 任务曾下载成功或本地存在该文件
            if (tsTask.downloadState != PLVVodDownloadStateSuccess){
                tsTask.downloadState = PLVVodDownloadStateSuccess;
                
            }
            tsDownloadedCount ++;
            continue;
        }
        
        // 为什么出现这种场景？不应该出现这种情况，必须弄清楚出现的原因。
        // 可能场景之一：stopDownload 里面清空了。
        if (!tsTask.downloadTask) {
            PLVVodLogWarn(@"[download] -- 下载任务不存在");
            continue;
        }
        
        if (tsTask.downloadTask.state == NSURLSessionTaskStateSuspended)
        {
            // 逐个启动
            [tsTask.downloadTask resume];
            tsTask.downloadState = PLVVodDownloadStateRunning;

            self.state = PLVVodDownloadStateRunning;
        }
       
        break;
    }
    
    // 维护变量
    if (self.tsDownloadedCount < tsDownloadedCount)
        self.tsDownloadedCount = tsDownloadedCount;
    
    // 检测成功下载
    if (self.tsDownloadedCount >= self.tsCount && (self.tsCount > 0)) { // 所有TS都已下载完成

        [self tsDownloadCompletion];
        return;
    }
    
    // 所有ts 任务已经完成，但ts个数不够，判定下载失败
    if ((self.tsCompletedCount > 0) &&
        (self.tsCompletedCount == self.downloadTasks.count) &&
        (self.tsDownloadedCount < self.tsCount)){
        
        // 下载失败
        PLVVodLogError(@"[download] -- 下载错误,ts 文件缺失，下载总数 ：%d 完成数 : %d", (int)self.tsDownloadedCount,(int)self.tsCompletedCount );
        NSError *plvError = PLVVodErrorMake(download_error_lost_ts, ^(NSMutableDictionary *userInfo) {
        });
        
        [self reportError:plvError downloader:self];
        return;
    }
}

/// ts 下载完成
- (void)tsDownloadCompletion {
    // 停止计时器
    [self stopSpeedTimer];
    
    // 维护变量
    self.tsDownloadedCount = 0;
    self.tsTaskDict = nil;
    
    // ???? 为什么不清空？待定
    // self.tsTasks = nil;
    
    NSError *error = nil;
    __weak typeof(self) weakSelf = self;
    
    // 处理 key
    if (self.requestKeyIfNeed){
        
        self.requestKeyIfNeed(^{
            [weakSelf writeToken];
            [weakSelf writeKey];
        });
    }
    
    // 处理M3U8
    NSString *m3u8Content = [PLVVodHlsHelper fixTsLocallyInM3u8:self.m3u8Content];
    if (!self.video.isPlain){
        m3u8Content = [PLVVodHlsHelper fixKeyInM3u8:m3u8Content];
    }
    
    if (!m3u8Content.length) {
        PLVVodLogError(@"[download] -- 修复切片路径失败");
        NSError *plvError = PLVVodErrorMake(ts_path_fix_error, ^(NSMutableDictionary *userInfo) {
            
        });
        [self reportError:plvError downloader:self];
        return;
    }
    
    NSString *m3u8Path = [self.rootName stringByAppendingPathExtension:@"m3u8"];
    m3u8Path = [self.downloadDir stringByAppendingPathComponent:m3u8Path];
    [m3u8Content writeToFile:m3u8Path atomically:YES encoding:NSUTF8StringEncoding error:&error];
    if (error) {
        PLVVodLogError(@"[download] -- m3u8下载错误，%@", error.localizedDescription);
        NSError *plvError = PLVVodErrorMakeWithError(m3u8_fetch_error, error, ^(NSMutableDictionary *userInfo) {
            
        });
        [self reportError:plvError downloader:self];
    } else {
        // 写入成功
        // 汇报进度，仅增加一个单位进度
        self.downloadProgress.completedUnitCount ++;
        
        // manager 键值观察回调
        self.state = PLVVodDownloadStateSuccess;
    }
    
    // 处理完 m3u8 不清空，以备下载直接从属性的值写到文件
    //self.m3u8Content = nil;
}

- (void)writeKey {
    if (!self.keyData.length) { // TODO: ask check self.keyData?
        PLVVodLogError(@"[download] -- 获取key文件失败");
        NSError *plvError = PLVVodErrorMake(key_fetch_error, ^(NSMutableDictionary *userInfo) {
        });
        [self reportError:plvError downloader:self];
        return;
    }
    
    NSError *error = nil;
    NSString *keyPath = [self.downloadDir stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.key", self.rootName]];
    if ([self fileExistForURL:[NSURL URLWithString:keyPath]]){
        return;
    }
    
    NSData *keyData = [PLVVodHlsHelper ecryptKey:self.keyData vid:self.downloadInfo.video.vid];
    [keyData writeToFile:keyPath options:NSDataWritingAtomic error:&error];
    if (error) {
        PLVVodLogError(@"[download] -- 播放秘钥写入失败，%@", error.localizedDescription);
        NSError *plvError = PLVVodErrorMakeWithError(key_write_error, error, nil);
        [self reportError:plvError downloader:self];
    }
    error = nil;
}

- (void)writeToken {
    if (!self.tokenData.length) {
        PLVVodLogError(@"[download] -- 获取令牌文件失败");
        NSError *plvError = PLVVodErrorMake(token_fetch_error, ^(NSMutableDictionary *userInfo) {
        });
        [self reportError:plvError downloader:self];
        return;
    }
    
    NSError *error = nil;
    NSString *keyPath = [self.downloadDir stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.token", self.rootName]];
    if ([self fileExistForURL:[NSURL URLWithString:keyPath]]){
        return;
    }
    
    // NSData *tokenData = [PLVVodHlsHelper ecryptKey:self.tokenData vid:self.downloadInfo.video.vid];
    NSData *tokenData = self.tokenData;
    [tokenData writeToFile:keyPath options:NSDataWritingAtomic error:&error];
    if (error) {
        PLVVodLogError(@"[download] -- 播放令牌写入失败，%@", error.localizedDescription);
        NSError *plvError = PLVVodErrorMakeWithError(token_write_error, error, nil);
        [self reportError:plvError downloader:self];
    }
    error = nil;
}

#pragma mark - NSURLSessionDownloadDelegate

// 每次获取数据
- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didWriteData:(int64_t)bytesWritten totalBytesWritten:(int64_t)totalBytesWritten totalBytesExpectedToWrite:(int64_t)totalBytesExpectedToWrite{
    PLVVodTsDownloadTask *tsTask = self.tsTaskDict[@(downloadTask.taskIdentifier)];
    
    if (self.downloadProgress && tsTask.tsDownloadProgress) {
        tsTask.tsDownloadProgress.totalUnitCount = totalBytesExpectedToWrite;
        tsTask.tsDownloadProgress.completedUnitCount = totalBytesWritten;
    }
    
    // 累计下载长度
    self.bytesAccumulator += bytesWritten;
}

/// 下载成功，此方法内完成文件移动
- (void)URLSession:(NSURLSession *)session downloadTask:(NSURLSessionDownloadTask *)downloadTask didFinishDownloadingToURL:(NSURL *)location{
    NSError *error;
    // add by libl [优先处理m3u8 文件] 2018-07-02 start
    if ([downloadTask.taskDescription isEqualToString:PLVTaskDescribeM3u8]){
        // TODO:
        NSString *m3u8Content = [NSString stringWithContentsOfURL:location encoding:NSUTF8StringEncoding error:&error];
        if (error){
            [self handleResponseForM3u8:m3u8Content error:error];
        }
        else {
            [self handleResponseForM3u8:m3u8Content error:error];
        }
        return;
    }
    // end
    
    // add by libl [优先处理key 文件] 2018-07-02 start
    if ([downloadTask.taskDescription isEqualToString:PLVTaskDescribeKey]){
        // TODO:
        NSData *keyData = [NSData dataWithContentsOfURL:location options:NSDataReadingUncached error:&error];
        if (error){
            // 暂不处理，先让ts 下载
        }
        else {
            [self handleResponseForKey:keyData error:error];
        }
        return;
    }
    // end
    
    PLVVodTsDownloadTask *tsTask = self.tsTaskDict[@(downloadTask.taskIdentifier)];
    NSString *fileName = tsTask.fileName;
    if (!fileName.length) {
        fileName = downloadTask.originalRequest.URL.lastPathComponent;
    }
    if (!fileName.length) {
        fileName = downloadTask.response.URL.lastPathComponent;
    }
    NSString *destinationFilePath = [self.downloadDir stringByAppendingPathComponent:fileName];
    
    // 移动文件
    BOOL isMoved = [self fileExistForURL:[NSURL URLWithString:destinationFilePath]];
    if (isMoved){
        PLVVodLogInfo(@"[download] -- 文件已经存在");
    }
    else{
        isMoved = [PLVVodUtil movePath:location.path toPath:destinationFilePath error:&error];
    }
    
    if (error) {
        // 这里不要报错，在ts 下载完成后统一检查
    }
    else if(isMoved){
        // 记录到对象
        if (tsTask) {
            if (downloadTask){
                [self.tsTaskDict removeObjectForKey:downloadTask];
            }
            
            tsTask.downloadState = PLVVodDownloadStateSuccess;
            tsTask.downloadTask = nil;
        }
        
        // 计数
        self.tsDownloadedCount++;
    }
}

#pragma mark URLSessionTaskDelegate
/// 任务完成
- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(NSError *)error{
    if (error.code == NSURLErrorCancelled) {
        // 任务被取消，主动调用了stop ，可以不报错
        PLVVodLogDebug(@"[download] -- 任务取消 taskID: %d", (int)task.taskIdentifier);
        return;
    }

    // 暂时不处理，继续后续流程
//    if (error.code == NSURLErrorBackgroundSessionWasDisconnected) {
//        // 失去后台服务， 这个如何处理？待定
//        return;
//    }
    
    // mod by libl [优化容错处理，这里需要减少报错，等待所有ts文件回调完成，再报错误] 2018-07-02 start
//    if (error) {
//        PLVVodLogError(@"下载错误，%@", error);
//        NSError *plvError = PLVVodErrorMakeWithError(download_error, error, ^(NSMutableDictionary *userInfo) {
//
//        });
//
//        [self tsDownloadFail];
//        [self reportError:plvError downloader:self];
//        return;
//    }
    // end
    
    //ts 下载回调计数,过滤key，m3u8
    if ([task.taskDescription isEqualToString:PLVTaskDescribeKey] ||
        [task.taskDescription isEqualToString:PLVTaskDescribeM3u8]){
        
    }
    else{
        self.tsCompletedCount ++;
        [self resumeNextTask];
    }
}


@end
