//
//  PLVVodSettings.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/9.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodSettings.h"
#import "PLVVodUtil.h"
#import "PLVVodHlsHelper.h"
#import "PLVAccountModel.h"
#import "PLVVodDownloadManager.h"

/// SDK 版本，组成：<主版本号>.<次版本号>.<子版本号>.<版本日期>
/**
 2.0.0+180319
 2.0.0+180321
 2.1.0+180327
 2.2.0+180412
 2.3.0+180608
 2.3.1+180611
 2.3.2+180621
 2.3.3+180626
 2.3.4+180627
 2.3.5+180628
 2.4.0+180717
 2.4.1+180831
 2.5.0+180917
 2.5.1+180926
 2.5.2+181108
 2.5.3+181217
 2.5.4+190212
 2.5.5+190226
 2.5.6+190403
 2.6.0+190515
 2.6.1+190520
 2.6.2+190625
 2.6.3+190813
 2.6.4+190827
 2.6.5+191028
 2.6.6+200106
 2.7.0+200106
 2.7.1+200322
 2.8.0+200331
 2.8.1+200508
 2.9.0+200519
 2.9.1+200601
 2.10.0+200615
 2.11.0+200720
 2.12.0+200908
 2.12.1+201019
 2.13.0+201023
 2.14.0+201223
 2.14.1+210225
 2.15.0+210414
 2.15.1+210512
 2.15.2+210610
 2.16.0+210629
 2.16.1+210722
 2.16.2+210806
 2.16.3+210906
 2.16.4+211015
 2.16.5+211202
 2.16.6+220406
 2.16.7-abn+220826
 2.17.0+220905
 2.18.0+220922
 2.18.1+221012
 2.18.3+221118
 2.18.4+230315
 2.19.0-abn+230713
 2.19.1+230803
 2.20.0+230901
 2.21.0+240105
 2.21.1+240319
 2.22.0+240607
 2.22.1+240718
 2.22.2+241008
 2.22.3+241128
 2.22.4+250116
 2.23.0+250415
 
 - ABN_VERSION -
 2.5.2+181101
 2.5.3+181127
 2.5.4+190109
 2.5.5+190126
 2.5.6+190307
 2.6.0+190403
 2.6.1+190506
 2.6.2+190528
 2.6.5+190828
 
 */

NSString * const PLVVodSdkVersion = @"2.24.0+250626";
NSString * const PLVVodSdkPlatform = @"polyv-ios-sdk";

NSString * const kDefaultViewerId = @"viewer_id";

NSString * const PLVHlsPrivateVersion = @"2";
NSString * const PLVNativeKeyVersion = @"13";

// 本地视频播放，移除secretkey适配标志
NSString * const kHasExecuteInitSecretKey = @"kHasExecuteInitSecretKey";


// viewlog 参数设置
@implementation PLVVodViewerInfo

@end


@interface PLVVodSettings ()

/// userid
@property (nonatomic, copy) NSString *userid;

/// readtoken
@property (nonatomic, copy) NSString *readtoken;

/// writetoken
@property (nonatomic, copy) NSString *writetoken;

/// secretkey
@property (nonatomic, copy) NSString *secretkey;

/// 子账号appid
@property (nonatomic, copy) NSString *appid;

/// 保存多账号的数组
@property (nonatomic, copy) NSMutableArray<PLVAccountModel *> *accountArray;

@end

@implementation PLVVodSettings
@synthesize viewerId = _viewerId;
@synthesize viewerName = _viewerName;
@synthesize viewerAvatar = _viewerAvatar;

static PLVVodSettings *_sharedSettings = nil;
static BOOL _delayHttpDNS = NO;
static NSInteger _delayHttpDNSTime = 2;

#pragma mark - singleton init

+ (instancetype)sharedSettings {
	static dispatch_once_t onceToken;
	dispatch_once(&onceToken, ^{
		_sharedSettings = [[PLVVodSettings alloc] init];
        // mod by libl [银河证券，部分ios8系统，调用commonInit 崩溃] 19-01-22 start
        if ([_sharedSettings respondsToSelector:@selector(commonInit)]){
            [_sharedSettings commonInit];
            NSLog(@"--- commonInit ---");
        }
        else{
            //
            NSLog(@"--- commonInit Not Execute ---");
        }
        // mod end
	});
	return _sharedSettings;
}

+ (instancetype)allocWithZone:(struct _NSZone *)zone {
	static dispatch_once_t onceToken;
	dispatch_once(&onceToken, ^{
		if (!_sharedSettings) {
			_sharedSettings = [super allocWithZone:zone];
		}
	});
	return _sharedSettings;
}

- (id)copyWithZone:(NSZone *)zone {
	return _sharedSettings;
}

- (void)commonInit {
	// 配置属性
	_logLevel = PLVVodLogLevelWithoutDebug;
    self.enableHttpDNS = YES; // 默认开启httpsdns
    self.enableIPV6 = NO; // 默认关闭IPV6
}

-(PLVVodViewerInfo *)viewerInfos{
    if (!_viewerInfos){
        _viewerInfos = [[PLVVodViewerInfo alloc] init];
    }
    
    return _viewerInfos;
}

-(NSMutableArray<PLVAccountModel *> *)accountArray {
    if (!_accountArray) {
        _accountArray = [NSMutableArray array];
    }
    return _accountArray;
}

#pragma mark - property

- (void)setUserid:(NSString *)userid {
	_userid = [userid stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
	if (!_userid.length) {
		PLVVodLogError(@"userid不能为空");
		NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
		NSError *plvError = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
			userInfo[PLVVodErrorSelectorKey] = selector;
		});
		[self reportError:plvError];
	}
}
- (void)setSecretkey:(NSString *)secretkey {
	_secretkey = [secretkey stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
	if (!_secretkey.length) {
		PLVVodLogError(@"secretkey不能为空");
		NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
		NSError *plvError = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
			userInfo[PLVVodErrorSelectorKey] = selector;
		});
		[self reportError:plvError];
	}
}
- (void)setReadtoken:(NSString *)readtoken {
	_readtoken = [readtoken stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
	if (!_readtoken.length) {
		PLVVodLogError(@"readtoken不能为空");
		NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
		NSError *plvError = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
			userInfo[PLVVodErrorSelectorKey] = selector;
		});
		[self reportError:plvError];
	}
}
- (void)setWritetoken:(NSString *)writetoken {
	_writetoken = [writetoken stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
	if (!_writetoken.length) {
		PLVVodLogError(@"writetoken不能为空");
		NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
		NSError *plvError = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
			userInfo[PLVVodErrorSelectorKey] = selector;
		});
		[self reportError:plvError];
	}
}

- (void)setEnableHttpDNS:(BOOL)enableHttpDNS {
	_enableHttpDNS = enableHttpDNS;
}

+ (BOOL)delayHttpDNS {
    return _delayHttpDNS;
}

+ (void)setDelayHttpDNS:(BOOL)delayHttpDNS {
    _delayHttpDNS = delayHttpDNS;
}

+ (NSInteger)delayHttpDNSTime {
    return _delayHttpDNSTime;
}

+ (void)setDelayHttpDNSTime:(NSInteger)delayHttpDNSTime {
    _delayHttpDNSTime = delayHttpDNSTime;
}

#pragma mark - public

+ (instancetype)settingsWithUserid:(NSString *)userid readtoken:(NSString *)readtoken writetoken:(NSString *)writetoken secretkey:(NSString *)secretkey {
	PLVVodSettings *settings = [self sharedSettings];
	settings.userid = userid;
	settings.readtoken = readtoken;
	settings.writetoken = writetoken;
	settings.secretkey = secretkey;
    
    //
    [self migrateCacheVideoWithSecretKey:settings.secretkey];

	return settings;
}

+ (instancetype)settingsWithAppId:(NSString *)appId secretKey:(NSString *)secretKey userId:(NSString *)userId{
    PLVVodSettings *settings = [self sharedSettings];
    settings.appid = appId;
    settings.secretkey = secretKey;
    settings.userid = userId;
    
    //
    [self migrateCacheVideoWithSecretKey:settings.secretkey];

    return settings;
}

+ (instancetype)settingsWithConfigString:(NSString *)configString error:(NSError **)error {
	return [self settingsWithConfigString:configString key:nil iv:nil error:error];
}

+ (instancetype)settingsWithConfigString:(NSString *)configString key:(NSString *)key iv:(NSString *)iv error:(NSError **)error {
	if (!key) key=@"VXtlHmwfS2oYm0CZ";
	if (!iv) iv=@"2u9gDPKdX6GyQJKU";
	
	if (key.length != 16){
		PLVVodLogError(@"加密秘钥不合法");
		NSError *plvError = PLVVodErrorWithCode(account_encode_key_illegal);
        if (error) *error = plvError;
		return nil;
	}
	if (iv.length != 16) {
		PLVVodLogError(@"加密向量不合法");
		NSError *plvError = PLVVodErrorWithCode(account_encode_iv_illegal);
        if (error) *error = plvError;
		return nil;
	}
	
	NSString *base64String = [[configString componentsSeparatedByCharactersInSet:[NSCharacterSet newlineCharacterSet]] componentsJoinedByString:@""];
	NSData *decodedData = [[NSData alloc] initWithBase64EncodedString:base64String options:0];
	
	NSData *endata = [PLVVodUtil AES128DecryptedDataWithKey:key iv:iv data:decodedData];
	NSString *decryptString = [[NSString alloc] initWithData:endata encoding:NSUTF8StringEncoding];
	NSArray *configs = [decryptString componentsSeparatedByString:@","];
	if (!configs || configs.count != 4) {
		PLVVodLogError(@"加密串解密参数有误！");
		NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
		NSError *plvError = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
			userInfo[PLVVodErrorSelectorKey] = selector;
		});
        if (error) *error = plvError;
		return nil;
	}
	PLVVodSettings *settings = [self sharedSettings];
	settings.userid = configs[0];
	settings.secretkey = configs[1];
	settings.readtoken = configs[2];
	settings.writetoken = configs[3];
    
	settings.userid = [settings.userid stringByReplacingOccurrencesOfString:@"\0" withString:@""];
	settings.secretkey = [settings.secretkey stringByReplacingOccurrencesOfString:@"\0" withString:@""];
	settings.readtoken = [settings.readtoken stringByReplacingOccurrencesOfString:@"\0" withString:@""];
	settings.writetoken = [settings.writetoken stringByReplacingOccurrencesOfString:@"\0" withString:@""];
    
    //
    [self migrateCacheVideoWithSecretKey:settings.secretkey];
	
	return settings;
}

- (NSString *)description {
	NSMutableString *description = [NSMutableString string];
	[description appendFormat:@"PolyvVodSDK version: %@\n", PLVVodSdkVersion];
	[description appendString:[super.description stringByAppendingString:@":\n"]];
	[description appendFormat:@" userid: %@;\n", _userid];
	[description appendFormat:@" readtoken: %@;\n", _readtoken];
	[description appendFormat:@" writetoken: %@;\n", _writetoken];
	[description appendFormat:@" secretkey: %@;\n", _secretkey];
	return description;
}

#pragma mark -- 多账号相关 --

+ (instancetype)addAccountWithUserid:(NSString *)userid readtoken:(NSString *)readtoken writetoken:(NSString *)writetoken secretkey:(NSString *)secretkey error:(NSError **)error {
    PLVVodSettings *settings = [self sharedSettings];
    if ([PLVVodUtil isNilString:settings.userid] ||
        [PLVVodUtil isNilString:settings.secretkey] ||
        [PLVVodUtil isNilString:settings.readtoken] ||
        [PLVVodUtil isNilString:settings.writetoken]) {
        PLVVodLogError(@"添加多账号失败，请先初始化SDK再添加多账号。");
        NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
        NSError *plvError = PLVVodErrorMake(account_video_illegal, ^(NSMutableDictionary *userInfo) {
            userInfo[PLVVodErrorSelectorKey] = selector;
        });
        if (error) *error = plvError;
        return nil;
    }
    
    if ([settings.userid isEqualToString:userid]) {
        return settings;
    }
    if (![settings accountModelExistWithUserId:userid]) {
        PLVAccountModel *accountModel = [[PLVAccountModel alloc]init];
        accountModel.userid = userid;
        accountModel.secretkey = secretkey;
        accountModel.readtoken = readtoken;
        accountModel.writetoken = writetoken;
        [settings.accountArray addObject:accountModel];
        return settings;
    }
    
    return settings;
}

+ (instancetype)addAccountWithConfigString:(NSString *)configString error:(NSError **)error {
    return [self addAccountWithConfigString:configString key:nil iv:nil error:error];
}

+ (instancetype)addAccountWithConfigString:(NSString *)configString key:(NSString *)key iv:(NSString *)iv error:(NSError **)error {
    PLVVodSettings *settings = [self sharedSettings];
    if ([PLVVodUtil isNilString:settings.userid] ||
        [PLVVodUtil isNilString:settings.secretkey] ||
        [PLVVodUtil isNilString:settings.readtoken] ||
        [PLVVodUtil isNilString:settings.writetoken]) {
        PLVVodLogError(@"添加多账号失败，请先初始化SDK再添加多账号。");
        NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
        NSError *plvError = PLVVodErrorMake(account_video_illegal, ^(NSMutableDictionary *userInfo) {
            userInfo[PLVVodErrorSelectorKey] = selector;
        });
        if (error) *error = plvError;
        return nil;
    }
    
    if (!key) key=@"VXtlHmwfS2oYm0CZ";
    if (!iv) iv=@"2u9gDPKdX6GyQJKU";
    
    if (key.length != 16){
        PLVVodLogError(@"加密秘钥不合法");
        NSError *plvError = PLVVodErrorWithCode(account_encode_key_illegal);
        if (error) *error = plvError;
        return nil;
    }
    if (iv.length != 16) {
        PLVVodLogError(@"加密向量不合法");
        NSError *plvError = PLVVodErrorWithCode(account_encode_iv_illegal);
        if (error) *error = plvError;
        return nil;
    }
    
    NSString *base64String = [[configString componentsSeparatedByCharactersInSet:[NSCharacterSet newlineCharacterSet]] componentsJoinedByString:@""];
    NSData *decodedData = [[NSData alloc] initWithBase64EncodedString:base64String options:0];
    
    NSData *endata = [PLVVodUtil AES128DecryptedDataWithKey:key iv:iv data:decodedData];
    NSString *decryptString = [[NSString alloc] initWithData:endata encoding:NSUTF8StringEncoding];
    NSArray *configs = [decryptString componentsSeparatedByString:@","];
    if (!configs || configs.count != 4) {
        PLVVodLogError(@"加密串解密参数有误！");
        NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
        NSError *plvError = PLVVodErrorMake(argument_illegal, ^(NSMutableDictionary *userInfo) {
            userInfo[PLVVodErrorSelectorKey] = selector;
        });
        if (error) *error = plvError;
        return nil;
    }
    
    NSString *userid = [configs[0] stringByReplacingOccurrencesOfString:@"\0" withString:@""];
    NSString *secretkey = [configs[1] stringByReplacingOccurrencesOfString:@"\0" withString:@""];
    NSString *readtoken = [configs[2] stringByReplacingOccurrencesOfString:@"\0" withString:@""];
    NSString *writetoken = [configs[3] stringByReplacingOccurrencesOfString:@"\0" withString:@""];
    
    if ([settings.userid isEqualToString:userid]) {
        return settings;
    }
    
    if (![settings accountModelExistWithUserId:userid]) {
        PLVAccountModel *accountModel = [[PLVAccountModel alloc]init];
        accountModel.userid = userid;
        accountModel.secretkey = secretkey;
        accountModel.readtoken = readtoken;
        accountModel.writetoken = writetoken;
        [settings.accountArray addObject:accountModel];
        return settings;
    }else {
        return settings;
    }
}

+ (void)removeAccountWithUserId:(NSString *)userId {
    if (![PLVVodUtil isNilString:userId]) {
        PLVVodSettings *settings = [self sharedSettings];
        for (PLVAccountModel *model in settings.accountArray) {
            if ([model.userid isEqualToString:userId]) {
                [settings.accountArray removeObject:model];
                break;
            }
        }
    }
}

+ (NSString *)findUserIdWithVid:(NSString *)vid {
    if ([PLVVodUtil isNilString:vid]) {
        return nil;
    }
    PLVVodSettings *settings = [self sharedSettings];
    if (![PLVVodUtil isNilString:settings.userid] && [vid hasPrefix:settings.userid]) {
        return settings.userid;
    }
    PLVAccountModel *model = [settings findAccountModelWithVid:vid];
    if (model) {
        return model.userid;
    }
    return nil;
}

+ (NSString *)findReadTokenWithVid:(NSString *)vid {
    if ([PLVVodUtil isNilString:vid]) {
        return nil;
    }
    PLVVodSettings *settings = [self sharedSettings];
    if (![PLVVodUtil isNilString:settings.userid] && [vid hasPrefix:settings.userid]) {
        return settings.readtoken;
    }
    PLVAccountModel *model = [settings findAccountModelWithVid:vid];
    if (model) {
        return model.readtoken;
    }
    return nil;
}

+ (NSString *)findSecretKeyWithVid:(NSString *)vid {
    if ([PLVVodUtil isNilString:vid]) {
        return nil;
    }
    PLVVodSettings *settings = [self sharedSettings];
    if (![PLVVodUtil isNilString:settings.userid] && [vid hasPrefix:settings.userid]) {
        return settings.secretkey;
    }
    PLVAccountModel *model = [settings findAccountModelWithVid:vid];
    if (model) {
        return model.secretkey;
    }
    return nil;
}

#pragma mark - viewlog 参数设置
- (void)setViewerId:(NSString *)viewerId{
    if ([PLVVodUtil isNilString:viewerId]){
        PLVVodLogError(@"[settings] -- viewerId 不能为空");
        return;
    }
    
    _viewerId  = viewerId;
    self.viewerInfos.viewerId = viewerId;
}

- (void)setViewerName:(NSString *)viewerName{
    if ([PLVVodUtil isNilString:viewerName]){
        PLVVodLogWarn(@"[settings] -- viewerName 不能为空");
        return;
    }
    
    _viewerName = viewerName;
    self.viewerInfos.viewerName = viewerName;
}

- (void)setViewerAvatar:(NSString *)viewerAvatar{
    if ([PLVVodUtil isNilString:viewerAvatar]){
        PLVVodLogWarn(@"[settings] -- viewerAvatar 不能为空");
        return;
    }
    
    _viewerAvatar = viewerAvatar;
    self.viewerInfos.viewerAvatar = viewerAvatar;
}

- (NSString *)viewerId{
    if (![PLVVodUtil isNilString:_viewerId]){
        return _viewerId;
    }
    
    if (![PLVVodUtil isNilString:self.viewerInfos.viewerId]){
        return self.viewerInfos.viewerId;
    }
    
    return kDefaultViewerId;
}

- (NSString *)viewerName{
    if (![PLVVodUtil isNilString:_viewerName]){
        return _viewerName;
    }
    
    return self.viewerInfos.viewerName;
}

- (NSString *)viewerAvatar{
    if (![PLVVodUtil isNilString:_viewerAvatar]){
        return _viewerAvatar;
    }
    
    return self.viewerInfos.viewerAvatar;
}

#pragma mark - private method

- (void)reportError:(NSError *)error {
	if (self.settingErrorHandler) self.settingErrorHandler(error);
}

/// 利用vid查找AccountModel
/// @param vid vid
- (PLVAccountModel *)findAccountModelWithVid:(NSString *)vid {
    for (PLVAccountModel *model in self.accountArray) {
        if (![PLVVodUtil isNilString:model.userid] && [vid hasPrefix:model.userid]) {
            return model;
        }
    }
    return nil;
}

/// userid是否存在于多账号中
/// @param userId userid
- (BOOL)accountModelExistWithUserId:(NSString *)userId {
    for (PLVAccountModel *model in self.accountArray) {
        if ([model.userid isEqualToString:userId]) {
            return YES;
        }
    }
    return NO;
}

#pragma mark HttpNDS

- (void)configHttpDNS {
//	/// 配置HTTPDNS账号信息
//    HttpDnsService *httpdns = [[HttpDnsService alloc] initWithAccountID:123018];
//    // 允许返回过期的IP
//    [httpdns setExpiredIPEnabled:YES];
//    httpdns.timeoutInterval = 3;
//    // 打开HTTPDNS Log，线上建议关闭
//    //[httpdns setLogEnabled:YES];
//    // 设置网络切换时是否自动刷新所有域名解析结果
//    [httpdns setPreResolveAfterNetworkChanged:YES];
//    // 设置预解析
//    [httpdns setPreResolveHosts:[PLVVodHlsHelper commomTsHosts]];
//    // 开启本地缓存
//    [httpdns setCachedIPEnabled:YES];
}

#pragma mark -- 缓存数据迁移兼容
+ (void)migrateCacheVideoWithSecretKey:(NSString *)secretKey{
    if([PLVVodUtil isNilString:secretKey]){
        PLVVodLogError(@"[参数错误,无法进行视频迁移]");
        return;
    }
    BOOL isExecute = [[NSUserDefaults standardUserDefaults] objectForKey:kHasExecuteInitSecretKey];
    if (!isExecute){
        // 只执行一次
        [[NSUserDefaults standardUserDefaults] setBool:YES forKey:kHasExecuteInitSecretKey];
        [[NSUserDefaults standardUserDefaults] synchronize];
        // 还未迁移成功，尝试迁移
        [[PLVVodDownloadManager sharedManager] migrateLocalVideoPlaylist:secretKey];
    }
}

@end
