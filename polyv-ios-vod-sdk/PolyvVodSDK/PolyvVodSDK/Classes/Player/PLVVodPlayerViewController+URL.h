//
//  PLVVodPlayerViewController+URL.h
//  PLVVodSDK
//
//  Created by mac on 2020/4/2.
//  Copyright © 2020 POLYV. All rights reserved.
//


#import <PLVVodSDK/PLVVodSDK.h>

NS_ASSUME_NONNULL_BEGIN

@interface PLVVodPlayerViewController (URL)

- (void)setURLInternal:(NSURL *)videoUrl;

/**
 播放外部平台视频，保利威平台视频不能采用该方法
 
 @param videoUrl 外部视频url 地址
 @param headers 请求头设置
 */
- (void)setURLInternal:(NSURL *)videoUrl withHeaders:(NSDictionary<NSString *, NSString *> *)headers;



@end

NS_ASSUME_NONNULL_END
