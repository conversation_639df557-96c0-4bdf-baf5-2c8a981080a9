//
//  PLVVodPlayerViewController+Switch.m
//  PLVVodSDK
//
//  Created by mac on 2020/4/2.
//  Copyright © 2020 POLYV. All rights reserved.
//

#import "PLVVodPlayerViewController+Switch.h"
#import "PLVVodPlayerViewController+internal.h"
#import "PLVVodPlayerViewController+IJK.h"
#import "PLVVodPlayerViewController+Token.h"


@implementation PLVVodPlayerViewController (Switch)

// 内部清晰度切换方法
- (void)switchVideoQuality:(PLVVodQuality)quality{
    
    PLVVodLogDebug(@"xx_%s - %@", __FUNCTION__, [NSThread currentThread]);
    PLVVodVideo *video = self.video;
    if (self.localPrior && self.localOnlineVideo) video = self.localOnlineVideo;
    
    // add by libl [清理缓存变量] 2019-05-14 start
    // elog
    [self.elogPlayerParamsArr removeAllObjects];
    
    // add end
    if ([video isKindOfClass:[PLVVodLocalVideo class]]) { // 本地视频
        self.localPlayback = YES;
        PLVVodLocalVideo *localVideo = (PLVVodLocalVideo *)video;
        
        // add by libl 2018-06-21 start
        // 校验路径，否则崩溃
        if ([localVideo.path isKindOfClass:[NSNull class]] ||
            [localVideo.path length] == 0) {
            PLVVodLogError(@"无法找到本地视频,未下载完成");
            NSError *plvError = PLVVodErrorMake(video_not_found, ^(NSMutableDictionary *userInfo) {
                
            });
            [self reportError:plvError];
            return;
        }
        
        NSURL *URL = [NSURL fileURLWithPath:localVideo.path]; // 路径前面已经校验
        
        PLVIJKFFOptions *ijkOptions = [PLVIJKFFOptions optionsByDefault];
        if (localVideo.isPlain) {
            PLVVodLogInfo(@"播放本地非加密视频");
        } else {
            PLVVodLogInfo(@"播放本地加密视频");
            ijkOptions = [self localTokenOptions:localVideo.tokenPath];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            [self switchURL:URL options:ijkOptions playToken:nil];
        });
    } else { // 在线视频
        self.localPlayback = NO;
        // 获取 URL
        NSString *url = nil;
        
        // mod by libl [添加源视频播放url逻辑] 2018-09-14 start
        if (video.keepSource){
            PLVVodLogInfo(@"播放源视频");
            url = video.play_source_url;
        }
        else{
            if (video.isPlain) {
                PLVVodLogInfo(@"播放在线非加密视频");
                NSUInteger index = quality-1;
                if(video.isHls){
                    NSArray<NSString *> *hlsVideos = video.isHls302 ? video.hlsVideos2 : video.hlsVideos;
                    index = [PLVVodUtil preferredIndexWithArray:hlsVideos index:index];
                    // add by libl [修复低码率编码成功，但高码率视频还未编码成功，播放失败的问题] 19-04-18 start
                    index = [self effectiveIndexWithVideo:video index:index];
                    self.quality = index+1;
                    url = hlsVideos[index];
                }else{
                    index = [PLVVodUtil preferredIndexWithArray:video.plainVideos index:index];
                    // add by libl [修复低码率编码成功，但高码率视频还未编码成功，播放失败的问题] 19-04-18 start
                    index = [self effectiveIndexWithVideo:video index:index];
                    self.quality = index+1;
                    url = video.plainVideos[index];
                }
            } else {
                PLVVodLogInfo(@"播放在线加密视频");
                NSUInteger index = quality-1;
                NSArray<NSString *> *hlsVideos = video.isHls302 ? video.hlsVideos2 : video.hlsVideos;
                index = [PLVVodUtil preferredIndexWithArray:hlsVideos index:index];
                // add by libl [修复低码率编码成功，但高码率视频还未编码成功，播放失败的问题] 19-04-18 start
                index = [self effectiveIndexWithVideo:video index:index];
                
                self.quality = index+1;
                url = hlsVideos[index];
            }
        }
       
        // mod end
        
        // mod by libl [更改线路切换逻辑] 2019-02-14 start
        NSMutableDictionary *paramDict = [NSMutableDictionary dictionary];
        paramDict[PLVVodPidKey] = self.pid;
        NSString *appId = [PLVVodSettings sharedSettings].appid;
        if (![PLVVodUtil isNilString:appId]){
            paramDict[PLVVodAppIdKey] = appId;
        }
        if (self.video.keepSource){
            url = [PLVVodUtil url:url appendParams:paramDict];
        }
        else if (self.video.isHls){
            // hls 视频处理
            paramDict[PLVVodRouteLineKey] = self.routeLine;
            url = [PLVVodUtil url:url appendParams:paramDict];
        }
        else{
            // mp4 视频处理，直接替换域名
            if ([self.routeLine isEqualToString:PLVVodMp4HostOne] || [self.routeLine isEqualToString:PLVVodMp4HostTwo])
            {
                NSString *hostName = [PLVVodUtil getHostWithUrl:url];
                if (self.routeLine){
                    url = [url stringByReplacingOccurrencesOfString:hostName withString:self.routeLine];
                }
            }
            // fixs: mp4 播放不带pid ，导致流量统计不正确 19.11.06
            url = [PLVVodUtil url:url appendParams:paramDict];
        }
        PLVVodLogInfo(@"player url: %@", url);
        if (!url.length) {
            PLVVodLogError(@"无法找到在线视频");
            NSError *plvError = PLVVodErrorMake(video_not_found, ^(NSMutableDictionary *userInfo) {
                
            });
            [self reportError:plvError];
            return;
        }
        NSURL *URL = [NSURL URLWithString:url];
        // 获取 token
        if (video.keepSource || video.isPlain) { // 若是源视频 或 非加密视频，则直接switchURL
            // 获取 video cdn 域名
            self.qosLastRequestVideoDomain = URL.host;
            dispatch_async(dispatch_get_main_queue(), ^{
                [self switchURL:URL options:nil playToken:nil];
            });
        }
        else
        {
            // 加密视频
            __weak typeof(self) weakSelf = self;
            PLVVodQosLoadingTracerModel *model = [[PLVVodQosLoadingTracerModel alloc]initAndAddStartTimeWithType:PLVVodQosLoadingTracerModelTypeRequestToken];
            [self requestTokenWithCompletion:^(PLVIJKFFOptions *ijkOptions, NSString *playToken, NSString *tokenHost) {
                [[PLVVodQosLoadingTracer shareManager] addEndTimeAndUpdateModel:model vid:weakSelf.video.vid];
                // mod by libl [ijkOptions 为空，表示请求失败，不处理] 2019-05-13 start
                if (nil != ijkOptions) {
                    // 保证当前要播放的Vid，与，回调ijkOptions配置对应URL，前者与后者，是对应的；
                    // 避免因请求时差，而Vid被更改，与URL不再对应
                    if (!weakSelf.video || !weakSelf.video.vid) {
                        PLVVodLogError(@"vid或video为nil。");
                        NSError *plvError = PLVVodErrorMake(vid_error, ^(NSMutableDictionary *userInfo) {
                        });
                        [self reportError:plvError];
                        return;
                    }
        
                    NSString * vidPoolId = [weakSelf.video.vid componentsSeparatedByString:@"_"].firstObject;
                    if ([URL.absoluteString containsString:vidPoolId]) {
                        // URL与Vid对应，是当前要播放的视频
                        dispatch_async(dispatch_get_main_queue(), ^{
                            [weakSelf switchURL:URL options:ijkOptions playToken:playToken];
                        });
                    }else{
                        // URL与Vid不对应（快速切换视频时，可能是上个视频的URL），忽视
                        PLVVodLogWarn(@"URL与Vid不匹配");
                    }
                }
            }];
        }
    }
}

// 根据filesize, 获取有效的播放链接
- (NSInteger)effectiveIndexWithVideo:(PLVVodVideo *)video index:(NSInteger )prefferIndex{
    if (prefferIndex < video.filesizes.count){
        NSInteger size = [[video.filesizes objectAtIndex:prefferIndex] integerValue];
        if (size > 0){
            return prefferIndex;
        }
    }
    
    // 从高到低获取
    int fileCount = (int)video.filesizes.count;
    for (int i=fileCount -1; i >=0; i--){
        NSInteger size = [[video.filesizes objectAtIndex:i] integerValue];
        if (size > 0){
            return i;
        }
    }
    
    return prefferIndex;
}

#pragma mark -- 线路切换

- (void)switchRouteLine:(NSString *)routeLine{
    if (routeLine == nil || [routeLine isKindOfClass:[NSNull class]]){
       PLVVodLogInfo(@"线路参数不能为空");
       return;
    }

    if (self.video.keepSource){
       PLVVodLogInfo(@"源视频不进行线路切换");
       return;
    }

    if ( PLVVodPlaybackModeAudio == self.playbackMode ){
       [self handleVoiceRouteline:routeLine];
    }
    else {
       [self handleVideoRouteline:routeLine];
    }
}

- (void)handleVideoRouteline:(NSString *)routeLine{
    __block BOOL isValid = NO;
    [self.video.availableRouteLines enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj isEqualToString:routeLine]){
            isValid = YES;
            *stop = YES;
        }
    }];
    
    if (!isValid){
        NSString *tips = [NSString stringWithFormat:@"线路参数不合法: %@", routeLine];
        PLVVodLogWarn(@"%@", tips);
        return;
    }
    
    self.isResetViewlog = NO;
    [self setRouteLineInternal:routeLine];
    [self switchVideoQuality:self.quality];
    
    __weak typeof(self) weakSelf = self;
    self.beforSwitchPosition = self.mainPlayer.currentPlaybackTime > 0 ? self.mainPlayer.currentPlaybackTime: self.lastPosition;

    self.needResumePlaybackProgress = YES;
    self.mainPlayerPrepareToPlayHandler = ^{
        
        if (weakSelf.rememberPlaybackRate){
            weakSelf.playbackRate = weakSelf.lastPlaybackRate;
        }
        weakSelf.needResumePlaybackProgress = NO;
        if (weakSelf.beforSwitchPosition) {
            [weakSelf setCurrentPlaybackTime:weakSelf.beforSwitchPosition];
            weakSelf.beforSwitchPosition = 0;
        }
        [weakSelf play];
    };
}

- (void)handleVoiceRouteline:(NSString *)voiceRouteline{
    
    __block BOOL isValid = NO;
    [self.video.availableAudioRouteLines enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj isEqualToString:voiceRouteline]){
            isValid = YES;
            *stop = YES;
        }
    }];
    
    if (!isValid){
        NSString *tips = [NSString stringWithFormat:@"线路参数不合法: %@", voiceRouteline];
        PLVVodLogWarn(@"%@", tips);
        return;
    }
    
    self.isResetViewlog = NO;
    self.autoPlayByDefault = YES;                       // 自动播放（临时参数）
    
    //在这里设置startPlaybackTime会导致切换下一个视频的时候会自动seek
//    self.startPlaybackTime = self.currentPlaybackTime;  // 记录当前播放时间
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.playbackMode == PLVVodPlaybackModeAudio) {
            NSString *aacLink = self.video.aac_link;
            
            if (aacLink && aacLink.length) {
                // 替换域名
                if ([voiceRouteline isEqualToString:PLVVodMp4HostOne] || [voiceRouteline isEqualToString:PLVVodMp4HostTwo]){
                    NSString *hostName = [PLVVodUtil getHostWithUrl:aacLink];
                    aacLink = [aacLink stringByReplacingOccurrencesOfString:hostName withString:voiceRouteline];
                    
                    //切换线路前，记录一下进度
                    NSTimeInterval playedTime = self.mainPlayer.currentPlaybackTime;
                    
                    [self switchURL:[NSURL URLWithString:aacLink] options:nil playToken:nil];
                    self.needResumePlaybackProgress = YES;
                    __weak typeof(self) weakSelf = self;
                    self.mainPlayerPrepareToPlayHandler = ^{
                        
                        if (weakSelf.rememberPlaybackRate){
                            weakSelf.playbackRate = weakSelf.lastPlaybackRate;
                        }
                        weakSelf.needResumePlaybackProgress = NO;
                        [weakSelf setCurrentPlaybackTime:playedTime];
                    };
                    PLVVodLogInfo(@"音频文件线路切换: %@", aacLink);
                }
            }
        }
    });
}

@end
