//
//  PLVVodAdPlayerViewController.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/11/8.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodAdPlayerViewController.h"
#import <PLVIJKPlayer/PLVIJKPlayer.h>
#import "PLVTimer.h"
#import "PLVVodPlayerViewController.h"
#import "PLVVodUtil.h"
#import "PLVVodNetworking.h"
#import "UIImageView+PLVGif.h"

typedef void(^PLVIjkPlayerFinishBlock)(void);

@interface PLVVodAdPlayerViewController ()

@property (nonatomic, strong) PLVIJKFFMoviePlayerController *ijkPlayer;
@property (nonatomic, strong) UIImageView *imageView;

@property (nonatomic, strong) NSArray<PLVVodAd *> *headAds;
@property (nonatomic, strong) NSArray<PLVVodAd *> *pauseAds;
@property (nonatomic, strong) NSArray<PLVVodAd *> *tailAds;

@property (nonatomic, copy) PLVVodAdCompletionBlock currentAdCompletion;
@property (nonatomic, assign) PLVVodAdLocation currentAdLocation;

@property (nonatomic, strong) PLVTimer *timer;
//@property (nonatomic, assign) NSTimeInterval stayDuration;

/// 控件容器
@property (nonatomic, strong) UIView *controlContainer;

/// 播放按钮
@property (nonatomic, strong) UIButton *playButton;

/// 剩余时间文本
@property (nonatomic, strong) UILabel *timeLabel;

/// 跳过按钮
@property (nonatomic, strong) UIButton *skipButton;

/// 静音按钮
@property (nonatomic, strong) UIButton *muteButton;

@property (nonatomic, copy) PLVIjkPlayerFinishBlock ijkPlayerFinishHandler;

@end

@implementation PLVVodAdPlayerViewController

#pragma mark - dealloc & init

- (void)dealloc {
	[self.timer cancel];
	self.timer = nil;
	[self.ijkPlayer stop];
	[self.ijkPlayer shutdown];
	self.ijkPlayer = nil;
	[[NSNotificationCenter defaultCenter] removeObserver:self];
	PLVVodLogDebug(@"%s", __FUNCTION__);
}

#pragma mark - property

- (void)setAds:(NSArray<PLVVodAd *> *)ads {
	_ads = ads;
	NSMutableArray *headAds = [NSMutableArray array];
	NSMutableArray *pauseAds = [NSMutableArray array];
	NSMutableArray *tailAds = [NSMutableArray array];
	for (PLVVodAd *ad in ads) {
		switch (ad.location) {
			case PLVVodAdLocationHead:{
				[headAds addObject:ad];
			}break;
			case PLVVodAdLocationPause:{
				[pauseAds addObject:ad];
			}break;
			case PLVVodAdLocationTail:{
				[tailAds addObject:ad];
			}break;
			default:{}break;
		}
	}
	self.headAds = headAds;
	self.pauseAds = pauseAds;
	self.tailAds = tailAds;
}

- (void)setState:(PLVVodAssetState)adState {
	_state = adState;
	[[NSNotificationCenter defaultCenter] postNotificationName:PLVVodPlayerAdStateDidChangeNotification object:self];
}

- (UILabel *)timeLabel {
	if (!_timeLabel) {
		_timeLabel = [[UILabel alloc] init];
	}
	return _timeLabel;
}

- (UIButton *)skipButton {
	if (!_skipButton) {
		_skipButton = [UIButton buttonWithType:UIButtonTypeSystem];
		[_skipButton setTitle:@"跳过" forState:UIControlStateNormal];
		_skipButton.titleLabel.font = [UIFont systemFontOfSize:12];
		_skipButton.showsTouchWhenHighlighted = YES;
		[_skipButton addTarget:self action:@selector(skipButtonAction:) forControlEvents:UIControlEventTouchUpInside];
	}
	return _skipButton;
}

- (UIButton *)playButton {
	if (!_playButton) {
		_playButton = [UIButton buttonWithType:UIButtonTypeCustom];
		_playButton.showsTouchWhenHighlighted = YES;
		[_playButton addTarget:self action:@selector(playButtonAction:) forControlEvents:UIControlEventTouchUpInside];
	}
	return _playButton;
}

- (UIButton *)muteButton {
	if (!_muteButton) {
		_muteButton = [UIButton buttonWithType:UIButtonTypeCustom];
		_muteButton.showsTouchWhenHighlighted = YES;
		[_muteButton addTarget:self action:@selector(muteButtonAction:) forControlEvents:UIControlEventTouchUpInside];
	}
	return _muteButton;
}

- (UIView *)controlContainer {
	if (!_controlContainer) {
		_controlContainer = [[UIView alloc] initWithFrame:CGRectZero];
	}
	return _controlContainer;
}

- (void)setCanSkip:(BOOL)canSkip {
	_canSkip = canSkip;
	dispatch_async(dispatch_get_main_queue(), ^{
		self.skipButton.hidden = !_canSkip;
	});
}

#pragma mark - view controller

- (void)viewDidLoad {
	[super viewDidLoad];
	// Do any additional setup after loading the view.
	//	UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(viewDidTap:)];
	//	[self.view addGestureRecognizer:tap];
	[self setupUI];
}

//- (void)viewDidAppear:(BOOL)animated {
//	[super viewDidAppear:animated];
//	UIViewController *currentViewController = [self.class currentViewController];
//	self.hasNavigationBar = !currentViewController.navigationController.navigationBar.hidden;
//	self.hasStatusBar = ![UIApplication sharedApplication].statusBarHidden;
//	[self viewDidLayoutSubviews];
//}
//
//+ (UIViewController *)currentViewController {
//	UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
//	UIViewController *vc = keyWindow.rootViewController;
//	while (vc.presentedViewController) {
//		vc = vc.presentedViewController;
//
//		if ([vc isKindOfClass:[UINavigationController class]]) {
//			vc = [(UINavigationController *)vc visibleViewController];
//		} else if ([vc isKindOfClass:[UITabBarController class]]) {
//			vc = [(UITabBarController *)vc selectedViewController];
//		}
//	}
//	return vc;
//}

- (void)setupUI {
	[self.view addSubview:self.controlContainer];
	UIView *superview = self.view;
	UIView *controlContainer = self.controlContainer;
	controlContainer.translatesAutoresizingMaskIntoConstraints = NO;
	NSDictionary *views = NSDictionaryOfVariableBindings(superview, controlContainer);
	[superview addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"H:|-[controlContainer]-|" options:0 metrics:nil views:views]];
	[superview addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"V:|-[controlContainer]-|" options:0 metrics:nil views:views]];
	
	[self.controlContainer addSubview:self.timeLabel];
	[self.controlContainer addSubview:self.skipButton];
	[self.controlContainer addSubview:self.playButton];
	[self.controlContainer addSubview:self.muteButton];
}

- (void)viewDidLayoutSubviews {
	CGFloat width = self.controlContainer.bounds.size.width;
	CGFloat height = self.controlContainer.bounds.size.height;
	CGFloat margin = 10;
	
	CGRect timeLabelFrame = self.timeLabel.frame;
	timeLabelFrame.size = self.timeLabel.attributedText.size;
	CGFloat oringinY = margin;
	oringinY += self.topInset;
	
	timeLabelFrame.origin.y = oringinY;
	if (self.canSkip) {
		[self.skipButton sizeToFit];
		CGRect skipButtonFrame = self.skipButton.frame;
		skipButtonFrame.size = [self.skipButton.titleLabel.text sizeWithAttributes:@{NSFontAttributeName: self.skipButton.titleLabel.font}];
		
		timeLabelFrame.origin.x = width - timeLabelFrame.size.width - margin*2 - skipButtonFrame.size.width;
		self.timeLabel.frame = timeLabelFrame;
		
		skipButtonFrame.origin.x = width - skipButtonFrame.size.width - margin;
		skipButtonFrame.origin.y = CGRectGetMaxY(timeLabelFrame) - skipButtonFrame.size.height;
		self.skipButton.frame = skipButtonFrame;
	} else {
		timeLabelFrame.origin.x = width - timeLabelFrame.size.width - margin;
	}
	self.timeLabel.frame = timeLabelFrame;
	
	self.playButton.center = CGPointMake(CGRectGetMidX(self.controlContainer.bounds), CGRectGetMidY(self.controlContainer.bounds));
	
	CGRect muteButtonFrame = self.muteButton.frame;
	muteButtonFrame.origin.x = margin;
	muteButtonFrame.origin.y = height - muteButtonFrame.size.height - margin;
	self.muteButton.frame = muteButtonFrame;
    
    
    if (self.currentAdLocation == PLVVodAdLocationPause &&
        self.imageView) {
        //设置自定义的暂停广告大小位置
        [self customSetUpImageView];
    }
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
	if (self.state >= PLVVodAssetStateLoading && self.state < PLVVodAssetStateFinished && self.adDidTapBlock) {
		self.adDidTapBlock(self.currentAd);
	}
}

#pragma mark - private method

#pragma mark action

- (void)muteButtonAction:(UIButton *)sender {
	sender.selected = !sender.selected;
	self.ijkPlayer.playbackVolume = sender.selected ? 0 : 1;
}

- (void)skipButtonAction:(UIButton *)sender {
	self.currentAd.played = YES;
	[self hideAd];
	[self showAdWithLocation:self.currentAdLocation completion:self.currentAdCompletion];
}

- (void)playButtonAction:(UIButton *)sender {
	[self hideAd];
}

#pragma mark util

- (PLVIJKFFMoviePlayerController *)ijkPlayerWithURL:(NSURL *)URL {
    PLVIJKFFOptions *options = [PLVIJKFFOptions optionsByDefault];
    [options setFormatOptionIntValue:1 forKey:@"dns_cache_clear"];
    [options setFormatOptionValue:[PLVVodUtil ijkplayerUserAgent] forKey:@"user-agent"];
	PLVIJKFFMoviePlayerController *ijkPlayer = [[PLVIJKFFMoviePlayerController alloc] initWithContentURL:URL withOptions:options];
	ijkPlayer.view.autoresizingMask = UIViewAutoresizingFlexibleWidth|UIViewAutoresizingFlexibleHeight;
	ijkPlayer.view.frame = self.view.bounds;
	ijkPlayer.view.userInteractionEnabled = NO;
	ijkPlayer.scalingMode = IJKMPMovieScalingModeAspectFit;
	[[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(ijkPlayerFinish:) name:IJKMoviePlayerPlaybackDidFinishNotification object:ijkPlayer];
	return ijkPlayer;
}

- (void)ijkPlayerFinish:(NSNotification *)notification {
	if (self.ijkPlayerFinishHandler) self.ijkPlayerFinishHandler();
}

- (UIImageView *)imageViewWithURL:(NSURL *)URL {
	UIImageView *imageView = [[UIImageView alloc] initWithFrame:self.view.bounds];
	imageView.autoresizingMask = UIViewAutoresizingFlexibleHeight | UIViewAutoresizingFlexibleWidth;
	imageView.contentMode = UIViewContentModeScaleAspectFit;
	//imageView.userInteractionEnabled = NO;
	__weak typeof(self) weakSelf = self;
	__weak typeof(imageView) weakImageView = imageView;
    
    NSString *extension = URL.pathExtension.lowercaseString;
	NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:URL];
	[PLVVodNetworking setupHTTPHeaderWithRequest:request];
	[[[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
		dispatch_async(dispatch_get_main_queue(), ^{
            if ([extension isEqualToString:@"gif"]){
                [weakImageView plv_ShowGifImageWithData:data];
            }
            else{
                weakImageView.image = [UIImage imageWithData:data];
            }
		});
		weakSelf.state = PLVVodAssetStatePlaying;
	}] resume];
	return imageView;
}

/// 自定义设置暂停广告的位置大小
-(void)customSetUpImageView
{
    if (!self.imageView) {
        return;
    }
    self.imageView.autoresizingMask = UIViewAutoresizingNone;
    
    //播放器宽高
    CGFloat playerWidth = self.view.bounds.size.width;
    CGFloat playerHeight = self.view.bounds.size.height;
    
    //暂停广告宽高
    CGFloat pauseAdWidth = 0;
    CGFloat pauseAdHeight = 0;
    
    
    //1、判断参数是否有效
    BOOL widthValid = self.pauseAdWidthRatio >= 1 && self.pauseAdWidthRatio <= 99;
    BOOL heightValid = self.pauseAdHeightRatio >= 1 && self.pauseAdHeightRatio <= 99;
    if (!widthValid && !widthValid) {
        // 宽高皆无效时，跟随播放器同尺寸，铺满播放器
        self.imageView.frame = CGRectMake(0, 0, playerWidth, playerHeight);
        return;
    }
    
    //播放器比例
    if (widthValid && !heightValid) {
        // 宽有效，高无效。自适应高
        pauseAdWidth = playerWidth * self.pauseAdWidthRatio * 0.01;
        pauseAdHeight = playerHeight * self.pauseAdWidthRatio * 0.01;
        
    }else if (!widthValid && heightValid){
        // 高有效，宽无效。自适应宽
        pauseAdHeight = playerHeight * self.pauseAdHeightRatio * 0.01;
        pauseAdWidth = playerWidth * self.pauseAdHeightRatio * 0.01;
    }else {
        //宽高都有效
        pauseAdWidth = playerWidth * self.pauseAdWidthRatio * 0.01;
        pauseAdHeight = playerHeight * self.pauseAdHeightRatio * 0.01;
    }
    
    //最大的合法左边距
    CGFloat leftMax = playerWidth - pauseAdWidth;
    //最大的合法上边距
    CGFloat topMax = playerHeight - pauseAdHeight;
    // 设置的暂停广告左边距
    CGFloat pauseAdMarginLeft = playerWidth * self.pauseAdCenterXRatio * 0.01 - pauseAdWidth * 0.5;
    // 设置的暂停广告上边距
    CGFloat pauseAdMarginTop = playerHeight * self.pauseAdCenterYRatio * 0.01 - pauseAdHeight * 0.5;
    
    if (pauseAdMarginLeft < 0 || pauseAdMarginLeft > leftMax) {
        //左边距不合法，居中
        pauseAdMarginLeft = (playerWidth - pauseAdWidth) * 0.5;
    }
    if (pauseAdMarginTop < 0 || pauseAdMarginTop > topMax) {
        //上边距不合法，居中
        pauseAdMarginTop = (playerHeight - pauseAdHeight) * 0.5;
    }
    self.imageView.frame = CGRectMake(pauseAdMarginLeft, pauseAdMarginTop, pauseAdWidth, pauseAdHeight);
}

- (void)setupSubviewsWithLocation:(PLVVodAdLocation)location {
	switch (location) {
		case PLVVodAdLocationPause:{
			self.playButton.hidden = NO;
			self.timeLabel.hidden = YES;
			self.skipButton.hidden = YES;
		}break;
		case PLVVodAdLocationHead:
		case PLVVodAdLocationTail:{
			self.playButton.hidden = YES;
			self.timeLabel.hidden = NO;
			self.skipButton.hidden = !self.canSkip;
		}break;
		default:{}break;
	}
}

- (void)showAds:(NSArray<PLVVodAd *> *)ads completion:(PLVVodAdCompletionBlock)completion {
	// 获取首个未播放的广告
	__block PLVVodAd *ad = [self firstUnplayAdFromAds:ads];
	if (!ad) { // 传入数据为空则播放正片
		self.state = PLVVodAssetStateFinished;
		[self hideAd];
		if (completion) completion(NO);
		self.currentAdCompletion = nil;
		return;
	}
	// 初始状态
	self.state = PLVVodAssetStateLoading;
    self.canSkip = NO;
    [self viewDidLayoutSubviews];
//	self.stayDuration = 0;
	
	// 播放首个广告，通过递归进行遍历
	self.currentAd = ad;
    if (ad.type == PLVVodAdTypeImage) {
        self.imageView = [self imageViewWithURL:[NSURL URLWithString:ad.adUrl]];
        if (self.currentAdLocation == PLVVodAdLocationPause) {
            //设置自定义的暂停广告大小位置
            [self customSetUpImageView];
        }
        [self.view insertSubview:self.imageView atIndex:0];
        self.muteButton.hidden = YES;
    } else if (ad.type == PLVVodAdTypeVideo) {
        self.ijkPlayer = [self ijkPlayerWithURL:[NSURL URLWithString:ad.adUrl]];
        [self.view insertSubview:self.ijkPlayer.view atIndex:0];
        [self.ijkPlayer prepareToPlay];
        [self.ijkPlayer play];
        self.muteButton.hidden = NO;
        __weak typeof(self) weakSelf = self;
        self.ijkPlayerFinishHandler = ^{
            [weakSelf.timer cancel];
            ad.played = YES;
            [weakSelf hideAd];
            [weakSelf showAds:ads completion:completion];
            //weakSelf.ijkPlayerFinishHandler = nil;
        };
    } else {
        PLVVodLogError(@"广告类型不支持");
        NSError *plvError = PLVVodErrorMake(ad_type_illegal, ^(NSMutableDictionary *userInfo) {});
        if (self.playerErrorHandler) self.playerErrorHandler(self, plvError);
        ad.played = YES;
        [self showAds:ads completion:completion];
    }
	
	self.view.hidden = NO;
	
	__weak typeof(self) weakSelf = self;
	if (ad.duration <= 0) { // 没有指定时长的广告，如暂停广告，则不启用定时器，且不执行回调
		return;
	}
	dispatch_async(dispatch_get_main_queue(), ^{
        [self.skipButton setTitle:![PLVVodUtil isNilString:ad.skipbutton] ? ad.skipbutton : @"跳过" forState:UIControlStateNormal];
		self.timeLabel.attributedText = [self.class timeStringWithSeconds:ad.duration displayText:ad.displaytext];
		[self viewDidLayoutSubviews];
	});
	self.timer = [PLVTimer countdownWithSecond:ad.duration countBlock:^(long remainSecond) {
		if (weakSelf.ijkPlayer.isPreparedToPlay) {
			weakSelf.state = PLVVodAssetStatePlaying;
		}
		dispatch_async(dispatch_get_main_queue(), ^{
			weakSelf.timeLabel.attributedText = [self.class timeStringWithSeconds:remainSecond displayText:ad.displaytext];
			[weakSelf viewDidLayoutSubviews];
		});
		if (remainSecond == 0) {
			dispatch_async(dispatch_get_main_queue(), ^{
				[weakSelf.timer cancel];
				ad.played = YES;
				[weakSelf hideAd];
				[weakSelf showAds:ads completion:completion];
				//weakSelf.ijkPlayerFinishHandler = nil;
			});
		}
        
        // 设置跳过 广告
        dispatch_async(dispatch_get_main_queue(), ^{
            if(ad.skipEnabled){
                if (ad.skipTime > 0){
                    if ((ad.duration - remainSecond) == ad.skipTime){
                        if (!_canSkip){
                            weakSelf.canSkip = YES;
                            [weakSelf viewDidLayoutSubviews];
                        }
                    }
                }
                else{
                    if (!_canSkip){
                        weakSelf.canSkip = YES;
                        [weakSelf viewDidLayoutSubviews];
                    }
                }
            }
            else{
                if (_canSkip){
                    weakSelf.canSkip = NO;
                    [weakSelf viewDidLayoutSubviews];
                }
            }
        });
        
	}];
}

- (PLVVodAd *)firstUnplayAdFromAds:(NSArray<PLVVodAd *> *)ads {
    for (int i = 0; i < ads.count; i ++) {
        PLVVodAd *ad = ads[i];
        if (ad.played) {
            continue;
        }
        return ad;
    }
    return nil;
}

#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}

#pragma mark - public method

+ (NSAttributedString *)timeStringWithSeconds:(NSInteger)remainSeconds displayText:(NSString *)displayText {
	NSMutableAttributedString *timeString = [[NSMutableAttributedString alloc] init];
	NSMutableDictionary *normalAttr = [NSMutableDictionary dictionary];
	normalAttr[NSFontAttributeName] = [UIFont systemFontOfSize:11];
	normalAttr[NSForegroundColorAttributeName] = [UIColor whiteColor];
	
	NSMutableDictionary *emphasisAttr = [NSMutableDictionary dictionary];
	emphasisAttr[NSFontAttributeName] = [UIFont systemFontOfSize:16];
	emphasisAttr[NSForegroundColorAttributeName] = [UIColor redColor];
	
    NSString *plainString0 = [NSString stringWithFormat:@"%@：",![PLVVodUtil isNilString:displayText] ? displayText : @"广告也精彩"];
	[timeString appendAttributedString:[[NSAttributedString alloc] initWithString:plainString0 attributes:normalAttr]];
	NSString *plainString1 = [NSString stringWithFormat:@"%zd", remainSeconds];
	[timeString appendAttributedString:[[NSAttributedString alloc] initWithString:plainString1 attributes:emphasisAttr]];
	NSString *plainString2 = [NSString stringWithFormat:@" 秒"];
	[timeString appendAttributedString:[[NSAttributedString alloc] initWithString:plainString2 attributes:normalAttr]];
	return timeString;
}

- (void)showAdWithLocation:(PLVVodAdLocation)location completion:(PLVVodAdCompletionBlock)completion {
	self.currentAdCompletion = completion;
	self.currentAdLocation = location;
	dispatch_async(dispatch_get_main_queue(), ^{
		if (!self.ads.count) {
			self.state = PLVVodAssetStateFinished;
			[self hideAd];
			if (completion) completion(YES);
			return;
		}
		switch (location) {
			case PLVVodAdLocationHead:{
				[self showAds:self.headAds completion:completion];
			}break;
			case PLVVodAdLocationPause:{
				[self showAds:self.pauseAds completion:completion];
			}break;
			case PLVVodAdLocationTail:{
				[self showAds:self.tailAds completion:completion];
			}break;
			default:{}break;
		}
		[self setupSubviewsWithLocation:location];
	});
}

- (void)hideAd {
	[self.ijkPlayer stop];
	[self.ijkPlayer.view removeFromSuperview];
	[self.ijkPlayer shutdown];
	self.ijkPlayer = nil;
	
	[self.imageView removeFromSuperview];
	self.imageView = nil;
	
//	self.stayDuration = 0;
	[self.timer cancel];
	self.timer = nil;
	self.state = PLVVodAssetStateFinished;
	self.view.hidden = YES;
}

@end
