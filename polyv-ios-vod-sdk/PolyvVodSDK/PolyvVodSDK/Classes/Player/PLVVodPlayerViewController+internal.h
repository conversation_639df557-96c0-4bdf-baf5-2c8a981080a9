//
//  PLVVodPlayerViewController+internal.h
//  PLVVodSDK
//
//  Created by mac on 2020/4/2.
//  Copyright © 2020 POLYV. All rights reserved.
//

#ifndef PLVVodPlayerViewController_internal_h
#define PLVVodPlayerViewController_internal_h

#import "PLVVodPlayerViewController.h"
#import <PLVIJKPlayer/PLVIJKPlayer.h>
#import <PLVVodSDK/PLVVodReachability.h>
#import "PLVVodUtil.h"
#import "PLVVodSettings.h"
#import "PLVVodReportManager.h"
#import "PLVKVOController.h"
#import "PLVTimer.h"
#import "PLVVodNetworking.h"
#import "PLVVodDownloadManager.h"
#import "PLVVodHlsHelper.h"
#import <AVFoundation/AVFoundation.h>
#import "PLVVodDefines.h"
#import "PLVVodLocalVideo+Private.h"
#import "PLVVodElogModel.h"
#import "UIImageView+PLVGif.h"
#import "PLVVodElogSeekModel.h"
#import "PLVVodPlayerLogo+internal.h"
#import <MediaPlayer/MPVolumeView.h>
#import "PLVVodHttpDnsManager.h"
#import "PLVPictureInPictureManager+Private.h"
#import "PLVVodQosLoadingTracer.h"

static NSString * const PLVVodPidKey = @"pid";
static NSString * const PLVVodRouteLineKey = @"route";
static NSString * const PLVVodLastPositionKey = @"net.polyv.sdk.vod.lastPosition";
static NSString * const PLVVodLastPositionTimestampKey = @"net.polyv.sdk.vod.lastPositionTimestamp";
static NSString * const PLVVodAppIdKey = @"appId";

// 阿里mp4/mp3 域名地址
static NSString * const PLVVodMp4HostOne = @"mpv.videocc.net";
static NSString * const PLVVodMp4HostTwo = @"freeovp.videocc.net";

/// 发送回调 playbackRecoveryHandle 的间隔
static const double PLVVodPlaybackRecoveryHandlerInterval = 10.0;
/// 正常播放结束，时间差判断标准。播放结束时，如果当前播放时间与总时间之差在10s内，判定正常播放结束
static const NSInteger PLVPlaybackEndTimeInterval = 10;
/// 播放器加载超时时长
static const NSTimeInterval PLVPlayerLoadTimeOut = 25;



@interface PLVVodPlayerViewController ()<IJKMediaUrlOpenDelegate, IJKMediaNativeInvokeDelegate, UIGestureRecognizerDelegate, PLVPictureInPictureManagerDelegate>

#pragma mark - KVO控制器
/// KVO控制器，用于监听播放器状态变化
@property (nonatomic, strong) PLVKVOController *KVOController;
/// 非强引用KVO控制器
@property (nonatomic, strong) PLVKVOController *KVOControllerNonRetaining;

#pragma mark - 播放器实例
/// 主播放器实例
@property (nonatomic, strong) id<PLVIJKMediaPlayback> mainPlayer;
/// 子播放器实例（用于片头播放）
@property (nonatomic, strong) id<PLVIJKMediaPlayback> subPlayer;
/// 广告播放器
@property (nonatomic, strong) PLVVodAdPlayerViewController *adPlayer;

#pragma mark - 播放统计数据（viewlog）
/// 用户播放时间
@property (nonatomic, assign) NSTimeInterval viewerWatchDuration;
/// 用户停留时间
@property (nonatomic, assign) NSTimeInterval viewerStayDuration;
/// 用户观看的视频内容时长
@property (nonatomic, assign) NSTimeInterval videoContentPlayedTime;
/// 一次播放（同一pid），播放器总流量统计
@property (nonatomic, assign) NSUInteger playerTotalFlow;
/// 播放id 标识一次播放
@property (nonatomic, copy) NSString *pid;
/// 用户信息统计定时器
@property (nonatomic, strong) PLVTimer *innerPlaybackTimer;

#pragma mark - 视频信息
/// 当前清晰度
@property (nonatomic, assign) PLVVodQuality quality;
/// 媒体时长
@property (nonatomic, assign) NSTimeInterval duration;
/// 可播放时长
@property (nonatomic, assign) NSTimeInterval playableDuration;
/// 缓冲进度
@property (nonatomic, assign) NSInteger bufferingProgress;
/// 本地视频模型
@property (nonatomic, strong) PLVVodLocalVideo *localOnlineVideo;
/// 当前播放URL
@property (nonatomic, strong) NSURL *currentURL;

#pragma mark - 播放状态控制
/// 是否就绪播放
@property (nonatomic, assign) BOOL preparedToPlay;
/// 播放状态
@property (nonatomic, assign) PLVVodPlaybackState playbackState;
/// 加载状态
@property (nonatomic, assign) PLVVodLoadState loadState;
/// 是否播放结束
@property (nonatomic, assign) BOOL reachEnd;
/// 是否成功播放结束
@property (nonatomic, assign) BOOL reachEndSuccess;
/// 是否正在seek
@property (nonatomic, assign) BOOL isSeeking;
/// 是否正在缓冲
@property (nonatomic, assign) BOOL isBuffering;

#pragma mark - 播放记忆功能
/// 记忆播放速率
@property (nonatomic, assign) double lastPlaybackRate;
/// 记忆播放位置
@property (nonatomic, assign) NSTimeInterval lastPosition;
/// 记录切换线路、播放模式、清晰度之前的播放进度
@property (nonatomic, assign) NSTimeInterval beforSwitchPosition;

#pragma mark - UI交互控制
/// 上次拖动时间
@property (nonatomic, assign) CGFloat playbackSliderLastValue;
/// 正在拖动
@property (nonatomic, assign) BOOL dragging;
/// 手势类型
@property (nonatomic, assign) PLVVodGestureType gestureType;
/// 进度条点击手势
@property (nonatomic, strong) UITapGestureRecognizer * sliderTapGesture;

#pragma mark - 片头播放功能
/// 片头图片
@property (nonatomic, strong) UIImageView *teaserImageView;
/// 自定义片头url
@property (nonatomic, copy) NSString *teaserUrl;
/// 自定义片头播放时长
@property (nonatomic, assign) NSUInteger teaserDuration;
/// 子播放器完成回调
@property (nonatomic, copy) void (^subPlayerFinishHandler)(void);
/// 片头播放完成回调
@property (nonatomic, copy) void (^teaserCompletion)(BOOL finish);
/// 在播放片头阶段，离开播放器，片头时间过去后需要暂停播放正片
@property (nonatomic, assign) BOOL leaveSubPlayer;

#pragma mark - 防录屏功能
/// 是否发现正在被录屏
@property (nonatomic, assign) BOOL videoCapturing;
/// 发现开始被录屏时，播放器是否处于播放中
@property (nonatomic, assign) BOOL isPlayingWhenCaptureStart;

#pragma mark - 网络状态监控
/// 差网络的间隔时间
@property (nonatomic, assign) NSTimeInterval poorNetWorkInterval;
/// 差网络次数
@property (nonatomic, assign) NSInteger poorNetWorkCount;

#pragma mark - 播放器状态标记
/// 是否未曾播放过(用于解决视频未启动播放过时，seek会无效的问题)
@property (nonatomic, assign) BOOL hasPlayed;
/// 是否需要恢复播放进度，用于在preparedToPlay阶段决定是使用startPlaybackTime还是切换清晰度线路之前的时间，来进行seek
@property (nonatomic, assign) BOOL needResumePlaybackProgress;
/// 播放器内部属性，默认配置参数。区别于 autoPlay 属性
@property (nonatomic, assign) BOOL autoPlayByDefault;
/// 已经离开播放器，需要暂停播放
@property (nonatomic, assign) BOOL leavePlayer;
/// 是否播放外部的url视频
@property (nonatomic, assign) BOOL isPlayURL;

#pragma mark - 时间记录与性能监控（QOS）
/// QosLoading 事件  统计开始时间
@property (nonatomic, assign) NSTimeInterval qosLoadingStartTime;
/// QosLoading 事件  从播放器起播（setURL）到完成首帧加载的时间片段
@property (nonatomic, assign) NSTimeInterval qosLoadingDuration;
/// ijk播放器加载视频时间累计, 默认为-1，>=0的时候开始累加
@property (nonatomic, assign) NSTimeInterval playerLoadTime;

/// QosStalling 事件 开始时间统计
@property (nonatomic, assign) NSTimeInterval qosStallingStartTime;
/// QosStalling 事件 结束时间统计
@property (nonatomic, assign) NSTimeInterval qosStallingEndTime;
/// QosStalling 事件 持续时间统计
@property (nonatomic, assign) NSTimeInterval qosStallingDuration;

/// QosBuffer 事件 开始统计时间
@property (nonatomic, assign) NSTimeInterval qosBufferStartTime;

/// QOS Error 上报记录 ijk最后一次请求url
@property (nonatomic, copy) NSString *qosLastRequestUrl;
/// QOS Error 相关服务  上报记录 ijk最后一次请求错误码
@property (nonatomic, assign) NSInteger qosLastRequestErrorCode;
/// QOS Error  上报记录 ijk最后一次请求ts 域名
@property (nonatomic, copy) NSString *qosLastRequestVideoDomain;

#pragma mark - 日志上报功能（Elog + ViewLog + Qos）
/// 是否首次上报viewlog，默认YES
@property (nonatomic, assign) BOOL isFirstViewlog;
/// 是否需要重置viewlog，默认YES
@property (nonatomic, assign) BOOL isResetViewlog;
/// 是否上报qos loading日志
/// 在广告或片头存播放时，主播放器首帧渲染不会回调 在preparetoplay 中上报qosloading
@property (nonatomic, assign) BOOL loadingLogSent;
/// seek 事件统计参数（发送elog日志）
@property (nonatomic, strong) PLVVodElogSeekModel *seekModel;
/// 前一次上报seek 事件时间戳
@property (nonatomic, assign) NSTimeInterval lastReportSeekTime;
/// 播放参数数组，用于elog 日志发送
@property (nonatomic, strong) NSMutableArray<PLVVodElogPlayerPramaItem *> *elogPlayerParamsArr;
/// 播放器起播事件埋点
@property (nonatomic, strong) NSMutableArray<NSDictionary*> *mediaPlayerStartPlayTraceArray;

#pragma mark - 重试机制
/// 码率重试记录
@property (nonatomic, strong) NSMutableDictionary *hasTryQuality;
/// cdn重试记录
@property (nonatomic, strong) NSMutableDictionary *hasTryCdns;

#pragma mark - 播放器回调
/// viewDidLoad block
@property (nonatomic, copy) void (^viewDidLoadHandler)(void);
/// 主播放器准备播放回调
@property (nonatomic, copy) void (^mainPlayerPrepareToPlayHandler)(void);
/// 在未曾播放前的Seek事件
@property (nonatomic, copy) void (^beforePlaySeekEvent)(void);

#pragma mark - UI组件
/// 播放器 logo 图层
@property (nonatomic, strong) PLVVodPlayerLogo *logoView;
/// 调节系统声音使用的控件
@property (nonatomic, strong) MPVolumeView *systemVolumeView;

#pragma mark - 外部视频播放
/// 播放外部视频，请求头设置
@property (nonatomic, copy) NSString *requestHeader;

#pragma mark - 画中画功能
/// 是否正在开启画中画
@property (nonatomic, assign) BOOL openingPictureInPicture;

#pragma mark - 主类中需要被其他分类访问的接口

#pragma mark Player+URL
/// 获取上次播放位置
- (NSTimeInterval)lastPosition;

/// 设置主播放器
/// @param ijkPlayer 播放器实例
- (void)setupMainPlayer:(id<PLVIJKMediaPlayback>)ijkPlayer;

/// 在视图加载完成后执行回调
/// @param handler 回调block
- (void)runAfterViewDidLoad:(void (^)(void))handler;

#pragma mark Player+Ad
/// 设置片头播放状态
/// @param teaserState 片头状态
- (void)setTeaserState:(PLVVodAssetState)teaserState;

/// 上报错误
/// @param error 错误信息
- (void)reportError:(NSError *)error;

#pragma mark Player+Switch
/// 切换播放URL
/// @param URL 播放地址
/// @param options 播放配置
/// @param playToken 播放token
- (void)switchURL:(NSURL *)URL options:(PLVIJKFFOptions *)options playToken:(NSString *)playToken;

/// 设置播放线路
/// @param routeLine 线路标识
- (void)setRouteLineInternal:(NSString *)routeLine;

#pragma mark Player + Token
/// 停止播放器
- (void)stopPlayer;

#pragma mark Player + PIP
/// 获取线路对应的域名
/// @param routeline 线路标识
/// @return 域名字符串
- (NSString *)getHostWithRouteline:(NSString *)routeline;

#pragma mark Player + IJK
/// 开启防录屏功能
- (void)startPreventScreenCapture;

@end


#endif /* PLVVodPlayerViewController_internal_h */
