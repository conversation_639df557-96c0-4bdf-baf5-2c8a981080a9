//
//  PLVVodPlayerViewController+Log.m
//  PolyvVodSDK
//
//  Created by mac on 2020/4/2.
//  Copyright © 2020 POLYV. All rights reserved.
//

#import "PLVVodPlayerViewController+Log.h"
#import "PLVVodPlayerViewController+internal.h"

@implementation PLVVodPlayerViewController (Log)

// 上报elog error 信息
- (void)reportElogWithError:(NSError *)error{
    // Elog 统计
    PLVVodElogModel *elogModel = [[PLVVodElogModel alloc] init];
    elogModel.userId2 = [PLVVodSettings findUserIdWithVid:self.video.vid];
    elogModel.errorCode = [@(error.code) stringValue];
    elogModel.module = PLV_Busi_Play_Module;
    elogModel.playId = self.pid;
    elogModel.videoId = self.video.vid;
    elogModel.vid = self.video.vid;
    
    // logfile
    PLVVodElogLogfileModel *logfile = [[PLVVodElogLogfileModel alloc] init];
    elogModel.logFile = logfile;
    
    logfile.exception = [error description];
    
    // player param
    logfile.playerParam = self.elogPlayerParamsArr;
    
    [PLVVodReportManager reportElogWithElogModel:elogModel completion:nil];
}

// 上报elog seek 事件信息
- (void)reportElogSeeekEvent{
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self reportElogSeeekEvent];
        });
        return;
    }

    // Elog 统计
    PLVVodElogModel *elogModel = [[PLVVodElogModel alloc] init];
    elogModel.userId2 = [PLVVodSettings findUserIdWithVid:self.video.vid];
    elogModel.event = @"seek";
    elogModel.module = PLV_Busi_Play_Module;
    elogModel.playId = self.pid;
    elogModel.videoId = self.video.vid;
    elogModel.vid = self.video.vid;
    
    // flow
    if (self.mainPlayer){
        self.seekModel.flow = [self getPlayerTrafficStatistic];
    }
    
    // logfile
    PLVVodElogLogfileModel *logfile = [[PLVVodElogLogfileModel alloc] init];
    elogModel.logFile = logfile;
    
    logfile.infomation = [self.seekModel jsonStringFromModel];
    
    // player param
    logfile.playerParam = self.elogPlayerParamsArr;
    
    PLVVodLogDebug(@"[elog seek event] -- %@", [elogModel jsonStringFromModel]);
    
    [PLVVodReportManager reportElogWithElogModel:elogModel completion:nil];
}

- (NSString *)getPlayerTrafficStatistic{
    NSString *flow = @"";
    if (self.mainPlayer){
        if ([self.mainPlayer isKindOfClass:[PLVIJKFFMoviePlayerController class]]){
            PLVIJKFFMoviePlayerController *player = (PLVIJKFFMoviePlayerController *)self.mainPlayer;
            flow = [NSString stringWithFormat:@"%llu", (int64_t)self.playerTotalFlow + player.trafficStatistic];
        }
        else if ([self.mainPlayer isKindOfClass:[PLVIJKAVMoviePlayerController class]]){
            // TODO: avplayer flow
            PLVIJKAVMoviePlayerController *avplayer = (PLVIJKAVMoviePlayerController *)self.mainPlayer;
            flow = [NSString stringWithFormat:@"%llu", (int64_t)self.playerTotalFlow + avplayer.numberOfBytesTransferred];
        }
    }
    
    return flow;
}

// 发送seek 日志
- (void)sendSeekEvent{
    
    // 两次seek 事件上报间隔至少5 秒, 首次自动产生的seek 事件不发送
    NSTimeInterval curTime = [[NSDate date] timeIntervalSince1970];
    if (self.lastReportSeekTime > 0 && (curTime - self.lastReportSeekTime) > 5){
        NSInteger seekCount = [self.seekModel.seek_count integerValue] + 1;
           self.seekModel.seek_count = [@(seekCount) stringValue];
           
           self.seekModel.seek_time_later = [NSString stringWithFormat:@"%d", (int)self.currentPlaybackTime];
           self.seekModel.play_time_duration = [NSString stringWithFormat:@"%d", (int)self.viewerWatchDuration];
           self.seekModel.stay_time_duration = [NSString stringWithFormat:@"%d", (int)self.viewerStayDuration];
           
           // 发送elog 日志
           [self reportElogSeeekEvent];
        
        self.lastReportSeekTime = curTime;
    }
    
    if (self.lastReportSeekTime == 0){
        self.lastReportSeekTime = curTime;
    }
}

/// 发送customKeyToken事件
- (void)sendCustomKeyTokenEvent {
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self sendCustomKeyTokenEvent];
        });
        return;
    }
    
    // Elog 统计
    PLVVodElogModel *elogModel = [[PLVVodElogModel alloc] init];
    elogModel.errorCode = [@(player_load_timeout) stringValue];
    elogModel.event = @"customKeyToken";
    elogModel.module = PLV_Busi_Play_Module;
    elogModel.playId = self.pid;
    elogModel.videoId = self.video.vid;
    elogModel.vid = self.video.vid;
    
    // logfile
    PLVVodElogLogfileModel *logfile = [[PLVVodElogLogfileModel alloc] init];
    elogModel.logFile = logfile;
    
    // player param
    logfile.playerParam = self.elogPlayerParamsArr;
    
    PLVVodLogDebug(@"[elog customKeyToken] -- %@", [elogModel jsonStringFromModel]);
    
    [PLVVodReportManager reportElogWithElogModel:elogModel completion:nil];
}

/// 发送loadTimeout事件
- (void)sendLoadTimeOutEvent {
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self sendLoadTimeOutEvent];
        });
        return;
    }

    // Elog 统计
    PLVVodElogModel *elogModel = [[PLVVodElogModel alloc] init];
    elogModel.errorCode = [@(player_load_timeout) stringValue];
    elogModel.event = @"loadTimeout";
    elogModel.module = PLV_Busi_Play_Module;
    elogModel.playId = self.pid;
    elogModel.videoId = self.video.vid;
    elogModel.vid = self.video.vid;
    
    // logfile
    PLVVodElogLogfileModel *logfile = [[PLVVodElogLogfileModel alloc] init];
    elogModel.logFile = logfile;
    
    // player param
    logfile.playerParam = self.elogPlayerParamsArr;
    
    PLVVodLogDebug(@"[elog loadTimeout event] -- %@", [elogModel jsonStringFromModel]);
    
    [PLVVodReportManager reportElogWithElogModel:elogModel completion:nil];
}

/// 发送httpdnsChangeCdn事件
- (void)sendHttpDnsChangeEvent {
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self sendHttpDnsChangeEvent];
        });
        return;
    }

    // Elog 统计
    PLVVodElogModel *elogModel = [[PLVVodElogModel alloc] init];
    elogModel.errorCode = [@(httpdns_change_Cdn) stringValue];
    elogModel.event = @"httpdnsChangeCdn";
    elogModel.module = PLV_Busi_Play_Module;
    elogModel.playId = self.pid;
    elogModel.videoId = self.video.vid;
    elogModel.vid = self.video.vid;
    
    // logfile
    PLVVodElogLogfileModel *logfile = [[PLVVodElogLogfileModel alloc] init];
    elogModel.logFile = logfile;
    
    // player param
    logfile.playerParam = self.elogPlayerParamsArr;
    
    PLVVodLogDebug(@"[elog httpdnsChangeCdn event] -- %@", [elogModel jsonStringFromModel]);
    
    [PLVVodReportManager reportElogWithElogModel:elogModel completion:nil];
}

- (void)sendHttpDnsPlayVideoEvent {
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self sendHttpDnsPlayVideoEvent];
        });
        return;
    }
    
    // Elog 统计
    PLVVodElogModel *elogModel = [[PLVVodElogModel alloc] init];
    elogModel.errorCode = [@(httpdns_change_Cdn) stringValue];
    elogModel.event = @"httpdnsPlayVideo";
    elogModel.module = PLV_Busi_Play_Module;
    elogModel.playId = self.pid;
    elogModel.videoId = self.video.vid;
    elogModel.vid = self.video.vid;
    
    // logfile
    PLVVodElogLogfileModel *logfile = [[PLVVodElogLogfileModel alloc] init];
    elogModel.logFile = logfile;
    
    // player param
    logfile.playerParam = self.elogPlayerParamsArr;
    
    PLVVodLogDebug(@"[elog httpdnsPlayVideo event] -- %@", [elogModel jsonStringFromModel]);
    
    [PLVVodReportManager reportElogWithElogModel:elogModel completion:nil];
}

- (void)sendTraceMediaPlayerStartPlayEvent {
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self sendTraceMediaPlayerStartPlayEvent];
        });
        return;
    }
    
    // Elog 统计
    PLVVodElogModel *elogModel = [[PLVVodElogModel alloc] init];
    elogModel.userId2 = [PLVVodSettings findUserIdWithVid:self.video.vid];
    elogModel.event = @"traceMediaPlayerStartPlay";
    elogModel.module = PLV_Busi_Play_Module;
    elogModel.playId = self.pid;
    elogModel.videoId = self.video.vid;
    elogModel.vid = self.video.vid;
    
    // logfile
    PLVVodElogLogfileModel *logfile = [[PLVVodElogLogfileModel alloc] init];
    elogModel.logFile = logfile;
    
    // information
    NSArray *array = self.mediaPlayerStartPlayTraceArray;
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:array options:kNilOptions error:&error];
    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    if (error) {
        PLVVodLogWarn(@"elog traceMediaPlayerStartPlay event] -- 发送失败：无法 JSON 序列化对象，错误：%@，对象：%@", error, array);
        return;
    }
    logfile.infomation = jsonString;

    // player param
    logfile.playerParam = self.elogPlayerParamsArr;
    
    PLVVodLogDebug(@"[elog traceMediaPlayerStartPlay event] -- %@", [elogModel jsonStringFromModel]);
    
    [PLVVodReportManager reportElogWithElogModel:elogModel completion:nil];
}

// type: format 1; codec 2; player 4
- (void)addElogPlayerParam:(NSString *)name value:(NSString *)value type:(NSString *)type{
    PLVVodElogPlayerPramaItem *item = [[PLVVodElogPlayerPramaItem alloc] init];
    item.name = name;
    item.value =value;
    item.type = type;
    
    if (item) {
        [self.elogPlayerParamsArr addObject:item];
    }
}

- (void)reportViewLog {
    if (fmod(self.viewerStayDuration, self.video.reportFreq) != 0.0) {
        return;
    }

    // 本次播放累积加载流量大小
    NSInteger flow = 0;
    if (self.mainPlayer && !self.localPlayback){
        if ([PLVPictureInPictureManager sharedInstance].pictureInPictureActive) {
            flow = [[PLVPictureInPictureManager sharedInstance] trafficStatistic] + self.playerTotalFlow;
        }else {
            if ([self.mainPlayer isKindOfClass:[PLVIJKFFMoviePlayerController class]]){
                PLVIJKFFMoviePlayerController *player = (PLVIJKFFMoviePlayerController *)self.mainPlayer;
                flow = player.trafficStatistic + self.playerTotalFlow;
            }
            else if ([self.mainPlayer isKindOfClass:[PLVIJKAVMoviePlayerController class]]){
                PLVIJKAVMoviePlayerController *avplayer = (PLVIJKAVMoviePlayerController *)self.mainPlayer;
                flow = avplayer.numberOfBytesTransferred + self.playerTotalFlow;
            }
        }
    }
    
    PLVVodSettings *settings = [PLVVodSettings sharedSettings];
    NSString *appId = settings.appid;
    NSString *userid = [PLVVodSettings findUserIdWithVid:self.video.vid];

    NSString *viewerId = settings.viewerId;
    NSString *viewName = settings.viewerName;
    NSString *viewAvatar = settings.viewerAvatar;
    
    NSString *pid = self.pid;
    NSString *vid = self.video.vid;
    NSString *categoryId = self.video.categoryId;
    
    NSInteger currentPlaybackTime = round(self.currentPlaybackTime);
    if ([PLVPictureInPictureManager sharedInstance].pictureInPictureActive) {
        currentPlaybackTime = [[PLVPictureInPictureManager sharedInstance] currentPlaybackTime];
    }
    NSInteger watchDuration = floor(self.viewerWatchDuration);
    NSInteger stayDuration = floor(self.viewerStayDuration);
    NSInteger videoDuration = floor(self.duration);
    NSString *timestamp = [PLVVodUtil timestamp];
    
    NSString *plainSign =[NSString stringWithFormat:@"rtas.net%@%@%zd%zd%zd", pid, vid, flow, watchDuration, currentPlaybackTime];
    NSString *sign = [PLVVodUtil md5String:plainSign];
    
    NSString *href = self.currentURL.absoluteString;
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    // 子账号appId
    if (![PLVVodUtil isNilString:appId]){
        params[@"appId"] = appId;
        userid = settings.userid;
    }
    // cataid
    params[@"cataid"] = categoryId;
    // pid
    params[@"pid"] = pid;
    // uid
    params[@"uid"] = userid;
    // vid
    params[@"vid"] = vid;
    // flow=0
    params[@"flow"] = @(flow);
    // pd
    params[@"pd"] = @(watchDuration);
    // sd
    params[@"sd"] = @(stayDuration);
    // ts
    params[@"ts"] = timestamp;
    // sign
    params[@"sign"] = sign;
    // cts
    params[@"cts"] = @(currentPlaybackTime);
    // duration
    params[@"duration"] = @(videoDuration);
    // kp
    params[@"kp"] = self.video.keep_play ? @(1) : @(0);
    
    // href，UrlSafeBase64
    params[@"href"] = [PLVVodUtil urlSafeBase64String:href];
    // pn
    params[@"pn"] = PLVVodSdkPlatform;
    // pv
    params[@"pv"] = PLVVodSdkVersion;
    // sid，UrlSafeBase64
    if (viewerId.length) params[@"sid"] = [PLVVodUtil urlSafeBase64String:viewerId];
    // param2，UrlSafeBase64
    if (viewName.length) params[@"param2"] = [PLVVodUtil urlSafeBase64String:viewName];
    
    // viewerInfo 为新的viewlog参数设置对象，优先获取
    if (![PLVVodUtil isNilString:settings.viewerInfos.viewerExtraInfo1]){
        NSString *param3 = settings.viewerInfos.viewerExtraInfo1;
        NSString *param4 = settings.viewerInfos.viewerExtraInfo2;
        NSString *param5 = settings.viewerInfos.viewerExtraInfo3;
        // param3，UrlSafeBase64
        if (![PLVVodUtil isNilString:param3]) params[@"param3"] = [PLVVodUtil urlSafeBase64String:param3];
        // param4，UrlSafeBase64
        if (![PLVVodUtil isNilString:param4]) params[@"param4"] = [PLVVodUtil urlSafeBase64String:param4];
        // param5，UrlSafeBase64
        if (![PLVVodUtil isNilString:param5]) params[@"param5"] = [PLVVodUtil urlSafeBase64String:param5];
    }
    else{
        for (NSString *key in self.viewlogExtraParams.allKeys) {
            if (key) {
                params[key] = self.viewlogExtraParams[key];
            }
        }
    }
    
    // 发送第一条viewlog 时候传递
    if (self.isFirstViewlog){
        self.isFirstViewlog = NO;
        // avatar
        if (viewAvatar.length) params[@"vieweravatar"] = [PLVVodUtil urlSafeBase64String:viewAvatar];
        // ute
        // 通用跟踪事件： bop (beginning of play) 代表是本次观看的首条viewlog
        params[@"ute"] = @"bop";
    }
        
    // 添加seek 信息
    if (!self.isFirstViewlog){
        params[@"ute"] = self.seekModel.uteSeek;
        params[@"uted"] = self.seekModel.uteSeekDes;
    }
    
    PLVVodLogDebug(@"[viewlog 日志] -- %@", params );
    [PLVVodReportManager reportViewLogWithParams:params];
}

/// Qos 播放错误统计
- (void)reportQosPlayError:(NSError *)errorInfo errorType:(PLVVodQosErrorType)type{
    //
    NSURL *url = errorInfo.userInfo[NSURLErrorKey];
    NSString *resCode = errorInfo.userInfo[PLVVodHttpStatusCodeKey];
    NSString *errorCode = [PLVVodUtil getQosErrorCodeWithType:type];
    
    [PLVVodReportManager reportErrorQosWithPid:self.pid
                                           vid:self.video.vid
                                     errorCode:errorCode
                                    requestUrl:url.absoluteString
                                  responseCode:resCode
                                        params:@{@"param5": [PLVVodUtil userAgent]}];
}

- (void)sendIJKNetInfomation:(NSDictionary *)infomation{
    // Elog 统计
    PLVVodElogModel *elogModel = [[PLVVodElogModel alloc] init];
    elogModel.userId2 = [PLVVodSettings findUserIdWithVid:self.video.vid];
    elogModel.module = PLV_Busi_Play_Module;
    elogModel.playId = self.pid;
    elogModel.videoId = self.video.vid;
    elogModel.vid = self.video.vid;
    elogModel.event = @"IjkNetworkException";
    
    // logfile
    PLVVodElogLogfileModel *logfile = [[PLVVodElogLogfileModel alloc] init];
    elogModel.logFile = logfile;
    NSMutableDictionary *sendData = [[NSMutableDictionary alloc] initWithDictionary:infomation];
    
    // 获取当前时间
    NSDate *currentDate = [NSDate date];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
    NSString *dateString = [formatter stringFromDate:currentDate];
    [sendData setObject:dateString forKey:@"time"];
    
    logfile.infomation = [sendData description];
        
    // player param
    logfile.playerParam = self.elogPlayerParamsArr;
    
    [PLVVodReportManager reportElogWithElogModel:elogModel completion:nil];
}

@end
