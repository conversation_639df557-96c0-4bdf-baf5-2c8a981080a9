//
//  PLVVodPlayerViewController+IJK.h
//  PLVVodSDK
//
//  Created by polyv on 2025/6/3.
//  Copyright © 2025 POLYV. All rights reserved.
//

#import <PLVVodSDK/PLVVodSDK.h>
#import <PLVIJKPlayer/PLVIJKPlayer.h>

NS_ASSUME_NONNULL_BEGIN

@interface PLVVodPlayerViewController (IJK)

- (void)setupIjkOptions:(PLVIJKFFOptions *)options;

- (PLVIJKFFOptions *)localTokenOptions:(NSString*)tokenPath;

- (PLVIJKAVOptions *)localAvOption;

#pragma mark - IJKMediaUrlOpenDelegate
- (void)willOpenUrl_IJK:(PLVIJKMediaUrlOpenData *)urlOpenData;

#pragma mark - IJKMediaNativeInvokeDelegate
- (int)invoke_IJK:(IJKMediaEvent)event attributes:(NSDictionary *)attributes;

#pragma mark IJKNotification
-(void)addObserversWithPlayer:(id)player;

-(void)removeObserversWithPlayer:(id)player;

/// 上报qos stalling 事件
- (void)reportQosStallingEvent;

#pragma mark - QoS Error Reporting
/// 判断是否应该上报视频加载失败的QoS错误
- (BOOL)shouldReportVideoLoadFailureError;

/// 上报视频加载失败的QoS错误（来自URL打开事件, 网络事件）
- (void)reportVideoLoadFailureQosError;

@end

NS_ASSUME_NONNULL_END
