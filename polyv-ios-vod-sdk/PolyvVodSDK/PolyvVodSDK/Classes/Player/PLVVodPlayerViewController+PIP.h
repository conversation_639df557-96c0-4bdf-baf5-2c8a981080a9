//
//  PLVVodPlayerViewController+PIP.h
//  PLVVodSDK
//
//  Created by polyv on 2025/6/4.
//  Copyright © 2025 POLYV. All rights reserved.
//

#import <PLVVodSDK/PLVVodSDK.h>

NS_ASSUME_NONNULL_BEGIN

@interface PLVVodPlayerViewController (PIP)

/// 开启画中画功能
- (void)startPictureInPicture_PIP;

/// 关闭画中画功能
- (void)stopPictureInPicture_PIP;

#pragma mark - PLVPictureInPictureManagerDelegate

/// 画中画即将开启的回调
- (void)plvPictureInPictureWillStart_PIP;

/// 画中画已开启的回调
- (void)plvPictureInPictureDidStart_PIP;

/// 画中画开启失败的回调
/// @param error 错误信息
- (void)plvPictureInPictureFailedToStartWithError_PIP:(NSError *)error;

/// 画中画即将关闭的回调
- (void)plvPictureInPictureWillStop_PIP;

/// 画中画已关闭的回调
- (void)plvPictureInPictureDidStop_PIP;

/// 画中画播放状态改变的回调
/// @param playing 是否正在播放
- (void)plvPictureInPicturePlayerPlayingStateDidChange_PIP:(BOOL)playing;

@end

NS_ASSUME_NONNULL_END
