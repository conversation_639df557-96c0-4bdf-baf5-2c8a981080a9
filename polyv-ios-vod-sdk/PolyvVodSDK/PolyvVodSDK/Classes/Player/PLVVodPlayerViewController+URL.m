//
//  PLVVodPlayerViewController+URL.m
//  PLVVodSDK
//
//  Created by mac on 2020/4/2.
//  Copyright © 2020 POLYV. All rights reserved.
//

#import "PLVVodPlayerViewController+URL.h"
#import "PLVVodPlayerViewController+internal.h"
#import "PLVVodPlayerViewController+Log.h"
#import "PLVVodPlayerViewController+Skin.h"
#import "PLVVodPlayerViewController+IJK.h"

@implementation PLVVodPlayerViewController (URL)

- (void)setupIjkOptionsExternal:(PLVIJKFFOptions *)options {
    [self setupIjkOptions:options];
    
    // seek-at-start
    if (0 < self.startPlaybackTime && self.startPlaybackTime < self.duration) {
        [options setPlayerOptionIntValue:(int64_t)self.startPlaybackTime forKey:@"seek-at-start"];
        // self.currentPlaybackTime = self.startPlaybackTime;
    }

    // 设置url header
    if (self.requestHeader.length){
        [options setFormatOptionValue:self.requestHeader forKey:@"headers"];

        [self addElogPlayerParam:@"headers" value:self.requestHeader type:@"4"];
    }
}

#pragma mark -- 外部视频播放
- (void)setURLInternal:(NSURL *)videoUrl{
    NSLog(@"xx_%s - %@ - %@", __FUNCTION__, [NSThread currentThread], videoUrl);
    
    if (videoUrl == nil || [videoUrl isKindOfClass:[NSNull class]]) return;
    
    // 保存上一视频的播放位置
    [self setLastPosition:self.currentPlaybackTime];
    
    self.currentURL = videoUrl;
    
    if (self.loadingHandler) self.loadingHandler(YES);
    
    // 重置此值
    self.hasPlayed = NO;
    //重置首次seek的事件暂存，否则新视频会执行上一个视频的seek事件
    self.beforePlaySeekEvent = nil;
    
    if (self.mainPlayer) { // 清理播放器
        if ([self.mainPlayer isKindOfClass:[PLVIJKFFMoviePlayerController class]]) {
            PLVIJKFFMoviePlayerController *ijkFFPlayer = self.mainPlayer;
            ijkFFPlayer.tcpOpenDelegate = nil;
            ijkFFPlayer.httpOpenDelegate = nil;
            ijkFFPlayer.liveOpenDelegate = nil;
            ijkFFPlayer.segmentOpenDelegate = nil;
            ijkFFPlayer.nativeInvokeDelegate = nil;
        }
        [self.mainPlayer.view removeFromSuperview];
        [self.mainPlayer shutdown];
        
        //
        [self removeExternalObserversWithPlayer:self.mainPlayer];
        
        // 恢复变量
        self.preparedToPlay = NO;
        self.playbackState = PLVVodPlaybackStateStopped;
        self.loadState = PLVVodLoadStateUnknown;
        self.reachEnd = NO;
    }
    
    PLVIJKFFOptions *options = [PLVIJKFFOptions optionsByDefault];
    // 配置 ijk 通用选项
    [self setupIjkOptionsExternal:options];
    
    self.isPlayURL = YES;
    self.mainPlayer = [[PLVIJKFFMoviePlayerController alloc] initWithContentURL:videoUrl withOptions:options];
    [self addExternalObserversWithPlayer:self.mainPlayer];
    
    // 在viewDidLoad后，操作view
    //[self setupMainPlayer:self.mainPlayer];
    __weak typeof(self) weakSelf = self;
    [self runAfterViewDidLoad:^{
        [weakSelf setupMainPlayer:weakSelf.mainPlayer];
    }];
    
    // UI 更新设置
    [self setupWhenVideoDidLoadExternal];
    
    // 开始播放
    self.mainPlayerPrepareToPlayHandler = ^{
                
        // 设置不自动播放无效20191015lh add start
        if(self.autoplay == NO){
            [weakSelf pause];
        }else{
            [weakSelf play];
        }
        // add end
    };
}

- (void)setURLInternal:(NSURL *)videoUrl withHeaders:(NSDictionary<NSString *,NSString *> *)headers{
    //
    __block NSMutableString *headerStr = [[NSMutableString alloc] init];
    [headers enumerateKeysAndObjectsUsingBlock:^(NSString * _Nonnull key, NSString * _Nonnull obj, BOOL * _Nonnull stop) {
        [headerStr appendFormat:@"%@: %@\r\n", key, obj];
    }];
    self.requestHeader = headerStr;
    PLVVodLogDebug(@"[外部播放url 请求头] %@", self.requestHeader);
    
    [self setURL:videoUrl];
}

// 播放外部视频，配置播放器通知
-(void)addExternalObserversWithPlayer:(id)player {
    // notification
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loadStateDidChangeExternal:) name:IJKMPMoviePlayerLoadStateDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(playBackDidFinishExternal:) name:IJKMPMoviePlayerPlaybackDidFinishNotification object:player];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(preparedToPlayExternal:) name:IJKMPMediaPlaybackIsPreparedToPlayDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(playbackStateDidChangeExternal:) name:IJKMPMoviePlayerPlaybackStateDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(seekCompleteExternal:)
        name:IJKMPMoviePlayerDidSeekCompleteNotification object:player];
    
}

// 播放外部视频，移除播放器相关通知
-(void)removeExternalObserversWithPlayer:(id)player {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMoviePlayerLoadStateDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMoviePlayerPlaybackDidFinishNotification object:player];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMediaPlaybackIsPreparedToPlayDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMoviePlayerPlaybackStateDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMoviePlayerDidSeekCompleteNotification object:player];
}

// seek
- (void)seekCompleteExternal:(NSNotification *)notification {
    if (self.loadingHandler) self.loadingHandler(NO);
}

// 播放结束
- (void)playBackDidFinishExternal:(NSNotification *)notification {
    
    self.reachEnd = YES;
    //    MPMovieFinishReasonPlaybackEnded,
    //    MPMovieFinishReasonPlaybackError,
    //    MPMovieFinishReasonUserExited
    IJKMPMovieFinishReason finishReason = [notification.userInfo[IJKMPMoviePlayerPlaybackDidFinishReasonUserInfoKey] integerValue];
    
    switch (finishReason) {
        case IJKMPMovieFinishReasonPlaybackEnded: {
            PLVVodLogDebug(@"playbackPlayBackDidFinish: IJKMPMovieFinishReasonPlaybackEnded: %zd\n", finishReason);
            if (self.mainPlayer.duration - self.currentPlaybackTime <= PLVPlaybackEndTimeInterval){
                self.lastPosition = self.startPlaybackTime = 0.0;
            }
            else{
                self.lastPosition = self.currentPlaybackTime;
            }
            
        } break;
        case IJKMPMovieFinishReasonUserExited: {
            PLVVodLogDebug(@"playbackPlayBackDidFinish: IJKMPMovieFinishReasonUserExited: %zd\n", finishReason);
            self.lastPosition = self.currentPlaybackTime;
        } break;
        case IJKMPMovieFinishReasonPlaybackError: {
            PLVVodLogDebug(@"playbackPlayBackDidFinish: IJKMPMovieFinishReasonPlaybackError: %zd\n", finishReason);
            self.lastPosition = self.currentPlaybackTime;
            
            if (self.playerErrorHandler) self.playerErrorHandler(self, nil);
           
        } break;
        default: {
            //NSLog(@"playbackPlayBackDidFinish: ???: %zd\n unkown", finishReason);
            self.lastPosition = self.currentPlaybackTime;
            
        } break;
    }
}

// 加载状态通知
- (void)loadStateDidChangeExternal:(NSNotification *)notification {
    PLVVodLoadState loadState = self.loadState = (PLVVodLoadState)self.mainPlayer.loadState;
    //    MPMovieLoadStateUnknown        = 0,
    //    MPMovieLoadStatePlayable       = 1 << 0,
    //    MPMovieLoadStatePlaythroughOK  = 1 << 1, // Playback will be automatically started in this state when shouldAutoplay is YES
    //    MPMovieLoadStateStalled        = 1 << 2, // Playback will be automatically paused in this state, if started
    
    if ((loadState & IJKMPMovieLoadStatePlaythroughOK) != 0) {
        PLVVodLogDebug(@"loadStateDidChange: IJKMPMovieLoadStatePlaythroughOK: %zd\n", loadState);
        if (self.loadingHandler) self.loadingHandler(NO);
        
    } else if ((loadState & IJKMPMovieLoadStateStalled) != 0) {
        PLVVodLogDebug(@"loadStateDidChange: IJKMPMovieLoadStateStalled: %zd\n", loadState);
        if (self.loadingHandler) self.loadingHandler(YES);

    }
    else if ((loadState & IJKMPMovieLoadStatePlayable) != 0) {
        PLVVodLogDebug(@"loadStateDidChange: IJKMPMovieLoadStatePlayable: %zd\n", loadState);
        if (self.loadingHandler) self.loadingHandler(NO);
    }
    else {
        PLVVodLogDebug(@"loadStateDidChange: ???: %zd\n", loadState);
        if (self.loadingHandler) self.loadingHandler(YES);
    }
}

// 准备播放
- (void)preparedToPlayExternal:(NSNotification *)notification {
   
    self.preparedToPlay = self.mainPlayer.isPreparedToPlay;
    self.duration = self.mainPlayer.duration;
    // 保留兼容其他非 FFmpeg 播放器
    if (0 < self.startPlaybackTime && self.startPlaybackTime < self.duration) {
        self.currentPlaybackTime = self.startPlaybackTime;
    }
    
    if (self.mainPlayerPrepareToPlayHandler) {
        self.mainPlayerPrepareToPlayHandler();
        self.mainPlayerPrepareToPlayHandler = nil;
    }
    
    [self attachPlayerControl];
}

// 播放状态通知
- (void)playbackStateDidChangeExternal:(NSNotification *)notification {
    self.playbackState = (PLVVodPlaybackState)self.mainPlayer.playbackState;
    
    switch (self.mainPlayer.playbackState) {
        case IJKMPMoviePlaybackStateStopped: {
            PLVVodLogDebug(@"IJKMPMoviePlayBackStateDidChange %d: stoped", (int)self.mainPlayer.playbackState);
            break;
        }
        case IJKMPMoviePlaybackStatePlaying: {
            PLVVodLogDebug(@"IJKMPMoviePlayBackStateDidChange %d: playing", (int)self.mainPlayer.playbackState);
            self.reachEnd = NO;
            if (!self.hasPlayed) {
                self.hasPlayed = YES;
                if(self.beforePlaySeekEvent){
                    self.beforePlaySeekEvent();
                    self.beforePlaySeekEvent = nil;
                }
            }
            break;
        }
        case IJKMPMoviePlaybackStatePaused: {
            PLVVodLogDebug(@"IJKMPMoviePlayBackStateDidChange %d: paused", (int)self.mainPlayer.playbackState);
            break;
        }
        case IJKMPMoviePlaybackStateInterrupted: {
            PLVVodLogDebug(@"IJKMPMoviePlayBackStateDidChange %d: interrupted", (int)self.mainPlayer.playbackState);
            break;
        }
        case IJKMPMoviePlaybackStateSeekingForward:
        case IJKMPMoviePlaybackStateSeekingBackward: {
            PLVVodLogDebug(@"IJKMPMoviePlayBackStateDidChange %d: seeking", (int)self.mainPlayer.playbackState);
            break;
        }
        default: {
            PLVVodLogDebug(@"IJKMPMoviePlayBackStateDidChange %d: unknown", (int)self.mainPlayer.playbackState);
            break;
        }
    }
    
    // 同步播放按钮状态
    [self syncPlayPauseButton];
}

/// 每次视频模型载入时设置相关参数
- (void)setupWhenVideoDidLoadExternal {
    // 记忆播放位置需在获得 video 之后
    if (self.rememberLastPosition) {
        self.startPlaybackTime = self.lastPosition;
    } else {
        self.lastPosition = 0.0;
    }
   
    NSTimeInterval interval = 0.5;
    __weak typeof(self) weakSelf = self;
    self.innerPlaybackTimer = [PLVTimer repeatWithInterval:interval repeatBlock:^{
        
        // 更新信息
        if (!weakSelf.reachEnd) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [weakSelf updateUIWithTimer];
            });
        }
        
        // add by libl [对于非成功播放结束的场景，需要主动轮询播放] 2018-08-11 start
        if (weakSelf.reachEnd && (weakSelf.mainPlayer.duration - weakSelf.mainPlayer.currentPlaybackTime >= PLVPlaybackEndTimeInterval)
            && fmod(weakSelf.viewerStayDuration, PLVVodPlaybackRecoveryHandlerInterval) == 0.0){
            
            if (weakSelf.mainPlayer.isPreparedToPlay){
                dispatch_async(dispatch_get_main_queue(), ^{
                    
                    if (weakSelf.playbackRecoveryHandle){
                        weakSelf.playbackRecoveryHandle(weakSelf);
                    }
                    
                    /** 放到上层应用去实现，减小sdk出错概率，降低风险
                     [self setCurrentPlaybackTime:self.lastPosition];
                     // 对于某些场景需要再次调用play函数才能播放
                     [self play];
                     */
                });
            }
        }
        // add end
    }];
}

@end
