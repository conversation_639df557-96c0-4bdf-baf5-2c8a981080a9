//
//  PLVVodPlayerUtil.m
//  PolyvVodSDK
//
//  Created by mac on 2018/10/30.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import "PLVVodPlayerUtil.h"
#import "PLVVodNetworking.h"
#import "PLVVodUtil.h"

static NSString * const PLVVodLastPositionKey = @"net.polyv.sdk.vod.lastPosition";
static NSString * const PLVVodLastPositionTimestampKey = @"net.polyv.sdk.vod.lastPositionTimestamp";

@implementation PLVVodPlayerUtil

+ (NSTimeInterval)lastPositionWithVid:(NSString *)videoId{
    NSTimeInterval lastPosition = 0.0;
    NSDictionary *lastPositionDict = [[NSUserDefaults standardUserDefaults] dictionaryForKey:PLVVodLastPositionKey];
    if (lastPositionDict.count) {
        lastPosition = [lastPositionDict[videoId] doubleValue];
    }
    return lastPosition;
}

+ (NSTimeInterval)lastPositionTimestampWithVid:(NSString *)videoId{
    NSTimeInterval lastTimestamp = 0;
    NSDictionary *lastPositionTimeDict = [[NSUserDefaults standardUserDefaults] dictionaryForKey:PLVVodLastPositionTimestampKey];
    if (lastPositionTimeDict.count) {
        lastTimestamp = [lastPositionTimeDict[videoId] doubleValue];
    }
    return lastTimestamp;
}

+ (void)requestCastKeyIvWitVideo:(PLVVodVideo *)video
                         quality:(NSInteger)quality
                      completion:(void (^)(NSString * _Nonnull, NSString * _Nonnull, NSError * _Nonnull))completion{
    
    if (video == nil || ![video isKindOfClass:[PLVVodVideo class]]) {
        PLVVodLogError(@"视频模型非法，%@", video);
        return;
    }
   
    [PLVVodNetworking requestCastKeyIvWithVid:video.vid constKey:video.constKey quality:quality completion:^(NSString *key, NSString *iv, NSError *error) {
        if (completion) completion(key,iv,error);
    }];
}

@end
