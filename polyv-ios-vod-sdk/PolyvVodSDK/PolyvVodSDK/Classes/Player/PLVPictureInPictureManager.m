//
//  PLVPictureInPictureManager.m
//  PLVVodSDK
//
//  Created by junotang on 2022/4/7.
//  Copyright © 2022 POLYV. All rights reserved.
//

#import "PLVPictureInPictureManager.h"
#import "PLVPictureInPictureManager+Private.h"
#import <AVKit/AVKit.h>
#import <PLVIJKPlayer/PLVIJKPlayer.h>
#import "PLVVodUtil.h"

@interface PLVPictureInPictureManager ()<
AVPictureInPictureControllerDelegate
>

#pragma mark 数据
@property (nonatomic, assign) BOOL pictureInPictureActive;  //!< 画中画小窗是否开启
@property (nonatomic, assign) NSInteger playbackState;  //!< avplayer的播放状态
@property (nonatomic, assign) CGFloat currentPlaybackRate;    //!< avplayer的播放速率

#pragma mark 功能对象
@property (nonatomic, strong) PLVIJKAVMoviePlayerController *avPlayer;
@property (nonatomic, strong) UIView *playerLayerSuperview;
@property (nonatomic, strong) AVPictureInPictureController *pictureInPictureController API_AVAILABLE(ios(9.0));

@end

@implementation PLVPictureInPictureManager

#pragma mark - [ Life Cycle ]

- (instancetype)init {
    if (self = [super init]) {
    }
    return self;
}

#pragma mark - [ Public Method ]

+ (instancetype)sharedInstance {
    static PLVPictureInPictureManager *_sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _sharedInstance = [self new];
    });
    return _sharedInstance;
}

- (void)startPictureInPictureWithContentURL:(NSURL *)contentURL withOptions:(PLVIJKAVOptions *)options displaySuperview:(UIView *)displaySuperview {
    if (@available(iOS 9.0, *)) {
        if ([AVPictureInPictureController isPictureInPictureSupported]) {
            [self cleanPictureInPicturePlayer];
            
            self.playerLayerSuperview = displaySuperview;
            
            // 初始化播放器
            self.avPlayer = [[PLVIJKAVMoviePlayerController alloc]initWithContentURL:contentURL withOptions:options];
            self.currentPlaybackRate = 1.0;
            [self addObserversWithPlayer:self.avPlayer];
            [self setupMainPlayer:self.avPlayer];
            
            // 初始化画中画控制器
            self.pictureInPictureController = [[AVPictureInPictureController alloc]initWithPlayerLayer:(AVPlayerLayer *)self.avPlayer.view.layer];
            if (@available(iOS 14.0, *)) {
                self.pictureInPictureController.requiresLinearPlayback = _requiresLinearPlayback;
            } else {
                // Fallback on earlier versions
            }
            if ([[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPad) {
                if (@available(iOS 15.0, *)) {
                    self.pictureInPictureController.canStartPictureInPictureAutomaticallyFromInline = NO;
                }
            }else if (@available(iOS 14.2, *)) {
                // home键之后自动开启画中画
                self.pictureInPictureController.canStartPictureInPictureAutomaticallyFromInline = NO;
            }
            self.pictureInPictureController.delegate = self;
            
            [self.avPlayer prepareToPlay];
            
        }else {
            [self pictureInPictureErrorCallbackWithError:nil];
            PLVVodLogError(@"PLVPictureInPictureManager - The current device does not support 'PictureInPicture'");
        }
    } else {
        [self pictureInPictureErrorCallbackWithError:nil];
        PLVVodLogError(@"PLVPictureInPictureManager - 'PictureInPicture' is only available on iOS 9.0 or newer");
    }
}

- (void)stopPictureInPicture {
    if (@available(iOS 9.0, *)) {
        if (!self.pictureInPictureController) {
            return;
        }
        [self.pictureInPictureController stopPictureInPicture];
    }else {
        PLVVodLogError(@"PLVPictureInPictureManager - 'PictureInPicture' is only available on iOS 9.0 or newer");
    }
}

/// 获取当前播放器使用的流量
- (int64_t)trafficStatistic {
    if (self.avPlayer) {
        return self.avPlayer.numberOfBytesTransferred;
    }
    return 0;
}

/// 获取当前avplayer播放器的播放进度
- (NSTimeInterval)currentPlaybackTime {
    if (self.avPlayer) {
        return self.avPlayer.currentPlaybackTime;
    }
    return 0;
}

- (void)setPlaybackRate:(CGFloat)rate {
    self.currentPlaybackRate = rate;
    if (self.avPlayer) {
        __weak typeof(self) weakSelf = self;
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf.avPlayer setPlaybackRate:rate];
        });
    }
}

- (void)setRequiresLinearPlayback:(BOOL)requiresLinearPlayback{
    _requiresLinearPlayback = requiresLinearPlayback;
    
    if (@available(iOS 14.0, *)) {
        if (self.pictureInPictureController){
            self.pictureInPictureController.requiresLinearPlayback = requiresLinearPlayback;
        }
    } else {
        // Fallback on earlier versions
    }
}

#pragma mark - [ Private Method ]

- (void)setupMainPlayer:(id<PLVIJKMediaPlayback>)ijkPlayer {
    ijkPlayer.view.autoresizingMask = UIViewAutoresizingFlexibleWidth|UIViewAutoresizingFlexibleHeight;
    ijkPlayer.view.frame = self.playerLayerSuperview.bounds;
    ijkPlayer.scalingMode = IJKMPMovieScalingModeAspectFit;
    ijkPlayer.shouldAutoplay = NO;
    ijkPlayer.allowsMediaAirPlay = NO;
    ijkPlayer.isDanmakuMediaAirPlay = YES;
    [ijkPlayer setPauseInBackground:NO];
    self.playerLayerSuperview.autoresizesSubviews = YES;
//    [self.playerLayerSuperview addSubview:ijkPlayer.view];
    [self.playerLayerSuperview insertSubview:ijkPlayer.view atIndex:0];
}

/// 清理画中画功能
- (void)cleanPictureInPicturePlayer {
    
    if (self.avPlayer) {
        [self.avPlayer.view removeFromSuperview];
        [self.avPlayer shutdown];
        [self removeObserversWithPlayer:self.avPlayer];
        self.avPlayer = nil;
    }
    self.playerLayerSuperview = nil;
    
    if (@available(iOS 9.0, *)) {
        self.pictureInPictureController.delegate = nil;
        self.pictureInPictureController = nil;
    }
    
    self.restoreDelegate = nil;
    self.vodPlayer = nil;
}

/// 开启画中画
- (void)startPictureInPicture {
    if (@available(iOS 9.0, *)) {
        if (!self.pictureInPictureController) {
            return;
        }
        [self.pictureInPictureController startPictureInPicture];
    }else {
        PLVVodLogError(@"PLVPictureInPictureManager - 'PictureInPicture' is only available on iOS 9.0 or newer");
    }
}

- (void)pictureInPictureErrorCallbackWithError:(NSError *)error {
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvPictureInPictureFailedToStartWithError:)]) {
        [self.delegate plvPictureInPictureFailedToStartWithError:error];
    }
}

#pragma mark - Getter & Setter

- (BOOL)pictureInPictureActive {
    if (@available(iOS 9.0, *)) {
        if (!self.pictureInPictureController) {
            return NO;
        }
        return self.pictureInPictureController.pictureInPictureActive;
    } else {
        return NO;
    }
}

- (NSString *)currentPlaybackVid {
    if (!self.vodPlayer) {
        return @"";
    }
    return self.vodPlayer.video.vid;
}

#pragma mark - player CallBack
- (void)playBackDidFinish:(NSNotification *)notification {
    
}

- (void)preparedToPlay:(NSNotification *)notification {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self startPictureInPicture];
    });
}

- (void)playbackStateDidChange:(NSNotification *)notification {
    self.playbackState = (NSInteger)self.avPlayer.playbackState;
    if (self.avPlayer.playbackState == IJKMPMoviePlaybackStatePlaying) {
        __weak typeof(self) weakSelf = self;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [weakSelf.avPlayer setPlaybackRate:weakSelf.currentPlaybackRate];
        });
    }
}

-(void)addObserversWithPlayer:(id)player {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(playBackDidFinish:) name:IJKMPMoviePlayerPlaybackDidFinishNotification object:player];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(preparedToPlay:) name:IJKMPMediaPlaybackIsPreparedToPlayDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(playbackStateDidChange:) name:IJKMPMoviePlayerPlaybackStateDidChangeNotification object:player];
}
-(void)removeObserversWithPlayer:(id)player {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMoviePlayerPlaybackDidFinishNotification object:player];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMediaPlaybackIsPreparedToPlayDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMoviePlayerPlaybackStateDidChangeNotification object:player];
}

#pragma mark - [ Delegate ]
#pragma mark AVPictureInPictureControllerDelegate
-(void)pictureInPictureControllerWillStartPictureInPicture:(AVPictureInPictureController *)pictureInPictureController
API_AVAILABLE(ios(9.0)) {
    PLVVodLogDebug(@"即将开启画中画");
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvPictureInPictureWillStart)]) {
        [self.delegate plvPictureInPictureWillStart];
    }
}
  
-(void)pictureInPictureControllerDidStartPictureInPicture:(AVPictureInPictureController *)pictureInPictureController
API_AVAILABLE(ios(9.0)) {
    PLVVodLogDebug(@"已经开启画中画");
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvPictureInPictureDidStart)]) {
        [self.delegate plvPictureInPictureDidStart];
    }
    
    NSTimeInterval currentTime = self.vodPlayer.currentPlaybackTime;
    [self.avPlayer setCurrentPlaybackTime:currentTime];
    [self.avPlayer play];
    [self.vodPlayer pause];
    __weak typeof(self) weakSelf = self;
    dispatch_async(dispatch_get_main_queue(), ^{
        [weakSelf.avPlayer setPlaybackRate:weakSelf.currentPlaybackRate];
    });
}
  
-(void)pictureInPictureController:(AVPictureInPictureController *)pictureInPictureController failedToStartPictureInPictureWithError:(NSError *)error API_AVAILABLE(ios(9.0)) {
    PLVVodLogDebug(@"开启画中画失败-%@", error);
    [self cleanPictureInPicturePlayer];
    [self pictureInPictureErrorCallbackWithError:error];
}
  
-(void)pictureInPictureControllerWillStopPictureInPicture:(AVPictureInPictureController *)pictureInPictureController API_AVAILABLE(ios(9.0)) {
    PLVVodLogDebug(@"即将关闭画中画");
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvPictureInPictureWillStop)]) {
        [self.delegate plvPictureInPictureWillStop];
    }
    
    [self.avPlayer pause];
    NSTimeInterval currentTime = self.avPlayer.currentPlaybackTime;
    [self.vodPlayer setCurrentPlaybackTime:currentTime];
    [self.vodPlayer play];
}
  
-(void)pictureInPictureControllerDidStopPictureInPicture:(AVPictureInPictureController *)pictureInPictureController API_AVAILABLE(ios(9.0)) {
    PLVVodLogDebug(@"已经关闭画中画");
    [self cleanPictureInPicturePlayer];
    
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvPictureInPictureDidStop)]) {
        [self.delegate plvPictureInPictureDidStop];
    }
}
  
-(void)pictureInPictureController:(AVPictureInPictureController *)pictureInPictureController restoreUserInterfaceForPictureInPictureStopWithCompletionHandler:(void (^)(BOOL))completionHandler API_AVAILABLE(ios(9.0)) {
    PLVVodLogDebug(@"关闭画中画且恢复播放界面");
    if (self.restoreDelegate &&
        [self.restoreDelegate respondsToSelector:@selector(plvPictureInPictureRestoreUserInterfaceForPictureInPictureStopWithCompletionHandler:)]) {
        [self.restoreDelegate plvPictureInPictureRestoreUserInterfaceForPictureInPictureStopWithCompletionHandler:completionHandler];
    }else {
        completionHandler(YES);
    }
}

@end
