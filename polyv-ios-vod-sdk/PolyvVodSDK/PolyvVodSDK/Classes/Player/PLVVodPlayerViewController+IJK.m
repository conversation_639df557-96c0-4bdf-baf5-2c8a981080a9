//
//  PLVVodPlayerViewController+IJK.m
//  PLVVodSDK
//
//  Created by polyv on 2025/6/3.
//  Copyright © 2025 POLYV. All rights reserved.
//

#import "PLVVodPlayerViewController+IJK.h"
#import "PLVVodPlayerViewController+internal.h"
#import "PLVVodPlayerViewController+Log.h"
#import "PLVVodPlayerViewController+Reconnect.h"
#import "PLVVodPlayerViewController+Skin.h"

@implementation PLVVodPlayerViewController (IJK)

- (void)setupIjkOptions:(PLVIJKFFOptions *)options {
    
    [options setPlayerOptionIntValue:5 forKey:@"framedrop"];
    [options setCodecOptionIntValue:IJK_AVDISCARD_NONREF forKey:@"skip_loop_filter"];
    [options setCodecOptionIntValue:IJK_AVDISCARD_DEFAULT forKey:@"skip_frame"];
    
    // 客户反馈iOS与安卓、web观看比较，清晰度相差较大；经验证，软解清晰度会比硬解更高；
    // 解码方式可自定义，默认硬解码
    if (self.isVideoToolBox){
         [options setPlayerOptionIntValue:1 forKey:@"videotoolbox"];
    }else {
         [options setPlayerOptionIntValue:0 forKey:@"videotoolbox"];
    }
    
    // 关闭 ijk dns 缓存
    [options setFormatOptionIntValue:1 forKey:@"dns_cache_clear"];
    // specify how many microseconds are analyzed to probe the input (from 0 to I64_MAX)
    //[options setFormatOptionValue:@"500000" forKey:@"analyzeduration"];
    // set probing size (from 32 to I64_MAX)
    //[options setFormatOptionValue:@"4096" forKey:@"probesize"];
    
    // max buffer size should be pre-read
    if (self.maxCacheSize > 0){
        [options setPlayerOptionIntValue:self.maxCacheSize forKey:@"max-buffer-size"];
    }else{
        [options setPlayerOptionIntValue:10*1024*1024 forKey:@"max-buffer-size"];
    }
    
    // mac cache daration
    if (self.minCacheFrame > 0){
        [options setPlayerOptionIntValue:self.minCacheFrame forKey:@"min-frames"];
    }
    else if (self.maxCacheDuration > 0){
        [options setPlayerOptionIntValue:self.maxCacheDuration*25 forKey:@"min-frames"];
    }

    // fps probe size
    [options setFormatOptionIntValue:self.fpsProbeSize forKey:@"fpsprobesize"];
    
    // seek-at-start
    int64_t startMs = self.startPlaybackTime * 1000;
    // NSLog(@"---- seek-at-start: %lld", startMs);
    [options setPlayerOptionIntValue:startMs forKey:@"seek-at-start"];
    
    // skip-accurate-seek-at-start
    // NSLog(@"---- skip-accurate-seek-at-start: %d", (!self.seekTypeAtStart));
    [options setPlayerOptionIntValue:(int64_t)(!self.seekTypeAtStart) forKey:@"skip-accurate-seek-at-start"];
    
    // add by libl [设置循环播放,m3u8 视频不支持次设置] 2018-10-12
    if (self.enablePlayRecycle && !self.video.isHls){
        [options setPlayerOptionIntValue:0 forKey:@"loop"];
        
        [self addElogPlayerParam:@"loop" value:@"0" type:@"4"];
    }
    // add end
    
    // add by libl [播放精准定位设置] 2018-11-14 start
    if (self.seekType == PLVVodPlaySeekTypePrecise){
        // 19-03-19
        // 问题：原精准模式，可以精准seek到某个时间点，但会出现慢放（音画不同步）的问题
        // 解决：不设置’seek_type‘参数，使用’enable-accurate-seek‘参数来设置，则可精准seek，且不会出现慢放情况
        // [options setFormatOptionIntValue:1 forKey:@"seek_type"];
        [options setOptionIntValue:1 forKey:@"enable-accurate-seek" ofCategory:kIJKFFOptionCategoryPlayer];
        
        [self addElogPlayerParam:@"enable-accurate-seek" value:@"1" type:@"4"];
    }
    // add end
    
    if (self.enableKeyFrameSeek){
        [options setOptionIntValue:1 forKey:@"enable-key-frame-seek" ofCategory:kIJKFFOptionCategoryPlayer];
    }
    
    // add by libl [新增设置项，否则无法播放本地视频] 2019-01-09 start
    [options setFormatOptionValue:@"ALL" forKey:@"allowed_extensions"];
    // add end
    
    // add by libl [收集ijkplayer参数，用于elog日志发送] start
    [self addElogPlayerParam:@"framedrop" value:@"1" type:@"4"];

    if (self.isVideoToolBox){
        [self addElogPlayerParam:@"videotoolbox" value:@"1" type:@"4"];
    }else{
        [self addElogPlayerParam:@"videotoolbox" value:@"0" type:@"4"];
    }

    [self addElogPlayerParam:@"dns_cache_clear" value:@"1" type:@"1"];
    
    [self addElogPlayerParam:@"max-buffer-size" value:@"1" type:@"4"];

    [self addElogPlayerParam:@"allowed_extensions" value:@"ALL" type:@"1"];
    
    // add end

    // drm 10 11
    if ([self.video.hlsVersion integerValue] > 0 && !self.video.isPlain){
        [options setFormatOptionIntValue:self.video.nativeKeyVersion forKey:@"ff_const"];
        NSString *viewerId = [PLVVodSettings sharedSettings].viewerId;
        viewerId = [PLVVodUtil urlSafeBase64String:viewerId];
        if (![PLVVodUtil isNilString:viewerId]){
           [options setFormatOptionValue:viewerId forKey:@"viewerid"];
        }
    }
    
    if (self.video.hlsPrivateVersion > 0) {
        [options setFormatOptionIntValue:self.video.hlsPrivateVersion forKey:@"hls_private"];
        
        // vrm12 以上需要使用软解播放
        if (self.video.hlsPrivateVersion > 1){
            self.isVideoToolBox = NO;
            [options setPlayerOptionIntValue:0 forKey:@"videotoolbox"];
        }
    }
    
    // hls_seek_optimize
    if ([self isPlayM3u8SourceVideo]){
        [options setFormatOptionIntValue:1 forKey:@"hls_seek_optimize"];
    }
    
    if (self.video.isHls) {
        //为了解决某些视频seek0会卡住的问题，如果是hls视频，seek0的时候强制改为seek1
        [options setPlayerOptionIntValue:1 forKey:@"force-seek-zeroth-to-first"];
        // 切片请求最大连续错误数量
        [options setFormatOptionIntValue:5 forKey:@"max_open_fail"];
    }
    
    //为了解决mp3播放的时候，seek太慢的问题
    if (self.playbackMode == PLVVodPlaybackModeAudio &&
        self.audioSeekType == PLVVodPlayAudioSeekTypeFast) {
        [options setPlayerOptionIntValue:1 forKey:@"mp3-fast-seek"];
    }
    
    // user-agent
    [options setFormatOptionValue:[PLVVodUtil ijkplayerUserAgent] forKey:@"user-agent"];
}

- (BOOL)isPlayM3u8SourceVideo{
    BOOL isM3u8 = NO;
    bool hasM3u8 = [self.video.play_source_url isKindOfClass:[NSString class]] && [self.video.play_source_url containsString:@"m3u8"];
    if (self.video.keepSource && hasM3u8){
        isM3u8 = YES;
    }
    return isM3u8;
}

- (PLVIJKAVOptions *)localAvOption {
    PLVIJKAVOptions *options = [PLVIJKAVOptions optionsByDefault];
    if (!self.video.isPlain) {
        [options setOptionValue:self.video.constKey forKey:PLVIJKAVOptionKeyKeySeed];
    }
    [options setOptionValue:@(self.video.nativeKeyVersion) forKey:PLVIJKAVOptionKeyFFConst];
    
    NSString *downloadDir = [PLVVodDownloadManager sharedManager].downloadDir;
    [options setOptionValue:downloadDir forKey:PLVIJKAVOptionKeyDownloadDir];
    
    NSString *viewerid = [PLVVodUtil urlSafeBase64String:[PLVVodSettings sharedSettings].viewerId];
    [options setOptionValue:viewerid forKey:PLVIJKAVOptionKeyViewerId];

    // 获取本地下载视频token 路径
    NSString *videoPoolId = videoPoolIdWithVid(self.video.vid);
    NSString *tokenPath = nil;
    if (self.localOnlineVideo){
        tokenPath = self.localOnlineVideo.tokenPath;
    }
    NSString *token = @"";
    if (![PLVVodUtil isNilString:tokenPath] && [[NSFileManager defaultManager] fileExistsAtPath:tokenPath]) {
       NSError *error = nil;
       token = [NSString stringWithContentsOfFile:tokenPath encoding:NSUTF8StringEncoding error:&error];
       // TODO: ask error handle
       if (error) {
           NSLog(@"读取本地视频token失败：%@", error);
       }
    } else {
       NSLog(@"本地视频token文件不存在：%@", tokenPath);
    }
    // 传递本地加密视频解码参数
    [options setOptionValue:@(0) forKey:PLVIJKAVOptionKeyDeviceType];
    [options setOptionValue:videoPoolId forKey:PLVIJKAVOptionKeyVideopoolId];
    if (![PLVVodUtil isNilString:token]){
        [options setOptionValue:token forKey:PLVIJKAVOptionKeySignature];
    }
    
    return options;
}

- (PLVIJKFFOptions *)localTokenOptions:(NSString*)tokenPath {
    PLVIJKFFOptions *options = [PLVIJKFFOptions optionsByDefault];
    NSString *videoPoolId = videoPoolIdWithVid(self.video.vid);
    // NSString *secretkey = [PLVVodSettings findSecretKeyWithVid:self.video.vid];
    // NSString *sign = [NSString stringWithFormat:@"%@%@", secretkey, videoPoolId];
    // sign = [PLVVodUtil md5String:sign];

    NSString *token = nil;
    if ([[NSFileManager defaultManager] fileExistsAtPath:tokenPath]) {
        NSError *error = nil;
        token = [NSString stringWithContentsOfFile:tokenPath encoding:NSUTF8StringEncoding error:&error];
        // TODO: ask error handle
        if (error) {
            NSLog(@"读取文件失败：%@", error);
        }
    } else {
        NSLog(@"文件不存在：%@", tokenPath);
    }
    
    [options setFormatOptionIntValue:0 forKey:@"device_type"];
    [options setFormatOptionValue:videoPoolId forKey:@"videopool_id"];
    // [options setFormatOptionValue:sign forKey:@"signature"];
    [options setFormatOptionValue:token forKey:@"signature"];
    // 播放本地加密视频需要此参数，value不为空即可；对应IJKMediaFramework最低版本k0.8.4+180607
    [options setFormatOptionValue:@"local" forKey:@"key_token"];
    
    // add by libl [收集ijkplayer参数，用于elog日志分析] 2019-05-14 start
    [self addElogPlayerParam:@"device_type" value:@"0" type:@"1"];
    [self addElogPlayerParam:@"videopool_id" value:videoPoolId type:@"1"];
    // [self addElogPlayerParam:@"signature" value:sign type:@"1"];
    [self addElogPlayerParam:@"signature" value:token type:@"1"];
    [self addElogPlayerParam:@"key_token" value:@"local" type:@"1"];
    // add end

    return options;
}

#pragma mark - IJKMediaUrlOpenDelegate

- (void)willOpenUrl_IJK:(PLVIJKMediaUrlOpenData *)urlOpenData {
    // 此处实现URL打开前的回调处理
    switch (urlOpenData.event) {
        case IJKMediaCtrl_WillTcpOpen:
            // 处理HTTP URL
            [self tcpOpen:urlOpenData];
            break;
        case IJKMediaCtrl_DidTcpOpen:
            // 处理TCP URL
            [self tcpOpen:urlOpenData];
            break;
        case IJKMediaCtrl_WillHttpOpen:
            [self httpOpen:urlOpenData];
            break;
        default:
            break;
    }
}

- (int)tcpOpen:(PLVIJKMediaUrlOpenData *)openData {
//    PLVVodLogDebug(@"[IJKMediaTcpOpenDelegate] tcpOpen: %@", openData.url);
    // 处理TCP连接打开事件
    self.qosLastRequestUrl = openData.url;
    self.qosLastRequestErrorCode = openData.error;
    
    if (openData.error < 0){
        // 只有在设置播放URL后且不是重试切换线路的情况下才上报QoS错误
//        if ([self shouldReportVideoLoadFailureError]) {
//            [self reportVideoLoadFailureQosError];
//        }
        // 仍然上报elog用于调试
        [self sendOpenUrlEvent:openData];
    }
 
    return 0;
}

- (int)httpOpen:(PLVIJKMediaUrlOpenData *)openData {
//    PLVVodLogDebug(@"[IJKMediaHttpOpenDelegate] httpOpen: %@", openData.url);
    // 处理HTTP连接打开事件
    self.qosLastRequestUrl = openData.url;
    self.qosLastRequestErrorCode = openData.error;
    
    if (openData.error < 0){
        // 只有在设置播放URL后且不是重试切换线路的情况下才上报QoS错误
        // 暂时不上报 可能过于频繁 定位错误依赖elog
//        if ([self shouldReportVideoLoadFailureError]) {
//            [self reportVideoLoadFailureQosError];
//        }
        // 仍然上报elog用于调试
        [self sendOpenUrlEvent:openData];
    }
 
    return 0;
}

#pragma mark - IJKMediaNativeInvokeDelegate

- (int)invoke_IJK:(IJKMediaEvent)event attributes:(NSDictionary *)attributes {
//    PLVVodLogDebug(@"[IJKMediaNativeInvokeDelegate] invoke: %zd - %@", event, attributes);
    // 处理IJK播放器的原生回调事件
    // 可以根据不同的event类型进行相应处理
    NSString *http_code = attributes[IJKMediaEventAttrKey_http_code];
    NSString *error_code = attributes[IJKMediaEventAttrKey_error];
    self.qosLastRequestUrl = attributes[IJKMediaEventAttrKey_url];
    self.qosLastRequestErrorCode = [error_code intValue];
    
    if (http_code && error_code){
        if ([http_code intValue] != 200 || [error_code intValue] < 0){
            // 只有在设置播放URL后且不是重试切换线路的情况下才上报QoS错误
            // 暂时不上报 可能过于频繁 定位错误依赖elog
//            if ([self shouldReportVideoLoadFailureError]) {
//                [self reportVideoLoadFailureQosError];
//            }
            // 仍然上报elog日志用于调试
            [self sendIJKNetInfomation:attributes];
        }
    }
  
    return 0;
}

- (void)sendOpenUrlEvent:(PLVIJKMediaUrlOpenData *)openData{
    NSMutableDictionary *dicts = [[NSMutableDictionary alloc] init];
    [dicts setObject:[@(openData.error) stringValue] forKey:@"error"];
    if (openData.url){
        [dicts setObject:openData.url forKey:@"url"];
    }
    if (openData.msg){
        [dicts setObject:openData.msg forKey:@"message"];
    }
    [dicts setObject:[@(openData.event) stringValue] forKey:@"event"];
    [self sendIJKNetInfomation:dicts];
}

#pragma mark - notification

-(void)addObserversWithPlayer:(id)player {
    // notification
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loadStateDidChange:) name:IJKMPMoviePlayerLoadStateDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(playBackDidFinish:) name:IJKMPMoviePlayerPlaybackDidFinishNotification object:player];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(preparedToPlay:) name:IJKMPMediaPlaybackIsPreparedToPlayDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(playbackStateDidChange:) name:IJKMPMoviePlayerPlaybackStateDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(seekComplete:) name:IJKMPMoviePlayerDidSeekCompleteNotification object:player];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(playerFirstVideoFrameRendered:) name:IJKMPMoviePlayerFirstVideoFrameRenderedNotification object:player];
    
    // 播放器起播事件埋点
    NSArray<NSString*> *traceMediaPlayerStartPlayEvent = @[
        IJKMPMoviePlayerProbeInputStartNotification,
        IJKMPMoviePlayerProbeInputFinishNotification,
        IJKMPMoviePlayerOpenInputStartNotification,
        IJKMPMoviePlayerOpenInputFinishNotification,
        IJKMPMoviePlayerFindStreamInfoStartNotification,
        IJKMPMoviePlayerFindStreamInfoFinishNotification,
        IJKMPMoviePlayerOpenStreamStartNotification,
        IJKMPMoviePlayerOpenStreamAudioFinishNotification,
        IJKMPMoviePlayerOpenStreamVideoFinishNotification,
        IJKMPMoviePlayerOpenStreamFinishNotification,
        IJKMPMoviePlayerPrepareFinishNotification,
        IJKMPMoviePlayerStartToPlayNotification,
        IJKMPMoviePlayerPutFirstAudioPacketNotification,
        IJKMPMoviePlayerPutFirstVideoPacketNotification,
        IJKMPMoviePlayerDecodeFirstAudioFrameNotification,
        IJKMPMoviePlayerDecodeFirstVideoFrameNotification,
        IJKMPMoviePlayerRenderFirstAudioFrameNotification,
        IJKMPMoviePlayerRenderFirstVideoFrameNotification,
    ];
    for (NSString *element in traceMediaPlayerStartPlayEvent) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(mediaPlayerStartPlayTraceReceived:) name:element object:player];
    }
}

-(void)removeObserversWithPlayer:(id)player {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMoviePlayerLoadStateDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMoviePlayerPlaybackDidFinishNotification object:player];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMediaPlaybackIsPreparedToPlayDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMoviePlayerPlaybackStateDidChangeNotification object:player];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMoviePlayerDidSeekCompleteNotification object:player];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:IJKMPMoviePlayerFirstVideoFrameRenderedNotification object:player];
    
    // 播放器起播事件埋点
    NSArray<NSString*> *traceMediaPlayerStartPlayEvent = @[
        IJKMPMoviePlayerProbeInputStartNotification,
        IJKMPMoviePlayerProbeInputFinishNotification,
        IJKMPMoviePlayerOpenInputStartNotification,
        IJKMPMoviePlayerOpenInputFinishNotification,
        IJKMPMoviePlayerFindStreamInfoStartNotification,
        IJKMPMoviePlayerFindStreamInfoFinishNotification,
        IJKMPMoviePlayerOpenStreamStartNotification,
        IJKMPMoviePlayerOpenStreamAudioFinishNotification,
        IJKMPMoviePlayerOpenStreamVideoFinishNotification,
        IJKMPMoviePlayerOpenStreamFinishNotification,
        IJKMPMoviePlayerPrepareFinishNotification,
        IJKMPMoviePlayerStartToPlayNotification,
        IJKMPMoviePlayerPutFirstAudioPacketNotification,
        IJKMPMoviePlayerPutFirstVideoPacketNotification,
        IJKMPMoviePlayerDecodeFirstAudioFrameNotification,
        IJKMPMoviePlayerDecodeFirstVideoFrameNotification,
        IJKMPMoviePlayerRenderFirstAudioFrameNotification,
        IJKMPMoviePlayerRenderFirstVideoFrameNotification,
    ];
    for (NSString *element in traceMediaPlayerStartPlayEvent) {
        [[NSNotificationCenter defaultCenter] removeObserver:self name:element object:player];
    }
}

- (void)seekComplete:(NSNotification *)notification {
    self.isSeeking = NO;
    if (self.loadingHandler) self.loadingHandler(NO);
    if (self.seekCompleteHandler) self.seekCompleteHandler(self);
}

- (void)playBackDidFinish:(NSNotification *)notification {
    
    self.reachEnd = YES;
    //    MPMovieFinishReasonPlaybackEnded,
    //    MPMovieFinishReasonPlaybackError,
    //    MPMovieFinishReasonUserExited
    IJKMPMovieFinishReason finishReason = [notification.userInfo[IJKMPMoviePlayerPlaybackDidFinishReasonUserInfoKey] integerValue];
    
    switch (finishReason) {
        case IJKMPMovieFinishReasonPlaybackEnded: {
            PLVVodLogDebug(@"playbackPlayBackDidFinish: IJKMPMovieFinishReasonPlaybackEnded: %zd\n", finishReason);
            if (self.mainPlayer.duration - self.currentPlaybackTime <= PLVPlaybackEndTimeInterval){
                // 正常播放结束
                self.lastPosition = self.startPlaybackTime = 0.0;
                [self.adPlayer showAdWithLocation:PLVVodAdLocationTail completion:nil];
            }
            else{
                self.lastPosition = self.currentPlaybackTime;
            }
            
            // add by libl [对于ts视频，开始播放以后，断网再播放会正常结束] 2019-06-04 start
            if (!self.localPlayback){
                if (PLVVodNotReachable == [PLVVodReachability sharedReachability].currentReachabilityStatus){
                    NSError *plvError = PLVVodErrorMake(network_unreachable, ^(NSMutableDictionary *userInfo) {
                    });
                    if (self.playerErrorHandler){
                        self.playerErrorHandler(self, plvError);
                    }
                }
            }
            // add
            
            // 停止累计播放器加载视频时间
            self.playerLoadTime = -1;

        } break;
        case IJKMPMovieFinishReasonUserExited: {
            PLVVodLogDebug(@"playbackPlayBackDidFinish: IJKMPMovieFinishReasonUserExited: %zd\n", finishReason);
            // 异常结束
            if (self.currentPlaybackTime > 0){
                self.lastPosition = self.currentPlaybackTime;
            }
            // 停止累计播放器加载视频时间
            self.playerLoadTime = -1;
        } break;
        case IJKMPMovieFinishReasonPlaybackError: {
            PLVVodLogDebug(@"playbackPlayBackDidFinish: IJKMPMovieFinishReasonPlaybackError: %zd\n", finishReason);
            if (self.currentPlaybackTime > 0){
                self.lastPosition = self.currentPlaybackTime;
            }
            // 停止累计播放器加载视频时间
            self.playerLoadTime = -1;
            [self handleIjkPlayError];
           
        } break;
        default: {
            //NSLog(@"playbackPlayBackDidFinish: ???: %zd\n unkown", finishReason);
            if (self.currentPlaybackTime > 0){
                self.lastPosition = self.currentPlaybackTime;
            }
            
        } break;
    }
}

- (void)loadStateDidChange:(NSNotification *)notification {
    PLVVodLoadState loadState = self.loadState = (PLVVodLoadState)self.mainPlayer.loadState;
    //    MPMovieLoadStateUnknown        = 0,
    //    MPMovieLoadStatePlayable       = 1 << 0,
    //    MPMovieLoadStatePlaythroughOK  = 1 << 1, // Playback will be automatically started in this state when shouldAutoplay is YES
    //    MPMovieLoadStateStalled        = 1 << 2, // Playback will be automatically paused in this state, if started
    
    //统计差网络次数，并且回调
    if ((loadState & IJKMPMovieLoadStateStalled) != 0 &&
        !self.isSeeking) {
        self.poorNetWorkCount ++;
        self.isBuffering = YES;
        
        // 10秒内差网络次数大于等于两次
        if (self.poorNetWorkCount >= 2 &&
            self.poorNetWorkInterval <= 10) {
            //差网络回调
            if (self.poorNetWorkHandler) {
                self.poorNetWorkHandler();
            }
        }
    }
    
    if ((loadState & IJKMPMovieLoadStatePlaythroughOK) != 0) {
        PLVVodLogDebug(@"loadStateDidChange: IJKMPMovieLoadStatePlaythroughOK: %zd\n", loadState);
        self.isBuffering = NO;
        if (self.loadingHandler) self.loadingHandler(NO);

        if (self.qosBufferStartTime > 0) {
            NSTimeInterval bufferTime = [PLVVodUtil timeIntervalSinceSeconds:self.qosBufferStartTime];
            [PLVVodReportManager reportBufferQosWithPid:self.pid vid:self.video.vid time:bufferTime params:@{@"param5": [PLVVodUtil userAgent]}];
            self.qosBufferStartTime = 0;
        }

        // qosstalling 结束统计
        self.qosStallingStartTime = 0;
        self.qosStallingEndTime = 0;
        self.qosStallingDuration = 0;
        
    } else if ((loadState & IJKMPMovieLoadStateStalled) != 0) {
        PLVVodLogDebug(@"loadStateDidChange: IJKMPMovieLoadStateStalled: %zd\n", loadState);
        if (self.loadingHandler) self.loadingHandler(YES);

        // 开始统计QOSbuffer 只统计一次 且在Loading 发送以后 
        if (self.qosBufferStartTime == 0 && self.loadingLogSent && !self.localPlayback && !self.isSeeking) {
            self.qosBufferStartTime = [PLVVodUtil currentSeconds];
        }

        // qosstalling 开始统计
        if (self.loadingLogSent && !self.localPlayback && !self.isSeeking){
            self.qosStallingStartTime = [PLVVodUtil currentSeconds];
            self.qosStallingDuration = 0;
        }
    }
    else if ((loadState & IJKMPMovieLoadStatePlayable) != 0) {
        PLVVodLogDebug(@"loadStateDidChange: IJKMPMovieLoadStatePlayable: %zd\n", loadState);
        self.isBuffering = NO;
        if (self.loadingHandler) self.loadingHandler(NO);
    }
    else {
        PLVVodLogDebug(@"loadStateDidChange: ???: %zd\n", loadState);
        if (self.loadingHandler) self.loadingHandler(YES);
    }
}

- (void)reportQosStallingEvent {
    if(self.qosStallingStartTime > 0){
        self.qosStallingEndTime = [PLVVodUtil currentSeconds];
        self.qosStallingDuration = (self.qosStallingEndTime - self.qosStallingStartTime)* 1000;
        if(self.qosStallingDuration > self.video.stallingThreshold && self.video.stallingThreshold > 0){
            NSTimeInterval duration = self.qosStallingDuration;
            // 参数准备
            NSMutableDictionary *extraParams = [NSMutableDictionary dictionary];    
            [extraParams setObject:[PLVVodUtil userAgent] forKey:@"param5"];
            if (self.qosLastRequestVideoDomain){
                [extraParams setObject:self.qosLastRequestVideoDomain forKey:@"domain"];
            }
            [PLVVodReportManager reportQosStallingWithPid:self.pid vid:self.video.vid time:duration params:extraParams];
            self.qosStallingStartTime = 0;
            self.qosStallingEndTime = 0;
            self.qosStallingDuration = 0;
        }
    }
}

- (void)preparedToPlay:(NSNotification *)notification {
    // notification 中获取播放器对象
    PLVIJKFFMoviePlayerController *player = notification.object;
    if(player != self.mainPlayer){
        return;
    }
  
    // 停止累计播放器加载视频的时间
    self.playerLoadTime = -1;
    
    self.preparedToPlay = self.mainPlayer.isPreparedToPlay;
    // mod by libl [vod-675 优化显示视频总时长，优先级取videojson 数据] 2018-07-12 start
    self.duration = self.video.duration > 0 ? self.video.duration: self.mainPlayer.duration;
    
    if (0 < self.startPlaybackTime && self.startPlaybackTime < self.duration) {

        //seek保护，直播转存且裁剪过的视频，在preparedToPlay时seek的话，为了避免找不到ts切片导致卡住，需要延迟0.1秒等播放器走完准备流程之后再seek。
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            if (!self.needResumePlaybackProgress) {
                self.currentPlaybackTime = self.startPlaybackTime;
            }
            if (self.rememberPlaybackRate) self.playbackRate = self.lastPlaybackRate;
            if (self.mainPlayerPrepareToPlayHandler) {
                self.mainPlayerPrepareToPlayHandler();
                self.mainPlayerPrepareToPlayHandler = nil;
            }
        });
    }else {
        if (self.mainPlayerPrepareToPlayHandler) {
            self.mainPlayerPrepareToPlayHandler();
            self.mainPlayerPrepareToPlayHandler = nil;
        }
    }
    
    // mod by libl [更改调用顺序，playbackModeDidChange 回调中用到的部分属性只有在attachPlayerControl 执行后才会有数据] start 2018-08-31 start
    [self attachPlayerControl];
    if (self.playbackMode == PLVVodPlaybackModeAudio) {
        // 音频模式下回调
        [self playbackModeDidChange];
        PLVVodLogDebug(@"[player] -- 音频模式切换回调: playbackModeDidChange");
    }
    // mod end
    
    // add by libl [如果已经离开播放器页面，根据客户的设置需要暂停播放] 2019-06-25 start
    if (self.leavePlayer){
        [self pause];
        self.leavePlayer = NO;
    }
    // add end
    
    // 上报qosLoading
    [self reportQosLoading];
}

- (void)reportQosLoading{
    // 在该回调中，近似认为首帧已经渲染，可以上报QosLoading
    // 有片头或者广告时，首帧回调不会及时执行
    NSTimeInterval loadTimeDuration = [PLVVodUtil timeIntervalSinceSeconds:self.qosLoadingStartTime] * 1000;
    if (loadTimeDuration > 0) {
        // 起播到首帧渲染完成所用时间
        self.qosLoadingDuration = loadTimeDuration;
        // 业务流程所花时间，videojson，token 获取等等
        NSTimeInterval loadingTimeBusinessRecod = self.localPlayback ? 0 :[[PLVVodQosLoadingTracer shareManager] playerLoadingBusinessTimeWithVid:self.video.vid];
        // 从业务开始到完成首帧渲染所用时间
        NSTimeInterval qosLoadingTotalTime = loadTimeDuration + loadingTimeBusinessRecod;
        // 参数准备
        NSMutableDictionary *extraParams = [NSMutableDictionary dictionary];
        [extraParams setObject:[PLVVodUtil userAgent] forKey:@"param5"];
        if (self.qosLastRequestVideoDomain){
            [extraParams setObject:self.qosLastRequestVideoDomain forKey:@"domain"];
        }
        
        if (self.qosLoadingDuration && !self.loadingLogSent) {
            [PLVVodReportManager reportLoadingQosWithPid:self.pid
                                                     vid:self.video.vid
                                                    time:qosLoadingTotalTime
                                             time_player:self.qosLoadingDuration
                                           time_business:loadingTimeBusinessRecod
                                                  params:extraParams];
            [self sendTraceMediaPlayerStartPlayEvent];
            self.loadingLogSent = YES;
        }
    }
}

- (void)playbackStateDidChange:(NSNotification *)notification {
    self.playbackState = (PLVVodPlaybackState)self.mainPlayer.playbackState;

    switch (self.mainPlayer.playbackState) {
        case IJKMPMoviePlaybackStateStopped: {
            PLVVodLogDebug(@"IJKMPMoviePlayBackStateDidChange %d: stoped", (int)self.mainPlayer.playbackState);
            break;
        }
        case IJKMPMoviePlaybackStatePlaying: {
            PLVVodLogDebug(@"IJKMPMoviePlayBackStateDidChange %d: playing", (int)self.mainPlayer.playbackState);
            // 在开启防录屏功能 且 正被录屏 的前提下，直接暂停播放
            if (self.videoCaptureProtect && self.videoCapturing) {
                [self startPreventScreenCapture];
                return;
            }

            self.reachEnd = NO;
            if (!self.hasPlayed) {
                self.hasPlayed = YES;
                if (self.beforePlaySeekEvent) {
                    self.beforePlaySeekEvent();
                    self.beforePlaySeekEvent = nil;
                }
            }
            break;
        }
        case IJKMPMoviePlaybackStatePaused: {
            PLVVodLogDebug(@"IJKMPMoviePlayBackStateDidChange %d: paused", (int)self.mainPlayer.playbackState);
            break;
        }
        case IJKMPMoviePlaybackStateInterrupted: {
            PLVVodLogDebug(@"IJKMPMoviePlayBackStateDidChange %d: interrupted", (int)self.mainPlayer.playbackState);
            break;
        }
        case IJKMPMoviePlaybackStateSeekingForward:
        case IJKMPMoviePlaybackStateSeekingBackward: {
            PLVVodLogDebug(@"IJKMPMoviePlayBackStateDidChange %d: seeking", (int)self.mainPlayer.playbackState);
            break;
        }
        default: {
            PLVVodLogDebug(@"IJKMPMoviePlayBackStateDidChange %d: unknown", (int)self.mainPlayer.playbackState);
            break;
        }
    }
    
    // 同步播放按钮状态
    [self syncPlayPauseButton];
    
    // 同步音频模式播发封面动画
    if (self.playbackMode == PLVVodPlaybackModeAudio) {
        BOOL isPlaying = (self.playbackState == IJKMPMoviePlaybackStatePlaying) ? YES : NO;
        [self updateAudioCoverAnimation:isPlaying];
    }
}

- (void)playerFirstVideoFrameRendered:(NSNotification *)notification {
    // 简化逻辑，此处不再上报QOS
}

- (void)mediaPlayerStartPlayTraceReceived:(NSNotification *)notification {
    NSDictionary *element = notification.userInfo;
    [self.mediaPlayerStartPlayTraceArray addObject: element];
}

// 判断是否应该上报视频加载失败的QoS错误
- (BOOL)shouldReportVideoLoadFailureError {
    // 1. 必须是在setPlayUrl之后（有当前播放URL或video对象）
    BOOL hasPlayUrl = (self.currentURL != nil) || (self.video != nil);
    
    // 2. 如果正在重试过程中，不上报错误（避免线路1失败切换线路2时上报）
    // 通过检查重试记录来判断是否是第一次尝试
    BOOL isFirstAttempt = YES;
    if (self.video && !self.localPlayback && !self.video.keepSource) {
        // 检查是否已经尝试过其他线路或码率
        if (self.hasTryCdns.count > 0 || self.hasTryQuality.count > 1) {
            isFirstAttempt = NO;
        }
    }
    
    return hasPlayUrl && isFirstAttempt;
}

// 从播放错误上报视频加载失败的QoS错误
- (void)reportVideoLoadFailureQosError {
    // 构建QoS错误上报参数
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    // 添加CDN域名
    if (self.qosLastRequestVideoDomain){
        params[@"domain"] = self.qosLastRequestVideoDomain;
    }
    // 添加其他必要参数
    params[@"param5"] = [PLVVodUtil userAgent];
    // 从错误信息中提取URL和状态码
    NSString *requestUrl = self.qosLastRequestUrl ?: @"";
    NSString *responseCode = [NSString stringWithFormat:@"%ld", (long)self.qosLastRequestErrorCode];
    
    // 上报QoS错误
    [PLVVodReportManager reportErrorQosWithPid:self.pid
                                           vid:self.video.vid ?: @""
                                     errorCode:PLVVodQosErrorLoadVideoFail
                                    requestUrl:requestUrl
                                  responseCode:responseCode
                                        params:params];
}

@end
