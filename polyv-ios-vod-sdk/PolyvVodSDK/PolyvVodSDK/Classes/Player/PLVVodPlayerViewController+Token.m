//
//  PLVVodPlayerViewController+Token.m
//  PLVVodSDK
//
//  Created by polyv on 2025/6/4.
//  Copyright © 2025 POLYV. All rights reserved.
//

#import "PLVVodPlayerViewController+Token.h"
#import "PLVVodPlayerViewController+internal.h"
#import "PLVVodPlayerViewController+Log.h"

@implementation PLVVodPlayerViewController (Token)

#pragma mark - Token
- (void)requestTokenWithCompletion:(void (^)(PLVIJKFFOptions *ijkOptions, NSString *playToken, NSString *tokenHost))completion {
    if (self.requestCustomKeyTokenBlock) {
        // 自定义keytoken
        NSString *keytoken = self.requestCustomKeyTokenBlock(self.video.vid);
        if ([PLVVodUtil isNilString:keytoken]) {
            PLVVodLogError(@"自定义播放令牌请求错误");
            [self stopPlayer];
            NSError *plvError = PLVVodErrorMake(playback_token_fetch_error, ^(NSMutableDictionary *userInfo) {
                userInfo[NSLocalizedFailureReasonErrorKey] = @"自定义播放令牌请求错误";
            });
            if (self.playerErrorHandler) self.playerErrorHandler(self, plvError);
            
            // QOS统计
            // 业务错误不再统计
//            [self reportQosPlayError:plvError errorType:ePLVVodQosErrorLoadVideoTokenFail];
            // Elog统计
            [self reportElogWithError:plvError];
            if (completion) completion(nil, nil, nil);
            return;
        }
        
        PLVIJKFFOptions *options = [PLVIJKFFOptions optionsByDefault];
        [options setFormatOptionValue:self.video.constKey forKey:@"key_seed"];
        [options setFormatOptionValue:keytoken forKey:@"key_token"];
        if (![PLVVodUtil isNilString:self.customSeed]){
            [options setFormatOptionValue:self.customSeed forKey:@"security_seed"];
            
            [self addElogPlayerParam:@"key_secseed" value:self.customSeed type:@"1"];
        }
        [self addElogPlayerParam:@"key_seed" value:self.video.constKey type:@"1"];
        [self addElogPlayerParam:@"key_token" value:keytoken type:@"1"];

        [self sendCustomKeyTokenEvent];
        
        if (completion) completion(options, keytoken, nil);
    }
    else {
        [PLVVodNetworking requestVideoTokenWithModel:self.video completion:^(NSString *videoToken, NSString *host, NSError *error) {
            if (error) {
                PLVVodLogError(@"播放令牌请求错误，%@", error);
                // add by libl [线路/码率/音视频切换场景，存在新视频播放报错，旧视频继续播放的情况] 2019-06-04 start
                [self stopPlayer];
                
                if (self.playerErrorHandler) self.playerErrorHandler(self, error);

                if (network_unreachable != error.code){
                    NSError *plvError = PLVVodErrorMakeWithError(playback_token_fetch_error, error, ^(NSMutableDictionary *userInfo) {
                        userInfo[PLVVodHttpStatusCodeKey] = error.userInfo[PLVVodHttpStatusCodeKey];
                    });
                    // 业务错误不再统计
//                    [self reportQosPlayError:plvError errorType:ePLVVodQosErrorLoadVideoTokenFail];
                    [self reportElogWithError:plvError];
                }
                
                if (completion) completion(nil, nil, host);
                return;
            }
            
            PLVIJKFFOptions *options = [PLVIJKFFOptions optionsByDefault];
            [options setFormatOptionValue:self.video.constKey forKey:@"key_seed"];
            [options setFormatOptionValue:videoToken forKey:@"key_token"];
            [self addElogPlayerParam:@"key_seed" value:self.video.constKey type:@"1"];
            [self addElogPlayerParam:@"key_token" value:videoToken type:@"1"];
            
            if (completion) completion(options, videoToken, host);
        }];
    }
}

// 未处理 self.requestCustomKeyTokenBlock -- by Sixu
- (void)requestPictureInPictureOptionsWithCompletion:(void (^)(PLVIJKAVOptions *avOptions))completion {
    [PLVVodNetworking requestVideoTokenWithVid:self.video.vid completion:^(NSString *videoToken, NSError *error) {
        if (error) {
            PLVVodLogError(@"播放令牌请求错误，%@", error);
            // add by libl [线路/码率/音视频切换场景，存在新视频播放报错，旧视频继续播放的情况] 2019-06-04 start
            [self stopPlayer];
            // add end
            
            if (self.playerErrorHandler) self.playerErrorHandler(self, error);

            if (network_unreachable != error.code){
                NSError *plvError = PLVVodErrorMakeWithError(playback_token_fetch_error, error, ^(NSMutableDictionary *userInfo) {
                    userInfo[PLVVodHttpStatusCodeKey] = error.userInfo[PLVVodHttpStatusCodeKey];
                });
                // QOS统计
                // 业务错误不再统计
//                [self reportQosPlayError:plvError errorType:ePLVVodQosErrorLoadVideoTokenFail];
                
                // Elog统计
                [self reportElogWithError:plvError];
            }
            
            if (completion) completion(nil);
            return;
        }
        
        PLVIJKAVOptions *options = [PLVIJKAVOptions optionsByDefault];
        [options setOptionValue:videoToken forKey:PLVIJKAVOptionKeyKeyToken];
        [options setOptionValue:self.video.constKey forKey:PLVIJKAVOptionKeyKeySeed];
        [options setOptionValue:@(self.video.nativeKeyVersion) forKey:PLVIJKAVOptionKeyFFConst];
        
        NSString *downloadDir = [PLVVodDownloadManager sharedManager].downloadDir;
        [options setOptionValue:downloadDir forKey:PLVIJKAVOptionKeyDownloadDir];
        
        NSString *viewerid = [PLVVodUtil urlSafeBase64String:[PLVVodSettings sharedSettings].viewerId];
        [options setOptionValue:viewerid forKey:PLVIJKAVOptionKeyViewerId];
        
        // add by libl [收集ijkplayer参数，用于elog日志分析] 2019-05-14 start
        [self addElogPlayerParam:@"key_seed" value:self.video.constKey type:@"1"];
        [self addElogPlayerParam:@"key_token" value:videoToken type:@"1"];
        // add end
        
        if (completion) completion(options);
    }];
}


@end
