//
//  PLVPictureInPictureManager+Private.h
//  PLVVodSDK
//
//  Created by junotang on 2022/4/7.
//  Copyright © 2022 POLYV. All rights reserved.
//

#import "PLVPictureInPictureManager.h"
#import <PLVIJKPlayer/PLVIJKPlayer.h>
#import "PLVVodPlayerViewController.h"

NS_ASSUME_NONNULL_BEGIN

@protocol PLVPictureInPictureManagerDelegate;

/// 仅面向内部的属性及方法
@interface PLVPictureInPictureManager ()

#pragma mark - [ 属性 ]

#pragma mark 可配置项
/// 点播播放器，开启画中画的时候由单例持有。作用如下：
/// 1、防止页面退出导致播放器被释放，进而导致viewlog统计失效。
/// 2、即将开启画中画的时候设置，用于同步播放进度到avplayer
/// 3、已经停止画中画的时候销毁，否则内存泄漏播放器无法释放。
/// 4、再次开启画中画，将会自动释放旧的播放器
@property (nonatomic, strong, nullable) PLVVodPlayerViewController *vodPlayer;

/// 画中画过程代理
@property (nonatomic, weak, nullable) id <PLVPictureInPictureManagerDelegate> delegate;

#pragma mark - [ 方法 ]

/// 开始画中画
/// @param contentURL 画中画播放内容
/// @param displaySuperview 画中画占位图所在父视图
- (void)startPictureInPictureWithContentURL:(NSURL *)contentURL withOptions:(PLVIJKAVOptions *)options displaySuperview:(UIView *)displaySuperview;

/// 获取当前avplayer播放器使用的流量
- (int64_t)trafficStatistic;

/// 获取当前avplayer播放器的播放进度
- (NSTimeInterval)currentPlaybackTime;

@end

#pragma mark - [ 代理方法 ]
/// 画中画代理
@protocol PLVPictureInPictureManagerDelegate <NSObject>

@optional

/// 画中画即将开始
- (void)plvPictureInPictureWillStart;

/// 画中画已经开始
- (void)plvPictureInPictureDidStart;

/// 画中画开启失败
/// @param error 失败错误原因
- (void)plvPictureInPictureFailedToStartWithError:(NSError *)error;

/// 画中画即将停止
- (void)plvPictureInPictureWillStop;

/// 画中画已经停止
- (void)plvPictureInPictureDidStop;

/// 画中画播放器播放状态改变
/// @param playing 是否正在播放
- (void)plvPictureInPicturePlayerPlayingStateDidChange:(BOOL)playing;

@end
NS_ASSUME_NONNULL_END
