//
//  PLVVodVideo.h
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/9.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PLVVodConstans.h"
#import "PLVVodAd.h"

@class PLVVodVideoKeyFrameItem;
@class PLVVodVideoPlayerSetting;
@class PLVVodVideoSubtitleItem;
@class PLVVodVideoDoubleSubtitleItem;

/// 视频数据模型
@interface PLVVodVideo : NSObject

/// 视频 id
@property (nonatomic, copy, readonly) NSString *vid;

/// 视频标题
@property (nonatomic, copy) NSString *title;

// 是否包含 ppt
@property (nonatomic, assign) BOOL hasPPT;

/// viewlog 上报的频率
@property (nonatomic, assign, readonly) int reportFreq;

/// 可用清晰度数量
@property (nonatomic, assign, readonly) int qualityCount;

/// 默认播放清晰度
@property (nonatomic, assign) PLVVodQuality preferredQuality;

/// 视频时长
@property (nonatomic, assign, readonly) NSTimeInterval duration;

/// 源文件大小
@property (nonatomic, assign, readonly) NSInteger sourcefilesize;

/// 源文件url
@property (nonatomic, copy, readonly) NSString *play_source_url;

/// 各码率视频大小
@property (nonatomic, strong, readonly) NSArray<NSNumber *> *filesizes;

/// 视频快照URL
@property (nonatomic, copy) NSString *snapshot;

/// 源文件播放
@property (nonatomic, assign, readonly) BOOL keepSource;

/// 分类id
@property (nonatomic, copy, readonly) NSString *categoryId;

/// 分类树
@property (nonatomic, strong, readonly) NSArray *categoryTree;

/// 是否存在问答
@property (nonatomic, assign, readonly) BOOL interactive;

/// 视频字幕
@property (nonatomic, strong) NSArray <PLVVodVideoSubtitleItem *> *srts;

/// 视频双字幕
@property (nonatomic, strong) NSArray <PLVVodVideoDoubleSubtitleItem *> *match_srt;

/// 播放器皮肤
@property (nonatomic, strong) PLVVodVideoPlayerSetting *player;

/// 字幕名称 支持双字幕且存在双字幕时 增加”双语“字幕名称
@property (nonatomic, strong, readonly) NSArray<NSString *> *srtTitles;

/// 默认字幕文件
@property (nonatomic, assign, readonly) NSInteger defaultSrtIndex;

/// 视频打点信息
@property (nonatomic, strong) NSArray<PLVVodVideoKeyFrameItem *> *videokeyframes;

/// 广告信息
@property (nonatomic, strong) NSArray<PLVVodAd *> *ads;

/// 片头URL
@property (nonatomic, copy) NSString *teaser;

/// 片头播放时长
@property (nonatomic, assign) NSTimeInterval teaserDuration;

/// 是否显示片头
@property (nonatomic, assign) BOOL teaserShow;

/// 可用线路
@property (nonatomic, strong, readonly) NSArray<NSString *> *availableRouteLines;
@property (nonatomic, strong, readonly) NSArray<NSString *> *tsCdns;

/// 可用线路，音频文件
@property (nonatomic, strong, readonly) NSArray<NSString *> *availableAudioRouteLines;

/// 若视频不合法，可以从该属性获取不合法原因，否则为空
@property (nonatomic, strong) NSError *error;

/// 若视频不合法，VOD-1676新增加的错误信息
@property (nonatomic, strong, readonly) NSDictionary *playerError;

/// 音频文件链接
@property (nonatomic, copy, readonly) NSString *aac_link;

/// 音频文件大小
@property (nonatomic, assign, readonly) NSInteger aac_filesize;

/// ppt文件链接
@property (nonatomic, copy, readonly) NSString *ppt_link;

/// 远端控制Httpdns模式，远端优先级大于本地enableHttpDNS的优先级
@property (nonatomic, assign, readonly) PLVVodHttpDnsMode httpDnsMode;

/// 是否续播
@property (nonatomic, assign) BOOL keep_play;

/// qosStalling 事件上报阈值
@property (nonatomic, assign) NSTimeInterval stallingThreshold;

/// 是否本地videojson
@property (nonatomic, assign) BOOL isLocalVideoJson;

/// 视频或账号是否可用
- (BOOL)available;

/// 视频是否为非加密视频
- (BOOL)isPlain;

/// 视频是否为hls视频
- (BOOL)isHls;

/// 视频是否为hls302视频
- (BOOL)isHls302;

/// 是否能够切换音视频模式
- (BOOL)canSwithPlaybackMode;

/// 获取投屏使用的媒体URL
/// @param quality 播放清晰度
- (NSString *)transformCastMediaURLStringWithQuality:(NSInteger)quality;

/**
 请求获取 PLVVodVideo 模型对象，（视频播放时使用）
 
 @param vid vid
 @param completion PLVVodVideo 模型对象
 */
+ (void)requestVideoWithVid:(NSString *)vid completion:(void (^)(PLVVodVideo *video, NSError *error))completion;

/**
 请求获取 PLVVodVideo 模型对象,有网络时优先发送网络请求，无网络时返回本地数据
 保存或更新数据到数据库 （视频下载时使用）
 
 @param vid vid
 @param completion PLVVodVideo 模型对象

 */
+ (void)requestVideoPriorityCacheWithVid:(NSString *)vid completion:(void (^)(PLVVodVideo *video, NSError *error))completion;


#pragma mark - 内部接口

/// status 视频状态
@property (nonatomic, assign) int status;

/// outflow 账户是否超流量
@property (nonatomic, assign) BOOL outflow;

/// timeoutflow 账户是否过期
@property (nonatomic, assign) BOOL timeoutflow;

/// hlsIndex 视频主索引URL
@property (nonatomic, copy) NSString *hlsIndex;

/// hlsIndex_backup 视频主索引备份地址
@property (nonatomic, copy) NSString *hlsIndex_backup;

/// hls 视频子索引URL
@property (nonatomic, strong) NSArray<NSString *> *hlsVideos;

/// hls_backup 主视频索引备用地址
@property (nonatomic, strong) NSArray<NSString *> *hlsVideos_backup;

/// hlsIndex2 视频主索引URL
@property (nonatomic, copy) NSString *hlsIndex2;

/// hls2 视频子索引URL
@property (nonatomic, strong) NSArray<NSString *> *hlsVideos2;

/// mp4 videolink 各码率非加密资源 URL
@property (nonatomic, strong) NSArray<NSString *> *plainVideos;

/// packageUrl ts打包
@property (nonatomic, strong) NSArray<NSString *> *tsPackages;

/// 加密常量
@property (nonatomic, copy) NSString *constKey;

/// ts host
@property (nonatomic, copy) NSString *tsHost;

/// hls version
@property (nonatomic, copy, readonly) NSString *hlsVersion;

///hls私有加密版本
@property (nonatomic, assign, readonly) NSInteger hlsPrivateVersion;
///hls解密key版本
@property (nonatomic, assign, readonly) NSInteger nativeKeyVersion;


@end

// 视频打点信息模型
@interface PLVVodVideoKeyFrameItem : NSObject

@property (nonatomic, copy) NSString *hours;   // 小时
@property (nonatomic, copy) NSString *minutes; // 分钟
@property (nonatomic, copy) NSString *seconds; // 秒
@property (nonatomic, copy) NSString *keycontext; // 关键帧描述
@property (nonatomic, copy) NSString *id;       // 记录id
@property (nonatomic, copy) NSString *keytime;  // 打点时间

@end

@interface PLVVodVideoSubtitleItem : NSObject

@property (nonatomic, copy, readonly) NSString *title; // 字幕名称
@property (nonatomic, copy, readonly) NSString *url; // 字幕地址

@end

// 双字幕
@interface PLVVodVideoDoubleSubtitleItem : NSObject

@property (nonatomic, copy, readonly) NSString *position; // 双字幕位置。topSubtitles-上方字幕；bottomSubtitles-下方字幕
@property (nonatomic, copy, readonly) NSString *title; // 字幕名称
@property (nonatomic, copy, readonly) NSString *url; // 字幕地址

@end

// 字幕样式
@interface PLVVodVideoSubtitlesStyle : NSObject

@property (nonatomic, copy, readonly) NSString *style; // 字幕模式。single-单字幕；double-双字幕
@property (nonatomic, copy, readonly) NSString *position; // 位置，单字幕时为空。top-上方字幕；bottom-下方字幕
@property (nonatomic, copy, readonly) NSString *fontColor; // 字体颜色，16进制RGB，例如#FFFFFF
@property (nonatomic, assign, readonly) BOOL fontBold; // 是否加粗，YES-加粗；NO-不加粗
@property (nonatomic, assign, readonly) BOOL fontItalics; // 是否倾斜，YES-倾斜；NO-不倾斜
@property (nonatomic, copy, readonly) NSString *backgroundColor; // 背景色，RGBA，例如rgba(0, 0, 0, 0.7)

@end

// 视频播放器皮肤配置
@interface PLVVodVideoPlayerSetting : NSObject

@property (nonatomic, assign) BOOL subtitlesEnabled; // 字幕是否显示。NO-不显示字幕；YES-默认显示第一份字幕文件，若双字幕开启且双字幕为默认字幕，则默认显示双字幕
@property (nonatomic, assign) BOOL subtitlesDoubleDefault; // 设置双字幕为默认字幕
@property (nonatomic, assign) BOOL subtitlesDoubleEnabled; // 双字幕功能是否开启。NO-未开启；YES-开启
@property (nonatomic, strong) NSArray <PLVVodVideoSubtitlesStyle *> *subtitles; // 字幕样式

@end

