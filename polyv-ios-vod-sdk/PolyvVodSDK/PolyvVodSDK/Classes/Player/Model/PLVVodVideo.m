//
//  PLVVodVideo.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/9.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodVideo.h"
#import "PLVVodNetworking.h"
#import "PLVVodUtil.h"
#import "PLVVodSettings.h"
#import "PLVVodDownloadInfoManager.h"
#import "PLVVodVideoJson.h"
#import "PLVVodReportManager.h"
#import "PLVVodElogModel.h"
#import "PLVVodHttpDnsManager.h"
#import "PLVVodQosLoadingTracer.h"
#import "PLVVodReachability.h"

/// 3 天
#define kPLVVideoJsonUpdateInterval 60*60*24*3

// 视频打点模型
@implementation PLVVodVideoKeyFrameItem

- (instancetype)initWithDictionary:(NSDictionary *)dict{
    if (self= [super init]){
        _hours = dict[@"houts"];
        _minutes = dict[@"minutes"];
        _seconds = dict[@"seconds"];
        _id = dict[@"id"];
        _keycontext = dict[@"keycontext"];
        _keytime = dict[@"keytime"];
    }
    
    return self;
}

@end

// 字幕
@implementation PLVVodVideoSubtitleItem

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    if (self = [super init]) {
        _title = dict[@"title"];
        _url = dict[@"url"];
    }
    return self;
}

- (NSString *)description {
    NSMutableString *description = [super.description stringByAppendingString:@":\n"].mutableCopy;
    [description appendFormat:@" title: %@;\n", _title];
    [description appendFormat:@" url: %@;\n", _url];
    
    return description;
}

@end

// 双字幕
@implementation PLVVodVideoDoubleSubtitleItem

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    if (self = [super init]) {
        _position = dict[@"position"];
        _title = dict[@"title"];
        _url = dict[@"url"];
    }
    return self;
}

- (NSString *)description {
    NSMutableString *description = [super.description stringByAppendingString:@":\n"].mutableCopy;
    [description appendFormat:@" position: %@;\n", _position];
    [description appendFormat:@" title: %@;\n", _title];
    [description appendFormat:@" url: %@;\n", _url];
    
    return description;
}

@end

// 字幕样式
@implementation PLVVodVideoSubtitlesStyle

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    if (self = [super init]) {
        _style = dict[@"style"];
        _position = dict[@"position"];
        _fontColor = dict[@"fontColor"];
        _fontBold = [dict[@"fontBold"] isEqualToString:@"Y"];
        _fontItalics = [dict[@"fontItalics"] isEqualToString:@"Y"];
        _backgroundColor = dict[@"backgroundColor"];
    }
    return self;
}

- (NSString *)description {
    NSMutableString *description = [super.description stringByAppendingString:@":\n"].mutableCopy;
    [description appendFormat:@" style: %@;\n", _style];
    [description appendFormat:@" position: %@;\n", _position];
    [description appendFormat:@" fontColor: %@;\n", _fontColor];
    [description appendFormat:@" fontBold: %@;\n", _fontBold ? @"Y" : @"N"];
    [description appendFormat:@" fontItalics: %@;\n", _fontItalics ? @"Y" : @"N"];
    [description appendFormat:@" backgroundColor: %@;\n", _backgroundColor];
    
    return description;
}

@end

@implementation PLVVodVideoPlayerSetting

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    if (self = [super init]) {
        _subtitlesEnabled = [dict[@"subtitlesEnabled"] isEqualToString:@"Y"];
        _subtitlesDoubleDefault = [dict[@"subtitlesDoubleDefault"] isEqualToString:@"Y"];
        _subtitlesDoubleEnabled = [dict[@"subtitlesDoubleEnabled"] isEqualToString:@"Y"];
        NSArray *array = dict[@"subtitles"];
        NSMutableArray *subtitlesMuArray = [NSMutableArray array];
        for (NSDictionary *subtitlesDic in array) {
            PLVVodVideoSubtitlesStyle *style = [[PLVVodVideoSubtitlesStyle alloc] initWithDictionary:subtitlesDic];
            [subtitlesMuArray addObject:style];
        }
        _subtitles = [subtitlesMuArray copy];
    }
    return self;
}

- (NSString *)description {
    NSMutableString *description = [super.description stringByAppendingString:@":\n"].mutableCopy;
    [description appendFormat:@" subtitlesEnabled: %@;\n", _subtitlesEnabled ? @"Y" : @"N"];
    [description appendFormat:@" subtitlesDoubleDefault: %@;\n", _subtitlesDoubleDefault ? @"Y" : @"N"];
    [description appendFormat:@" subtitles: %@;\n", _subtitles];
    
    return description;
}

@end

@interface PLVVodVideo ()

/// 是否为 HLS 视频
@property (nonatomic, assign) BOOL isHls;

/// 是否为非加密视频
@property (nonatomic, assign) BOOL isPlain;

/// 是否合法
@property (nonatomic, assign) BOOL available;

/// 是否为 HLS302 视频
@property (nonatomic, assign) BOOL isHls302;

@end

@implementation PLVVodVideo

+ (void)requestVideoWithVid:(NSString *)vid completion:(void (^)(PLVVodVideo *video, NSError *error))completion {
    PLVVodQosLoadingTracerModel *model = [[PLVVodQosLoadingTracerModel alloc] initAndAddStartTimeWithType:PLVVodQosLoadingTracerModelTypeRequestVideoJson];
	[PLVVodNetworking requestVideoDicWithVid:vid completion:^(NSDictionary *videoDic, NSError *error) {
        [[PLVVodQosLoadingTracer shareManager] addEndTimeAndUpdateModel:model vid:vid];
		if (error) {
			completion(nil, error);
            
            if (network_unreachable == error.code){
                // 网络连接断开，不发送统计日志
                return ;
            }
            // add by libl [发送Elog错误日志] 2019-05-13 start
            PLVVodElogModel *logModel = [[PLVVodElogModel alloc] init];
            logModel.userId2 = [PLVVodSettings findUserIdWithVid:vid];
            logModel.module = PLV_Busi_Play_Module;
            logModel.vid = vid;
            logModel.errorCode = [@(error.code) stringValue];
            
            // logFile 设置
            PLVVodElogLogfileModel *logfileModel = [[PLVVodElogLogfileModel alloc] init];
            logfileModel.infomation = [error description];
            logModel.logFile = logfileModel;
            
            [PLVVodReportManager reportElogWithElogModel:logModel completion:nil];
            // add end
            
			return;
		}
		PLVVodVideo *video = [[PLVVodVideo alloc] initWithVid:vid dictionary:videoDic];
		completion(video, error);
	}];
}

+ (void)requestVideoPriorityCacheWithVid:(NSString *)vid completion:(void (^)(PLVVodVideo *video, NSError *error))completion{
    PLVVodQosLoadingTracerModel *model = [[PLVVodQosLoadingTracerModel alloc] initAndAddStartTimeWithType:PLVVodQosLoadingTracerModelTypeRequestVideoJsonPriorityCache];
    
    void (^requestVideoCacheBlock)(void) = ^{ // 请求本地缓存
        PLVVodVideoJson *videoJson = [[PLVVodDownloadInfoManager sharedManager] videoJsonWithVid:vid];
        if (videoJson && [videoJson.dataBody length] > 0){
            NSString *encodeBody = [NSString stringWithFormat:@"%@", videoJson.dataBody];
            NSData *videoJsonData = [PLVVodNetworking AES128DecryptedDataWithBody:encodeBody vid:vid];
            NSError *error = nil;
            NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:videoJsonData options:NSJSONReadingMutableContainers error:&error];
            [[PLVVodQosLoadingTracer shareManager] addEndTimeAndUpdateModel:model vid:vid];
            if (error){
                PLVVodLogError(@"videoJson 解析失败，%@ encodedBody: \n%@", error.localizedDescription, videoJsonData);
                NSError *plvError = PLVVodErrorMakeWithError(json_read_error, error, ^(NSMutableDictionary *userInfo) {
                    userInfo[NSLocalizedFailureReasonErrorKey] = dic;
                });
                if (completion) completion(nil, plvError);
                return;
            }
            
            // 无论超时与否，直接返回
            PLVVodVideo *video = [[PLVVodVideo alloc] initWithVid:vid dictionary:dic];
            video.isLocalVideoJson = YES;
            if (completion) completion(video, error);
            return;
        }
        NSError *error = PLVVodErrorWithCode(network_unreachable);
        [[PLVVodQosLoadingTracer shareManager] addEndTimeAndUpdateModel:model vid:vid];
        if (completion) completion(nil, error);
    };
    
    if (PLVVodNotReachable != [PLVVodReachability sharedReachability].currentReachabilityStatus) {
        // 有网络，优先请求网络数据，并保存更新到数据库中
        [PLVVodVideo requestAndCacheVideoWithVid:vid completion:^(PLVVodVideo *video, NSError *error) {
            [[PLVVodQosLoadingTracer shareManager] addEndTimeAndUpdateModel:model vid:vid];
            if (error) {
                // 请求失败后查询一次本地缓存
                requestVideoCacheBlock();
            } else if (completion){
                video.isLocalVideoJson = YES;
                completion(video, error);
            }
        }];
        return;
    }
    
    // 无网络，返回本地数据
    requestVideoCacheBlock();
}

+ (void)requestAndCacheVideoWithVid:(NSString *)vid completion:(void (^)(PLVVodVideo *video, NSError *error))completion {
    [PLVVodNetworking requestVideoJsonWithVid:vid completion:^(NSDictionary *videoDic, NSString *videoData, NSError *error) {
        if (error) {
            completion(nil, error);
            PLVVodElogModel *logModel = [[PLVVodElogModel alloc] init];
            logModel.userId2 = [PLVVodSettings findUserIdWithVid:vid];
            logModel.module = PLV_Busi_Download_Module;
            logModel.vid = vid;
            logModel.errorCode = [@(error.code) stringValue];
            PLVVodElogLogfileModel *logfileModel = [[PLVVodElogLogfileModel alloc] init];
            logfileModel.infomation = [error description];
            logModel.logFile = logfileModel;
            [PLVVodReportManager reportElogWithElogModel:logModel completion:nil];
            return;
        }
        
        // 返回模型
        PLVVodVideo *video = [[PLVVodVideo alloc] initWithVid:vid dictionary:videoDic];
        completion(video, error);
        
        BOOL isUpdateDB = YES;
        if ([video.playerError[@"code"] intValue] != 0) {
            isUpdateDB = NO;
        }else {
            isUpdateDB = video.status >= 60;
        }
        // 更新数据库
        if (isUpdateDB){
            dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                PLVVodVideoJson *videoJson = [[PLVVodVideoJson alloc] init];
                videoJson.modifyDate = [NSDate date];
                videoJson.vid = vid;
                videoJson.dataBody = videoData;
                BOOL success = [[PLVVodDownloadInfoManager sharedManager] updateDatabaseWithVideoJson:videoJson];
                if (success){
                    PLVVodLogDebug(@"--- update videojson to DB success ----");
                }
            });
        }
    }];
}

- (instancetype)init {
	if (self = [super init]) {
		_isPlain = YES;
		_isHls = NO;
		_available = YES;
        _isHls302 = NO;
	}
	return self;
}

- (instancetype)initWithVid:(NSString *)vid dictionary:(NSDictionary *)dic {
	if (self = [super init]) {
		_vid = vid;
        int reportFreq = [dic[@"reportFreq"] intValue];
        _reportFreq = reportFreq <= 0 ? 10 : reportFreq;
		_qualityCount = [dic[@"df_num"] intValue];
        _hasPPT = [dic[@"ppt"] boolValue];
		_preferredQuality = [dic[@"my_br"] integerValue];
		_snapshot = dic[@"first_image"];
		_duration = [dic[@"duration"] doubleValue];
		_interactive = [dic[@"interactive_video"] boolValue];
		_title = dic[@"title"];
        
        NSArray *srtArray = dic[@"srt"];
        NSMutableArray *srtMuArray =  [NSMutableArray array];
        for (NSDictionary *dic in srtArray) {
            PLVVodVideoSubtitleItem *item = [[PLVVodVideoSubtitleItem alloc] initWithDictionary:dic];
            [srtMuArray addObject:item];
        }
        _srts = [srtMuArray copy];
        
        NSArray *match_srt = dic[@"match_srt"];
        NSMutableArray *match_srtMuArray = [NSMutableArray array];
        for (NSDictionary *dic in match_srt) {
            PLVVodVideoDoubleSubtitleItem *item = [[PLVVodVideoDoubleSubtitleItem alloc] initWithDictionary:dic];
            [match_srtMuArray addObject:item];
        }
        _match_srt = [match_srtMuArray copy];
        
        _player = [[PLVVodVideoPlayerSetting alloc] initWithDictionary:dic[@"player"]];
		_teaser = dic[@"teaser_url"];
		_teaserDuration = [dic[@"teaser_time"] doubleValue];
		_teaserShow = [dic[@"teaser_show"] boolValue];
		_status = [dic[@"status"] intValue];
		_outflow = [dic[@"outflow"] boolValue];
		_timeoutflow = [dic[@"timeoutflow"] boolValue];
		_hlsIndex = dic[@"hlsIndex"];
        _hlsIndex_backup = dic[@"hlsIndex_backup"];
		_hlsVideos = dic[@"hls"];
        _hlsVideos_backup = dic[@"hls_backup"];
		_plainVideos = dic[@"mp4"];
		_tsPackages = dic[@"packageUrl"];
		_constKey = [dic[@"seed_const"] description];
		_categoryId = [dic[@"cataid"] description];
        _aac_link = [dic[@"aac_link"] description];
        _hlsVersion = [dic[@"hlsDrmVersion"] stringValue];
        _hlsPrivateVersion = [dic[@"hlsPrivate"] integerValue];
        _nativeKeyVersion = [dic[@"nkv"] integerValue];
        _stallingThreshold = [dic[@"stallingThreshold"] floatValue];
        
        if (_hlsPrivateVersion == 1) {
            _nativeKeyVersion = _nativeKeyVersion >= 12 ? _nativeKeyVersion : 12;
        }else if (_hlsPrivateVersion == 2) {
            _nativeKeyVersion = _nativeKeyVersion >= 13 ? _nativeKeyVersion : 13;
        }
        
        BOOL hls302 = [dic[@"hls302"] integerValue] == 1;
        if (hls302) {
            _isHls302 = YES;
            _hlsVideos2 = dic[@"hls2"];
            _hlsIndex2 = dic[@"hlsIndex2"];
        }
        
        NSInteger httpdns = [dic[@"httpDns"] integerValue];
        if (httpdns == 1) {
            _httpDnsMode = PLVVodHttpDnsModeOpen;
        } else if (httpdns == 0) {
            _httpDnsMode = PLVVodHttpDnsModeClose;
        } else {
            _httpDnsMode = PLVVodHttpDnsModeDefault;
        }
        
        _keep_play = [dic[@"keep_play"] boolValue];
        
        if (dic[@"aac_filesize"]){
            _aac_filesize = [dic[@"aac_filesize"] integerValue];
        }
		
		NSString *catatree = dic[@"catatree"];
		_categoryTree = [catatree componentsSeparatedByString:@","];
		
		_keepSource = [dic[@"keepsource"] boolValue];
		_sourcefilesize = [dic[@"source_filesize"] integerValue];
        _play_source_url = [dic[@"play_source_url"] description];
        
		if (_keepSource) {
            NSMutableArray *tempArray = [NSMutableArray array];
            for (int i = 0; i < _qualityCount; i++) {
                [tempArray addObject:@(_sourcefilesize)];
            }
            _filesizes = [NSArray arrayWithArray:tempArray];
            
		} else {
			_filesizes = dic[@"tsfilesize"];
			if (!_filesizes.count) {
				_filesizes = dic[@"filesize"];
			}
		}
		
		NSArray *ads = dic[@"adMatter"];
		_ads = [self adModelsWithAds:ads];
		
		NSDictionary *playerDic = dic[@"player"];
		if (playerDic.count) {
			NSString *playerTeaser = playerDic[@"teaser_url"];
			NSTimeInterval playerTeaserDuration = [playerDic[@"teaser_time"] doubleValue];
			BOOL playerTeaserShow = [playerDic[@"teaser_show"] boolValue];
			if (playerTeaser) _teaser = playerTeaser;
			if (playerTeaserDuration) _teaserDuration = playerTeaserDuration;
			if (playerTeaserShow) _teaserShow = playerTeaserShow;
		}
		
        // mod by libl [源文件/加密视频判断逻辑修改] 19-04-24 start
        // 判断优先级 源文件 > 加密非加密 > fullmp4
        // fullMp4 表示支持mp4 与hls 两种格式，非加密方式
        BOOL fullMp4 = [dic[@"fullmp4"] boolValue];
        BOOL seed = [dic[@"seed"] boolValue];
        
        if (_keepSource){
            // 源文件
            _isPlain = YES;
            
            // 是否为hls
            NSString *plainUrl = _play_source_url;
            _isHls = [@"m3u8" isEqualToString:plainUrl.pathExtension.lowercaseString];
        }
        else if (seed){
            _isPlain = NO;
            _isHls = YES;  // 加密视频一定为hls视频
        }
        else if (fullMp4){
            _isPlain = YES;
            _isHls = YES; // 非加密hls
        }
        else{
            _isPlain = YES;
            _isHls = NO;  // mp4 视频
        }
        
        // add by libl [cdn 线路判断逻辑] 2019-02-13 start
        if (!_keepSource) {
            if (_isHls){
                _availableRouteLines = [dic[@"cdn_types"] componentsSeparatedByString:@","];
                _tsCdns = [dic[@"tsCdns"] componentsSeparatedByString:@","];
            }
            else{
                // 当作mp4 视频处理
                _availableRouteLines = @[@"mpv.videocc.net",@"freeovp.videocc.net"];
            }
            
            // 添加音频文件cdn域名
            if (![PLVVodUtil isNilString:_aac_link]){
                _availableAudioRouteLines = @[@"mpv.videocc.net",@"freeovp.videocc.net"];
            }
        }
        else{
            // 如果是源文件视频，不进行切换
        }
        // add end
		
		NSArray *hls2 = dic[@"hls2"];
		NSString *tempUrl = hls2.firstObject;
		if (!tempUrl) {
			tempUrl = _tsPackages.firstObject;
		}
		_tsHost = [NSURL URLWithString:tempUrl].host;
        
        // add by libl [解析视频打点信息] 2019-01-14 start
        NSArray *videokeyfrmes = [dic objectForKey:@"videokeyframes"];
        if (videokeyfrmes.count){
            [self addVideoKeyFrames:videokeyfrmes];
        }
        // add end
        
        // add by dengjunlun [添加视频错误信息] 2020-12-23 start
        _playerError = dic[@"playerError"];
                
        BOOL videoAvailable = YES;
        if ([_playerError[@"code"] intValue] != 0) {
            videoAvailable = NO;
            PLVVodLogError(@"%@，视频状态不合法，状态：%d", _vid, _status);
            NSError *plvError = PLVVodErrorMake(video_universally_illegal, ^(NSMutableDictionary *userInfo) {
                if ([PLVVodUtil isChineseSystemLanguage]) {
                    userInfo[NSHelpAnchorErrorKey] = _playerError[@"tips"][@"zh_CN"];
                }else {
                    userInfo[NSHelpAnchorErrorKey] = _playerError[@"tips"][@"en"];
                }
                userInfo[NSLocalizedDescriptionKey] = _playerError[@"code"];
            });
            _error = plvError;
        }else {
            videoAvailable = _status >= 60;
            if (!videoAvailable) {
                PLVVodLogError(@"%@，视频状态不合法，状态：%d", _vid, _status);
                NSError *plvError = PLVVodErrorMake(video_status_illegal, ^(NSMutableDictionary *userInfo) {
                            
                });
                _error = plvError;
            }
        }
        // add end
		
		BOOL accountAvailable = !(_outflow || _timeoutflow);
		if (_outflow) {
			PLVVodLogError(@"账户套餐流量不足");
			NSError *plvError = PLVVodErrorMake(account_flow_out, ^(NSMutableDictionary *userInfo) {
				
			});
			_error = plvError;
		}
		if (_timeoutflow) {
			PLVVodLogError(@"账户套餐已过期");
			NSError *plvError = PLVVodErrorMake(account_time_out, ^(NSMutableDictionary *userInfo) {
				
			});
			_error = plvError;
		}
        
        //判断hls加密视频，当前的解密方法是否支持
        BOOL hlsDecryptAvailable = YES;
        
        if (_hlsPrivateVersion > 0) {
            if (![self currentPrivateDecryptIsSupport:_hlsPrivateVersion]) {
                hlsDecryptAvailable = NO;
                NSError *plvError = PLVVodErrorMake(player_decrypt_not_support, ^(NSMutableDictionary *userInfo) {
                    
                });
                _error = plvError;
            }
        }
        
        if (seed) {
            if (![self currentNativeKeyDecryptIsSupport:_nativeKeyVersion]) {
                hlsDecryptAvailable = NO;
                NSError *plvError = PLVVodErrorMake(player_nkv_not_support, ^(NSMutableDictionary *userInfo) {
                    
                });
                _error = plvError;
            }
        }
		_available = _vid.length && videoAvailable && accountAvailable && hlsDecryptAvailable;
        BOOL httpDnsOpen = NO;
        if (!_isHls302) {
            if (_httpDnsMode != PLVVodHttpDnsModeDefault) {
                httpDnsOpen = _httpDnsMode == PLVVodHttpDnsModeOpen;
            }else {
                httpDnsOpen = [PLVVodSettings sharedSettings].enableHttpDNS;
            }
        }
        if (httpDnsOpen) {
            // HttpDns初始化
            [[PLVVodHttpDnsManager sharedManager] configHttpDNSWithSecretKey:dic[@"httpDnsKey"]];
        }
        
	}
	return self;
}


/// 判断当前的私有解密方法是否支持
/// @param videoNeedVersion 视频需要的解密版本
-(BOOL)currentPrivateDecryptIsSupport:(NSInteger)videoNeedVersion
{
    NSInteger currentVersion = [PLVHlsPrivateVersion integerValue];
    if (currentVersion >= videoNeedVersion) {
        return YES;
    }
    return NO;
}

/// 判断当前的key解密方法是否支持
/// @param videoNeedVersion 视频需要的解密版本
-(BOOL)currentNativeKeyDecryptIsSupport:(NSInteger)videoNeedVersion
{
    NSInteger currentVersion = [PLVNativeKeyVersion integerValue];
    if (currentVersion >= videoNeedVersion) {
        return YES;
    }
    return NO;
}

- (NSArray *)adModelsWithAds:(NSArray *)ads {
	NSMutableArray *adModels = [NSMutableArray array];
	NSMutableArray *headAds = [NSMutableArray array];
	NSMutableArray *pauseAds = [NSMutableArray array];
	NSMutableArray *tailAds = [NSMutableArray array];
	for (NSDictionary *adDic in ads) {
		PLVVodAd *ad = [PLVVodAd adWithDic:adDic];
		switch (ad.location) {
			case PLVVodAdLocationHead:{
				[headAds addObject:ad];
			}break;
			case PLVVodAdLocationPause:{
				[pauseAds addObject:ad];
			}break;
			case PLVVodAdLocationTail:{
				[tailAds addObject:ad];
			}break;
			default:{}break;
		}
	}
	
	BOOL hasHeadAd = NO;
	BOOL hasPauseAd = NO;
	BOOL hasTailAd = NO;
	
	NSUInteger currentCategoryIdIndex = [_categoryTree indexOfObject:_categoryId];
	for (NSInteger i = _categoryTree.count - 1; i >= 0; i--) {
		if (i > currentCategoryIdIndex) continue;
		if (hasHeadAd && hasTailAd && hasPauseAd) break;
		NSString *categoryId = _categoryTree[i];
		if (!hasHeadAd) {
			hasHeadAd = [self subAdsMatchCategoryId:categoryId ads:headAds superads:adModels];
		}
		if (!hasPauseAd) {
			hasPauseAd = [self subAdsMatchCategoryId:categoryId ads:pauseAds superads:adModels];
		}
		if (!hasTailAd) {
			hasTailAd = [self subAdsMatchCategoryId:categoryId ads:tailAds superads:adModels];
		}
	}
	return adModels;
}

- (BOOL)subAdsMatchCategoryId:(NSString *)categoryId ads:(NSMutableArray<PLVVodAd *> *)ads superads:(NSMutableArray<PLVVodAd *> *)superads {
	NSMutableArray *subAdModels = [NSMutableArray array];
	for (PLVVodAd *ad in ads) {
		if ([ad.categoryId isEqualToString:categoryId]) {
			[subAdModels addObject:ad];
			//NSLog(@"第%@级: %@ 匹配广告: %@", @([_categoryTree indexOfObject:categoryId]), categoryId, ad);
		}
	}
	if (subAdModels.count > 0) {
		[superads addObjectsFromArray:subAdModels];
		[ads removeObjectsInArray:subAdModels];
		return YES;
	}
	return NO;
}

- (void)addVideoKeyFrames:(NSArray *)dataArray{
    NSMutableArray *keyframesArr = [NSMutableArray arrayWithCapacity:0];
    [dataArray enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NSDictionary *item = (NSDictionary *)obj;
        PLVVodVideoKeyFrameItem *frameModel = [[PLVVodVideoKeyFrameItem alloc] initWithDictionary:item];
        if (frameModel){
            [keyframesArr addObject:frameModel];
        }
    }];
    
    if (keyframesArr.count){
        
        // 按照打点时间排序
        NSArray *tmpArr = [keyframesArr sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
            PLVVodVideoKeyFrameItem *item1 = (PLVVodVideoKeyFrameItem *)obj1;
            PLVVodVideoKeyFrameItem *item2 = (PLVVodVideoKeyFrameItem *)obj2;
            if ([item1.keytime integerValue] > [item2.keytime integerValue])
                return NSOrderedDescending;
            
            return NSOrderedAscending;
        }];
        self.videokeyframes = [NSArray arrayWithArray:tmpArr];
    }
}

- (BOOL)available {
	PLVVodSettings *settings = [PLVVodSettings sharedSettings];
	// 加入多账号限制
	BOOL userAvilable = NO;
	@try {
        
        // todo: _vid 有时取值为空 localVideo对象vid属性覆盖父类vid，导致父类vid为空
        // self.vid 会获取子类的vid属性，有数据
        //        userAvilable = [_vid hasPrefix:settings.userid];
        
        NSString *userid = [PLVVodSettings findUserIdWithVid:self.vid];
        userAvilable = ![PLVVodUtil isNilString:userid];

	} @catch (NSException *exception) {
		userAvilable = NO;
	} @finally {
	}
	
	userAvilable = userAvilable || settings.mutilAccount;
	BOOL available = _available && userAvilable;
	if (!userAvilable) {
		PLVVodLogError(@"%@，视频与账号不匹配", _vid);
		NSError *plvError = PLVVodErrorMake(video_unmatch_account, ^(NSMutableDictionary *userInfo) {
			
		});
		_error = plvError;
	}
	return available;
}

- (BOOL)canSwithPlaybackMode {
    if (self.aac_link.length > 0) {
        return YES;
    } else {
        return NO;
    }
}

/// 获取投屏使用的媒体URL
/// @param quality 播放清晰度
- (NSString *)transformCastMediaURLStringWithQuality:(NSInteger)quality
{
    NSString * urlString;
    if (self.keepSource) {
        urlString = self.play_source_url;
    } else{
        NSArray *urlArr;
        if(self.isPlain == YES && self.isHls == NO){
            urlArr = self.plainVideos;
        }else{
            urlArr = self.hlsVideos;
        }
        NSInteger idx = quality - 1;
        urlString = (urlArr.count > idx && idx >= 0) ? urlArr[idx] : @"";
    }
    
    if (urlString == nil || [urlString isKindOfClass: [NSString class]] == NO || urlString.length == 0) {
        PLVVodLogError(@"PLVCastManager - 播放链接非法 链接：%@", urlString);
        return @"";
    }
    
    PLVVodSettings *settings = [PLVVodSettings sharedSettings];
    NSString *viewerId = settings.viewerId.length ? [PLVVodUtil urlSafeBase64String:settings.viewerId] : @"";
    NSString *viewerName = settings.viewerName.length ? [PLVVodUtil urlSafeBase64String:settings.viewerName] : @"";
    NSString *param3 = settings.viewerInfos.viewerExtraInfo1 ? [PLVVodUtil urlSafeBase64String:settings.viewerInfos.viewerExtraInfo1] : @"";
    NSString *param4 = settings.viewerInfos.viewerExtraInfo2 ? [PLVVodUtil urlSafeBase64String:settings.viewerInfos.viewerExtraInfo2] : @"";
    NSString *param5 = settings.viewerInfos.viewerExtraInfo3 ? [PLVVodUtil urlSafeBase64String:settings.viewerInfos.viewerExtraInfo3] : @"";
    NSString *pv = [PLVVodUtil urlSafeBase64String:PLVVodSdkVersion];
    NSString *pid = [PLVVodUtil pid];
    NSString *v1 = viewerId;
    NSString *d1 = @"1";
    NSString *pn = [PLVVodUtil urlSafeBase64String:@"polyv-ios-sdk"];
    urlString  = [NSString stringWithFormat:@"%@?p1=%@&p2=%@&p3=%@&p4=%@&p5=%@&pv=%@&pid=%@&v1=%@&d1=%@&pn=%@", urlString,viewerId, viewerName, param3, param4, param5, pv, pid, v1, d1, pn];
    return urlString;
}


- (NSString *)description {
	NSMutableString *description = [super.description stringByAppendingString:@":\n"].mutableCopy;
	[description appendFormat:@" vid: %@;\n", _vid];
	[description appendFormat:@" title: %@;\n", _title];
    [description appendFormat:@" ppt: %@;\n", _hasPPT?@"YES":@"NO"];
	[description appendFormat:@" qualityCount: %@;\n", @(_qualityCount)];
	[description appendFormat:@" preferredQuality: %@;\n", NSStringFromPLVVodQuality(_preferredQuality)];
	[description appendFormat:@" duration: %@;\n", @(_duration)];
	[description appendFormat:@" sourcefilesize: %@;\n", @(_sourcefilesize)];
	[description appendFormat:@" filesizes: %@;\n", _filesizes];
	[description appendFormat:@" snapshot: %@;\n", _snapshot];
	[description appendFormat:@" keepSource: %@;\n", _keepSource?@"YES":@"NO"];
	[description appendFormat:@" categoryId: %@;\n", _categoryId];
	[description appendFormat:@" categoryTree: %@;\n", _categoryTree];
	[description appendFormat:@" interactive: %@;\n", _interactive?@"YES":@"NO"];
	[description appendFormat:@" srts: %@;\n", _srts];
    [description appendFormat:@" match_srt: %@;\n", _match_srt];
    [description appendFormat:@" player: %@;\n", _player];
	[description appendFormat:@" ads: %@;\n", _ads];
	[description appendFormat:@" teaser: %@;\n", _teaser];
	[description appendFormat:@" teaserDuration: %@;\n", @(_teaserDuration)];
	[description appendFormat:@" teaserShow: %@;\n", _teaserShow?@"YES":@"NO"];
	[description appendFormat:@" availableRouteLines: %@;\n", _availableRouteLines];
	[description appendFormat:@" error: %@;\n", _error];
    [description appendFormat:@" playerError: %@;\n", _playerError];
	[description appendFormat:@" isPlain: %s;\n", _isPlain?"YES":"NO"];
	[description appendFormat:@" isHls: %s;\n", _isHls?"YES":"NO"];
	
	return description;
}

- (NSArray *)srtTitles {
    NSMutableArray *srtTitleMuArray = [NSMutableArray array];
    for (PLVVodVideoSubtitleItem *item in self.srts) {
        [srtTitleMuArray addObject:item.title];
    }
    
    if (self.player.subtitlesEnabled) {
        if (self.player.subtitlesDoubleEnabled && self.match_srt && [self.match_srt isKindOfClass:NSArray.class] && self.match_srt.count > 0) {
            [srtTitleMuArray insertObject:@"双语" atIndex:0];
            return [srtTitleMuArray copy];
        } else {
            return [srtTitleMuArray copy];
        }
    }
    return @[];
}

- (NSInteger)defaultSrtIndex {
    if (self.player.subtitlesEnabled) {
        if (self.player.subtitlesDoubleEnabled && self.player.subtitlesDoubleDefault && self.match_srt && [self.match_srt isKindOfClass:NSArray.class] && self.match_srt.count > 0) {
            return 0;
        } else {
            if ([self.srtTitles containsObject:@"双语"]) {
                return 1;
            }
            return 0;
        }
    }
    return -1;
}

@end
