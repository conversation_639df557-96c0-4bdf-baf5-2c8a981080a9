//
//  PLVVodPPT.m
//  PLVVodSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/7/24.
//  Copyright © 2019 POLYV. All rights reserved.
//

#import "PLVVodPPT.h"
#import "PLVVodNetworking.h"
#import "PLVVodAttachMgr.h"
#import "PLVVodUtil.h"

@implementation PLVVodPPTPage

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    if (self = [super init]) {
        _title = dict[@"title"];
        _index = [dict[@"index"] integerValue];
        _timing = [dict[@"sec"] doubleValue];
        _key = [NSString stringWithFormat:@"%zd", _timing];
        _imageUrl = dict[@"img"];
        _thumbImageUrl = dict[@"t_img"];
    }
    return self;
}

- (BOOL)isLocalImage {
    return ![self.imageUrl hasPrefix:@"http"];
}

- (UIImage *)localImage {
    NSURL *url = [NSURL fileURLWithPath:self.imageUrl];
    NSData *data = [NSData dataWithContentsOfURL:url];
    UIImage *image = [UIImage imageWithData:data];
    return image;
}

@end

@implementation PLVVodPPT

- (instancetype)initWithVid:(NSString *)vid dictionary:(NSDictionary *)dict {
    if (self = [super init]) {
        _vid = vid;
        
        NSArray *pages = dict[@"ppt"][@"page"];
        NSMutableArray *muArray;
        NSMutableDictionary *muDict;
        if ([pages count] > 0) {
            muArray = [[NSMutableArray alloc] initWithCapacity:[pages count]];
            muDict = [[NSMutableDictionary alloc] init];
        }
        
        for (NSDictionary *pageDict in pages) {
            PLVVodPPTPage *page = [[PLVVodPPTPage alloc] initWithDictionary:pageDict];
            if (muDict[page.key]) {
                continue;
            } else {
                [muDict setObject:page forKey:page.key];
                [muArray addObject:page];
            }
        }
        
        _pagesDict = [muDict copy];
        _pages = [muArray copy];
    }
    return self;
}

#pragma mark - Public

+ (void)requestPPTWithVid:(NSString *)vid completion:(void (^)(PLVVodPPT *ppt, NSError *error))completion {
    [PLVVodNetworking requestPPTJsonWithVid:vid completion:^(NSDictionary *responseDict, NSError *error) {
        if (error) {
            completion(nil, error);
        } else {
            PLVVodPPT *ppt = [[PLVVodPPT alloc] initWithVid:vid dictionary:responseDict];
            completion(ppt, nil);
        }
    }];
}

+ (void)requestCachePPTWithVid:(NSString *)vid completion:(void (^)(PLVVodPPT * _Nullable, NSError * _Nullable))completion{
    if (!vid.length){
        PLVVodLogError(@"[ppt] -- vid 参数不正确");
        if (completion){
            completion (nil, nil);
        }
    }
    NSString *pptJsonPath = [PLVVodAttachMgr getLocalPPTJsonPathWithVid:vid];
    if ([[NSFileManager defaultManager] fileExistsAtPath:pptJsonPath]){
        //
        NSDictionary *pptDic = [NSDictionary dictionaryWithContentsOfFile:pptJsonPath];
        if (pptDic){
            PLVVodPPT *ppt = [[PLVVodPPT alloc] initWithVid:vid dictionary:pptDic];
            
            // 替换图片路径
            NSString *pptDir = pptJsonPath.stringByDeletingLastPathComponent;
            [ppt.pages enumerateObjectsUsingBlock:^(PLVVodPPTPage * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                //
                PLVVodPPTPage *item = obj;
                
                NSArray *arrImg = [item.imageUrl componentsSeparatedByString:@"/"];
                NSInteger arrCount = arrImg.count;
                if (arrCount >=2 ){
                    NSString *relPath = [NSString stringWithFormat:@"%@/%@", arrImg[arrCount-2], arrImg[arrCount-1]];
                    item.imageUrl = [pptDir stringByAppendingPathComponent:relPath];
                    PLVVodLogDebug(@"[ppt] -- image path %@", item.imageUrl);
                }
                
                NSArray *arrThumbImg = [item.thumbImageUrl componentsSeparatedByString:@"/"];
                arrCount = arrThumbImg.count;
                if (arrCount >=2 ){
                    NSString *relPath = [NSString stringWithFormat:@"%@/%@", arrThumbImg[arrCount-2], arrThumbImg[arrCount-1]];
                    item.thumbImageUrl = [pptDir stringByAppendingPathComponent:relPath];
                    PLVVodLogDebug(@"[ppt] -- thumb image path %@", item.thumbImageUrl);
                }
                
            }];
            completion(ppt, nil);
        }
        else{
            if (completion){
                PLVVodLogDebug(@"[ppt] -- ppt json 本地文件读取失败");
                completion (nil, nil);
            }
        }
    }
    else{
        //
        if (completion){
            PLVVodLogDebug(@"[ppt] -- ppt 还未下载成功");
            completion (nil, nil);
        }
    }
}

/// 是否缓存
+ (BOOL)isExistWithVid:(NSString *)vid{
    NSString *pptJsonPath = [PLVVodAttachMgr getLocalPPTJsonPathWithVid:vid];
    if ([[NSFileManager defaultManager] fileExistsAtPath:pptJsonPath]){
        PLVVodLogDebug(@"[ppt json] -- %@", pptJsonPath);
        return YES;
    }
    
    return NO;
}


@end
