//
//  PLVVodQosLoadingTracer.h
//  _PolyvVodSDK
//
//  Created by <PERSON><PERSON> on 2023/6/2.
//  Copyright © 2023 POLYV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, PLVVodQosLoadingTracerModelType) {
    PLVVodQosLoadingTracerModelTypeRequestVideoJson = 1,
    PLVVodQosLoadingTracerModelTypeRequestVideoJsonPriorityCache = 2,
    PLVVodQosLoadingTracerModelTypeRequestExam = 3,
    PLVVodQosLoadingTracerModelTypeRequestToken = 4
};

@interface PLVVodQosLoadingTracerModel : NSObject

@property (nonatomic, assign) PLVVodQosLoadingTracerModelType type;

@property (nonatomic, assign) BOOL rangeUseable;

// 起始时间
@property (nonatomic, assign, readonly) NSTimeInterval startTime;

// 结束时间
@property (nonatomic, assign, readonly) NSTimeInterval endTime;

- (instancetype)initAndAddStartTimeWithType:(PLVVodQosLoadingTracerModelType)type;

- (void)addStartTime;

- (void)addEndTime;

@end

@interface PLVVodQosLoadingTracer : NSObject

+ (instancetype)shareManager;

- (void)addEndTimeAndUpdateModel:(PLVVodQosLoadingTracerModel *)model vid:(NSString *)vid;

// 返回loading业务时间，单位毫秒
- (NSTimeInterval)playerLoadingBusinessTimeWithVid:(NSString *)vid;

@end


NS_ASSUME_NONNULL_END
