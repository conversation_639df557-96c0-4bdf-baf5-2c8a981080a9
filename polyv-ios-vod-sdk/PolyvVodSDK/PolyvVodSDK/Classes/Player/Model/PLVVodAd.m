//
//  PLVVodAd.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/9.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodAd.h"

NSString *NSStringFromPLVVodAdType(PLVVodAdType adType) {
	switch (adType) {
		case PLVVodAdTypeSwf:
			return @"SWF";
		case PLVVodAdTypeImage:
			return @"图片";
		case PLVVodAdTypeVideo:
			return @"视频";
		case PLVVodAdTypeUnknown:
			return @"未知";
	}
}

NSString *NSStringFromPLVVodAdLocation(PLVVodAdLocation adLocation) {
	switch (adLocation) {
		case PLVVodAdLocationHead:
			return @"片头广告";
		case PLVVodAdLocationTail:
			return @"片尾广告";
		case PLVVodAdLocationPause:
			return @"暂停广告";
		case PLVVodAdLocationUnknown:
			return @"未知";
	}
}

@implementation PLVVodAd

+ (instancetype)adWithDic:(NSDictionary *)dic {
	return [[self alloc] initWithDic:dic];
}

- (instancetype)initWithDic:(NSDictionary *)dic {
	if (self = [super init]) {
		_address = dic[@"addrurl"];
		_type = [dic[@"adtype"] integerValue];
		_categoryId = [dic[@"cataid"] description];
		_location = [dic[@"location"] integerValue];
		_adUrl = dic[@"matterurl"];
		_duration = [dic[@"timesize"] doubleValue];
        _skipEnabled = [dic[@"skipenabled"] boolValue];
        _skipTime = [dic[@"skiptime"] integerValue];
        _skipbutton = dic[@"skipbutton"];
        _displaytext = dic[@"displaytext"];
	}
	return self;
}

- (NSString *)description {
	NSMutableString *description = [super.description stringByAppendingString:@":\n"].mutableCopy;
	[description appendFormat:@" type: %@;\n", NSStringFromPLVVodAdType(_type)];
	[description appendFormat:@" location: %@;\n", NSStringFromPLVVodAdLocation(_location)];
	[description appendFormat:@" categoryId: %@;\n", _categoryId];
	[description appendFormat:@" duration: %@;\n", @(_duration)];
	[description appendFormat:@" adUrl: %@;\n", _adUrl];
	[description appendFormat:@" address: %@;\n", _address];
    [description appendFormat:@" skipbutton: %@;\n", _skipbutton];
    [description appendFormat:@" displaytext: %@;\n", _displaytext];
	
	return description;
}

@end
