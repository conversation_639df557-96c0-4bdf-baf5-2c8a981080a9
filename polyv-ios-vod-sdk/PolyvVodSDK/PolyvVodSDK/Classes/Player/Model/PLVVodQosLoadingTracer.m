//
//  PLVVodQosLoadingTracer.m
//  _PolyvVodSDK
//
//  Created by <PERSON><PERSON> on 2023/6/2.
//  Copyright © 2023 POLYV. All rights reserved.
//

#import "PLVVodQosLoadingTracer.h"
#import "PLVVodUtil.h"

@interface PLVVodQosLoadingTracerModel ()

@property (nonatomic, assign) NSTimeInterval startTime;

@property (nonatomic, assign) NSTimeInterval endTime;

@end

@implementation PLVVodQosLoadingTracerModel

- (instancetype)initAndAddStartTimeWithType:(PLVVodQosLoadingTracerModelType)type {
    if (self = [super init]) {
        self.type = type;
        [self addStartTime];
        self.endTime = 0;
    }
    return self;
}

- (void)addStartTime {
    self.startTime = [PLVVodUtil currentSeconds] * 1000;
}

- (void)addEndTime {
    self.endTime = [PLVVodUtil currentSeconds] * 1000;
}

- (BOOL)rangeUseable {
    return self.startTime > 0 && self.endTime > 0 && (self.endTime - self.startTime) > 0;
}

@end

@interface PLVVodQosLoadingTracer ()

@property (nonatomic, strong) NSMutableDictionary *tracerDict;


@end

@implementation PLVVodQosLoadingTracer

+ (instancetype)shareManager {
    static dispatch_once_t onceToken;
    static PLVVodQosLoadingTracer *mananger = nil;
    dispatch_once(&onceToken, ^{
        mananger = [[self alloc] init];
    });
    return mananger;
}

- (void)addEndTimeAndUpdateModel:(PLVVodQosLoadingTracerModel *)model vid:(NSString *)vid {
    if (!model || ![model isKindOfClass:[PLVVodQosLoadingTracerModel class]]) {
        return;
    }
    
    if (!vid || ![vid isKindOfClass: [NSString class]] || vid.length <= 0) {
        return;
    }
    
    [model addEndTime];
    if (!model.rangeUseable) {
        return;
    }
    
    NSMutableArray *models = [self currentTracerModelsWithVid:vid];
    
    BOOL isExist = NO;
    if (models.count > 0) {
        for (PLVVodQosLoadingTracerModel *tracerModel in models) {
            if (tracerModel.type == model.type) {
                [models replaceObjectAtIndex:[models indexOfObject:tracerModel] withObject:model];
                isExist = YES;
                break;
            }
        }
    }
    if (!isExist) {
        [models addObject:model];
    }
    [self.tracerDict setObject:models forKey:vid];
}

- (NSTimeInterval)playerLoadingBusinessTimeWithVid:(NSString *)vid {
    if (!vid || ![vid isKindOfClass: [NSString class]] || vid.length <= 0) {
        return 0;
    }
    NSMutableArray *models = [self currentTracerModelsWithVid:vid];
    if (models.count <= 0) {
        return 0;
    }
    NSMutableArray *ranges = [NSMutableArray array];
    for (PLVVodQosLoadingTracerModel *model in models) {
        if (model.rangeUseable) {
            [ranges addObject:@[@(model.startTime),@(model.endTime)]];
        }
    }
    if (ranges.count > 0) {
        [ranges sortUsingComparator:^NSComparisonResult(NSArray *rangeArr1, NSArray *rangeArr2) {
            NSTimeInterval start1 = [rangeArr1[0] doubleValue];
            NSTimeInterval start2 = [rangeArr2[0] doubleValue];
            if (start1 == start2) {
                return NSOrderedSame;
            } else if (start1 > start2) {
                return NSOrderedDescending;
            } else {
                return NSOrderedAscending;
            }
        }];
        
        NSMutableArray *mergedRanges = [NSMutableArray array];
        for (NSArray *rangeArr in ranges) {
            NSTimeInterval start = [rangeArr[0] doubleValue];
            NSTimeInterval end = [rangeArr[1] doubleValue];
            if (mergedRanges.count == 0 || [mergedRanges.lastObject[1] doubleValue] < start) {
                [mergedRanges addObject:@[@(start), @(end)]];
            } else {
                NSArray *lastRangeArr = mergedRanges.lastObject;
                NSTimeInterval lastEnd = [lastRangeArr[1] doubleValue];
                NSTimeInterval newEnd = MAX(lastEnd, end);
                [mergedRanges replaceObjectAtIndex:(mergedRanges.count - 1) withObject:@[@(start), @(newEnd)]];
            }
        }
        NSTimeInterval totalDuration = 0;
        for (NSArray *rangeArr in mergedRanges) {
            NSTimeInterval start = [rangeArr[0] doubleValue];
            NSTimeInterval end = [rangeArr[1] doubleValue];
            NSTimeInterval duration = end - start;
            totalDuration += duration;
        }
        
        return totalDuration;
    }
    return 0;
}

- (NSMutableArray *)currentTracerModelsWithVid:(NSString *)vid {
    if (!vid || ![vid isKindOfClass: [NSString class]] || vid.length <= 0) {
        return nil;
    }
    NSArray *currentTracerModelsArray = [self.tracerDict objectForKey:vid];
    NSMutableArray *currentTracerModelsMutableArray;
    if (currentTracerModelsArray && [currentTracerModelsArray isKindOfClass:[NSArray class]]) {
        currentTracerModelsMutableArray = [NSMutableArray arrayWithArray:currentTracerModelsArray];
    } else {
        currentTracerModelsMutableArray = [NSMutableArray array];
    }
    return currentTracerModelsMutableArray;
}

#pragma mark - property
- (NSMutableDictionary *)tracerDict {
    if (!_tracerDict) {
        _tracerDict = [NSMutableDictionary dictionary];
    }
    return _tracerDict;
}

@end
