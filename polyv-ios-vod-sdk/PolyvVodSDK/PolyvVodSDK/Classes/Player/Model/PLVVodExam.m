//
//  PLVVodExam.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/10.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodExam.h"
#import "PLVVodNetworking.h"
#import "PLVVodUtil.h"
#import "PLVVodDefines.h"
#import "PLVVodQosLoadingTracer.h"

@implementation PLVVodExam

+ (void)requestVideoWithVid:(NSString *)vid completion:(void (^)(NSArray<PLVVodExam *> *, NSError *))completion {
    PLVVodQosLoadingTracerModel *model = [[PLVVodQosLoadingTracerModel alloc]initAndAddStartTimeWithType:PLVVodQosLoadingTracerModelTypeRequestExam];
	[PLVVodNetworking requestExamDicWithVid:vid completion:^(NSArray *exams, NSError *error) {
        [[PLVVodQosLoadingTracer shareManager] addEndTimeAndUpdateModel:model vid:vid];
		if (error) {
			if (completion) completion(nil, error);
			return;
		}
		NSMutableArray *examModels = [NSMutableArray array];
		for (NSDictionary *examDic in exams) {
			PLVVodExam *exam = [[PLVVodExam alloc] initWithDic:examDic];
			[examModels addObject:exam];
		}
		if (completion) completion(examModels, nil);
	}];
}

+ (PLVVodExam *)createExamWithDic:(NSDictionary *)dic{
    PLVVodExam *exam = [[PLVVodExam alloc] initWithDic:dic];
    return exam;
}

+ (NSArray<PLVVodExam *> *)createExamArrayWithDicArray:(NSArray<NSDictionary *> *)dicArray{
    NSMutableArray *examModels = [NSMutableArray array];
    for (NSDictionary *examDic in dicArray) {
        PLVVodExam *exam = [[PLVVodExam alloc] initWithDic:examDic];
        [examModels addObject:exam];
    }
    return examModels;
}

- (instancetype)initWithDic:(NSDictionary *)dic {
	if (self = [super init]) {
		_examId = dic[@"examId"];
		_userId = dic[@"userid"];
		_vid = dic[@"videoPoolId"];
		_showTime = [PLVVodUtil secondsWithtimeString:dic[@"showTime"]];
		
		_correctExplanation = dic[@"answer"];
		_wrongExplanation = dic[@"wrongAnswer"];
		_skippable = [dic[@"skip"] boolValue];
		_backTime = [dic[@"wrongTime"] doubleValue];
		_wrongShow = [dic[@"wrongShow"] doubleValue];
		_createdTime = [NSDate dateWithTimeIntervalSince1970:[dic[@"createdTime"] integerValue] / 1000];
        
        _examType = [dic[@"type"] integerValue];
		
		NSError *error = nil;
		NSArray *choiceDics = [NSJSONSerialization JSONObjectWithData:[dic[@"choices"] dataUsingEncoding:NSUTF8StringEncoding] options:NSJSONReadingMutableContainers error:&error];
		if (choiceDics.count && !error) {
			NSMutableArray *choices = [NSMutableArray array];
			NSMutableArray *correctChoices = [NSMutableArray array];
			for (int i = 0; i < choiceDics.count; i++) {
				NSDictionary *choiceDic = choiceDics[i];
				NSString *answer = choiceDic[@"answer"];
				if (answer.length) [choices addObject:answer];
				if ([choiceDic[@"right_answer"] boolValue]) [correctChoices addObject:@(i)];
			}
			_options = choices;
			_correctIndex = correctChoices;
		}
        
        NSString *questionString = dic[@"question"];
        NSString *imageUrl;
        NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"\\[\\[[^\\[\\]]*\\]\\]" options:NSRegularExpressionCaseInsensitive error:&error];
        while (YES) {
            NSTextCheckingResult *result = [regex firstMatchInString:questionString options:0 range:NSMakeRange(0, [questionString length])];
            if (result) {
                if (!imageUrl) {
                    NSRange range = NSMakeRange(result.range.location+2, result.range.length-4);
                    imageUrl = [questionString substringWithRange:range];
                }
                questionString = [questionString stringByReplacingCharactersInRange:result.range withString:@""];
            } else {
                break;
            }
        }
        _question = questionString;
        
        BOOL hasIllustration = NO;
        if ([dic[@"illustration"] isKindOfClass:[NSString class]]) {
            NSString *illustrationString = dic[@"illustration"];
            if (illustrationString.length > 0) {
                hasIllustration = YES;
            }
        }
        if (hasIllustration) {  // illustrationString字段是没有 http: 前缀的，需要补上
            _illustration = [NSString stringWithFormat:@"https:%@", dic[@"illustration"]];
        } else if (imageUrl.length > 0) { // illustration字段没有值，就使用question中的第一个图片链接
            _illustration = imageUrl;
        } else {
            _illustration = nil;
        }
	}
	return self;
}

- (NSString *)explanation {
	return self.correct ? _correctExplanation : _wrongExplanation;
}

+ (NSArray<PLVVodExam *> *)localExamsWithVid:(NSString *)vid downloadDir:(NSString *)downloadDir{
    // 参数检查
    if ([PLVVodUtil isNilString:vid]){
        PLVVodLogWarn(@"[download] -- vid 参数为nil");
        return nil;
    }
    
    if ([PLVVodUtil isNilString:downloadDir]){
        PLVVodLogWarn(@"[download] -- 下载路径参数为nil");
        return nil;
    }
    
    if (![[NSFileManager defaultManager] fileExistsAtPath:downloadDir]){
        PLVVodLogInfo(@"[download] -- 下载路径不存在");
        return nil;
    }
    
    // 拼接问答文件路径,并创建模型
    NSString *examsDir = [downloadDir stringByAppendingPathComponent:PLVExamsDir];
    NSString *filePath = [[examsDir stringByAppendingPathComponent:vid] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.json", vid]];
    if ([[NSFileManager defaultManager] fileExistsAtPath:filePath]){
        //
        NSData *dataDic = [NSData dataWithContentsOfFile:filePath];
        NSArray *arrDicts = [NSJSONSerialization JSONObjectWithData:dataDic options:NSJSONReadingMutableContainers error:nil];
        if ([arrDicts isKindOfClass:[NSArray class]]){
            NSArray *examsArr =  [self createExamArrayWithDicArray:arrDicts];
            return examsArr;
        }
    }
    
    return nil;
}

/// 答题统计上报
+ (void)saveExamStatisticsWithPid:(NSString *)pid
                              eid:(NSString *)examId
                              uid:(NSString *)userId
                        quesition:(NSString *)question
                              vid:(NSString *)vid
                          correct:(BOOL)correct
                           anwser:(NSString *)userAnswer
                       completion:(void (^)(NSError *))completion{
    //
    [PLVVodNetworking saveExamStatisticsWithPid:pid eid:examId uid:userId quesition:question vid:vid correct:correct anwser:userAnswer completion:^(NSError *error) {
        completion(error);
    }];
}

@end
