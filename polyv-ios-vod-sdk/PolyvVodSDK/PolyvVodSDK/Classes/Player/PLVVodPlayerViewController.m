//
//  PLVVodPlayerViewController.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/9.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodPlayerViewController+internal.h"
#import "PLVVodPlayerViewController.h"
#import "PLVVodPlayerViewController+URL.h"
#import "PLVVodPlayerViewController+Log.h"
#import "PLVVodPlayerViewController+Reconnect.h"
#import "PLVVodPlayerViewController+Ad.h"
#import "PLVVodPlayerViewController+Switch.h"
#import "PLVVodPlayerViewController+Position.h"
#import "PLVVodPlayerViewController+Capture.h"
#import "PLVVodPlayerViewController+Skin.h"
#import "PLVVodPlayerViewController+IJK.h"
#import "PLVVodPlayerViewController+Token.h"
#import "PLVVodPlayerViewController+PIP.h"

#define SCREEN_WIDTH [UIScreen mainScreen].bounds.size.width
#define SCREEN_HEIGHT [UIScreen mainScreen].bounds.size.height
#define isIpad(newCollection) (newCollection.verticalSizeClass == UIUserInterfaceSizeClassRegular && newCollection.horizontalSizeClass == UIUserInterfaceSizeClassRegular)


@implementation PLVVodPlayerViewController

- (void)dealloc {
	[self.adPlayer hideAd];
	[self.innerPlaybackTimer cancel];
	self.innerPlaybackTimer = nil;
	[self.subPlayer shutdown];
	[self.mainPlayer shutdown];
    self.mainPlayer = nil;
	[[NSNotificationCenter defaultCenter] removeObserver:self];
    
    // del by libl [临时屏蔽，银河证券反馈，快速切换视频会出现声音场景] 2019-01-25 start
    // 重置AVAudioSession的Active状态为NO，避免播放器已销毁，但状态仍为YES而中断第三方App
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        NSError * error = nil;
//        if (NO == [[AVAudioSession sharedInstance] setActive:NO error:&error]) {
//            PLVVodLogDebug(@"setActive(NO) failed: %@", error ? [error localizedDescription] : @"nil");
//        }
//    });
    // del end
	PLVVodLogDebug(@"%s", __FUNCTION__);
}

- (void)viewDidDisappear:(BOOL)animated {
	[super viewDidDisappear:animated];
    PLVVodLogDebug(@"%s", __FUNCTION__);

    if (_mainPlayer) {
        self.lastPosition = self.currentPlaybackTime;
        if ([self.mainPlayer isKindOfClass:[PLVIJKFFMoviePlayerController class]]) {
            PLVIJKFFMoviePlayerController *ijkFFPlayer = self.mainPlayer;
            ijkFFPlayer.tcpOpenDelegate = nil;
            ijkFFPlayer.httpOpenDelegate = nil;
            ijkFFPlayer.liveOpenDelegate = nil;
            ijkFFPlayer.segmentOpenDelegate = nil;
            ijkFFPlayer.nativeInvokeDelegate = nil;
        }
    }
}

- (void)destroyPlayer{
    //
    self.lastPosition = self.currentPlaybackTime;
    
    //
    if ([self.mainPlayer isKindOfClass:[PLVIJKFFMoviePlayerController class]]) {
        PLVIJKFFMoviePlayerController *ijkFFPlayer = self.mainPlayer;
        ijkFFPlayer.tcpOpenDelegate = nil;
        ijkFFPlayer.httpOpenDelegate = nil;
        ijkFFPlayer.liveOpenDelegate = nil;
        ijkFFPlayer.segmentOpenDelegate = nil;
        ijkFFPlayer.nativeInvokeDelegate = nil;
    }
    
    //
    [self.adPlayer hideAd];
    [self.innerPlaybackTimer cancel];
    self.innerPlaybackTimer = nil;
    [self.subPlayer shutdown];
    [self.subPlayer.view removeFromSuperview];
    self.subPlayer = nil;
    [self.mainPlayer shutdown];
    [self.mainPlayer.view removeFromSuperview];
    self.mainPlayer = nil;
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    PLVVodLogDebug(@"%s", __FUNCTION__);
}

- (void)stopPlayer{
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([self.mainPlayer isKindOfClass:[PLVIJKFFMoviePlayerController class]]) {
            PLVIJKFFMoviePlayerController *ijkFFPlayer = self.mainPlayer;
            ijkFFPlayer.tcpOpenDelegate = nil;
            ijkFFPlayer.httpOpenDelegate = nil;
            ijkFFPlayer.liveOpenDelegate = nil;
            ijkFFPlayer.segmentOpenDelegate = nil;
            ijkFFPlayer.nativeInvokeDelegate = nil;
        }
        
        [self.adPlayer hideAd];
        // del by libl [播放重试后，与定时器相关功能会失效] 2019-06-05 start
    //    [self.innerPlaybackTimer cancel];
    //    self.innerPlaybackTimer = nil;
        // del end
        
        // 需要保存播放进度
        if (self.mainPlayer.currentPlaybackTime > 0){
            [self setLastPosition:self.mainPlayer.currentPlaybackTime];
        }
        
        // 需要在主线程中停止播放器，否则会导致崩溃
        [self.subPlayer shutdown];
        [self.mainPlayer.view removeFromSuperview];
        [self.mainPlayer shutdown];
    });
    
    _playbackState = PLVVodPlaybackStateStopped;
}

- (instancetype)init {
	if (self = [super init]) {
		[self commonInit];
	}
	return self;
}
- (instancetype)initWithCoder:(NSCoder *)decoder {
	if (self = [super initWithCoder:decoder]) {
		[self commonInit];
	}
	return self;
}

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
	if (self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil]) {
		[self commonInit];
	}
	return self;
}

- (void)commonInit {
	_autoplay = YES;
	_scalingMode = PLVVodMovieScalingModeAspectFit;
	_localPrior = YES;
    _rememberPlaybackRate = YES;
    _lastPlaybackRate = 1.0;
    _enableLocalTeaser = YES;
    _enableTeaserBackgroundPlayback = YES;
    
    _pauseAdCenterXRatio = 50;
    _pauseAdCenterYRatio = 50;

    _mediaPlayerStartPlayTraceArray = [NSMutableArray array];
    
    _fpsProbeSize = -1;
    
    _enableDNSOptimize = NO;
    
    [self initParams];
    
    [self initObserver];
}

// 初始化/重置变量
- (void)initParams{
    _isFirstViewlog = YES;
    _isResetViewlog = YES;
    _loadingLogSent = NO;
    
    _playerTotalFlow = 0;
    _seekModel = [[PLVVodElogSeekModel alloc] init];
    
    _hasTryQuality = [[NSMutableDictionary alloc] init];
    _hasTryCdns = [[NSMutableDictionary alloc] init];
    
    _elogPlayerParamsArr = [[NSMutableArray alloc] init];
    
    _isVideoToolBox = YES;
    
    _playerLoadTime = -1;
    _qosLoadingDuration = 0;
}

- (void)initObserver{
    // 添加通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleAppWillResignActive)
                                                 name:UIApplicationWillResignActiveNotification
                                               object:nil];
}

- (UIView *)videoView{
    return self.mainPlayer.view;
}

- (void)addPlayerLogo:(PLVVodPlayerLogo *)logo {
    self.logoView = logo;
    if (self.customMaskView) {
        [self.logoView addAtView:self.customMaskView];
    }
}

#pragma mark - view controller

- (void)viewDidLoad {
    [super viewDidLoad];
    //NSLog(@"xx_%s - %@", __FUNCTION__, [NSThread currentThread]);
    
    // 只能存在一个画中画窗口，所以当已经有画中画窗口存在的时候，进入另一个播放器将会关闭之前的画中画窗口
    if ([PLVPictureInPictureManager sharedInstance].pictureInPictureActive) {
        [PLVPictureInPictureManager sharedInstance].restoreDelegate = nil;
        [[PLVPictureInPictureManager sharedInstance] stopPictureInPicture];
    }
    
    // 配置 customMaskView
    self.customMaskView = [[UIView alloc] initWithFrame:self.view.bounds];
    self.customMaskView.autoresizingMask = UIViewAutoresizingFlexibleWidth|UIViewAutoresizingFlexibleHeight;
    if ([self.view.subviews containsObject:self.mainPlayer.view]) {
        [self.view insertSubview:self.customMaskView aboveSubview:self.mainPlayer.view];
    } else {
        [self.view insertSubview:self.customMaskView atIndex:0];
    }
    
    if (self.logoView && self.logoView.superview == nil) {
        [self.logoView addAtView:self.customMaskView];
    }
    
    // viewDidLoad handler
    if (self.viewDidLoadHandler) {
        self.viewDidLoadHandler();
        self.viewDidLoadHandler = nil;
    }
    
    // setup UI
    self.view.backgroundColor = [UIColor blackColor];
    
    [self addSkinGestures];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self addOrientationObserver];
}
- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self removeOrientationObserver];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

#pragma mark -- App 失去焦点时逻辑处理
- (void)handleAppWillResignActive{
    // 失去焦点时要处理的事情
    
    // 1.保存当前播放时间记录
    if (self.currentPlaybackTime > 0){
        [self setLastPosition:self.currentPlaybackTime];
    }
}

- (void)setPlayerFullScreen:(BOOL)full {
    [self setFullScreenState:full];
}

- (void)changePlayerFullScreen:(BOOL)full {
    [self setFullScreenState:full];
}

- (void)setFullScreenState:(BOOL)full{
    self.fullscreen = full;
    
    CGSize videoSize = [self getVideoSize];
    if (self.fullscreen == NO) {
        [PLVVodPlayerViewController rotateOrientation:UIInterfaceOrientationPortrait];
    } else {
       if (self.fullScreenOrientation == PLVVodFullScreenOrientationLandscape ||
            (self.fullScreenOrientation == PLVVodFullScreenOrientationAuto && videoSize.width >= videoSize.height)) {
            // 横向全屏
            [PLVVodPlayerViewController rotateOrientation:UIInterfaceOrientationLandscapeRight];
        }
    }

    [self updateConstraintsForRotate];
    
    if (self.didFullScreenSwitch) {
        self.didFullScreenSwitch(self.fullscreen);
    }
}

/// 旋转设备到指定方向
+ (void)rotateOrientation:(UIInterfaceOrientation)orientation {
    if ([[UIDevice currentDevice] respondsToSelector:@selector(setOrientation:)]) {
        SEL selector = NSSelectorFromString(@"setOrientation:");
        NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:[UIDevice instanceMethodSignatureForSelector:selector]];
        [invocation setSelector:selector];
        [invocation setTarget:[UIDevice currentDevice]];
        int val = (int)orientation;
        [invocation setArgument:&val atIndex:2];
        [invocation invoke];
    }
}

- (void)addPlayerOnPlaceholderView:(UIView *)placeholderView rootViewController:(UIViewController *)rootViewController {
    self.view.translatesAutoresizingMaskIntoConstraints = NO;
    self.rootViewController = rootViewController;
    [rootViewController addChildViewController:self];
    self.placeholderView = placeholderView;
    [rootViewController.view addSubview:self.view];
    [self updateConstraintsForRotate];
}

- (void)updateConstraintsForTraitCollection:(UITraitCollection *)collection {
    BOOL fullscreen = collection.verticalSizeClass == UIUserInterfaceSizeClassCompact;
    
    // add by libl [iPad 旋转无效bug 修复] 2018-09-07 start
    if (isIpad(collection)){
        UIInterfaceOrientation orientation = [UIApplication sharedApplication].statusBarOrientation;
        if (UIInterfaceOrientationIsLandscape(orientation)){
            fullscreen = YES;
        }
    }
    // add end
    
    [self.adPlayer viewDidLayoutSubviews];
    
    if (self.placeholderView) {
        if (fullscreen) {
            [self makePlayerConstraintsWithRootView];
        } else {
            [self makePlayerConstraintsWithPlaceholder];
        }
    }
    
    if (_fullscreen != fullscreen) {
        self.fullscreen = fullscreen;
    }
}

#pragma mark - navigation bar & status bar

- (void)addOrientationObserver {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(interfaceOrientationDidChange:)
                                                 name:UIApplicationDidChangeStatusBarOrientationNotification object:nil];
    
    // del by libl [无需在此处调用该方法，若没有在viewdidload 初始化播放器，约束会导致崩溃] 2018-10-10
//    [self interfaceOrientationDidChange:nil];
    // del end
}

- (void)removeOrientationObserver {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationDidChangeStatusBarOrientationNotification object:nil];
}

- (void)interfaceOrientationDidChange:(NSNotification *)notification {
    UIInterfaceOrientation interfaceOrientation = [UIApplication sharedApplication].statusBarOrientation;
    switch (interfaceOrientation) {
        case UIInterfaceOrientationPortrait:{
            
        }break;
        case UIInterfaceOrientationLandscapeLeft:
        case UIInterfaceOrientationLandscapeRight:{
            
        }break;
        case UIInterfaceOrientationPortraitUpsideDown:{
            
        }break;
        default:{}break;
    }
    
    CGSize videoSize = [self getVideoSize];
    if (interfaceOrientation == UIInterfaceOrientationPortrait &&
        (self.fullScreenOrientation == PLVVodFullScreenOrientationLandscape ||
        (self.fullScreenOrientation == PLVVodFullScreenOrientationAuto && videoSize.width >= videoSize.height))) {
        [self changePlayerFullScreen:NO];
    } else {
        [self updateConstraintsForRotate];
    }
}

- (BOOL)prefersStatusBarHidden {
    return self.playerControl.shouldHideStatusBar;
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    return self.playerControl.statusBarStyle;
}


- (BOOL)checkVideoWillPlayLocal:(PLVVodVideo *)video{
    // 若非本地视频优先播放，则直接返回NO
    if (!self.localPrior) { return NO; }
    
    BOOL willPlayLocal = NO;
    // 是否支持音视频切换
    if ([video canSwithPlaybackMode]){
        if (PLVVodPlaybackModeAudio == self.playbackMode){
            // 音频播放模式
            PLVVodLocalVideo * localAudio = [PLVVodLocalVideo localAudioWithVideo:video dir:[PLVVodDownloadManager sharedManager].downloadDir];
            if (localAudio){ willPlayLocal = YES; }
        }
        else {
            // 视频播放模式
            PLVVodLocalVideo * localVideo = [PLVVodLocalVideo localVideoWithVideo:video dir:[PLVVodDownloadManager sharedManager].downloadDir];
            if (![localVideo.path isKindOfClass:[NSNull class]] && [localVideo.path length] ){
                willPlayLocal = YES;
            }
        }
    }
    else{
        // 不支持音视频切换,只有视频播放模式
        PLVVodLocalVideo * localVideo = [PLVVodLocalVideo localVideoWithVideo:video dir:[PLVVodDownloadManager sharedManager].downloadDir];
        if (![localVideo.path isKindOfClass:[NSNull class]] && [localVideo.path length] ){
            willPlayLocal = YES;
        }
    }
    
    return willPlayLocal;
}


#pragma mark - property

- (PLVKVOController *)KVOController {
	if (!_KVOController) {
		_KVOController = [PLVKVOController controllerWithObserver:self];
	}
	return _KVOController;
}
- (PLVKVOController *)KVOControllerNonRetaining {
	if (!_KVOControllerNonRetaining) {
		_KVOControllerNonRetaining = [[PLVKVOController alloc] initWithObserver:self retainObserved:NO];
	}
	return _KVOControllerNonRetaining;
}

- (void)setPlayerControl:(id<PLVVodPlayerSkinProtocol>)playerControl {
	_playerControl = playerControl;
//    [self attachPlayerControl];
	
	// 初始化控件
	playerControl.playbackSlider.value = 0;
	playerControl.bufferProgressView.progress = 0;
	playerControl.timeLabel.text = @"";
    
    // add by libl [监听导航栏控制属性，解决播放加载过程中，前进入其他页面导致的导航栏错乱问题] 2019-06-25 start
    __weak typeof (self) weakSelf = self;
    [self attachControlWithKeyPath:@"shouldHideNavigationBar" handler:^{
        if (weakSelf.navigationController) {
            [weakSelf.navigationController setNavigationBarHidden:weakSelf.playerControl.shouldHideNavigationBar animated:NO];
            [weakSelf.customMaskView setNeedsUpdateConstraints];
        }
    }];
    // add
}

/// 时间进度
- (void)setCurrentPlaybackTime:(NSTimeInterval)currentPlaybackTime {
	//NSLog(@"set current playback time: %f/%f", currentPlaybackTime, self.duration);
	if ((NSInteger)currentPlaybackTime >= (NSInteger)self.duration) {
		currentPlaybackTime = (NSInteger)currentPlaybackTime - 1;
		PLVVodLogWarn(@"跳转超出视频时长");
	}
    
    if (currentPlaybackTime >= self.mainPlayer.duration) {
        currentPlaybackTime = (NSInteger)self.mainPlayer.duration;
    }
    
	if (self.loadingHandler) self.loadingHandler(YES);
	self.reachEnd = NO;
    self.isSeeking = YES;
    
    // 播放器未曾播放过时，seek将失败并不走回调，此处需延时自动回收，并将seek事件存储起来
    // 原因注：若是prepareToPlay的回调中seek，就会成功。但这次seek成功后，再次seek就会失败。除非playbackState有变为playing
    if (!self.hasPlayed) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (self.loadingHandler) self.loadingHandler(NO);
        });
        
        __weak typeof(self) weakSelf = self;
        self.beforePlaySeekEvent = ^{
            // NSLog(@"--------- call beforePlaySeekEvent block: %f", currentPlaybackTime);
            weakSelf.currentPlaybackTime = currentPlaybackTime;
            // 使用 seek-at-start 控制播放起始位置
            // 保留兼容非 FFmpeg 播放器
        };
    }else{
        [self.mainPlayer setCurrentPlaybackTime:currentPlaybackTime];
    }
    
    // 发送seek 日志
    if (self.preparedToPlay){
        [self sendSeekEvent];
    }
}

- (NSTimeInterval)currentPlaybackTime {
	return self.mainPlayer.currentPlaybackTime;
}

/// 可播放时长
- (NSTimeInterval)playableDuration {
	return self.mainPlayer.playableDuration;
}

#pragma mark -- 视频播放入口，线路切换
#pragma mark -- <PLVVodPlayerViewController+Switch> --

- (void)setRouteLine:(NSString *)routeLine {
    [self switchRouteLine:routeLine];
}

- (void)setRouteLineInternal:(NSString *)routeLine{
    _routeLine = routeLine;
    // 上报线路切换事件
    [self sendHttpDnsChangeEvent];
}

/// 播放速率
- (void)setPlaybackRate:(double)playbackRate {
	self.mainPlayer.playbackRate = playbackRate;
    self.playerControl.playbackRate = playbackRate;
    
    // 若打开记忆播放速率功能，则保存最后一次播放速率
    if (_rememberPlaybackRate) _lastPlaybackRate = playbackRate;
}

- (double)playbackRate {
    float playrate = 1.0;
    // iOS 15 以下系统，播放器最高支持2.0 倍速播放
    if (@available(iOS 15.0,*)){
        playrate = self.mainPlayer.playbackRate;
    }
    else{
        playrate = (self.mainPlayer.playbackRate <= 2.0 ? self.mainPlayer.playbackRate: 2.0);
    }
    return playrate;
}

/// 音量
- (void)setPlaybackVolume:(double)playbackVolume {
	if (playbackVolume > 1) {
		playbackVolume = 1;
	}
	if (playbackVolume < 0) {
		playbackVolume = 0;
	}
    if (self.adjustSystemVolume) {
        [self adjustSystemVolumeWithValue:playbackVolume];
    }else {
        self.mainPlayer.playbackVolume = playbackVolume;
    }
	self.playerControl.volumeSlider.value = playbackVolume;
}

- (double)playbackVolume {
    if (self.adjustSystemVolume) {
        return [self currentSystemVolume];
    }
	return self.mainPlayer.playbackVolume;
}

/// 调节系统音量
/// @param volume 音量
- (void)adjustSystemVolumeWithValue:(CGFloat)volume {
    if (volume > 1) {
        volume = 1;
    }
    
    if (volume < 0) {
        volume = 0;
    }
    
    if (self.systemVolumeView == nil) {
        self.systemVolumeView = [[MPVolumeView alloc] init];
        self.systemVolumeView.showsVolumeSlider = YES;
    }
    
    for (UIView *v in self.systemVolumeView.subviews) {
        if ([v.class.description isEqualToString:@"MPVolumeSlider"]) {
            UISlider *volumeSlider = (UISlider *)v;
            [volumeSlider setValue:volume];
            [volumeSlider sendActionsForControlEvents:UIControlEventTouchUpInside];
            break;
        }
    }
}

/// 获取当前系统音量
- (float)currentSystemVolume {
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    return audioSession.outputVolume;
}

///  视频加载速率
- (NSString *)tcpSpeed{
    if (self.mainPlayer){
        if ([self.mainPlayer isKindOfClass:[PLVIJKFFMoviePlayerController class]]){
            PLVIJKFFMoviePlayerController *player = (PLVIJKFFMoviePlayerController *)self.mainPlayer;
            if ([player.tcpSpeed isEqualToString:@"0"]){
                return @"0 KB/s";
            }
            return player.tcpSpeed;
        }
        else if ([self.mainPlayer isKindOfClass:[PLVIJKAVMoviePlayerController class]]){
            PLVIJKAVMoviePlayerController *avplayer= (PLVIJKAVMoviePlayerController *)self.mainPlayer;
            return avplayer.tcpSpeed;
        }
    }
    return @"0 KB/s";
}

/// 播放器状态
- (void)setTeaserState:(PLVVodAssetState)teaserState {
	_teaserState = teaserState;
	[[NSNotificationCenter defaultCenter] postNotificationName:PLVVodPlayerTeaserStateDidChangeNotification object:self];
}

- (void)setScalingMode:(PLVVodMovieScalingMode)scalingMode {
	_scalingMode = scalingMode;
	dispatch_async(dispatch_get_main_queue(), ^{
		self.mainPlayer.scalingMode = (IJKMPMovieScalingMode)scalingMode;
	});
}

- (void)setPreparedToPlay:(BOOL)preparedToPlay {
	_preparedToPlay = preparedToPlay;
	__weak typeof(self) weakSelf = self;
	if (self.preparedToPlayHandler) self.preparedToPlayHandler(weakSelf);
}

- (void)setPlaybackState:(PLVVodPlaybackState)playbackState {
	_playbackState = playbackState;
	__weak typeof(self) weakSelf = self;
	if (self.playbackStateHandler) self.playbackStateHandler(weakSelf);
}

- (void)setLoadState:(PLVVodLoadState)loadState {
	_loadState = loadState;
	__weak typeof(self) weakSelf = self;
	if (self.loadStateHandler) self.loadStateHandler(weakSelf);
}

//  可动态设置是否允许后台播放
- (void)setEnableBackgroundPlayback:(BOOL)enableBackgroundPlayback{
    _enableBackgroundPlayback = enableBackgroundPlayback;
    BOOL pauseInBackground = !_enableBackgroundPlayback;
    [self.mainPlayer setPauseInBackground:pauseInBackground];
}

- (void)setReachEnd:(BOOL)reachEnd {
	_reachEnd = reachEnd;
    
    // add by libl [添加播放成功结束的判断逻辑] 2018-08-27 start
    if (self.reachEnd){
        if ((self.mainPlayer.duration - self.currentPlaybackTime >= PLVPlaybackEndTimeInterval) ||
            (self.currentPlaybackTime < 1)){
            self.reachEndSuccess = NO;
        }
        else{
            self.reachEndSuccess = YES;
        }
    }
    else{
        self.reachEndSuccess = NO;
    }
    // add end
    
	__weak typeof(self) weakSelf = self;
	if (reachEnd && self.reachEndHandler) self.reachEndHandler(weakSelf);
}

#pragma mark -- 视频播放入口，音视频切换
- (void)setPlaybackMode:(PLVVodPlaybackMode)playbackMode {
    if (playbackMode == _playbackMode) return;
    _playbackMode = playbackMode;
    
    __weak typeof(self) weakSelf = self;
    if (self.playbackModeHandler) self.playbackModeHandler(weakSelf);
    
    // add by libl [如果当作默认播放模式设置，直接返回] 2018-08-31 start
    if (!self.video) return;
    // add end
    
    _autoPlayByDefault = YES;                       // 自动播放（临时参数）
    
    //在这里设置startPlaybackTime会导致切换下一个视频的时候会自动seek
//    _startPlaybackTime = self.currentPlaybackTime;  // 记录当前播放时间
    
    dispatch_async(dispatch_get_main_queue(), ^{
        // 无需重置统计数据
        self.isResetViewlog = NO;
        // 需要调用此方法，更新UI界面，而不是简单的视频切换
        [self setVideo:self.video quality:self.quality];
        self.beforSwitchPosition = self.currentPlaybackTime;
        self.needResumePlaybackProgress = YES;
        self.mainPlayerPrepareToPlayHandler = ^{
           // 若打开记忆播放速率功能，则播放模式切换时，不重置播放速率
           if (weakSelf.rememberPlaybackRate) weakSelf.playbackRate = weakSelf.lastPlaybackRate;

            weakSelf.needResumePlaybackProgress = NO;
            if (weakSelf.beforSwitchPosition) {
                [weakSelf setCurrentPlaybackTime:weakSelf.beforSwitchPosition];
                weakSelf.beforSwitchPosition = 0;
            }
            [weakSelf play];
        };
    });
}

- (void)setEnableDNSOptimize:(BOOL)enableDNSOptimize {
    _enableDNSOptimize = enableDNSOptimize;
    // 暂时屏蔽此功能 方案等待完善
    // 先采用httpdns 的ip优选功能
    // [[PLVVodHttpDnsManager sharedManager] enableDNSOptimize:enableDNSOptimize];
}

#pragma mark -- <PLVVodPlayerViewController+Cature> --
- (void)setVideoCaptureProtect:(BOOL)videoCaptureProtect{
    if (_videoCaptureProtect) {
        if (videoCaptureProtect) {
            // 避免重复添加监听
        }else{
            PLVVodLogWarn(@"暂不支持过程中停用防录屏功能");
        }
        return;
    }
    
    // 若启用防录屏功能
    if (videoCaptureProtect) {
        [self configVideoCaptureProtect:videoCaptureProtect];
    }
    
    _videoCaptureProtect = videoCaptureProtect;
}

#pragma mark - private method

/// 切换主播放器URL
// 需要在主线程执行
- (void)switchURL:(NSURL *)URL options:(PLVIJKFFOptions *)options playToken:(NSString *)playToken{
    self.qosLoadingStartTime = [PLVVodUtil currentSeconds];
    NSLog(@"xx_%s - %@ - %@", __FUNCTION__, [NSThread currentThread], URL);
    
	self.currentURL = URL;
    
    // add by libl [记录播放参数，用于elog日志] 2019-05-15 start
    [self addElogPlayerParam:@"url" value:URL.absoluteString type:@"4"];
    // add end

	if (self.loadingHandler) self.loadingHandler(YES);
	
	PLVVodSettings *settings = [PLVVodSettings sharedSettings];
    
    BOOL httpDnsOpen = NO;
    if (!self.video.isHls302) {
        if (self.video.httpDnsMode != PLVVodHttpDnsModeDefault) {
            httpDnsOpen = self.video.httpDnsMode == PLVVodHttpDnsModeOpen;
        }else {
            httpDnsOpen = [PLVVodSettings sharedSettings].enableHttpDNS;
        }
    }
    
	if (![URL isFileURL] && httpDnsOpen && !settings.enableIPV6) { // 处理 httpDNS
        
        // add by libl [加密的hls视频才使用httpdns 功能] 2019-02-18 starts
        if (!self.video.isPlain &&
            self.video.isHls &&
            ([URL.path containsString:@"m3u8"] || [URL.path containsString:@"pdx"])
            ){
            NSString *host = self.video.tsHost;
            // 如果切换了线路，需要重新获取host
            if (![PLVVodUtil isNilString:self.routeLine]){
                NSString *newHost = [self getHostWithRouteline:self.routeLine];
                if (newHost){
                    host = newHost;
                }
            }
            self.qosLastRequestVideoDomain = host;
            NSString *ip = [[PLVVodHttpDnsManager sharedManager] getIpByHostAsyncInURLFormat:host];
            if (ip.length) {
                [options setFormatOptionValue:host forKey:@"ip_host"];
                [options setFormatOptionValue:ip forKey:@"ip_addr"];
                PLVVodLogDebug(@"[ts httpdns] 播放使用 ip：%@，替换 host：%@。", ip, host);
                
                NSString *sessionid = [PLVVodHttpDnsManager sharedManager].sessionid;
                [self addElogPlayerParam:@"ip_host" value:host type:@"1"];
                [self addElogPlayerParam:@"ip_addr" value:ip type:@"1"];
                [self addElogPlayerParam:@"sessionid" value:sessionid type:@"1"];
            }
            
            // add by libl [m3u8 支持httpdns] 2019-07-03 start
            NSString *m3u8_host = self.currentURL.host;
            NSString *m3u8_ip = [[PLVVodHttpDnsManager sharedManager] getIpByHostAsyncInURLFormat:m3u8_host];
            if (m3u8_ip.length){
                [options setFormatOptionValue:m3u8_host forKey:@"ip_host_key"];
                [options setFormatOptionValue:m3u8_ip forKey:@"ip_addr_key"];
                PLVVodLogDebug(@"[m3u8 httpdns] 播放使用 ip：%@，替换 host：%@。", m3u8_ip, m3u8_host);
                
                NSString *sessionid = [PLVVodHttpDnsManager sharedManager].sessionid;
                [self addElogPlayerParam:@"ip_host_key" value:m3u8_host type:@"1"];
                [self addElogPlayerParam:@"ip_addr_key" value:m3u8_ip type:@"1"];
                [self addElogPlayerParam:@"sessionid_key" value:sessionid type:@"1"];
            }
            
            [self sendHttpDnsPlayVideoEvent];
        }
	}
	
	if (self.mainPlayer) { // 清理播放器
		if ([self.mainPlayer isKindOfClass:[PLVIJKFFMoviePlayerController class]]) {
			PLVIJKFFMoviePlayerController *ijkFFPlayer = self.mainPlayer;
			ijkFFPlayer.tcpOpenDelegate = nil;
			ijkFFPlayer.httpOpenDelegate = nil;
			ijkFFPlayer.liveOpenDelegate = nil;
			ijkFFPlayer.segmentOpenDelegate = nil;
			ijkFFPlayer.nativeInvokeDelegate = nil;
		}
		[self.mainPlayer.view removeFromSuperview];
		[self.mainPlayer shutdown];
		[self removeObserversWithPlayer:self.mainPlayer];
        self.mainPlayer = nil;
		
		// 恢复变量
		self.preparedToPlay = NO;
		self.playbackState = PLVVodPlaybackStateStopped;
		self.loadState = PLVVodLoadStateUnknown;
		self.reachEnd = NO;
	}
	if (!options) {
		options = [PLVIJKFFOptions optionsByDefault];
	}

	// 配置 ijk 通用选项
	[self setupIjkOptions:options];
    
    if ([self isPlayOnlineM3u8Source:URL]){
        self.mainPlayer = [[PLVIJKAVMoviePlayerController alloc] initWithContentURL:URL];
    }
    else{
        if ([self isPlayOfflineM3u8Source:URL]){
            // 本地直播转存视频，使用软解
            [options setPlayerOptionIntValue:0 forKey:@"videotoolbox"];
        }
        
        NSString *filePathExtension = URL.pathExtension;
        if ([filePathExtension containsString:@"pdx"] && ![URL isFileURL]) {
            // 请求pdx内容
            [self playPdxURL:URL token:playToken options:options];
            return;
            
        }else {
            if(![PLVVodUtil isNilString:playToken]){
                NSString *tokenUrl = [NSString stringWithFormat:@"%@&token=%@", URL.absoluteString, playToken];
                URL = [NSURL URLWithString:tokenUrl];
            }
            self.mainPlayer = [[PLVIJKFFMoviePlayerController alloc] initWithContentURL:URL withOptions:options];
        }
    }
	[self addObserversWithPlayer:self.mainPlayer];
    
	// 在viewDidLoad后，操作view
	__weak typeof(self) weakSelf = self;
    // mod by libl [LIVE-7135 适配无UI音频播放场景] 2018-12-07 start
    if (self.playbackMode == PLVVodPlaybackModeAudio){
        if (!self.viewLoaded){
            [self view];
        }
        [self setupMainPlayer:self.mainPlayer];
        PLVVodLogDebug(@"[player] -- 无UI 音频播放");
    }else{
        [self runAfterViewDidLoad:^{
            [weakSelf setupMainPlayer:weakSelf.mainPlayer];
        }];
    }
}

- (void)playPdxURL:(NSURL *)URL token:(NSString *)playToken options:(PLVIJKFFOptions *)options{
    if(![PLVVodUtil isNilString:playToken]){
        NSString *tokenUrl = [NSString stringWithFormat:@"%@&token=%@", URL.absoluteString, playToken];
        URL = [NSURL URLWithString:tokenUrl];
    }
    // 异步请求pdx
    NSString *backHost = nil;
    if (self.video.hlsVideos_backup.count){
        NSURL *backURL = [NSURL URLWithString:self.video.hlsVideos_backup.firstObject];
        backHost = backURL.host;
    }
    [PLVVodNetworking requestPdxWithUrl:URL.absoluteString backHost:backHost completion:^(NSDictionary *pdxDictionary, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (error){
                // 错误处理
                PLVVodLogError(@"pdx请求失败");
                [self reportError:error];
                return;
            }
            else{
                NSString *bodyContent = pdxDictionary[@"body"];
                NSString *bodyBase64String = [[bodyContent componentsSeparatedByCharactersInSet:[NSCharacterSet newlineCharacterSet]] componentsJoinedByString:@""];
                NSData *pdxBodyData = [[NSData alloc] initWithBase64EncodedString:bodyBase64String options:0];
                NSString *prefixString = [URL.absoluteString stringByDeletingLastPathComponent];
                prefixString = [prefixString stringByAppendingString:@"/"];
                self.mainPlayer = [[PLVIJKFFMoviePlayerController alloc] initWithContentPrefixURLString:prefixString aUrlData:pdxBodyData withOptions:options];
                
                [self addObserversWithPlayer:self.mainPlayer];
                
                // 在viewDidLoad后，操作view
                __weak typeof(self) weakSelf = self;
                // mod by libl [LIVE-7135 适配无UI音频播放场景] 2018-12-07 start
                if (self.playbackMode == PLVVodPlaybackModeAudio){
                    if (!self.viewLoaded){
                        [self view];
                    }
                    [self setupMainPlayer:self.mainPlayer];
                    PLVVodLogDebug(@"[player] -- 无UI 音频播放");
                }else{
                    [self runAfterViewDidLoad:^{
                        [weakSelf setupMainPlayer:weakSelf.mainPlayer];
                    }];
                }
            }
           
        });
    }];
}

- (BOOL)isPlayOnlineM3u8Source:(NSURL *)URL{
    bool hasM3u8 = [self.video.play_source_url isKindOfClass:[NSString class]] && [self.video.play_source_url containsString:@"m3u8"];
    if (self.video.keepSource && hasM3u8 && ![URL isFileURL] && self.enableAVPlayer){
        return YES;
    }
    return NO;
}

- (BOOL)isPlayOfflineM3u8Source:(NSURL *)URL{
    bool hasM3u8 = [self.video.play_source_url isKindOfClass:[NSString class]] && [self.video.play_source_url containsString:@"m3u8"];
    if (self.video.keepSource && hasM3u8 && [URL isFileURL]){
        return YES;
    }
    return NO;
}


- (NSString *)getHostWithRouteline:(NSString *)routeline{
    __block NSString *host = nil;
    if (![PLVVodUtil isNilString:routeline]){
        [self.video.tsCdns enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj containsString:routeline]){
                host = obj;
                *stop = YES;
            }
        }];
    }
    
    return host;
}

- (void)setupMainPlayer:(id<PLVIJKMediaPlayback>)ijkPlayer {
//	NSLog(@"xx_%s - %@", __FUNCTION__, [NSThread currentThread]);
	ijkPlayer.view.autoresizingMask = UIViewAutoresizingFlexibleWidth|UIViewAutoresizingFlexibleHeight;
	ijkPlayer.view.frame = self.view.bounds;
	
	ijkPlayer.scalingMode = (IJKMPMovieScalingMode)self.scalingMode;
    //ijkPlayer.shouldAutoplay = NO;
    ijkPlayer.shouldAutoplay = _autoPlayByDefault;
    _autoPlayByDefault = NO;
	// 禁用 Airplay
	ijkPlayer.allowsMediaAirPlay = NO;
	[ijkPlayer setPauseInBackground:!self.enableBackgroundPlayback];
	
	if ([ijkPlayer isKindOfClass:[PLVIJKFFMoviePlayerController class]]) {
		PLVIJKFFMoviePlayerController *ijkFFPlayer = ijkPlayer;
		ijkFFPlayer.tcpOpenDelegate = self;
		ijkFFPlayer.httpOpenDelegate = self;
		ijkFFPlayer.liveOpenDelegate = self;
		ijkFFPlayer.segmentOpenDelegate = self;
		ijkFFPlayer.nativeInvokeDelegate = self;
	}
	
	self.view.autoresizesSubviews = YES;
	[self.view insertSubview:ijkPlayer.view atIndex:0];
	
	[ijkPlayer prepareToPlay];
    //开始累计加载时间
    self.playerLoadTime = 0;
    
    PLVVodLogDebug(@"[player] -- url: %@", self.currentURL);
    
    // add libl [添加ijkplayer 创建通知，支持三分屏功能] 2019-07-25
    [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationIJKPlayerCreateKey
                                                        object:nil];
    
}

/// 每次视频模型载入时设置相关参数
- (void)setupWhenVideoDidLoad {
    // 记忆播放位置需在获得 video 之后
    if (_rememberLastPosition) {
        _startPlaybackTime = self.lastPosition;
    } else {
        self.lastPosition = 0.0;
    }
    
    __weak typeof(self) weakSelf = self;
    if (self.video.keep_play) {
        dispatch_semaphore_t semaphore = dispatch_semaphore_create(0); // 创建信号量
        [PLVVodNetworking requestLastPositionWithVid:self.video.vid viewerId:[PLVVodSettings sharedSettings].viewerId completion:^(NSTimeInterval lastPosition) {
            if(lastPosition > 0){
                if (self.video.duration - lastPosition < 10) {
                    // 时间点与视频结束时间相隔<10秒，从视频起点开始播放
                    lastPosition = 0.0;
                }
                weakSelf.lastPosition = lastPosition;
                weakSelf.startPlaybackTime = lastPosition;
            }
            dispatch_semaphore_signal(semaphore); // 发送信号，表示请求完成
        }];
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER); // 等待信号，阻塞当前线程
    }
	
	// 重置用户统计数据
    // mod by libl [live-10327 音视频/线路/码率切换无需重置统计参数] 2019-03-15 start
    if (self.isResetViewlog){
        self.viewerStayDuration = 0.0;
        self.viewerWatchDuration = 0.0;
        self.playerTotalFlow = 0;
        self.videoContentPlayedTime = 0.0;
        [self.seekModel reset];
    }
    else{
        // 下次正常切换视频需要重置viewlog
        self.isResetViewlog = YES;
    }
    // mod end
    
    // 重置差网络统计数据
    self.poorNetWorkCount = 0;
    self.poorNetWorkInterval = 0;
    self.isBuffering = NO;
    
	__block NSTimeInterval lastViewerWatchDuration = self.viewerWatchDuration;
    __block NSInteger repeatCount = 0;
	NSTimeInterval interval = 0.5;
    [self.innerPlaybackTimer cancel];
    self.innerPlaybackTimer = nil;
	self.innerPlaybackTimer = [PLVTimer repeatWithInterval:interval repeatBlock:^{
        repeatCount ++;
        
		// 计算停留时长
		weakSelf.viewerStayDuration += interval;
		
        // 片头播放逻辑处理
        if ([weakSelf handleTeaserPlay]){
            return ;
        }
        
        //计算检查网络的周期时间
        if (weakSelf.poorNetWorkCount > 0) {
            weakSelf.poorNetWorkInterval += interval;
            if (weakSelf.isBuffering &&
                weakSelf.poorNetWorkInterval >= 5) {
                // 缓冲时长 >= 5s，回调差网络
                if (weakSelf.poorNetWorkHandler) {
                    weakSelf.poorNetWorkHandler();
                }
                weakSelf.poorNetWorkCount = 0;
                weakSelf.poorNetWorkInterval = 0;
            }
            if (weakSelf.poorNetWorkInterval >= 11) {
                weakSelf.poorNetWorkCount = 0;
                weakSelf.poorNetWorkInterval = 0;
            }
        }
        
        // 计算播放器加载超时
        if (weakSelf.playerLoadTime >= 0) {
            weakSelf.playerLoadTime += interval;
            if (weakSelf.playerLoadTime >= PLVPlayerLoadTimeOut) {
                [weakSelf sendLoadTimeOutEvent];
                weakSelf.playerLoadTime = -1;
            }
        }
        
		// 计算观看时长
        if ([PLVPictureInPictureManager sharedInstance].pictureInPictureActive) {
            if ([PLVPictureInPictureManager sharedInstance].playbackState == 1) {
                weakSelf.viewerWatchDuration += interval;
                // 视频内容时长
                weakSelf.videoContentPlayedTime += interval*weakSelf.playbackRate;
            }
        }else {
            if (weakSelf.playbackState == IJKMPMoviePlaybackStatePlaying) {
                weakSelf.viewerWatchDuration += interval;
                // 视频内容时长
                weakSelf.videoContentPlayedTime += interval*weakSelf.playbackRate;
            }
        }
		
        
		// 定时发送日志
        if (!weakSelf.localPlayback || weakSelf.enableLocalViewLog)
        {
            if (!weakSelf.reachEnd && weakSelf.viewerWatchDuration > 0) {
                [weakSelf reportViewLog];
            }
        }

        // 发送qosStalling事件
        if(!weakSelf.localPlayback){
             [weakSelf reportQosStallingEvent];
        }
        
        // add libl [添加播放进度回调] 2019.09.25
        if (repeatCount%2 == 0 && !weakSelf.reachEnd){
            if (weakSelf.playbackProgressHandler){
                weakSelf.playbackProgressHandler(weakSelf, weakSelf.currentPlaybackTime);
            }
        }
        
		lastViewerWatchDuration = weakSelf.viewerWatchDuration;
		
		// 更新信息
		if (!weakSelf.reachEnd) {
			dispatch_async(dispatch_get_main_queue(), ^{
				[weakSelf updateUIWithTimer];
			});
		}
        
        // add by libl [对于非成功播放结束的场景，需要主动轮询播放] 2018-08-11 start
        if (weakSelf.reachEnd && (weakSelf.mainPlayer.duration - weakSelf.mainPlayer.currentPlaybackTime >= PLVPlaybackEndTimeInterval)
            && fmod(weakSelf.viewerStayDuration, PLVVodPlaybackRecoveryHandlerInterval) == 0.0){

            if (weakSelf.mainPlayer.isPreparedToPlay){
                dispatch_async(dispatch_get_main_queue(), ^{
                    
                    // 已经成功播放过，尝试自动恢复播放
                    if (weakSelf.playbackRecoveryHandle){
                        weakSelf.playbackRecoveryHandle(weakSelf);
                    }
                });
            }
        }
	}];
}


/// 适用Elog统计错误
- (void)reportError:(NSError *)error {
    [self reportElogWithError:error];
    if (self.playerErrorHandler){
        self.playerErrorHandler(self, error);
    }
}

- (void)runAfterViewDidLoad:(void (^)(void))handler {
	if (!handler) {
		return;
	}
	dispatch_async(dispatch_get_main_queue(), ^{
		if (self.viewLoaded) {
			handler();
		} else {
			self.viewDidLoadHandler = handler;
		}
	});
}

#pragma mark - 视频播放主入口，传递video参数
- (void)setVideo:(PLVVodVideo *)video {
	//NSLog(@"xx_%s - %@", __FUNCTION__, [NSThread currentThread]);
	
    // 解决各种场景播放，子线程中UI操作导致的崩溃问题
    dispatch_async(dispatch_get_main_queue(), ^{
        // 若传入的是本地视频，则对路径校验通过的调用 -setVideo:quality:
        if ([video isKindOfClass:[PLVVodLocalVideo class]]) {
            PLVVodLocalVideo *localVideo = (PLVVodLocalVideo *)video;
            
            // mod by libl [新增本地音频文件类型] 19-03-24 start
            if ([localVideo respondsToSelector:@selector(path)] && [[NSFileManager defaultManager] fileExistsAtPath:localVideo.path]) {
                [self setVideo:localVideo quality:localVideo.quality];
                return;
            }
            else{
                if ([localVideo respondsToSelector:@selector(audioPath)] && [[NSFileManager defaultManager] fileExistsAtPath:localVideo.audioPath]) {
                    [self setVideo:localVideo quality:localVideo.quality];
                    return;
                }
            }
            // mod end
        } else if (![video isKindOfClass:[PLVVodVideo class]]) {
            _video = video;
            PLVVodLogError(@"未知视频对象");
            NSError *plvError = PLVVodErrorMake(video_type_unknown, ^(NSMutableDictionary *userInfo) {
                
            });
            [self reportError:plvError];
           
            return;
        }
        PLVVodQuality quality = video.preferredQuality;
        [self setVideo:video quality:quality];
    });
}

#pragma mark -- 视频播放主入口，指定video， 码率
- (void)setVideo:(PLVVodVideo *)video quality:(PLVVodQuality)quality {
    PLVVodLogDebug(@"xx_%s - %@", __FUNCTION__, [NSThread currentThread]);
    // 检查视频的可用性
    if (!video.available) {
        _video = video;
        // 环球优路反馈，视频切换，会继续播放前一个视频: 此处需要停止播放器
        [self stopPlayer];
        
        NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
        NSError *plvError = video.error ? video.error : PLVVodErrorMake(account_video_illegal, ^(NSMutableDictionary *userInfo) {
            userInfo[PLVVodErrorSelectorKey] = selector;
        });
        [self reportError:plvError];
        
        return;
    }
        
    // 以后播放，统一传递PLVVodVideo 参数
    if ([video isKindOfClass:[PLVVodLocalVideo class]]){
        // 本地视频参数,不支持音频模式播放
        // 兼容2.5.6 版本以下的参数传递方式
        _playbackMode = PLVVodPlaybackModeVideo;
        
        [self playVideoWithVideo:video quality:quality];
        
        PLVVodLogWarn(@"当前播放参数不支持音频播放模式，已切换为视频模式 -- vid:%@", video.vid);

        return;
    }
    else {
       
        // 在线参数
        if ([video canSwithPlaybackMode]){
            // 支持音视频切换
            if (PLVVodPlaybackModeAudio == _playbackMode){
                // 音频播放模式
                [self playAudioWithVideo:video];
                
                return;
            }
            else {
                // 视频播放模式
                [self playVideoWithVideo:video quality:quality];
            }
        }
        else{
            // 不支持音视频切换,只有视频播放模式
            if (PLVVodPlaybackModeAudio == _playbackMode){
                _playbackMode = PLVVodPlaybackModeVideo;
                PLVVodLogWarn(@"当前视频不支持音频播放模式，已切换为视频模式 -- vid:%@", video.vid);
            }
            [self playVideoWithVideo:video quality:quality];
        }
    }
}

- (void)playVideoWithVideo:(PLVVodVideo *)video quality:(PLVVodQuality )quality{
    // 清理重试记录
    [_hasTryCdns removeAllObjects];
    [_hasTryQuality removeAllObjects];
    
    // 保存上一视频的播放位置
    [self setLastPosition:self.currentPlaybackTime];
    
    self.isPlayURL = NO;
    
    // 广告配置
    // 广告，片头播放逻辑
    // 1 继续播放，跳过广告，片头播放
    // 2 否则，在线播放，根据开关设置播放；离线播放，广告跳过，片头根据开关播放
    if (video != _video) { // 去重，保证是video更新后的
        _video = video;
        self.preparedToPlay = NO;
        __weak typeof(self) weakSelf = self;
        
        // 片头播放完成回调设置
        void (^teaserCompletion)(BOOL finish) = ^(BOOL finish) {
            // 播放正片
            if (finish || weakSelf.autoplay) {
                if (weakSelf.preparedToPlay) {
                    [weakSelf play];
                } else {
                    weakSelf.mainPlayerPrepareToPlayHandler = ^{
                        [weakSelf play];
                    };
                }
            }
            PLVVodLogInfo(@"开始播放正片");
            
            //如果在播放片头阶段，离开播放器，片头时间过去后需要暂停播放正片
            dispatch_async(dispatch_get_main_queue(), ^{
                if (weakSelf.leaveSubPlayer) {
                    [weakSelf pause];
                    weakSelf.leaveSubPlayer = NO;
                }
            });
        };
        
        if (self.enableAd &&  (self.rememberLastPosition && self.lastPosition > 0)){
            if (self.shouldPlayAdBeforeContinue) {
                // 续播，不跳过广告
                [self configAdPlayerWithVideo:video];
                [self.adPlayer showAdWithLocation:PLVVodAdLocationHead completion:^(BOOL finish) {
                    // 广告播放完成，播放片头
                    [weakSelf playTeaserWithVideo:video completion:teaserCompletion];
                }];
            }else {
                // 续播，直接跳过广告
                [self playTeaserWithVideo:video completion:teaserCompletion];
            }
        }
        else
        {
            BOOL willLocalPlay = [self checkVideoWillPlayLocal:video];
            if (self.enableAd && !willLocalPlay) {
                // 先播放广告
                [self configAdPlayerWithVideo:video];
                [self.adPlayer showAdWithLocation:PLVVodAdLocationHead completion:^(BOOL finish) {
                    // 广告播放完成，播放片头
                    [weakSelf playTeaserWithVideo:video completion:teaserCompletion];
                }];
            } else {
                // 播放片头
                [self playTeaserWithVideo:video completion:teaserCompletion];
            }
        }
    }
    
    // 重置记忆的上个视频的播放速率
    if (!self.rememberPlaybackRate){
        self.lastPlaybackRate = 1.0;
    }
    
    if (self.lastPlaybackRate <= 0) {
        self.lastPlaybackRate = 1.0;
    }
    
    // 视频切换，做一些清理工作
    _routeLine = nil; // 清理上一个视频的线路设置
    
    // 重置此值
    self.hasPlayed = NO;
    //重置首次seek的事件暂存，否则新视频会执行上一个视频的seek事件
    self.beforePlaySeekEvent = nil;
    
    _video = video;
    // mod by libl [音频切换到视频场景，无需重置viewlog相关参数] 2019-03-15 start
    if (self.isResetViewlog){
        self.pid = [PLVVodUtil pid];
        self.isFirstViewlog = YES;
        self.playerTotalFlow = 0;
        [self.seekModel reset];
    }
    else{
        if (!self.pid){
            self.pid = [PLVVodUtil pid];
        }
        else{
            // pid 不变，保存当前播放器流量统计
            if (self.mainPlayer){
                if ([self.mainPlayer isKindOfClass:[PLVIJKFFMoviePlayerController class]]){
                    PLVIJKFFMoviePlayerController *player = (PLVIJKFFMoviePlayerController *)self.mainPlayer;
                    self.playerTotalFlow = player.trafficStatistic;
                }
                else if ([self.mainPlayer isKindOfClass:[PLVIJKAVMoviePlayerController class]]){
                    PLVIJKAVMoviePlayerController *avplayer = (PLVIJKAVMoviePlayerController *)self.mainPlayer;
                    self.playerTotalFlow = avplayer.numberOfBytesTransferred;
                }
            }
        }
    }
    // mod end
    
    if (!_video) {
        PLVVodLogError(@"无法获取视频");
        NSError *plvError = PLVVodErrorMake(video_not_found, ^(NSMutableDictionary *userInfo) {
            
        });
        [self reportError:plvError];
        return;
    }
    
    // TODO: 该逻辑是否正确
    // 本地播放优先，且传入的是在线视频，则额外获取一个本地video
    self.localOnlineVideo = nil;
    if (self.localPrior && ![video isKindOfClass:[PLVVodLocalVideo class]]) {
        
        // path 字段有效，才是完整的本地视频，否则播放错误
        // 不能彻底解决问题，还是无法判断加密视频是否完整
        PLVVodLocalVideo *localVideo = [PLVVodLocalVideo localVideoWithVideo:video dir:[PLVVodDownloadManager sharedManager].downloadDir];
        if (![localVideo.path isKindOfClass:[NSNull class]] && [localVideo.path length] ){
            self.localOnlineVideo = localVideo;
        }
        
        // 检测本地视频完整性 上报elog用于本地文件丢失排查
        [self checkLocalVideo];
        
        
    } else {
        self.localOnlineVideo = nil;
    }
    
    // 设置标题
    dispatch_async(dispatch_get_main_queue(), ^{
        self.title = video.title;
    });
    
    // 开始处理正片
    [self switchVideoQuality:quality];
    
    [self setupWhenVideoDidLoad];
}

- (void)checkLocalVideo{
    // 如果是本地加载videojson 获取不到本地视频文件 需要上报Elog 信息 定位本地播放失败原因
    // 上报downloadDir
    if (self.video.isLocalVideoJson && !self.localOnlineVideo){
        NSError *errorInfo = PLVVodErrorMake(local_file_unaccessible, ^(NSMutableDictionary *userInfo) {
            NSString *downloadDir = [PLVVodDownloadManager sharedManager].downloadDir;
            if (![PLVVodUtil isNilString:downloadDir]){
                // 获取存储路径
                [userInfo setObject:downloadDir forKey:@"download_dir"];
                
                BOOL isDir = NO;
                if (![[NSFileManager defaultManager] fileExistsAtPath:downloadDir isDirectory:&isDir] || !isDir) {
                    PLVVodLogError(@"%s - 目录不存在, 请检查下载目录", __FUNCTION__);
                }
                if (isDir){
                    NSArray *subpaths  = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:downloadDir error:nil];
                    // 保存 downloadDir 下的所有路径
                    NSString *subpathsDes = subpaths.count > 0 ? [subpaths componentsJoinedByString:@","] : @"";
                    [userInfo setObject:subpathsDes forKey:@"download_subpaths"];
                    
                    // 查找特定目录并遍历
                    for (NSString *subpath in subpaths) {
                        // 找到当前视频目录 获取当前目录下的所有文件
                        NSString *poolVideoId = videoPoolIdWithVid(self.video.vid);
                        if ([subpath containsString:poolVideoId]) {
                            NSString *tmpPath = [downloadDir stringByAppendingPathComponent:subpath];
                            NSArray *videoPaths = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:tmpPath error:nil];
                            NSString *videoPathsDes = videoPaths.count > 0 ? [videoPaths componentsJoinedByString:@","] : @"";
                            [userInfo setObject:videoPathsDes forKey:@"video_paths"];
                        }
                    }
                }
            }
        });
        [self reportElogWithError:errorInfo];
    }
}

#pragma mark -- 音频播放流程
- (void)playAudioWithVideo:(PLVVodVideo *)video{
    // 清理重试记录
    [_hasTryCdns removeAllObjects];
    [_hasTryQuality removeAllObjects];
    
    // 0 保存当前音视频的相关信息
    [self setLastPosition:self.currentPlaybackTime];
    
    // 1 播放新音频，初始化设置
    // mod by libl [音频播放，autoPlay 属性无效问题修复] 2019-05-28 start
//    _autoPlayByDefault = YES; // 自动播放（临时参数）
    if (!_autoPlayByDefault){
        _autoPlayByDefault = self.autoplay;
    }
    
    self.teaserState = PLVVodAssetStateFinished;
    
    // mod end
    _video = video;
    if (self.isResetViewlog){
        self.pid = [PLVVodUtil pid];
        self.playerTotalFlow = 0;
    }
    else{
        if (!self.pid){
            self.pid = [PLVVodUtil pid];
        }
        else{
            // pid 不变，保存当前播放器流量
            if (self.mainPlayer){
                if ([self.mainPlayer isKindOfClass:[PLVIJKFFMoviePlayerController class]]){
                    PLVIJKFFMoviePlayerController *player = (PLVIJKFFMoviePlayerController *)self.mainPlayer;
                    self.playerTotalFlow = player.trafficStatistic;
                }
                else if ([self.mainPlayer isKindOfClass:[PLVIJKAVMoviePlayerController class]]){
                    PLVIJKAVMoviePlayerController *avplayer = (PLVIJKAVMoviePlayerController *)self.mainPlayer;
                    self.playerTotalFlow = avplayer.numberOfBytesTransferred;
                }
            }
        }
    }
    
    if (!_video) {
        PLVVodLogError(@"无法获取音视频数据");
        NSError *plvError = PLVVodErrorMake(video_not_found, ^(NSMutableDictionary *userInfo) {
            
        });
        [self reportError:plvError];
        return;
    }
    
    // 2 开始播放音频
    // 优先查找本地音频，再查找本地视频
    PLVVodLocalVideo *localAudio = [PLVVodLocalVideo localAudioWithVideo:video dir:[PLVVodDownloadManager sharedManager].downloadDir];
    if (!localAudio){
        localAudio = [PLVVodLocalVideo localVideoWithVideo:video dir:[PLVVodDownloadManager sharedManager].downloadDir];
    }
    if (self.localPrior && localAudio){
        // 本地音频
        self.localPlayback = YES;
        NSString *filePath = localAudio.audioPath? localAudio.audioPath: localAudio.path;;
        if (!filePath){
            PLVVodLogError(@"无法获取本地音频数据");
            NSError *plvError = PLVVodErrorMake(video_not_found, ^(NSMutableDictionary *userInfo) {
                
            });
            [self reportError:plvError];
            return;
        }
        NSURL *URL = [NSURL fileURLWithPath:filePath]; // 路径前面已经校验
        PLVIJKFFOptions *ijkOptions = [PLVIJKFFOptions optionsByDefault];
        if (localAudio.isPlain) {
            PLVVodLogInfo(@"播放本地非加密音频/视频");
        } else {
            PLVVodLogInfo(@"播放本地加密视频");
            ijkOptions = [self localTokenOptions:localAudio.tokenPath];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [self switchURL:URL options:ijkOptions playToken:nil];
        });
        
        // 3 设置标题
        dispatch_async(dispatch_get_main_queue(), ^{
            self.title = video.title;
        });
        
        // 4 定时器更新UI，播放统计
        [self setupWhenVideoDidLoad];

    }
    else{
        // 在线音频
        NSString *aacLink = self.video.aac_link;
        if (aacLink && aacLink.length) {
            self.localPlayback = NO;
            
            dispatch_async(dispatch_get_main_queue(), ^{
                [self switchURL:[NSURL URLWithString:aacLink] options:nil playToken:nil];
            });
            
            // 3 设置标题
            dispatch_async(dispatch_get_main_queue(), ^{
                self.title = video.title;
            });
            
            // 4 播放统计
            [self setupWhenVideoDidLoad];
        }
    }
}

#pragma mark 清晰度切换
#pragma mark -- <PLVVodPlayerViewController+Switch> --
/// 切换码率，若码率不符合则自动切换到附近的码率
/// 该方法决定了最终给播放的 URL
- (void)switchQuality:(PLVVodQuality)quality {
    // add by libl [视频状态可能不正确，需要检查] 2019-06-04 start
    // 检查视频的可用性
    if (!self.video.available) {
        NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
        NSError *plvError = self.video.error ? self.video.error : PLVVodErrorMake(account_video_illegal, ^(NSMutableDictionary *userInfo) {
            userInfo[PLVVodErrorSelectorKey] = selector;
        });
        [self reportError:plvError];
        return;
    }
    // add end
    
    _isResetViewlog = NO;
    NSTimeInterval playedTime = self.mainPlayer.currentPlaybackTime > 0 ? self.mainPlayer.currentPlaybackTime: self.lastPosition;
    [self switchVideoQuality:quality];
    self.needResumePlaybackProgress = YES;
    __weak typeof(self) weakSelf = self;
    self.mainPlayerPrepareToPlayHandler = ^{
        weakSelf.needResumePlaybackProgress = NO;
        if (weakSelf.beforSwitchPosition) {
            [weakSelf setCurrentPlaybackTime:weakSelf.beforSwitchPosition];
            weakSelf.beforSwitchPosition = 0;
        }else {
            [weakSelf setCurrentPlaybackTime:playedTime];
        }
        [weakSelf play];
    };
}

/// 切换软硬解
/// @param hardToDecode 是否硬解
- (void)switchVideoToolBox:(BOOL)hardToDecode {
    if (!self.video.available) {
        NSString *selector = [NSString stringWithUTF8String:__FUNCTION__];
        NSError *plvError = self.video.error ? self.video.error : PLVVodErrorMake(account_video_illegal, ^(NSMutableDictionary *userInfo) {
            userInfo[PLVVodErrorSelectorKey] = selector;
        });
        [self reportError:plvError];
        return;
    }
    
    _isResetViewlog = NO;
    _isVideoToolBox = hardToDecode;
    NSTimeInterval playedTime = self.mainPlayer.currentPlaybackTime > 0 ? self.mainPlayer.currentPlaybackTime: self.lastPosition;
    [self switchVideoQuality:self.quality];
    self.needResumePlaybackProgress = YES;
    __weak typeof(self) weakSelf = self;
    self.mainPlayerPrepareToPlayHandler = ^{
        weakSelf.needResumePlaybackProgress = NO;
        if (weakSelf.beforSwitchPosition) {
            [weakSelf setCurrentPlaybackTime:weakSelf.beforSwitchPosition];
            weakSelf.beforSwitchPosition = 0;
        }else {
            [weakSelf setCurrentPlaybackTime:playedTime];
        }
        [weakSelf play];
    };
}

/// 播放
- (void)play {
    // 确保 ijk 播放在主线程进行，避免崩溃
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{ [self play]; });
        return;
    }
    
    // 若开启防录屏功能 且 正被录屏 则无法播放
    if (self.videoCaptureProtect && self.videoCapturing) { return; }
    
	[self.mainPlayer play];
    [self.adPlayer hideAd];
}

- (void)pause {
	[self.mainPlayer pause];
	[self.adPlayer showAdWithLocation:PLVVodAdLocationPause completion:nil];
}

/// 暂停，不显示暂停广告
- (void)pauseWithoutAd {
    [self.mainPlayer pause];
}

- (void)leavePlayerWithPause{
    [self pause];
    if (self.subPlayer) {
        [self.subPlayer pause];
        self.leaveSubPlayer = YES;
    }
    self.leavePlayer = YES;
}

/// 播放片头播放器
- (void)resumeTeaserPlayer
{
    if (self.subPlayer && self.subPlayer.playbackState == IJKMPMoviePlaybackStatePaused) {
        [self.subPlayer play];
        self.leaveSubPlayer = NO;
    }
}

/// 当前时间截图
- (UIImage *)snapshot {
	return self.mainPlayer.thumbnailImageAtCurrentTime;
}

- (void)playbackModeDidChange {
    // should be overrided
}

- (void)updateAudioCoverAnimation:(BOOL)isPlaying {
    // should be overrided
}

- (CGSize)getVideoSize{
    return self.mainPlayer.naturalSize;
}

- (NSString *)getPlayId {
    return self.pid;
}

- (NSDictionary *)getRealPlayStatus {
    NSDictionary *dict = @{@"pid": (self.pid ?: @""), @"vid": (self.video.vid ?: @""), @"playduation": @(self.viewerWatchDuration)};
    return dict;
}

// 自定义片头设置
- (BOOL)setCustomTeaser:(NSString *)teaserUrl teaserDuration:(NSInteger )teaserDuration{
    if ([teaserUrl isKindOfClass:[NSNull class]] || !teaserUrl.length){
        PLVVodLogError(@"[player] -- 自定义片头url 为空");
        return NO;
    }
    if (teaserDuration <= 0){
        PLVVodLogError(@"[player] -- 未设置自定义片头显示时长");
        return NO;
    }
    
    _teaserUrl = teaserUrl;
    _teaserDuration = teaserDuration;
    
    return YES;
}

#pragma mark - 画中画相关
/// 开启画中画功能
- (void)startPictureInPicture {
    [self startPictureInPicture_PIP];
}

/// 关闭画中画功能
- (void)stopPictureInPicture {
    [self stopPictureInPicture_PIP];
}

#pragma mark PLVPictureInPictureManagerDelegate
- (void)plvPictureInPictureWillStart {
    [self plvPictureInPictureWillStart_PIP];
}

/// 画中画已经开始
- (void)plvPictureInPictureDidStart {
    [self plvPictureInPictureDidStart_PIP];
}

/// 画中画开启失败
/// @param error 失败错误原因
- (void)plvPictureInPictureFailedToStartWithError:(NSError *)error {
    [self plvPictureInPictureFailedToStartWithError_PIP:error];
}

/// 画中画即将停止
- (void)plvPictureInPictureWillStop {
    [self plvPictureInPictureWillStop_PIP];
}

/// 画中画已经停止
- (void)plvPictureInPictureDidStop {
    [self plvPictureInPictureDidStop_PIP];
}

-(void)plvPictureInPicturePlayerPlayingStateDidChange:(BOOL)playing {
    [self plvPictureInPicturePlayerPlayingStateDidChange_PIP:playing];
}

#pragma mark - IJKMediaUrlOpenDelegate

- (void)willOpenUrl:(PLVIJKMediaUrlOpenData *)urlOpenData {
    [self willOpenUrl_IJK:urlOpenData];
}

#pragma mark - IJKMediaNativeInvokeDelegate

- (int)invoke:(IJKMediaEvent)event attributes:(NSDictionary *)attributes {
    return [self invoke_IJK:event attributes:attributes];
}

#pragma mark - UI Event

/// 时间描述
- (NSString *)timeDescription {
    return [self timeDescription_Skin];
}

/// 播放/暂停
- (IBAction)playPauseAction:(UIButton *)sender {
    [self playPauseAction_Skin:sender];
}

/// 播放滑杆
- (IBAction)playbackSliderTouchDownAction:(UISlider *)sender {
    [self playbackSliderTouchDownAction_Skin:sender];
}

- (IBAction)playbackSliderValueChangeAction:(UISlider *)sender {
    [self playbackSliderValueChangeAction_Skin:sender];
}

- (IBAction)playbackSliderTouchUpCancelAction:(UISlider *)sender {
    [self playbackSliderTouchUpCancelAction_Skin:sender];
}

/// 亮度调节
- (IBAction)brightnessAction:(UISlider *)sender {
    [self brightnessAction_Skin:sender];
}

/// 音量调节
- (IBAction)volumeAction:(UISlider *)sender {
    [self volumeAction_Skin:sender];
}

#pragma mark -- 支持外部URL 链接播放
#pragma mark  <PLVVodPlayerViewController+URL>
- (void)setURL:(NSURL *)videoUrl{
    [self setURLInternal:videoUrl];
}

- (void)setURL:(NSURL *)videoUrl withHeaders:(NSDictionary<NSString *,NSString *> *)headers{
    [self setURLInternal:videoUrl withHeaders:headers];
}

#pragma mark -- 视频播放位置记忆
#pragma mark <PLVVodPlayerViewController+Position>
- (void)setLastPosition:(NSTimeInterval)lastPosition{
    [self setLastPositionInternal:lastPosition];
}

- (NSTimeInterval)lastPosition{
    return [self lastPositionInternal];
}

@end
