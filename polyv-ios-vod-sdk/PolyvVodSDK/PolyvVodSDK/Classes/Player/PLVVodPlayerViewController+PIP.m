//
//  PLVVodPlayerViewController+PIP.m
//  PLVVodSDK
//
//  Created by polyv on 2025/6/4.
//  Copyright © 2025 POLYV. All rights reserved.
//

#import "PLVVodPlayerViewController+PIP.h"
#import "PLVVodPlayerViewController+internal.h"
#import "PLVVodPlayerViewController+IJK.h"
#import "PLVVodPlayerViewController+Token.h"
#import "PLVVodPlayerViewController+Log.h"

@implementation PLVVodPlayerViewController (PIP)

#pragma mark - 画中画相关
/// 开启画中画功能
- (void)startPictureInPicture_PIP {
    if (!self.video) {
        NSError *plvError = PLVVodErrorWithCode(pictureinpicture_external_not_support);
        if (self.pictureInPictureErrorHandler) {
            self.pictureInPictureErrorHandler(self, plvError);
        }
        return;
    }
    if (self.video.hlsPrivateVersion > 0) {
        NSError *plvError = PLVVodErrorWithCode(pictureinpicture_private_decrypt_not_support);
        if (self.pictureInPictureErrorHandler) {
            self.pictureInPictureErrorHandler(self, plvError);
        }
        return;
    }
    if (self.playbackMode == PLVVodPlaybackModeAudio ||
        [self.currentURL.absoluteString containsString:@".mp3"]){
        NSError *plvError = PLVVodErrorWithCode(pictureinpicture_audiomode_not_support);
        if (self.pictureInPictureErrorHandler) {
            self.pictureInPictureErrorHandler(self, plvError);
        }
        return;
    }
    
    if (self.openingPictureInPicture) {
        return;
    }
    self.openingPictureInPicture = YES;
    __weak typeof(self) weakSelf = self;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        weakSelf.openingPictureInPicture = NO;
    });
    
    PLVPictureInPictureManager *pipManager = [PLVPictureInPictureManager sharedInstance];
    pipManager.delegate = self;
    
    if ([self.currentURL isFileURL]) {
        // 本地视频
        dispatch_async(dispatch_get_main_queue(), ^{
            [pipManager startPictureInPictureWithContentURL:self.currentURL
                                                withOptions:[self localAvOption]
                                           displaySuperview:self.view];
            [pipManager setPlaybackRate:weakSelf.playbackRate];
        });
        
    }else {
        // 在线视频
        // 非加密
        if (self.video.isPlain) {
            PLVIJKAVOptions *options = [PLVIJKAVOptions optionsByDefault];
            dispatch_async(dispatch_get_main_queue(), ^{
                [pipManager startPictureInPictureWithContentURL:self.currentURL
                                                    withOptions:options
                                               displaySuperview:self.view];
                [pipManager setPlaybackRate:weakSelf.playbackRate];
            });
        }else {
            // 加密
            [self requestPictureInPictureOptionsWithCompletion:^(PLVIJKAVOptions *avOptions) {
                if (!avOptions) {
                    self.openingPictureInPicture = NO;
                    return;
                }
                // 处理httpdns
                PLVVodSettings *settings = [PLVVodSettings sharedSettings];
                BOOL httpDnsOpen = NO;
                if (self.video.isHls302) {
                    if (self.video.httpDnsMode != PLVVodHttpDnsModeDefault) {
                        httpDnsOpen = self.video.httpDnsMode == PLVVodHttpDnsModeOpen;
                    }else {
                        httpDnsOpen = settings.enableHttpDNS;
                    }
                }
                if (![self.currentURL isFileURL] && httpDnsOpen && !settings.enableIPV6) {
                    if (!self.video.isPlain &&
                        self.video.isHls &&
                        [self.currentURL.path containsString:@"m3u8"]) {
                        NSString *host = self.video.tsHost;
                        // 如果切换了线路，需要重新获取host
                        if (![PLVVodUtil isNilString:self.routeLine]){
                            NSString *newHost = [self getHostWithRouteline:self.routeLine];
                            if (newHost){
                                host = newHost;
                            }
                        }
                        NSString *ip = [[PLVVodHttpDnsManager sharedManager] getIpByHostAsyncInURLFormat:host];
                        if (ip.length) {
                            [avOptions setOptionValue:host forKey:PLVIJKAVOptionKeyTsHost];
                            [avOptions setOptionValue:ip forKey:PLVIJKAVOptionKeyTsIp];
                            PLVVodLogDebug(@"[ts httpdns] 播放使用 ip：%@，替换 host：%@。", ip, host);
                            NSString *sessionid = [PLVVodHttpDnsManager sharedManager].sessionid;
                            [self addElogPlayerParam:@"ip_host" value:host type:@"1"];
                            [self addElogPlayerParam:@"ip_addr" value:ip type:@"1"];
                            [self addElogPlayerParam:@"sessionid" value:sessionid type:@"1"];
                        }
                        
                        NSString *m3u8_host = self.currentURL.host;
                        NSString *m3u8_ip = [[PLVVodHttpDnsManager sharedManager] getIpByHostAsyncInURLFormat:self.currentURL.host];
                        if (m3u8_ip.length){
                            [avOptions setOptionValue:m3u8_host forKey:PLVIJKAVOptionKeyM3u8Host];
                            [avOptions setOptionValue:m3u8_ip forKey:PLVIJKAVOptionKeyM3u8Ip];
                            PLVVodLogDebug(@"[m3u8 httpdns] 播放使用 ip：%@，替换 host：%@。", m3u8_ip, m3u8_host);
                            NSString *sessionid = [PLVVodHttpDnsManager sharedManager].sessionid;

                            [self addElogPlayerParam:@"ip_host_key" value:m3u8_host type:@"1"];
                            [self addElogPlayerParam:@"ip_addr_key" value:m3u8_ip type:@"1"];
                            [self addElogPlayerParam:@"sessionid_key" value:sessionid type:@"1"];
                        }
                    }
                }
                
                dispatch_async(dispatch_get_main_queue(), ^{
                    [pipManager startPictureInPictureWithContentURL:self.currentURL
                                                        withOptions:avOptions
                                                   displaySuperview:self.view];
                    [pipManager setPlaybackRate:weakSelf.playbackRate];
                });
            }];
        }
        
    }
}

/// 关闭画中画功能
- (void)stopPictureInPicture_PIP {
    [[PLVPictureInPictureManager sharedInstance] stopPictureInPicture];
}

#pragma mark PLVPictureInPictureManagerDelegate
- (void)plvPictureInPictureWillStart_PIP {
    [PLVPictureInPictureManager sharedInstance].vodPlayer = self;
    if (self.pictureInPictureStateHandler) {
        self.pictureInPictureStateHandler(self, PLVPictureInPictureStateWillStart);
    }
}

/// 画中画已经开始
- (void)plvPictureInPictureDidStart_PIP {
    self.openingPictureInPicture = NO;
    // 保存播放器使用的流量
    if ([self.mainPlayer isKindOfClass:[PLVIJKFFMoviePlayerController class]]){
        PLVIJKFFMoviePlayerController *player = (PLVIJKFFMoviePlayerController *)self.mainPlayer;
        self.playerTotalFlow += player.trafficStatistic;
    }
    else if ([self.mainPlayer isKindOfClass:[PLVIJKAVMoviePlayerController class]]){
        PLVIJKAVMoviePlayerController *avplayer = (PLVIJKAVMoviePlayerController *)self.mainPlayer;
        self.playerTotalFlow += avplayer.numberOfBytesTransferred;
    }
    
    if (self.pictureInPictureStateHandler) {
        self.pictureInPictureStateHandler(self, PLVPictureInPictureStateDidStart);
    }
}

/// 画中画开启失败
/// @param error 失败错误原因
- (void)plvPictureInPictureFailedToStartWithError_PIP:(NSError *)error {
    NSError *plvError;
    if (error) {
        plvError = PLVVodErrorMakeWithError(pictureinpicture_error, error, ^(NSMutableDictionary *userInfo) {
        });
    }else {
        plvError = PLVVodErrorWithCode(pictureinpicture_systemversion_not_support);
    }
    self.openingPictureInPicture = NO;
    [self reportError:plvError];
    if (self.pictureInPictureErrorHandler) {
        self.pictureInPictureErrorHandler(self, plvError);
    }
}

/// 画中画即将停止
- (void)plvPictureInPictureWillStop_PIP {
    // 保存播放器使用的流量
    self.playerTotalFlow += [PLVPictureInPictureManager sharedInstance].trafficStatistic;
    
    if (self.pictureInPictureStateHandler) {
        self.pictureInPictureStateHandler(self, PLVPictureInPictureStateWillEnd);
    }
}

/// 画中画已经停止
- (void)plvPictureInPictureDidStop_PIP {
    [PLVPictureInPictureManager sharedInstance].vodPlayer = nil;
    if (self.pictureInPictureStateHandler) {
        self.pictureInPictureStateHandler(self, PLVPictureInPictureStateDidEnd);
    }
}

-(void)plvPictureInPicturePlayerPlayingStateDidChange_PIP:(BOOL)playing {
    
}


@end
