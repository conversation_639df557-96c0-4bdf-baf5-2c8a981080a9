//
//  PLVVodPlayerViewController+Position.m
//  PLVVodSDK
//
//  Created by mac on 2020/4/2.
//  Copyright © 2020 POLYV. All rights reserved.
//

#import "PLVVodPlayerViewController+Position.h"
#import "PLVVodPlayerViewController+internal.h"

@implementation PLVVodPlayerViewController (Position)

- (void)setLastPositionInternal:(NSTimeInterval)lastPosition {
    if (!self.preparedToPlay) {
        return;
    }
    
    NSMutableDictionary *lastPositionDict = [[NSUserDefaults standardUserDefaults] dictionaryForKey:PLVVodLastPositionKey].mutableCopy;
    if (!lastPositionDict) lastPositionDict = [NSMutableDictionary dictionary];
    
    // add by libl [判断播放结束的标志为当前播放时间与媒体时长之差在10s 之内] 2018-08-29 start
    // rememberLastPosition 使用的时候有用，默认都保存该值
    if (0.0 < lastPosition && (lastPosition + PLVPlaybackEndTimeInterval) < self.duration /*&& self.rememberLastPosition*/) {
        
        if (self.isPlayURL) {
            //当前正在播放外部url
            NSString *videoUrlKey = self.currentURL.absoluteString;
            if (videoUrlKey != nil && [videoUrlKey isKindOfClass: [NSString class]] && videoUrlKey.length > 0) {
                NSString *md5Key = [PLVVodUtil md5String:videoUrlKey];
                lastPositionDict[md5Key] = @(lastPosition);
            }
        }else {
            //当前正在播放保利威vid视频
            NSString * vidStr = self.video.vid;
            if (vidStr != nil && [vidStr isKindOfClass: [NSString class]] && vidStr.length > 0) {
                lastPositionDict[self.video.vid] = @(lastPosition);
            }
        }
        
    }else if (lastPositionDict.count){
        if (self.isPlayURL) {
            NSString *md5Key = [PLVVodUtil md5String:self.currentURL.absoluteString];
            if (![PLVVodUtil isNilString:md5Key]) {
                [lastPositionDict removeObjectForKey:md5Key];
            }
        }else {
            if (![PLVVodUtil isNilString:self.video.vid]) {
                [lastPositionDict removeObjectForKey:self.video.vid];
            }
        }
    }else {
        return;
    }
    
    [[NSUserDefaults standardUserDefaults] setObject:lastPositionDict forKey:PLVVodLastPositionKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    // add by libl [设置保存播放位置的时间戳] 2018-10-30 start
    [self setRecordTimestampWithPosition:lastPosition];
    // add end
}

- (void)setRecordTimestampWithPosition:(NSTimeInterval )lastPosition{
    NSMutableDictionary *lastPositionTimeDict = [[NSUserDefaults standardUserDefaults] dictionaryForKey:PLVVodLastPositionTimestampKey].mutableCopy;
    if (!lastPositionTimeDict) lastPositionTimeDict = [NSMutableDictionary dictionary];
    
    // 默认保存记录
    if (0.0 < lastPosition && (lastPosition + PLVPlaybackEndTimeInterval) < self.duration /*&& self.rememberLastPosition*/) {
        NSTimeInterval curTime = [[NSDate date] timeIntervalSince1970]*1000;
        
        if (self.isPlayURL) {
            NSString *videoUrlKey = self.currentURL.absoluteString;
            if (videoUrlKey != nil && [videoUrlKey isKindOfClass: [NSString class]] && videoUrlKey.length > 0) {
                NSString *md5Key = [PLVVodUtil md5String:videoUrlKey];
                lastPositionTimeDict[md5Key] = @(curTime);
            }
        }else {
            NSString * vidStr = self.video.vid;
            if (vidStr != nil && [vidStr isKindOfClass: [NSString class]] && vidStr.length > 0) {
                lastPositionTimeDict[self.video.vid] = @(curTime);
            }
        }
        
    }else if (lastPositionTimeDict.count) {
        if (self.isPlayURL) {
            NSString *md5Key = [PLVVodUtil md5String:self.currentURL.absoluteString];
            if (![PLVVodUtil isNilString:md5Key]) {
                [lastPositionTimeDict removeObjectForKey:md5Key];
            }
        }else {
            if (![PLVVodUtil isNilString:self.video.vid]) {
                [lastPositionTimeDict removeObjectForKey:self.video.vid];
            }
        }
    }else {
        return;
    }
    
    [[NSUserDefaults standardUserDefaults] setObject:lastPositionTimeDict forKey:PLVVodLastPositionTimestampKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

- (NSTimeInterval)lastPositionInternal {
    NSTimeInterval lastPosition = 0.0;
    NSDictionary *lastPositionDict = [[NSUserDefaults standardUserDefaults] dictionaryForKey:PLVVodLastPositionKey];
    if (lastPositionDict.count) {
        
        if (self.isPlayURL) {
            NSString *videoUrlKey = self.currentURL.absoluteString;
            if (videoUrlKey != nil && [videoUrlKey isKindOfClass: [NSString class]] && videoUrlKey.length > 0) {
                NSString *md5Key = [PLVVodUtil md5String:videoUrlKey];
                lastPosition = [lastPositionDict[md5Key] doubleValue];
            }
        }else {
            NSString *vidStr = self.video.vid;
            if (vidStr != nil && [vidStr isKindOfClass: [NSString class]] && vidStr.length > 0) {
                lastPosition = [lastPositionDict[vidStr] doubleValue];
            }
        }
        if (isnan(lastPosition))
            lastPosition = 0.0;
    }
    return lastPosition;
}

@end
