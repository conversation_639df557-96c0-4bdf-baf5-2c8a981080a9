//
//  PLVVodPlayerViewController+Token.h
//  PLVVodSDK
//
//  Created by polyv on 2025/6/4.
//  Copyright © 2025 POLYV. All rights reserved.
//

#import <PLVVodSDK/PLVVodSDK.h>
#import <PLVIJKPlayer/PLVIJKPlayer.h>

NS_ASSUME_NONNULL_BEGIN

@interface PLVVodPlayerViewController (Token)

- (void)requestTokenWithCompletion:(void (^)(PLVIJKFFOptions *ijkOptions, NSString *playToken, NSString *tokenHost))completion;

- (void)requestPictureInPictureOptionsWithCompletion:(void (^)(PLVIJKAVOptions *avOptions))completion;

@end

NS_ASSUME_NONNULL_END
