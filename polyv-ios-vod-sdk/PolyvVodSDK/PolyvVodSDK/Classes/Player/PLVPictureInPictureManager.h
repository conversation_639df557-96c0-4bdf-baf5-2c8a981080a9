//
//  PLVPictureInPictureManager.h
//  PLVVodSDK
//
//  Created by junotang on 2022/4/7.
//  Copyright © 2022 POLYV. All rights reserved.
//
//  画中画功能管理类

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol PLVPictureInPictureRestoreDelegate;

@interface PLVPictureInPictureManager : NSObject

#pragma mark - [ 属性 ]

/// avplayer的播放状态
@property (nonatomic, assign, readonly) NSInteger playbackState;

#pragma mark 可配置项

/// 画中画恢复前的用户界面逻辑代理
@property (nonatomic, weak, nullable) id <PLVPictureInPictureRestoreDelegate> restoreDelegate;

/// 画中画小窗是否开启
@property (nonatomic, assign, readonly) BOOL pictureInPictureActive;

/// 画中画当前播放的视频vid
@property (nonatomic, assign, readonly) NSString *currentPlaybackVid;

/// 隐藏快退、快进交互按钮  YES：隐藏  NO：展示
@property (nonatomic, assign) BOOL requiresLinearPlayback;


#pragma mark - [ 方法 ]

/// 单例方法
+ (instancetype)sharedInstance;

/// 停止画中画
- (void)stopPictureInPicture;

/// 设置画中画播放器的播放速率
/// @param rate 速率【0，2】
- (void)setPlaybackRate:(CGFloat)rate;

@end

#pragma mark - [ 代理方法 ]

/// 画中画恢复代理
@protocol PLVPictureInPictureRestoreDelegate <NSObject>

@optional

/// 点击画中画恢复按钮，画中画关闭之前的用户界面恢复实现
/// @param completionHandler completionHandler
- (void)plvPictureInPictureRestoreUserInterfaceForPictureInPictureStopWithCompletionHandler:(void (^)(BOOL restored))completionHandler;

@end

NS_ASSUME_NONNULL_END
