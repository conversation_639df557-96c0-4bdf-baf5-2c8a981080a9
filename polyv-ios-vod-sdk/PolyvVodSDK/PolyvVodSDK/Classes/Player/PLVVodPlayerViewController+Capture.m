//
//  PLVVodPlayerViewController+Capture.m
//  PLVVodSDK
//
//  Created by mac on 2020/4/2.
//  Copyright © 2020 POLYV. All rights reserved.
//

#import "PLVVodPlayerViewController+Capture.h"
#import "PLVVodPlayerViewController+internal.h"


@implementation PLVVodPlayerViewController (Capture)

- (void)screenCapturedEventWithNoti:(NSNotification *)noti{
    UIScreen * sc = [UIScreen mainScreen];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunguarded-availability-new"
    if (/*@available(iOS 11.0, *)*/ [UIDevice currentDevice].systemVersion.integerValue >= 11) {
        if (sc.isCaptured) {
            
            [self startPreventScreenCapture];
        }else{
            [self stopPreventScreenCapture];
        }
    }
#pragma clang diagnostic pop
}

- (void)audioSessionRouteChangeWithNoti:(NSNotification *)noti{
    AVAudioSessionRouteDescription * routeDes = [AVAudioSession sharedInstance].currentRoute;
    for (AVAudioSessionPortDescription * subDes in routeDes.outputs) {
        if ([subDes.portType isEqualToString:AVAudioSessionPortAirPlay] ||
            [subDes.portType isEqualToString:@"AirPlay"]) {
            [self startPreventScreenCapture];
            
        }else if ([subDes.portType isEqualToString:AVAudioSessionPortHDMI] ||
                  [subDes.portType isEqualToString:@"HDMIOutput"]) {
            [self startPreventScreenCapture];
            
        }else if([subDes.portType isEqualToString:AVAudioSessionPortBuiltInSpeaker] ||
                 [subDes.portType isEqualToString:@"Speaker"]){
            [self stopPreventScreenCapture];
            
        }
    }
}

- (void)startPreventScreenCapture{
    // 此时播放器是否播放中
    self.isPlayingWhenCaptureStart = self.mainPlayer.isPlaying;
    
    // 修改为'正被录屏'状态
    self.videoCapturing = YES;
    
    // 暂停播放
    [self pause];
    
    // 黑屏
    self.mainPlayer.view.hidden = YES;
    self.subPlayer.view.hidden = YES;
    self.adPlayer.view.hidden = YES;
    
    // 出现提示
    [self showVideoProtectMessage];
}

- (void)stopPreventScreenCapture{
    // 修改为'非正被录屏'状态
    self.videoCapturing = NO;
    
    // 若原先处于播放中状态，则恢复播放
    if (self.isPlayingWhenCaptureStart) {
        [self play];
    }
    
    // 取消黑屏
    self.mainPlayer.view.hidden = NO;
    self.subPlayer.view.hidden = NO;
    self.adPlayer.view.hidden = NO;
}

- (void)showVideoProtectMessage{
    UIAlertController * alertController = [UIAlertController alertControllerWithTitle:@"视频暂时无法播放"
                                                                              message:@"停止录屏或投屏操作才能继续播放视频"
                                                                       preferredStyle:UIAlertControllerStyleAlert];
    [alertController addAction:[UIAlertAction actionWithTitle:@"知道了" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:^{}];
    }]];
    [self presentViewController:alertController animated:YES completion:^{
        
    }];
}

- (void)configVideoCaptureProtect:(BOOL)videoCapturePretect{
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunguarded-availability-new"
    if (/*@available(iOS 11.0, *)*/ [UIDevice currentDevice].systemVersion.integerValue >= 11) { // iOS 11以上
        [self screenCapturedEventWithNoti:nil];
        [[NSNotificationCenter defaultCenter]addObserver:self
                                                selector:@selector(screenCapturedEventWithNoti:)
                                                    name:UIScreenCapturedDidChangeNotification
                                                  object:nil];
    }else{                        // iOS 11以下
        [self audioSessionRouteChangeWithNoti:nil];
        [[NSNotificationCenter defaultCenter]addObserver:self
                                                selector:@selector(audioSessionRouteChangeWithNoti:)
                                                    name:AVAudioSessionRouteChangeNotification
                                                  object:nil];
    }
#pragma clang diagnostic pop
}

@end
