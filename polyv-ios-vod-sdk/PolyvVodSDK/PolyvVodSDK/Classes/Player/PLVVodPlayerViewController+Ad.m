//
//  PLVVodPlayerViewController+Ad.m
//  PLVVodSDK
//
//  Created by mac on 2020/4/2.
//  Copyright © 2020 POLYV. All rights reserved.
//

#import "PLVVodPlayerViewController+internal.h"
#import "PLVVodPlayerViewController+Ad.h"
#import "PLVVodReachability.h"

@implementation PLVVodPlayerViewController (Ad)

- (void)setupSubPlayer:(PLVIJKFFMoviePlayerController *)ijkPlayer {
    //NSLog(@"xx_%s - %@", __FUNCTION__, [NSThread currentThread]);
    
    // add observer
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(subPlayerDidFinish:)
                                                 name:IJKMPMoviePlayerPlaybackDidFinishNotification
                                               object:ijkPlayer];
    
    // add view
    ijkPlayer.view.autoresizingMask = UIViewAutoresizingFlexibleWidth|UIViewAutoresizingFlexibleHeight;
    ijkPlayer.view.frame = self.view.bounds;
    dispatch_async(dispatch_get_main_queue(), ^{
        ijkPlayer.scalingMode = (IJKMPMovieScalingMode)self.scalingMode;
    });
    
    // 禁用 Airplay
    ijkPlayer.allowsMediaAirPlay = NO;
    [ijkPlayer setPauseInBackground:!self.enableTeaserBackgroundPlayback];
    
    self.view.autoresizesSubviews = YES;
    if ([self.view.subviews containsObject:self.mainPlayer.view]) {
        [self.view insertSubview:ijkPlayer.view aboveSubview:self.mainPlayer.view];
    } else {
        [self.view insertSubview:ijkPlayer.view atIndex:0];
    }
    
    [ijkPlayer prepareToPlay];
    [ijkPlayer play];
}

- (void)subPlayerDidFinish:(NSNotification *)notification {
    //NSLog(@"%s - %@", __FUNCTION__, [NSThread currentThread]);
    PLVVodLogInfo(@"片头播放结束");
    
    if (self.subPlayerFinishHandler) {
        self.subPlayerFinishHandler();
        self.subPlayerFinishHandler = nil;
    }
    
    // 移除
    __weak typeof(self) weakSelf = self;
    dispatch_async(dispatch_get_main_queue(), ^{
        [weakSelf.teaserImageView removeFromSuperview];
        weakSelf.teaserImageView = nil;
        [weakSelf.subPlayer.view removeFromSuperview];
        [weakSelf.subPlayer shutdown];
        weakSelf.subPlayer = nil;
    });
}

// 片头播放逻辑处理
- (BOOL)handleTeaserPlay{
    BOOL handled = NO;

    if (self.enableTeaser && self.teaserState >= PLVVodAssetStateLoading && self.teaserState < PLVVodAssetStateFinished) {
        // 播放时长足够则播正片
        // 优先获取本地片头配置
        NSUInteger tTeaserDuration = self.teaserDuration > 0 ? self.teaserDuration: self.video.teaserDuration;
        // 设置的片头时长 > 片头视频时长，以片头视频实际时长为准
        if (self.subPlayer.currentPlaybackTime > 0 && self.teaserDuration > 0){
            tTeaserDuration = tTeaserDuration > self.subPlayer.duration ? self.subPlayer.duration: tTeaserDuration;
        }
        
        if (self.subPlayer.currentPlaybackTime >= /*self.video.teaserDuration*/tTeaserDuration && self.teaserState != PLVVodAssetStateFinished) {
            self.teaserState = PLVVodAssetStateFinished;
            if (self.teaserCompletion) self.teaserCompletion(YES);
            [self subPlayerDidFinish:nil];
            
            handled = YES;
            return handled;
        }
        
        // 等待时长足够则播正片
        if (self.viewerStayDuration >= /*self.video.teaserDuration*/tTeaserDuration && self.teaserState != PLVVodAssetStateFinished) {
            self.teaserState = PLVVodAssetStateFinished;
            if (self.teaserCompletion) self.teaserCompletion(YES);
            [self subPlayerDidFinish:nil];
            self.viewerStayDuration = 0;
            
            handled = YES;
            return handled;
        }
    }
    
    return handled;
}

#pragma mark -- 播放片头
/// 播放片头
- (void)playTeaserWithVideo:(PLVVodVideo *)video completion:(void (^)(BOOL finish))completion {
    __weak typeof(self) weakSelf = self;
    void (^finishHandler)(void) = ^{
        weakSelf.teaserState = PLVVodAssetStateFinished;
    };
    
    //播放视频前需要关掉片头播放器，避免上一个视频的片头还在播放
    dispatch_async(dispatch_get_main_queue(), ^{
        [weakSelf.teaserImageView removeFromSuperview];
        weakSelf.teaserImageView = nil;
        if (self.subPlayer) {
            [self.subPlayer.view removeFromSuperview];
            [self.subPlayer shutdown];
            self.subPlayer = nil;
        }
    });
    
    // 维护变量
    self.viewerStayDuration = 0;
    self.teaserState = PLVVodAssetStateLoading;
    NSString *teaserUrl = nil;
    // 优先判断自定义片头设置
    if (self.teaserDuration > 0 && self.teaserUrl.length){
        if (!self.enableTeaser) {
            finishHandler();
            if (completion) completion(NO);
            return;
        }
        
        teaserUrl = self.teaserUrl;
    }
    else{
        if (!video.teaserShow || !self.enableTeaser || video.teaserDuration <= 0 ) {
            finishHandler();
            if (completion) completion(NO);
            return;
        }
        
        if ([PLVVodUtil isNilString:video.teaser]) {
            PLVVodLogWarn(@"[player] -- 片头为空");
            finishHandler();
            if (completion) completion(NO);
            return;
        }
        
        // add 2020.12.09 无网络跳过片头
        if (PLVVodNotReachable == [PLVVodReachability sharedReachability].currentReachabilityStatus){
            PLVVodLogWarn(@"[player] -- 网络不可用，跳过片头播放");
            finishHandler();
            if (completion) completion(NO);
            return;
        }
        
        // add 2020.11.20 续播跳过片头播放
        if (self.rememberLastPosition && self.lastPosition > 0){
            PLVVodLogWarn(@"[player] -- 续播跳过片头播放");
            finishHandler();
            if (completion) completion(NO);
            return;
        }
        
        
        // add 2020.11.16 新增本地视频片头播放逻辑,默认可播放
        BOOL willPlayLocal = [self checkVideoWillPlayLocal:video];
        if (willPlayLocal && !self.enableLocalTeaser){
            PLVVodLogWarn(@"[player] -- 本地视频，不允许播放片头");
            finishHandler();
            if (completion) completion(NO);
            return;
        }
        
        teaserUrl = video.teaser;
    }
    
    // 清洗字符串
    NSString *url = [teaserUrl stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    
    NSURL *URL = [NSURL URLWithString:url];
    NSString *extension = URL.pathExtension.lowercaseString;
    BOOL isVideo = [extension isEqualToString:@"mp4"] || [extension isEqualToString:@"flv"];
    BOOL isImage = [extension isEqualToString:@"png"] || [extension isEqualToString:@"jpg"] || [extension isEqualToString:@"gif"];
    if (!(isVideo || isImage)) {
        PLVVodLogError(@"%@, 片头格式不支持", url.lastPathComponent);
        NSError *plvError = PLVVodErrorMake(teaser_type_illegal, ^(NSMutableDictionary *userInfo) {
            userInfo[NSURLErrorKey] = URL;
        });
        [self reportError:plvError];
        finishHandler();
        if (completion) completion(NO);
        return;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        if (isVideo) { // 无控制，自动播放
            
            PLVVodLogInfo(@"[player] --开始播放视频片头");
            PLVIJKFFOptions *options = [PLVIJKFFOptions optionsByDefault];
            [options setFormatOptionValue:[PLVVodUtil ijkplayerUserAgent] forKey:@"user-agent"];
            [options setFormatOptionIntValue:1 forKey:@"dns_cache_clear"];
            self.subPlayer = [[PLVIJKFFMoviePlayerController alloc] initWithContentURL:URL withOptions:options];
            [self runAfterViewDidLoad:^{
                [weakSelf setupSubPlayer:weakSelf.subPlayer];
                weakSelf.teaserState = PLVVodAssetStatePlaying;
            }];
        } else if (isImage) {
            PLVVodLogInfo(@"[player] --开始播放图片片头");
            UIImageView *teaserView = [[UIImageView alloc] init];
            teaserView.contentMode = UIViewContentModeScaleAspectFit;
            teaserView.backgroundColor = [UIColor blackColor];
            self.teaserImageView = teaserView;
            
            // UI
            [self runAfterViewDidLoad:^{
                weakSelf.teaserImageView.frame = weakSelf.view.bounds;
                weakSelf.teaserImageView.autoresizingMask = UIViewAutoresizingFlexibleHeight | UIViewAutoresizingFlexibleWidth;
                weakSelf.view.autoresizesSubviews = YES;
                if ([weakSelf.view.subviews containsObject:weakSelf.mainPlayer.view]) {
                    [weakSelf.view insertSubview:weakSelf.teaserImageView aboveSubview:weakSelf.mainPlayer.view];
                } else {
                    [weakSelf.view insertSubview:weakSelf.teaserImageView atIndex:0];
                }
            }];
            
            NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:URL];
            [PLVVodNetworking setupHTTPHeaderWithRequest:request];
            [[[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    if (error){
                        //
                        [weakSelf subPlayerDidFinish:nil];
                        finishHandler();
                        if (completion) completion(NO);
                    }
                    else{
                        if ([extension isEqualToString:@"gif"]){
                            [weakSelf.teaserImageView plv_ShowGifImageWithData:data];
                        }
                        else{
                            weakSelf.teaserImageView.image = [UIImage imageWithData:data];
                        }
                        weakSelf.teaserState = PLVVodAssetStatePlaying;
                    }
                });
            }] resume];
        }
        
        self.teaserCompletion = completion;
    });
}

/// 配置广告播放器
- (void)configAdPlayerWithVideo:(PLVVodVideo *)video {
    // 若已存在广告播放器VC
    if (self.adPlayer) {
        // 停止 + 销毁
        [self.adPlayer hideAd];
        [self.adPlayer.view removeFromSuperview];
        [self.adPlayer willMoveToParentViewController:nil];
        [self.adPlayer removeFromParentViewController];
    }
      
    self.adPlayer = [[PLVVodAdPlayerViewController alloc] init];
    self.adPlayer.pauseAdCenterXRatio = self.pauseAdCenterXRatio;
    self.adPlayer.pauseAdCenterYRatio = self.pauseAdCenterYRatio;
    self.adPlayer.pauseAdWidthRatio = self.pauseAdWidthRatio;
    self.adPlayer.pauseAdHeightRatio = self.pauseAdHeightRatio;
    
    self.adPlayer.ads = video.ads;
    if (!self.adPlayer.ads.count || !self.enableAd) {
        return;
    }
    [self.adPlayer.playButton addTarget:self action:@selector(play) forControlEvents:UIControlEventTouchUpInside];
    // UI
    __weak typeof(self) weakSelf = self;
    [self runAfterViewDidLoad:^{
        weakSelf.adPlayer.view.autoresizingMask = UIViewAutoresizingFlexibleWidth|UIViewAutoresizingFlexibleHeight;
        [weakSelf.view insertSubview:weakSelf.adPlayer.view aboveSubview:weakSelf.customMaskView];
        //[weakSelf.customMaskView addSubview:weakSelf.adPlayer.view];
        [weakSelf addChildViewController:weakSelf.adPlayer];
        UIView *adView = weakSelf.adPlayer.view;
        adView.translatesAutoresizingMaskIntoConstraints = NO;
        NSDictionary *views = NSDictionaryOfVariableBindings(adView);
        [weakSelf.view addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"H:|[adView]|" options:0 metrics:nil views:views]];
        [weakSelf.view addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"V:|[adView]|" options:0 metrics:nil views:views]];
    }];
}

@end
