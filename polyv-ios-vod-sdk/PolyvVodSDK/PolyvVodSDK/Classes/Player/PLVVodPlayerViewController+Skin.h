//
//  PLVVodPlayerViewController+Skin.h
//  PLVVodSDK
//
//  Created by polyv on 2025/6/3.
//  Copyright © 2025 POLYV. All rights reserved.
//

#import <PLVVodSDK/PLVVodSDK.h>

NS_ASSUME_NONNULL_BEGIN

@interface PLVVodPlayerViewController (Skin)

- (void)addSkinGestures;

- (void)attachControlWithKeyPath:(NSString *)keyPath handler:(void (^)(void))handler;

- (void)attachPlayerControl;

- (void)updateConstraintsForRotate;

- (void)makePlayerConstraintsWithRootView;

- (void)makePlayerConstraintsWithPlaceholder;

- (void)updateUIWithTimer;
- (void)syncPlayPauseButton;
- (NSString *)timeDescription_Skin;

- (void)playPauseAction_Skin:(UIButton *)sender;
- (void)playbackSliderTouchDownAction_Skin:(UISlider *)sender;
- (void)playbackSliderValueChangeAction_Skin:(UISlider *)sender;
- (void)playbackSliderTouchUpCancelAction_Skin:(UISlider *)sender;
- (void)brightnessAction_Skin:(UISlider *)sender;
- (void)volumeAction_Skin:(UISlider *)sender;


@end

NS_ASSUME_NONNULL_END
