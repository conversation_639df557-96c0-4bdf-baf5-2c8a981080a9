//
//  PLVVodPlayerViewController+Reconnect.m
//  PLVVodSDK
//
//  Created by mac on 2020/4/2.
//  Copyright © 2020 POLYV. All rights reserved.
//

#import "PLVVodPlayerViewController+Reconnect.h"
#import "PLVVodPlayerViewController+internal.h"
#import "PLVVodPlayerViewController+Log.h"
#import "PLVVodPlayerViewController+IJK.h"
#import "PLVVodReachability.h"

@implementation PLVVodPlayerViewController (Reconnect)

#pragma mark -- 在线播放重试机制，先切换码率/再切换cdn各码率
- (void)playRetryWithFinishHandle:(void(^)(void))filishHandle{
    // 如果是本地播放，不重试;源视频播放，不重试
    if (!self.localPlayback && !self.video.keepSource){
        
        // 如果网络断开，直接报错不重试
        if (PLVVodNotReachable == [PLVVodReachability sharedReachability].currentReachabilityStatus){
            NSError *plvError = PLVVodErrorMake(network_unreachable, ^(NSMutableDictionary *userInfo) {
            });
            if (self.playerErrorHandler){
                self.playerErrorHandler(self, plvError);
                return;
            }
        }
        
        // 视频模式才有码率切换
        if (self.playbackMode != PLVVodPlaybackModeAudio){
            for (int i=1; i<=self.video.qualityCount; i++){
                //
                if ([self.hasTryQuality objectForKey:@(i)])
                    continue;
                
                if ( i<= self.video.filesizes.count){
                    int fileSize = [[self.video.filesizes objectAtIndex:i-1] intValue];
                    if (fileSize > 0){
                        [self switchQuality:i];
                        PLVVodLogDebug(@"码率重试切换: %d vid:%@", i, self.video.vid);

                        [self.hasTryQuality setObject:@"1" forKey:@(i)];
                        
                        return;
                    }
                }
                
                // 记录重试记录
                [self.hasTryQuality setObject:@"1" forKey:@(i)];
            }
        }
        
        // 线路切换
        if (self.playbackMode == PLVVodPlaybackModeAudio){
            for (int i=0; i<self.video.availableAudioRouteLines.count; i++){
                
                if ([self.hasTryCdns objectForKey:@(i)]){
                    continue;
                }
                
                NSString *cdn = [self.video.availableAudioRouteLines objectAtIndex:i];
                [self setRouteLine:cdn];
                PLVVodLogDebug(@"线路重试切换: %@ vid:%@", cdn, self.video.vid);

                [self.hasTryCdns setObject:@"1" forKey:cdn];

                return;
            }
        }
        else{
            
            // 视频线路切换
            for (int i=0; i<self.video.availableRouteLines.count; i++){
                
                if ([self.hasTryCdns objectForKey:self.video.availableRouteLines[i]]){
                    continue;
                }
                
                NSString *cdn = [self.video.availableRouteLines objectAtIndex:i];
                [self setRouteLine:cdn];
                
                PLVVodLogDebug(@"线路重试切换: %@ vid:%@", cdn, self.video.vid);
                [self.hasTryCdns setObject:@"1" forKey:cdn];
                
                // 清空码率重试记录
                [self.hasTryQuality removeAllObjects];
                // 保存当前重试记录
                [self.hasTryQuality setObject:@"1" forKey:@(self.quality)];
                
                return;
            }
        }
        
        // 重试完毕，回调处理错误
        if (filishHandle){
            filishHandle();
        }
        
        return;
    }
    
    // 重试完毕，回调处理错误
    if (filishHandle){
        filishHandle();
    }
    
    return;
}

#pragma mark -- 播放失败处理
- (void)handleIjkPlayError{
    if (self.localPlayback){
        // 本地视频加载失败
        NSError *plvError = PLVVodErrorMake(play_local_video_error, ^(NSMutableDictionary *userInfo) {
        });
        if (self.playerErrorHandler) self.playerErrorHandler(self, plvError);
        [self alalyzeLocalVideoWithCompletion:^(NSError *error) {
            // Elog 统计
            if (error){
                [self reportElogWithError:error];
            }
        }];
    }
    else if (self.video.keepSource){
        // 源视频播放失败
        // 统一回调网络错误
        NSError *plvError = PLVVodErrorMake(network_error, ^(NSMutableDictionary *userInfo) {
        });
        if (self.playerErrorHandler) self.playerErrorHandler(self, plvError);
        // 源视频播放失败，直接上报QoS错误（不进行重试）
        [self reportVideoLoadFailureQosError];
        [self reportElogWithError:plvError];
    }
    else{
        // 播放重试
        NSError *plvError = PLVVodErrorMake(network_error, ^(NSMutableDictionary *userInfo) {});
        [self reportElogWithError:plvError];
        
        [self playRetryWithFinishHandle:^{
            PLVVodLogDebug(@"重试播放完毕");
            // 重试完毕后，仍然播放失败处理
            if (!self.localPlayback){
                // 错误回调处理
                NSError *plvError = PLVVodErrorMake(network_error, ^(NSMutableDictionary *userInfo) {
                });
                if (self.playerErrorHandler) self.playerErrorHandler(self, plvError);
                // 只有在重试完毕后仍然失败才上报QoS错误（表示所有线路都失败了）
                [self reportVideoLoadFailureQosError];
                // IJK 中记录最后一次请求的网络状态
                [self reportElogWithError:plvError];
            }
        }];
    }
}

// 分析本地视频播放失败原因
- (void)alalyzeLocalVideoWithCompletion:(void (^)(NSError *error))completion{
    // - 离线：分析m3u8、key、ts文件是否完整
    NSError *error = nil;
    BOOL local = self.currentURL.fileURL;
    if (local) {
        //NSLog(@"文件路径");
        BOOL reachable = [self.currentURL checkResourceIsReachableAndReturnError:&error];
        if (!reachable) {
            PLVVodLogError(@"本地资源不可达，%@", error);
            NSError *plvError = PLVVodErrorMakeWithError(local_file_unaccessible, error, ^(NSMutableDictionary *userInfo) {
                
            });
            if (completion) completion(plvError);
            error = nil;
        }
        PLVVodLogDebug(@"reachable: %s", reachable?"YES":"NO");
    }
    
    //
    if (self.video.isHls){
        // 检查m3u8 文件
        [PLVVodHlsHelper checkM3u8WithURL:self.currentURL completion:^(NSError *error) {
            if (error){
                if (completion) completion (error);
            }
            else{
                // 检查key文件
                if (!self.video.isPlain){
                    if (local) {
                        NSString *keyPath = [self.currentURL.path.stringByDeletingPathExtension stringByAppendingPathExtension:@"key"];
                        NSDictionary *keyAttributes = [[NSFileManager defaultManager] attributesOfItemAtPath:keyPath error:&error];
                        NSInteger fileSize = (NSInteger)keyAttributes.fileSize;
                        PLVVodLogDebug(@"filesize: %zd", fileSize);
                        if (fileSize != 48
                            && fileSize != 64 // new key format
                            ) {
                            PLVVodLogError(@"本地视频播放秘钥错误");
                            NSError *plvError = PLVVodErrorMake(local_key_illegal, ^(NSMutableDictionary *userInfo) {
                                
                            });
                            if (completion) completion(plvError);
                        }
                    }
                }
            }
        }];
    }
}

@end
