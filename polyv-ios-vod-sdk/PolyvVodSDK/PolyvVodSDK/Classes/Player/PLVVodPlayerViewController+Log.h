//
//  PLVVodPlayerViewController+Log.h
//  PolyvVodSDK
//
//  Created by mac on 2020/4/2.
//  Copyright © 2020 POLYV. All rights reserved.
//

#import <PLVVodSDK/PLVVodSDK.h>
#import "PLVVodDefines.h"

NS_ASSUME_NONNULL_BEGIN

@interface PLVVodPlayerViewController (Log)

/// 上报seek 事件
- (void)sendSeekEvent;

/// 发送customKeyToken事件
- (void)sendCustomKeyTokenEvent;

/// 发送loadTimeout事件
- (void)sendLoadTimeOutEvent;

/// 发送httpdnsChangeCdn事件
- (void)sendHttpDnsChangeEvent;

/// 发送加密视频使用HttpDns的事件
- (void)sendHttpDnsPlayVideoEvent;

/// 发送播放器起播事件埋点数据
- (void)sendTraceMediaPlayerStartPlayEvent;

/// 上报错误事件
- (void)reportElogWithError:(NSError *)error;

/// 添加播放参数
- (void)addElogPlayerParam:(NSString *)name value:(NSString *)value type:(NSString *)type;

/// 发送viewlog
- (void)reportViewLog;

/// QOS 统计
- (void)reportQosPlayError:(NSError *)errorInfo errorType:(PLVVodQosErrorType)type;

/// 上报ijkplayer 相关网络错误
- (void)sendIJKNetInfomation:(NSDictionary *)infomation;

@end

NS_ASSUME_NONNULL_END
