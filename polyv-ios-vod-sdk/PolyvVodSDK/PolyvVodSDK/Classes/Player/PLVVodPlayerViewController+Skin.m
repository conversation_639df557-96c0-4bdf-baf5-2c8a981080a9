//
//  PLVVodPlayerViewController+Skin.m
//  PLVVodSDK
//
//  Created by polyv on 2025/6/3.
//  Copyright © 2025 POLYV. All rights reserved.
//

#import "PLVVodPlayerViewController+Skin.h"
#import "PLVVodPlayerViewController+internal.h"
#import "PLVVodPlayerViewController+Switch.h"

@implementation PLVVodPlayerViewController (Skin)

- (void)addSkinGestures{
    // 添加手势
    UIPanGestureRecognizer *pan = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(panAction:)];
    pan.delegate = self;
    pan.maximumNumberOfTouches = 1;
    pan.delaysTouchesBegan = YES;
    pan.delaysTouchesEnded = YES;
    pan.cancelsTouchesInView = YES;
    [self.view addGestureRecognizer:pan];
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapAction:)];
    tap.delegate = self;
    tap.numberOfTouchesRequired = 1;
    tap.numberOfTapsRequired = 1;
    //tap.delaysTouchesBegan = YES;
    [self.view addGestureRecognizer:tap];
    
    UITapGestureRecognizer *doubleTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(doubleTapAction:)];
    doubleTap.delegate = self;
    doubleTap.numberOfTouchesRequired = 1;
    doubleTap.numberOfTapsRequired = 2;
    //doubleTap.delaysTouchesBegan = YES;
    [self.view addGestureRecognizer:doubleTap];
    
    [tap requireGestureRecognizerToFail:doubleTap];
    
    UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(longPressAction:)];
    [self.view addGestureRecognizer:longPress];
}

#pragma mark - UIGestureRecognizerDelegate

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    UIView *touchView = touch.view;
    if ([touchView isKindOfClass:[UIControl class]]) {
        return NO;
    }
    if (![touchView.backgroundColor isEqual:[UIColor clearColor]]) {
        //NSLog(@"%@，非透明，跳过手势", touch.view);
        return NO;
    }
    if (touchView.gestureRecognizers.count) {
        //NSLog(@"touch view gesture: %@", touchView.gestureRecognizers);
        return NO;
    }
    if ([self.doNotReceiveGestureViews containsObject:touchView]) {
        return NO;
    }
    if (self.enableAd && (NULL != self.adPlayer) && (self.adPlayer.state != PLVVodAssetStateFinished)) {
        return NO;
    }
    if (self.enableTeaser && self.teaserState != PLVVodAssetStateFinished) {
        return NO;
    }
    if ([PLVPictureInPictureManager sharedInstance].pictureInPictureActive) {
        return NO;
    }
    
    //NSLog(@"touch view: %@", touch.view);
    
    return YES;
}

- (void)panAction:(UIPanGestureRecognizer *)pan {
    // 手势方向
    PLVVodGestureType gestureType = self.gestureType;
    
    // 手势所在视图
    UIView *gestureView = pan.view;
    
    // 位置
    CGPoint location = [pan locationInView:gestureView];
    
    // 速率
    CGPoint velocty = [pan velocityInView:gestureView];
    
    // 判断是垂直移动还是水平移动
    switch (pan.state) {
        case UIGestureRecognizerStateBegan: { // 开始移动
            // 使用绝对值来判断移动的方向
            CGFloat x = fabs(velocty.x);
            CGFloat y = fabs(velocty.y);
            if (x > y) { // 水平移动
                if (velocty.x > 0) { // 右滑
                    gestureType = PLVVodGestureTypeRightPan;
                } else if (velocty.x < 0) { // 左滑
                    gestureType = PLVVodGestureTypeLeftPan;
                }
            } else if (x < y){ // 垂直移动
                if (location.x > gestureView.bounds.size.width / 2) { // 右侧
                    if (velocty.y > 0) {
                        gestureType = PLVVodGestureTypeRightSideDownPan;
                    } else if (velocty.y < 0){
                        gestureType = PLVVodGestureTypeRightSideUpPan;
                    }
                }else { // 左侧
                    if (velocty.y > 0) {
                        gestureType = PLVVodGestureTypeLeftSideDownPan;
                    } else if (velocty.y < 0){
                        gestureType = PLVVodGestureTypeLeftSideUpPan;
                    }
                    [self startRecordSeekEvent];
                }
            }
        } break;
        case UIGestureRecognizerStateChanged: {
            switch (gestureType) {
                case PLVVodGestureTypeRightSideDownPan:
                case PLVVodGestureTypeRightSideUpPan:{
                    if (velocty.y > 0) {
                        gestureType = PLVVodGestureTypeRightSideDownPan;
                    } else if (velocty.y < 0){
                        gestureType = PLVVodGestureTypeRightSideUpPan;
                    }
                }break;
                case PLVVodGestureTypeLeftSideDownPan:
                case PLVVodGestureTypeLeftSideUpPan:{
                    if (velocty.y > 0) {
                        gestureType = PLVVodGestureTypeLeftSideDownPan;
                    } else if (velocty.y < 0){
                        gestureType = PLVVodGestureTypeLeftSideUpPan;
                    }
                }break;
                case PLVVodGestureTypeLeftPan:
                case PLVVodGestureTypeRightPan:{
                    if (velocty.x > 0) { // 右滑
                        gestureType = PLVVodGestureTypeRightPan;
                    } else if (velocty.x < 0) { // 左滑
                        gestureType = PLVVodGestureTypeLeftPan;
                    }
                }break;
                default:{}break;
            }
        } break;
        default: {} break;
    }
    self.gestureType = gestureType;
    if (self.gestureCallback) self.gestureCallback(self, pan, gestureType);
}

- (void)tapAction:(UIGestureRecognizer *)tap {
    self.gestureType = PLVVodGestureTypeTap;
    if (self.gestureCallback) self.gestureCallback(self, tap, self.gestureType);
}

- (void)doubleTapAction:(UIGestureRecognizer *)doubleTap {
    self.gestureType = PLVVodGestureTypeDoubleTap;
    if (self.gestureCallback) self.gestureCallback(self, doubleTap, self.gestureType);
}

- (void)longPressAction:(UILongPressGestureRecognizer *)longPress {
    
    if (longPress.state == UIGestureRecognizerStateCancelled || longPress.state == UIGestureRecognizerStateEnded) {
        self.gestureType = PLVVodGestureTypeLongPressEnd;
        if (self.gestureCallback) self.gestureCallback(self, longPress, self.gestureType);
        return;
    }
    
    if (longPress.state == UIGestureRecognizerStateBegan || longPress.state == UIGestureRecognizerStateChanged) {
        self.gestureType = PLVVodGestureTypeLongPress;
        if (self.gestureCallback) self.gestureCallback(self, longPress, self.gestureType);
    }
}

// 开始记录seek 日志
- (void)startRecordSeekEvent{
    self.seekModel.seek_time_ago = [NSString stringWithFormat:@"%d", (int)self.currentPlaybackTime];
}

#pragma mark - IBAction

- (void)attachPlayerControl {
    if (!self.playerControl) {
        return;
    }
    
    id<PLVVodPlayerSkinProtocol> playerControl = self.playerControl;
    __weak typeof(self) weakSelf = self;
    
    // 设置属性
    playerControl.delegatePlayer = self;
    // 清晰度个数
    playerControl.qualityCount = self.video.qualityCount;
    // 清晰度
    playerControl.quality = self.quality;
    // 播放速率
    playerControl.playbackRate = self.playbackRate;
    playerControl.scalingMode = self.scalingMode;
    // 字幕,支持双语
    [playerControl setupSubtitleKeys:self.video.srtTitles defaultSrtIndex:self.video.defaultSrtIndex];
    // 本地视频
    playerControl.localPlayback = self.localPlayback;
    
    // 码率切换
    playerControl.qualityDidChangeBlock = ^(PLVVodQuality quality) {
       
        weakSelf.beforSwitchPosition = weakSelf.mainPlayer.currentPlaybackTime;
        weakSelf.isResetViewlog = NO;
        [weakSelf switchVideoQuality:quality];
        weakSelf.needResumePlaybackProgress = YES;
        weakSelf.mainPlayerPrepareToPlayHandler = ^{
            // 若打开记忆播放速率功能，则码率切换时，不重置播放速率
            if (weakSelf.rememberPlaybackRate) weakSelf.playbackRate = weakSelf.lastPlaybackRate;
            
            weakSelf.needResumePlaybackProgress = NO;
            if (weakSelf.beforSwitchPosition) {
                [weakSelf setCurrentPlaybackTime:weakSelf.beforSwitchPosition];
                weakSelf.beforSwitchPosition = 0;
            }
            [weakSelf play];
            
            if (weakSelf.switchQualitySuccessHandler) {
                weakSelf.switchQualitySuccessHandler(weakSelf.quality);
            }
        };
    };
    
    // 播放倍速切换
    playerControl.selectedPlaybackRateDidChangeBlock = ^(double playbackRate) {
        weakSelf.playbackRate = playbackRate;
        weakSelf.playerControl.playbackRate = playbackRate;
    };
    
    // 屏幕拉伸模式
    playerControl.scalingModeDidChangeBlock = ^(NSInteger scalingMode) {
        weakSelf.scalingMode = weakSelf.playerControl.scalingMode;
    };
    
    // 绑定控件
    [self.KVOController observe:self.playerControl keyPath:@"shouldHideStatusBar" options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSKeyValueChangeKey,id> * _Nonnull change) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf setNeedsStatusBarAppearanceUpdate];
        });
    }];
    
    [self attachControlWithKeyPath:@"statusBarStyle" handler:^{
        [weakSelf setNeedsStatusBarAppearanceUpdate];
    }];
    
    [self attachControlWithKeyPath:@"shouldHideNavigationBar" handler:^{
        if (weakSelf.navigationController) {
            [weakSelf.navigationController setNavigationBarHidden:weakSelf.playerControl.shouldHideNavigationBar animated:NO];
            [weakSelf.customMaskView setNeedsUpdateConstraints];
        }
    }];
    
    [self attachControlWithKeyPath:@"playPauseButton" handler:^{
        [weakSelf.playerControl.playPauseButton addTarget:weakSelf action:@selector(playPauseAction:) forControlEvents:UIControlEventTouchUpInside];
        // 同步UI
        [weakSelf syncPlayPauseButton];
    }];
    
    // 播放进度滑杆
    [self attachControlWithKeyPath:@"playbackSlider" handler:^{
        // slider开始滑动事件
        [weakSelf.playerControl.playbackSlider addTarget:weakSelf action:@selector(playbackSliderTouchDownAction:) forControlEvents:UIControlEventTouchDown];
        // slider滑动中事件
        [weakSelf.playerControl.playbackSlider addTarget:weakSelf action:@selector(playbackSliderValueChangeAction:) forControlEvents:UIControlEventValueChanged];
        // slider结束滑动事件
        [weakSelf.playerControl.playbackSlider addTarget:weakSelf action:@selector(playbackSliderTouchUpCancelAction:) forControlEvents:UIControlEventTouchUpInside | UIControlEventTouchCancel | UIControlEventTouchUpOutside];
        // slider点击事件
        weakSelf.sliderTapGesture = [[UITapGestureRecognizer alloc] initWithTarget:weakSelf action:@selector(playbackSliderTapAction:)];
        [weakSelf.playerControl.playbackSlider addGestureRecognizer:weakSelf.sliderTapGesture];
        // 同步UI
        weakSelf.playerControl.playbackSlider.value = weakSelf.currentPlaybackTime / weakSelf.duration;
    }];
    
    // 播放缓存进度
    [self attachControlWithKeyPath:@"bufferProgressView" handler:^{
        float progress = 1.0 * weakSelf.playableDuration / weakSelf.duration;
        progress = progress == progress ? progress : 0;
        weakSelf.playerControl.bufferProgressView.progress = progress;
    }];
    
    // 播放时间显示
    [self attachControlWithKeyPath:@"timeLabel" handler:^{
        // 同步UI
        weakSelf.playerControl.timeLabel.text = [weakSelf timeDescription];
    }];
    
    // 播放亮度滑杆
    [self attachControlWithKeyPath:@"brightnessSlider" handler:^{
        weakSelf.playerControl.brightnessSlider.value = [UIScreen mainScreen].brightness;
        [weakSelf.playerControl.brightnessSlider addTarget:weakSelf action:@selector(brightnessAction:) forControlEvents:UIControlEventValueChanged];
    }];
    
    [self.KVOController observe:[UIScreen mainScreen] keyPath:@"brightness" options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSKeyValueChangeKey,id> * _Nonnull change) {
        dispatch_async(dispatch_get_main_queue(), ^{
            weakSelf.playerControl.brightnessSlider.value = [UIScreen mainScreen].brightness;
        });
    }];
    self.playerControl.brightnessSlider.value = [UIScreen mainScreen].brightness;
    
    // 播放音量
    [self attachControlWithKeyPath:@"volumeSlider" handler:^{
        weakSelf.playerControl.volumeSlider.value = weakSelf.playbackVolume;
        [weakSelf.playerControl.volumeSlider addTarget:weakSelf action:@selector(volumeAction:) forControlEvents:UIControlEventValueChanged];
    }];
}

- (void)attachControlWithKeyPath:(NSString *)keyPath handler:(void (^)(void))handler {
    if (!keyPath.length) {
        return;
    }
    id playerControl = self.playerControl;
    if ([playerControl valueForKeyPath:keyPath]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (handler) handler();
        });
    }
    [self.KVOController observe:self.playerControl keyPath:keyPath options:NSKeyValueObservingOptionNew | NSKeyValueObservingOptionOld block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSKeyValueChangeKey,id> * _Nonnull change) {
        if (!change[NSKeyValueChangeNewKey]) {
            return;
        }
        if (change[NSKeyValueChangeOldKey] == change[NSKeyValueChangeNewKey]) {
            return;
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            if (handler) handler();
        });
    }];
}

- (void)playbackSliderTapAction:(UITapGestureRecognizer *)sender {

    UISlider *slider = self.playerControl.playbackSlider;
    CGPoint point = [sender locationInView:slider];
   
    // 设置了回调block，才认为开启打点功能，后续需要优化
    if (self.videoTipsSelectedHandler){
        UIInterfaceOrientation orientation = [UIApplication sharedApplication].statusBarOrientation;
        BOOL isPortrait = UIInterfaceOrientationIsPortrait(orientation);
        if (self.fullscreen || !isPortrait) {
            // 查询是否有打点信息在上面,有则回调
            __block BOOL found = NO;
            __block NSUInteger foundIndex = 0;
            {
                [self.video.videokeyframes enumerateObjectsUsingBlock:^(PLVVodVideoKeyFrameItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                    
                    // 时间点换算为坐标点
                    float offset_x = [obj.keytime floatValue]/self.video.duration *slider.frame.size.width + 5;
                    
                    if (fabs(point.x - offset_x) <=15 ){
                        //
                        PLVVodLogInfo(@"[player] -- 当前打点信息 index: %d ", (int)idx);
                        foundIndex = idx;
                        found = YES;
                        *stop = YES;
                    }
                }];
            }
            
            if (found){
                if (self.videoTipsSelectedHandler){
                    self.videoTipsSelectedHandler(foundIndex);
                }
                
                return;
            }
        }
    }
   
    // 统计seek 事件,seek 开始
    if (self.preparedToPlay){
        [self startRecordSeekEvent];
    }
    
    CGFloat value = (slider.maximumValue - slider.minimumValue) * (point.x / slider.frame.size.width);
    slider.value = value;
    [slider sendActionsForControlEvents:UIControlEventValueChanged];
    [slider sendActionsForControlEvents:UIControlEventTouchUpInside];
}

- (void)updateConstraintsForRotate {
    [self.adPlayer viewDidLayoutSubviews];
    if (self.placeholderView == nil) {
        return;
    }
    
    if (self.fullscreen) {
        [self makePlayerConstraintsWithRootView];
    } else {
        UIInterfaceOrientation orientation = [UIApplication sharedApplication].statusBarOrientation;
        BOOL isPortrait = UIInterfaceOrientationIsPortrait(orientation);
        if (isPortrait) {
            [self makePlayerConstraintsWithPlaceholder];
        } else {
            [self makePlayerConstraintsWithRootView];
        }
    }
}

- (void)makePlayerConstraintsWithRootView {
    UIView *playerView = self.view;
    UIView *rootView = [UIApplication sharedApplication].keyWindow;
    if (self.rootViewController.view) {
        rootView = self.rootViewController.view;
    }
    NSDictionary *views = NSDictionaryOfVariableBindings(playerView, rootView);
    NSMutableArray<NSLayoutConstraint *> *newConstrains = [NSMutableArray array];
    
    [newConstrains addObjectsFromArray:[NSLayoutConstraint constraintsWithVisualFormat:@"V:|[playerView]|" options:0 metrics:nil views:views]];
    [newConstrains addObjectsFromArray:[NSLayoutConstraint constraintsWithVisualFormat:@"H:|[playerView]|" options:0 metrics:nil views:views]];
    
    [NSLayoutConstraint deactivateConstraints:self.constraints];
    self.constraints = newConstrains;
    [NSLayoutConstraint activateConstraints:newConstrains];
}

- (void)makePlayerConstraintsWithPlaceholder {
    UIView *playerView = self.view;
    UIView *placeholderView = self.placeholderView;
    NSMutableArray<NSLayoutConstraint *> *newConstrains = [NSMutableArray array];
    
    [newConstrains addObject:[NSLayoutConstraint constraintWithItem:playerView attribute:NSLayoutAttributeLeft relatedBy:NSLayoutRelationEqual toItem:placeholderView attribute:NSLayoutAttributeLeft multiplier:1 constant:0]];
    [newConstrains addObject:[NSLayoutConstraint constraintWithItem:playerView attribute:NSLayoutAttributeRight relatedBy:NSLayoutRelationEqual toItem:placeholderView attribute:NSLayoutAttributeRight multiplier:1 constant:0]];
    [newConstrains addObject:[NSLayoutConstraint constraintWithItem:playerView attribute:NSLayoutAttributeTop relatedBy:NSLayoutRelationEqual toItem:placeholderView attribute:NSLayoutAttributeTop multiplier:1 constant:0]];
    [newConstrains addObject:[NSLayoutConstraint constraintWithItem:playerView attribute:NSLayoutAttributeBottom relatedBy:NSLayoutRelationEqual toItem:placeholderView attribute:NSLayoutAttributeBottom multiplier:1 constant:0]];
    
    [NSLayoutConstraint deactivateConstraints:self.constraints];
    self.constraints = newConstrains;
    [NSLayoutConstraint activateConstraints:newConstrains];
}

#pragma mark - UI Event

/// 定时更新
- (void)updateUIWithTimer {
    float progress = self.playableDuration / self.duration;
    progress = progress == progress ? progress : 0;
    self.playerControl.bufferProgressView.progress = progress;
    if (self.playbackState == IJKMPMoviePlaybackStatePlaying && !self.dragging) {
        self.playerControl.timeLabel.text = [self timeDescription];
        self.playerControl.playbackSlider.value = self.currentPlaybackTime / self.duration;
    }
}

/// 时间描述
- (NSString *)timeDescription_Skin {
    return [self timeDescriptionWithCurrentTime:self.currentPlaybackTime duration:self.duration];
}

- (NSString *)timeDescriptionWithCurrentTime:(NSTimeInterval)currentTime duration:(NSTimeInterval)duration {
    // add by libl [currentTime 向上取整，解决部分视频播放结束时间与视频时长不一致问题] 2019-06-25 start
    NSUInteger current = ceil(currentTime);
    current = current > duration ? duration:current;
    NSString *currentTimeDescription = [PLVVodUtil timeStringWithSeconds:current];
    // add end

    NSString *durationTimeDescription = [PLVVodUtil timeStringWithSeconds:duration];
    NSString *timeDescription = [NSString stringWithFormat:@"%@ / %@", currentTimeDescription, durationTimeDescription];
    return timeDescription;
}

/// 播放或暂停
- (void)syncPlayPauseButton {
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self syncPlayPauseButton];
        });
        return;
    }
    // 选中为播放状态
    self.playerControl.playPauseButton.selected = self.mainPlayer.isPlaying;
}

/// 播放/暂停
- (void)playPauseAction_Skin:(UIButton *)sender {
    BOOL isPlaying = self.mainPlayer.isPlaying;
    sender.selected = isPlaying;
    if (!isPlaying) {
        [self play];
    } else {
        [self pause];
    }
}

/// 播放滑杆
- (void)playbackSliderTouchDownAction_Skin:(UISlider *)sender {
    // TODO: 不自动隐藏皮肤
    
    self.sliderTapGesture.enabled = NO;
    self.allowShowToast = YES;
    // 统计seek 事件,seek 开始
    if (self.preparedToPlay){
        [self startRecordSeekEvent];
    }
}

- (void)playbackSliderValueChangeAction_Skin:(UISlider *)sender {
    // 若未能就绪，则拖动无效
    if (!self.preparedToPlay) {
        PLVVodLogWarn(@"视频未就绪");
        sender.value = 0;
        return;
    }
    
    // 视频时长不可用时，拖动无效
    if (!self.duration || isnan(self.duration)) {
        PLVVodLogError(@"视频时长非法，%f", self.duration);
        NSError *plvError = PLVVodErrorMake(playback_duration_illegal, ^(NSMutableDictionary *userInfo) {
            
        });
        [self reportError:plvError];
        sender.value = 0;
        return;
    }
    
    CGFloat offset = sender.value - self.playbackSliderLastValue;
    // 没有进行拖动
    if (offset == 0.0) {
        //NSLog(@"没有拖动");
//        return;
    }
    self.playbackSliderLastValue = sender.value;
    
    // TODO: 取消上次的跳转
    
    // 标记状态
    self.dragging = YES;
    
    //计算出拖动的当前秒数
    CGFloat destinationTime = floorf(self.duration * sender.value);
    
    // 更新UI
    self.playerControl.timeLabel.text = [self timeDescriptionWithCurrentTime:destinationTime duration:self.duration];
}

- (void)playbackSliderTouchUpCancelAction_Skin:(UISlider *)sender {
    self.allowShowToast = YES;
    // 拖动改变视频播放进度
    CGFloat destinationTime = floorf(self.duration * sender.value);
    self.currentPlaybackTime = destinationTime;
    
    // 标记状态
    self.dragging = NO;
    
    self.sliderTapGesture.enabled = YES;
}


/// 亮度调节
- (void)brightnessAction_Skin:(UISlider *)sender {
    [UIScreen mainScreen].brightness = sender.value;
}

/// 音量调节
- (void)volumeAction_Skin:(UISlider *)sender {
    self.playbackVolume = sender.value;
}


@end
