//
//  PLVVodDownloaderExpTableViewController.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/16.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "PLVVodDownloaderExpTableViewController.h"
#import "PLVVodDownloadManager.h"
#import "PLVTimer.h"
#import "PlayViewController.h"
#import "PLVDownloadDBMgr.h"
#import "PLVExtendDownloadInfo.h"

@interface PLVVodDownloaderExpTableViewController ()

@property (nonatomic, strong) NSMutableArray<PLVVodDownloadInfo *> *downloads;
@property (nonatomic, strong) PLVTimer *timer;
@property (nonatomic, strong) NSArray *videoIds;

@property (nonatomic, assign) BOOL isNeedExpand;

@end

@implementation PLVVodDownloaderExpTableViewController

#pragma mark - property
- (NSMutableArray<PLVVodDownloadInfo *> *)downloads {
	if (!_downloads) {
		_downloads = [NSMutableArray array];
	}
	return _downloads;
}

/*
 
 e97dbe3e646bb7c3afcadb5380a1404c_e
 e97dbe3e64204ee0998faf94a7f10b80_e
 e97dbe3e64c247499b55f213a4470052_e
 e97dbe3e6492b053bc9b5afc677a1394_e
 e97dbe3e64546124c790de5ffdf117c1_e
 
 */

- (NSArray *)videoIds{
    
    if (!_videoIds){
    
        // lien

        _videoIds = @[
                        @"e97dbe3e646bb7c3afcadb5380a1404c_e", // 第一集
                        @"e97dbe3e64c247499b55f213a4470052_e", /// 第二集
                        @"e97dbe3e64204ee0998faf94a7f10b80_e",  /// 第三级
                        
                        /*@"e97dbe3e64546124c790de5ffdf117c1_e"*/];  // 私有云demo
        // ++++++++++ lien +++++++++++
//        _videoIds = @[@"a0f97cbb56bb9d25e198332423569fde_a"]; // 非加密hls
        
//        _videoIds = @[@"a0f97cbb5676eea6be4b70e4fdc3d8a3_a"]; // 原视频url 测试
        
//        // ts 下载测试
//        _videoIds = @[@"220643a4132fb85180c3f80e22be669c_2",
//                      @"220643a41313ea4779b908235b73c62b_2",
//                      @"220643a41365ac9fe66c0e3fc39fc54f_2"];
        
        
        /*
        _videoIds = @[@"a0f97cbb56bb9d25e198332423569fde_a",   // 非加密hls，海洋王国
                      @"a0f97cbb5676eea6be4b70e4fdc3d8a3_a",   // mp4 ,一次看个够
                      @"a0f97cbb56d005ee416d337bbb9677d1_a",   // flv
                      @"a0f97cbb568ce78a798790798e6394eb_a",   // hls 加密
                      ];
        */
        
        
        // ++++++++++ polyv +++++++++
//        _videoIds = @[@"e97dbe3e64c247499b55f213a4470052_e"];
//        _videoIds = @[@"e97dbe3e64204ee0998faf94a7f10b80_e", // 私有云demo
//                      @"e97dbe3e646bb7c3afcadb5380a1404c_e", // 第一集
//                      @"e97dbe3e64c247499b55f213a4470052_e",  // 第三集
//                      @"e97dbe3e6492b053bc9b5afc677a1394_e", // 上传客户端*/
//                      @"e97dbe3e64546124c790de5ffdf117c1_e"]; // 第二集
        
//        // 非加密@
//        _videoIds = @[@"a0f97cbb56eb7a9ea4550702168f6565_a",
//                      @"a0f97cbb56652f2c3eff3e00f0742872_a",
//                      @"a0f97cbb5662dd2a793da9e810d5d3d3_a",
//                      @"a0f97cbb5650fc18ae1d16850f5bba7b_a",
//                      @"a0f97cbb568ce78a798790798e6394eb_a",
//                      @"a0f97cbb56b92041f9a4454d2cb9d401_a"
//                      /*@"a0f97cbb56dd16f2226b3a335901c4d1_a"*/];
        
        // 加密
//        _videoIds = @[@"e97dbe3e64546124c790de5ffdf117c1_e"];
        
        _videoIds = [self videoTestIds];
    }
    
    return _videoIds;
}

- (NSArray *)videoTestIds{
    NSArray *array = @[@"a0f97cbb56b92041f9a4454d2cb9d401_a", // 珠海长隆北欧风情 已加密 支持音视频切换 hls
                       @"a0f97cbb56bb9d25e198332423569fde_a", // 珠海长隆海洋王国 mp4
                       @"a0f97cbb56b8d96ff76a495865730dc6_a", // 一次看过瘾！2017超燃电影合集！2017电影片段剪辑！ 支持音视频切换 hls 非加密
                       @"a0f97cbb5676eea6be4b70e4fdc3d8a3_a",
                       ]; // keepssource hls 非加密
    ; // hls  非加密 discontinues test-  源文件
    
    
    return array;
}

- (void)playerVideoWithVid:(NSString *)vid{
    PlayViewController *detailVC = [[PlayViewController alloc] init];
    PLVVodLocalVideo *localVideo = [PLVVodLocalVideo localVideoWithVid:vid dir:[PLVVodDownloadManager sharedManager].downloadDir];
    
    detailVC.localVideo = localVideo;

    [self.navigationController pushViewController:detailVC animated:YES];
}

- (IBAction)startDownload:(UIBarButtonItem *)sender {
	PLVVodDownloadManager *manager = [PLVVodDownloadManager sharedManager];
	[manager startDownload];
}
- (IBAction)stopDownload:(UIBarButtonItem *)sender {
	PLVVodDownloadManager *manager = [PLVVodDownloadManager sharedManager];
	[manager stopDownload];
}

- (IBAction)deleteDownload:(UIBarButtonItem *)sender{
    NSError *errorDel;
    [PLVVodDownloadManager removeAllVideoWithError:&errorDel];
//    [[PLVVodDownloadManager sharedManager] removeAllDownloadWithComplete:^(void *result) {
//
//        [self.downloads removeAllObjects];
//        [self.tableView reloadData];
//    }];
    
    NSLog(@"-- %@ --", errorDel);
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // Uncomment the following line to preserve selection between presentations.
    // self.clearsSelectionOnViewWillAppear = NO;
    
    // Uncomment the following line to display an Edit button in the navigation bar for this view controller.
    // self.navigationItem.rightBarButtonItem = self.editButtonItem;
	
	PLVVodDownloadManager *manager = [PLVVodDownloadManager sharedManager];
	manager.completeBlock = ^{
		NSLog(@"全部下载完成");
	};
    
	// mp3: f46ead66de9abf40dda489c0b0ecf991_f
	// dont look back: f46ead66de9147d6cc0162e0ff7bb5d4_f
	// plain hls: f46ead66de5d785b8e1e56b3a9fa5cc5_f
	// f46ead66deeb2ffc2c56aceb39005917_f
	
	__weak typeof(self) weakSelf = self;

//    [[PLVVodDownloadManager sharedManager] requstDownloadProcessingListWithCompletion:^(NSArray<PLVVodDownloadInfo *> *downloadInfos) {
//        [weakSelf.downloads addObjectsFromArray:downloadInfos];
//
//            if (weakSelf.downloads.count <= 0){
//
//                [weakSelf loadVideoList];
//            }
//    }];
    
    self.isNeedExpand = YES;
    
    if (self.isNeedExpand){
        // 扩展字段
        [PLVDownloadDBMgr shareInstance];
    }
    
    [weakSelf loadVideoList];
}

- (void)viewDidDisappear:(BOOL)animated {
	[super viewDidDisappear:animated];
	[self.timer cancel];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)loadVideoList{
    
    PLVVodDownloadManager *manager = [PLVVodDownloadManager sharedManager];
    manager.completeBlock = ^{
        NSLog(@"全部下载完成");
    };
    __weak typeof(self) weakSelf = self;

    // 注意账号与视频匹配
    
    
    [self.videoIds enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        
        [PLVVodVideo requestVideoWithVid:obj completion:^(PLVVodVideo *video, NSError *error) {
//                PLVVodDownloadInfo *downloadInfo = [manager downloadVideo:video];
            PLVVodDownloadInfo *downloadInfo = [manager downloadAudio:video];
                if (downloadInfo){
                    [weakSelf.downloads addObject:downloadInfo];
                    
                    if (self.isNeedExpand){
                        
                        // 自定义存储数据库
                        PLVExtendDownloadInfo *record = [[PLVExtendDownloadInfo alloc] init];
                        record.snapshot = downloadInfo.snapshot;
                        record.fileSize = downloadInfo.filesize;
                        record.title = downloadInfo.title;
                        record.vid = downloadInfo.vid;
                        record.downloadId = downloadInfo.downloadId;
                        record.state = downloadInfo.state;
                        record.progress = downloadInfo.progress;
                        record.duration = downloadInfo.duration;
                        record.quality = downloadInfo.quality;
                        
                        // 其他字段
                        record.Cusduration = 1024;
                        record.CusfileSize = 1024*1024;
                        
//                        [[PLVDownloadDBMgr shareInstance] insertOrUpdateDatabaseWithCusInfo:record];
                    }
                    
                    if (weakSelf.downloads.count == weakSelf.videoIds.count){
                        
                        dispatch_async(dispatch_get_main_queue(), ^{
                            [weakSelf.tableView reloadData];
                        });
                    }
                }
            }];
    }];
   
}

#pragma mark - Table view data source

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
	return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.downloads.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"net.polyv.download.info" forIndexPath:indexPath];
    
//    PLVVodDownloadInfo *info = self.downloads[indexPath.row];
    
//    __weak typeof(self) weakSelf = self;
//    if (!info.bytesPerSecondsDidChangeBlock){
//        info.bytesPerSecondsDidChangeBlock = ^(PLVVodDownloadInfo *info) {
//            // 下载速度回调
//            [weakSelf handleDownloadSpeed:info];
//        };
//    }
//    if (info.snapshot != nil && ![info.snapshot isKindOfClass:[NSNull class]]){
////        NSData *imageData = [NSData dataWithContentsOfURL:[NSURL URLWithString:info.snapshot]];
////        cell.imageView.image = [UIImage imageWithData:imageData];
//    }
   
        PLVVodDownloadInfo *info = self.downloads[indexPath.row];
    
    cell.textLabel.text = info.title;
    
    NSString *speedDescription = [NSByteCountFormatter stringFromByteCount:info.bytesPerSeconds countStyle:NSByteCountFormatterCountStyleFile];
    double rate = info.bytesPerSeconds / 1024;
    if (rate > 0) {
        NSLog(@"speed: %@", speedDescription);
    }
    NSString *downState = NSStringFromPLVVodDownloadState(info.state);
    NSString *secret = [info.video isPlain] ? @"非加密":@"加密";
    NSString *hlsStr = [info.video isHls] ? @"hls":@"mp4";

    NSString *videoFomat = [NSString stringWithFormat:@"%@-%@", secret, hlsStr];
    cell.detailTextLabel.text = [NSString stringWithFormat:@"%.01f%%\t\t%@ ---- state: %@ format: %@", info.progress * 100, speedDescription, downState, videoFomat];
	
    return cell;
}

- (void)handleDownloadSpeed:(PLVVodDownloadInfo *)info{
    //
    NSArray<NSIndexPath *> *indexs = [self.tableView indexPathsForVisibleRows];
    __block NSIndexPath *freshIndex = nil;
    [indexs enumerateObjectsUsingBlock:^(NSIndexPath * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        PLVVodDownloadInfo *downloadInfo = self.downloads[obj.row];
        
        if ([downloadInfo.vid isEqualToString:info.vid] &&
            (downloadInfo.quality == info.quality)){
            
            // 需要刷新
            freshIndex = obj;
            *stop = YES;
        }
    }];
    
    if (freshIndex){
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.tableView reloadRowsAtIndexPaths:@[freshIndex] withRowAnimation:UITableViewRowAnimationNone];
        });
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    
    PLVVodDownloadInfo *info = self.downloads[indexPath.row];
    
    switch (info.state) {
        case PLVVodDownloadStateReady:
        case PLVVodDownloadStateRunning:
        {
            // 暂停下载
            [self handleStopDownloadVideo:info];
        }
            break;
        default:
        {
            // 开始下载
            [self handleStartDownloadVideo:info];
        }
            break;
    }

    return;
    
    // 播放本地视频
//    if (info.state == PLVVodDownloadStateSuccess)
    {
    
        //
        PLVVodVideo *videoModel = info.video;
        PLVVodLocalVideo *localModel = nil;
        
        localModel = [PLVVodLocalVideo localVideoWithVideo:videoModel dir:[PLVVodDownloadManager sharedManager].downloadDir];
        if (!localModel){
            localModel = [PLVVodLocalVideo localVideoWithVid:info.vid dir:[PLVVodDownloadManager sharedManager].downloadDir];
        }
        PlayViewController *detailVC = [[PlayViewController alloc] init];
        
        if (!localModel){
            detailVC.video = videoModel;
        }
        else{
            detailVC.localVideo = localModel;
        }
        
        [self.navigationController pushViewController:detailVC animated:YES];
    }
}

/// 删除
- (UITableViewCellEditingStyle)tableView:(UITableView *)tableView editingStyleForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewCellEditingStyleDelete;
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath {
    return YES;
}

#pragma mark -- handle
- (void)handleStopDownloadVideo:(PLVVodDownloadInfo *)info{
    [[PLVVodDownloadManager sharedManager] stopDownloadWithVid:info.vid];
}

- (void)handleStartDownloadVideo:(PLVVodDownloadInfo *)info{
    [[PLVVodDownloadManager sharedManager] startDownloadWithVid:info.vid];
    
    if ([PLVVodDownloadManager sharedManager].isDownloading){
        //
    }
}


#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}


@end
