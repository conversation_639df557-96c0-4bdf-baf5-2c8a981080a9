//
//  ViewController.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/9.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "ViewController.h"
#import "PlayViewController.h"
#import "PLVVodPlayerViewController.h"
#import "PLVVodDownloadManager.h"
#import "PLVVodConstans.h"
#import "PLVVodPlayerSkin.h"
#import "PLVVodDanmuManager.h"
#import "PLVTimer.h"
#import "AppDelegate.h"
#import "PLVVodPlayerViewController.h"

@interface ViewController ()

@property (nonatomic, strong) UIViewController *viewController;

@property (nonatomic, strong) PLVVodVideo *video;

@property (nonatomic, copy) NSString *lastVid;

@property (nonatomic, strong) id object;

@property (nonatomic, strong) PLVVodDanmuManager *danmuManager;

@property (nonatomic, strong) PLVTimer *timer;

@property (nonatomic, strong) UIView *playerPlaceView;
@property (nonatomic, assign) PLVVodPlayerViewController *player;

@property (nonatomic, strong) UIButton *addPlayer;
@property (nonatomic, strong) UIButton *distroyPlayer;

@end

@implementation ViewController

- (UIView *)playerPlaceView{
    if(!_playerPlaceView){
        
        _playerPlaceView = [[UIView alloc] init];
    }
    
    return _playerPlaceView;
}

- (UIButton *)addPlayer{
    if (!_addPlayer)
    {
        _addPlayer = [[UIButton alloc] init];
        [_addPlayer setTitle:@"加载播放器" forState:UIControlStateNormal];
        [_addPlayer setBackgroundColor:[UIColor redColor]];
        [_addPlayer addTarget:self action:@selector(addPlayAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    
    return _addPlayer;
}

- (UIButton *)distroyPlayer{
    if (!_distroyPlayer){
        _distroyPlayer = [[UIButton alloc] init];
        _distroyPlayer.backgroundColor = [UIColor redColor];
        [_distroyPlayer setTitle:@"销毁播放器" forState:UIControlStateNormal];
        [_distroyPlayer addTarget:self action:@selector(destroyPlayerAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    
    return _distroyPlayer;
}

- (void)viewDidLoad {
	[super viewDidLoad];
	// Do any additional setup after loading the view, typically from a nib.
    self.edgesForExtendedLayout = UIRectEdgeNone;
    
	__weak typeof(self) weakSelf = self;
	// 非加密：f46ead66def7a52e0e7c17b8d0088a67_f
	// 加密：f46ead66de02c75c220ddf3b543580bf_f
	// f46ead66dec30461646947dea668c1c1_f
	// f46ead66deeb2ffc2c56aceb39005917_f
    
    // a0f97cbb56bb9d25e198332423569fde_a
    
	[PLVVodVideo requestVideoWithVid:@"e97dbe3e646bb7c3afcadb5380a1404c_e" completion:^(PLVVodVideo *video, NSError *error) {
		weakSelf.video = video;
		NSLog(@"在线视频模型已获取");
		dispatch_async(dispatch_get_main_queue(), ^{
			//[weakSelf performSegueWithIdentifier:@"play" sender:nil];
		});
	}];
	
	
	
//	NSMutableArray *danmus = [NSMutableArray array];
//	for (int i = 0; i < 3; i++) {
//		PLVVodDanmu *danmu = [[PLVVodDanmu alloc] init];
//		danmu.content = [NSString stringWithFormat:@"送送送送送送送送送%d", i];
//		danmu.time = 1.2 * i + 1.0;
//		danmu.mode = PLVVodDanmuModeTop;
//		if (i%2==0) {
//			danmu.mode = PLVVodDanmuModeRoll;
//		}
//		NSLog(@"time: %f", danmu.time);
//		[danmus addObject:danmu];
//	}
//	self.danmuManager = [[PLVVodDanmuManager alloc] initWithDanmus:danmus inView:self.view insets:UIEdgeInsetsMake(64, 0, 44, 0)];
//	self.timer = [PLVTimer repeatWithInterval:0.9 repeatBlock:^{
//		dispatch_async(dispatch_get_main_queue(), ^{
//			[weakSelf.danmuManager synchronouslyShowDanmu];
//			weakSelf.danmuManager.currentTime = weakSelf.timer.currentInterval;
//			[weakSelf.danmuManager synchronouslyShowDanmu];
//			if (weakSelf.danmuManager.state == PLVVodDanmuStateFinish) {
//				[weakSelf.timer cancel];
//				weakSelf.timer = nil;
//			}
//		});
//	}];
//	[self.danmuManager testDanmu:0];
//	self.navigationController.navigationBarHidden = YES;
//	NSLog(@"top layout: %f", self.topLayoutGuide.length);
    
    [self.view addSubview:self.playerPlaceView];
    self.playerPlaceView.frame = CGRectMake(0, 120, self.view.bounds.size.width, 240);

    [self.view addSubview:self.addPlayer];
    self.addPlayer.frame = CGRectMake(0, 44, 88, 88);
    
    [self.view addSubview:self.distroyPlayer];
    self.distroyPlayer.frame = CGRectMake(68, 44, 88, 88);
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
}

- (void)addPlayAction:(UIButton *)btn{
    
    AppDelegate *appDelegate = (AppDelegate *)[UIApplication sharedApplication].delegate;
    PLVVodPlayerViewController *player = appDelegate.player;
    if (player && !player.view.superview){
        //
        [player addPlayerOnPlaceholderView:self.playerPlaceView rootViewController:self];
        player.reachEndHandler = ^(PLVVodPlayerViewController *player) {
            NSLog(@"%@ finish handler.", player.video.vid);
        };
        //    [self.player setVideo:self.video];
        //    [self.player play];
        //        player.video = self.video;
        self.player = player;
        
        [self.player play];
    }
}

- (void)destroyPlayerAction:(UIButton *)btn{
    
    AppDelegate *appDelegate = (AppDelegate *)[UIApplication sharedApplication].delegate;
    if (!appDelegate.player.view.superview){
        
        appDelegate.player = nil;
    }
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
	if (self.danmuManager.state == PLVVodDanmuStateRunning) {
		[self.danmuManager pause];
	} else if (self.danmuManager.state == PLVVodDanmuStatePause) {
		[self.danmuManager resume];
	}
	
}

- (void)viewDidAppear:(BOOL)animated {
	[super viewDidAppear:animated];
	//NSLog(@"top layout: %f", self.topLayoutGuide.length);
}

- (IBAction)test:(id)sender {
	NSBundle *plvBundle = PLV_VOD_RESOURCES_BUNDLE;
	PLVVodPlayerSkin *skin = [[PLVVodPlayerSkin alloc] initWithNibName:NSStringFromClass([PLVVodPlayerSkin class]) bundle:plvBundle];
	[self.view addSubview:skin.view];
	skin.view.frame = self.view.bounds;
	[self presentViewController:skin animated:YES completion:^{
		
	}];
}

- (IBAction)playerExp:(UIBarButtonItem *)sender {
//	[PLVVodVideo requestVideoWithVid:@"f46ead66dec30461646947dea668c1c1_f" completion:^(PLVVodVideo *video, NSError *error) {
//		PLVVodPlayerViewController *player = [[PLVVodPlayerViewController alloc] init];
//		player.video = video;
//		[player loadingHandler:^(BOOL isLoading) {
//			NSLog(@"菊花转转 %s", isLoading?"YES":"NO");
//		}];
//		dispatch_async(dispatch_get_main_queue(), ^{
//			[self.navigationController pushViewController:player animated:YES];
//		});
//	}];
	
//	NSArray<PLVVodLocalVideo *> *localVideos = [PLVVodDownloadManager sharedManager].localVideos;
//	PLVVodPlayerViewController *player = [[PLVVodPlayerViewController alloc] init];
//	for (PLVVodLocalVideo *localVideo in localVideos) {
////		if ([@"sl8da4jjbx1c8baed8a48212d735d905_s" isEqualToString:localVideo.vid]) {
////			player.video = localVideo;
////			break;
////		}
//		if ([localVideo.vid isEqualToString:self.lastVid] && localVideos.count > 1) continue;
//		player.video = localVideo;
//		self.lastVid = localVideo.vid;
//		break;
//	}
//	[self.navigationController pushViewController:player animated:YES];
	
//	PLVVodPlayerViewController *player = [[PLVVodPlayerViewController alloc] init];
//	player.video = self.video;
//	dispatch_async(dispatch_get_main_queue(), ^{
//		[self.navigationController pushViewController:player animated:YES];
//	});
}
- (IBAction)downloaderExp:(UIBarButtonItem *)sender {
}

- (void)didReceiveMemoryWarning {
	[super didReceiveMemoryWarning];
	// Dispose of any resources that can be recreated.
}

- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    //    NSArray<PLVVodLocalVideo *> *localVideos = [PLVVodDownloadManager sharedManager].localVideos;
    //    for (PLVVodLocalVideo *localVideo in localVideos) {
    ////        if ([@"sl8da4jjbx1c8baed8a48212d735d905_s" isEqualToString:localVideo.vid]) {
    ////            player.video = localVideo;
    ////            break;
    ////        }
    //        if ([localVideo.vid isEqualToString:self.lastVid] && localVideos.count > 1) continue;
    //        self.video = localVideo;
    //        self.lastVid = localVideo.vid;
    //        break;
    //    }
    
    if ([segue.identifier isEqualToString:@"play"]) {
        PlayViewController *playVC = segue.destinationViewController;
        //player.autoplay = NO;
        playVC.video = self.video;
        NSLog(@"vid: %@", self.video.vid);
    }
}

@end
