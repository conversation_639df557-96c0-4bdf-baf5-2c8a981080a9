//
//  PlayViewController.m
//  _PolyvVodSDK
//
//  Created by 李长杰 on 2018/5/24.
//  Copyright © 2018年 POLYV. All rights reserved.
//

#import "PlayViewController.h"
#import "PLVVodSkinPlayerController.h"
#import "AppDelegate.h"
#include "PLVVodSDK.h"

@interface PlayViewController ()

@property (weak, nonatomic) IBOutlet UIView *playerPlaceholder;

@property (nonatomic, strong) PLVVodSkinPlayerController *player;

@end

@implementation PlayViewController

-(void)dealloc{
    
    NSLog(@"--- 销毁了 %@", @"PlayViewController");
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    
    self.playerPlaceholder.frame = [CGRectMake(0, 200, self.view.frame.size.width, 240);
    
    // 初始化播放器
    PLVVodSkinPlayerController *player = [[PLVVodSkinPlayerController alloc] initWithNibName:nil bundle:nil];
    [player addPlayerOnPlaceholderView:self.playerPlaceholder rootViewController:self];
    player.reachEndHandler = ^(PLVVodPlayerViewController *player) {
        NSLog(@"%@ finish handler.", player.video.vid);
    };
    self.player = player;
    
    PLVVodLocalVideo *playVieo = self.localVideo;
    if (playVieo){
        // 在兼容目录获取到本地视频，直接播放
        self.player.video = playVieo;
    }
    else{
        // 只要存在本地视频，都会优先播放本地视频
        __weak typeof(self) weakSelf = self;
        
        NSString *vid = @"a0f97cbb5686803c2e23ed46d8dfd1ce_a";
        // @"a0f97cbb56b92041f9a4454d2cb9d401_a"
        [PLVVodVideo requestVideoWithVid:/*playVieo.vid*/vid completion:^(PLVVodVideo *video, NSError *error) {
            if (!video.available) return;
            weakSelf.player.video = video;
            video.title = @"开放title定义";
            dispatch_async(dispatch_get_main_queue(), ^{
                weakSelf.title = video.title;
            });
        }];
    }
    
//    self.localVideo = [PLVVodLocalVideo localVideoWithVid:@"e97dbe3e64546124c790de5ffdf117c1_e" dir:preDownlaodPath];
//    if (self.localVideo){
//        self.player.video = self.localVideo;
//    }
//    else {
////        self.player.video = self.video;
//
//    }
//
}

- (void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
     
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end

