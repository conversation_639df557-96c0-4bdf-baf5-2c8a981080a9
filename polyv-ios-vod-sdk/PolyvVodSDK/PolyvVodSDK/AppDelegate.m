//
//  AppDelegate.m
//  PolyvVodSDK
//
//  Created by BqLin on 2017/10/9.
//  Copyright © 2017年 POLYV. All rights reserved.
//

#import "AppDelegate.h"
#import "PLVVodSettings.h"
#import <AVFoundation/AVFoundation.h>
#import "PLVVodDownloadManager.h"
#import "PLVVodUtil.h"
#import "PLVVodNetworking.h"
#import "PLVVodDanmu.h"
#import "PLVVodDBManager.h"

//#import "PLVVodDownloadManager+Database.h"
#import "PLVVodExtendVideoInfo.h"
#import <IJKMediaFramework/IJKMediaFramework.h>

// TODO: 此处修改个人账号配置信息
#define polyvVodSdkKey  @"CMWht3MlpVkgpFzrLNAebYi4RdQDY/Nhvk3Kc+qWcck6chwHYKfl9o2aOVBvXVTRZD/14XFzVP7U5un43caq1FXwl0cYmTfimjTmNUYa1sZC1pkHE8gEsRpwpweQtEIiTGVEWrYVNo4/o5jI2/efzA=="

// 默认
// #define polyvVodSdkKey  @"yQRmgnzPyCUYDx6weXRATIN8gkp7BYGAl3ATjE/jHZunrULx8CoKa1WGMjfHftVChhIQlCA9bFeDDX+ThiuBHLjsNRjotqxhiz97ZjYaCQH/MhUrbEURv58317PwPuGEf3rbLVPOa4c9jliBcO+22A=="

// 秀珠
//#define polyvVodSdkKey  @"q+IymS51UBFVSXdsS/HWHaguQuQ8M923Ow+Ajx0BxlvbNBGVmW0XVbir12ebEg7tEvVukaBpqciLIvfKxKSL4VuK1q3B0t6vRhZ8hL4CROZp2mo6cpM0EoQgqaD1d/4yL/zrgxNyKrIZWnl43lcwLg=="

#define polyvVodSdkSecretKey @"VXtlHmwfS2oYm0CZ"
#define polyvVodSdkVectorKey @"2u9gDPKdX6GyQJKU"

@interface AppDelegate ()

@property (nonatomic, assign) UIBackgroundTaskIdentifier bgTask;

@end

@implementation AppDelegate


- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
	// Override point for customization after application launch.
	
    NSError *error = [NSError new];
    PLVVodSettings *settings = [PLVVodSettings settingsWithConfigString:polyvVodSdkKey key:polyvVodSdkSecretKey iv:polyvVodSdkVectorKey error:&error];
    [settings setLogLevel:PLVVodLogLevelAll];
    
    // 测试阶段输出所有信息
//    settings.logLevel = PLVVodLogLevelDebug;
    
//  PLVVodSettings *settings = [PLVVodSettings settingsWithConfigString:sdkKey key:@"VXtlHmwfS2oYm0CZ" iv:@"2u9gDPKdX6GyQJKU"];
//	PLVVodSettings *settings = [PLVVodSettings settingsWithUserid:@"2273463aee" readtoken:@"f16e5715-4705-4894-b180-880df6f14797" writetoken:@"89c482bc-bb3e-4486-83b5-4dd9784278c4" secretkey:@"nz60ihPtnS"];
	PLVVodLogInfo(@"settings = %@", settings);
	[self setupAudioSession];
    
//	[[UIApplication sharedApplication] beginReceivingRemoteControlEvents];
	
	NSString *downloadDir = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).lastObject;
//    downloadDir = [downloadDir stringByAppendingPathComponent:@"PolyvVideos"];
    downloadDir = [downloadDir stringByAppendingPathComponent:@"plvideo/a"];

	[PLVVodDownloadManager sharedManager].downloadDir = downloadDir;
	
    // 兼容
    [[PLVVodDownloadManager sharedManager] compatibleWithPreviousVideos];
    NSLog(@"-- %@ --", downloadDir);
    //

	// 启用httpDNS
//    settings.enableHttpDNS = YES;
    
//    [PLVVodDBManager testDBManager];
    
    [PLVIJKFFMoviePlayerController setLogLevel:k_IJK_LOG_SILENT];
    
    [PLVVodSettings sharedSettings].viewerId = @"Uid";
    [PLVVodSettings sharedSettings].viewerName = @"nickname";
    
	return YES;
    
}


/// 配置音量会话
- (void)setupAudioSession {
	NSError *categoryError = nil;
	if (![[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback error:&categoryError]){
		NSLog(@"音量会话类别设置错误：%@", categoryError);
	}
	
	NSError *activeError = nil;
	if (![[AVAudioSession sharedInstance] setActive:YES error:&activeError])	{
		NSLog(@"音量会话激活设置错误：%@", activeError);
	}
}

- (void)application:(UIApplication *)application handleEventsForBackgroundURLSession:(NSString *)identifier completionHandler:(void (^)(void))completionHandler {
    
    PLVVodLogInfo(@"++++++++ %@ ++++++++ identifier: %@", NSStringFromSelector(_cmd), identifier);
	[PLVVodDownloadManager sharedManager].backgroundCompletionHandler = completionHandler;
}

- (void)applicationWillResignActive:(UIApplication *)application {
	// Sent when the application is about to move from active to inactive state. This can occur for certain types of temporary interruptions (such as an incoming phone call or SMS message) or when the user quits the application and it begins the transition to the background state.
	// Use this method to pause ongoing tasks, disable timers, and invalidate graphics rendering callbacks. Games should use this method to pause the game.
}


- (void)applicationDidEnterBackground:(UIApplication *)application {
	// Use this method to release shared resources, save user data, invalidate timers, and store enough application state information to restore your application to its current state in case it is terminated later.
	// If your application supports background execution, this method is called instead of applicationWillTerminate: when the user quits.
    [[PLVVodDownloadManager sharedManager] applicationDidEnterBackground];
    
//    [self comeToBackgroundMode];
}


- (void)applicationWillEnterForeground:(UIApplication *)application {
	// Called as part of the transition from the background to the active state; here you can undo many of the changes made on entering the background.
    [[PLVVodDownloadManager sharedManager] applicationWillEnterForeground];
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
	// Restart any tasks that were paused (or not yet started) while the application was inactive. If the application was previously in the background, optionally refresh the user interface.
}


- (void)applicationWillTerminate:(UIApplication *)application {
	// Called when the application is about to terminate. Save data if appropriate. See also applicationDidEnterBackground:.
    
    [[PLVVodDownloadManager sharedManager] applicationWillTerminate];
}


-(void)comeToBackgroundMode{
    //初始化一个后台任务BackgroundTask，这个后台任务的作用就是告诉系统当前app在后台有任务处理，需要时间
    UIApplication*  app = [UIApplication sharedApplication];
    self.bgTask = [app beginBackgroundTaskWithExpirationHandler:^{
        [app endBackgroundTask:self.bgTask];
        self.bgTask = UIBackgroundTaskInvalid;
    }];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [app endBackgroundTask:self.bgTask];
        self.bgTask = UIBackgroundTaskInvalid;
    });
    
    NSLog( @"--- remain back time %f ---", app.backgroundTimeRemaining);
    
    //开启定时器 不断向系统请求后台任务执行的时间
//    self.timer = [NSTimer scheduledTimerWithTimeInterval:25.0 target:self selector:@selector(applyForMoreTime) userInfo:nil repeats:YES];
//    [self.timer fire];
}

-(void)applyForMoreTime {
    //如果系统给的剩余时间小于60秒 就终止当前的后台任务，再重新初始化一个后台任务，重新让系统分配时间，这样一直循环下去，保持APP在后台一直处于active状态。
    if ([UIApplication sharedApplication].backgroundTimeRemaining < 60) {
        [[UIApplication sharedApplication] endBackgroundTask:self.bgTask];
        self.bgTask = [[UIApplication sharedApplication] beginBackgroundTaskWithExpirationHandler:^{
            [[UIApplication sharedApplication] endBackgroundTask:self.bgTask];
            self.bgTask = UIBackgroundTaskInvalid;
        }];
    }
}

@end
