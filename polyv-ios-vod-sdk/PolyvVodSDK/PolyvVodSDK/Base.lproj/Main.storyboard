<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="13771" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="pAC-Pb-F3X">
    <device id="retina3_5" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13772"/>
        <capability name="Aspect ratio constraints" minToolsVersion="5.1"/>
        <capability name="Navigation items with more than one left or right bar item" minToolsVersion="7.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Demo-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="ViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="ZzK-qo-GfE"/>
                        <viewControllerLayoutGuide type="bottom" id="Td9-dE-Q71"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="480"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <toolbar opaque="NO" clearsContextBeforeDrawing="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Xkd-IE-Qzc">
                                <rect key="frame" x="0.0" y="436" width="320" height="44"/>
                                <items>
                                    <barButtonItem style="plain" systemItem="flexibleSpace" id="ePa-De-IEX"/>
                                    <barButtonItem title="播放" id="MHb-eD-N8J">
                                        <connections>
                                            <segue destination="GpB-tU-8Ba" kind="show" identifier="play" id="ckL-wT-DBr"/>
                                        </connections>
                                    </barButtonItem>
                                    <barButtonItem style="plain" systemItem="flexibleSpace" id="DLz-N0-tuR"/>
                                    <barButtonItem title="下载" id="l22-cy-O4B">
                                        <connections>
                                            <action selector="downloaderExp:" destination="BYZ-38-t0r" id="ASQ-DV-YZN"/>
                                            <segue destination="el0-eb-oqh" kind="show" id="eQs-Ju-UOk"/>
                                        </connections>
                                    </barButtonItem>
                                    <barButtonItem style="plain" systemItem="flexibleSpace" id="WEn-nj-n8x"/>
                                    <barButtonItem title="T" id="KSg-rA-Wkc">
                                        <connections>
                                            <action selector="test:" destination="BYZ-38-t0r" id="PpH-ud-LMc"/>
                                        </connections>
                                    </barButtonItem>
                                </items>
                            </toolbar>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="Td9-dE-Q71" firstAttribute="top" secondItem="Xkd-IE-Qzc" secondAttribute="bottom" id="PBp-V6-cbf"/>
                            <constraint firstItem="Xkd-IE-Qzc" firstAttribute="leading" secondItem="8bC-Xf-vdC" secondAttribute="leading" id="d3U-hb-4zV"/>
                            <constraint firstAttribute="trailing" secondItem="Xkd-IE-Qzc" secondAttribute="trailing" id="qQ9-6c-Bzy"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="Demo" id="u2i-1n-3XJ"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="311" y="314"/>
        </scene>
        <!--Play View Controller-->
        <scene sceneID="TmM-V8-XNq">
            <objects>
                <viewController id="GpB-tU-8Ba" customClass="PlayViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="b6L-o6-nmz"/>
                        <viewControllerLayoutGuide type="bottom" id="74z-WB-viJ"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="lSJ-uP-aKX">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="480"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ar1-eI-CMk">
                                <rect key="frame" x="0.0" y="64" width="320" height="180"/>
                                <color key="backgroundColor" red="0.0" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="ar1-eI-CMk" secondAttribute="height" multiplier="16:9" id="f54-et-5UC"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="ar1-eI-CMk" firstAttribute="top" secondItem="b6L-o6-nmz" secondAttribute="bottom" id="DQC-tb-gPJ"/>
                            <constraint firstItem="ar1-eI-CMk" firstAttribute="leading" secondItem="lSJ-uP-aKX" secondAttribute="leading" id="WQy-CD-qLV"/>
                            <constraint firstAttribute="trailing" secondItem="ar1-eI-CMk" secondAttribute="trailing" id="vet-rn-5yM"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="playerPlaceholder" destination="ar1-eI-CMk" id="ZVk-fU-Y5a"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ys4-Li-HuU" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1156.875" y="715"/>
        </scene>
        <!--Vod Downloader Exp Table View Controller-->
        <scene sceneID="gqE-ID-Ll7">
            <objects>
                <tableViewController id="el0-eb-oqh" customClass="PLVVodDownloaderExpTableViewController" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" id="8cy-73-GrM">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="480"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <prototypes>
                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="net.polyv.download.info" textLabel="jdd-Ye-7Ap" detailTextLabel="LLq-aI-xaT" style="IBUITableViewCellStyleSubtitle" id="Kdd-ao-bfS">
                                <rect key="frame" x="0.0" y="28" width="320" height="44"/>
                                <autoresizingMask key="autoresizingMask"/>
                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Kdd-ao-bfS" id="688-59-OGf">
                                    <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                    <subviews>
                                        <label opaque="NO" multipleTouchEnabled="YES" contentMode="left" insetsLayoutMarginsFromSafeArea="NO" text="Title" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="jdd-Ye-7Ap">
                                            <rect key="frame" x="16" y="5" width="33.5" height="20.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                            <nil key="textColor"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" multipleTouchEnabled="YES" contentMode="left" insetsLayoutMarginsFromSafeArea="NO" text="Subtitle" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="LLq-aI-xaT">
                                            <rect key="frame" x="16" y="25.5" width="44" height="14.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                            <nil key="textColor"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                </tableViewCellContentView>
                            </tableViewCell>
                        </prototypes>
                        <connections>
                            <outlet property="dataSource" destination="el0-eb-oqh" id="Xy5-o3-03J"/>
                            <outlet property="delegate" destination="el0-eb-oqh" id="FoI-v3-bmQ"/>
                        </connections>
                    </tableView>
                    <toolbarItems/>
                    <navigationItem key="navigationItem" id="ktM-fL-tOe">
                        <rightBarButtonItems>
                            <barButtonItem title="停止" id="Scb-Dk-0S3">
                                <connections>
                                    <action selector="stopDownload:" destination="el0-eb-oqh" id="gaw-5L-lXE"/>
                                </connections>
                            </barButtonItem>
                            <barButtonItem title="开始" id="ECE-xc-vvl">
                                <connections>
                                    <action selector="startDownload:" destination="el0-eb-oqh" id="Uc9-ga-A2k"/>
                                </connections>
                            </barButtonItem>
                            <barButtonItem title="删除" id="K21-x8-BRf">
                                <connections>
                                    <action selector="deleteDownload:" destination="el0-eb-oqh" id="66N-4d-4AI"/>
                                </connections>
                            </barButtonItem>
                        </rightBarButtonItems>
                    </navigationItem>
                    <simulatedToolbarMetrics key="simulatedBottomBarMetrics"/>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="CEP-xw-f1X" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1144" y="-26"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="Kry-Pz-zMY">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" id="pAC-Pb-F3X" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="lr2-kQ-L1z">
                        <rect key="frame" x="0.0" y="20" width="320" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="BYZ-38-t0r" kind="relationship" relationship="rootViewController" id="aOw-JN-GZ4"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="sj2-WR-zXJ" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-488" y="314"/>
        </scene>
        <!--Vod Player Skin-->
        <scene sceneID="Lon-Pq-vPD">
            <objects>
                <viewController id="qKc-Xd-f2U" customClass="PLVVodPlayerSkin" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="gj9-QL-muP" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="326" y="1143"/>
        </scene>
    </scenes>
</document>
