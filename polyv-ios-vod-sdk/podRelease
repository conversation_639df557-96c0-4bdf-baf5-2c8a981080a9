#!/bin/bash
# -------------------------------------------------------------------------------
# Date:     	2020/06/30
# Author:   	MissY<PERSON><PERSON>
# Description: 	该脚本用于发布 SDK 到 CocoaPods
#			   	执行脚本之前要确保 podspec 文件的 version、source 已更新
# -------------------------------------------------------------------------------
# 获取脚本所在路径
SOURCE="$0"
while [ -h "$SOURCE"  ]; do # resolve $SOURCE until the file is no longer a symlink
    DIR="$( cd -P "$( dirname "$SOURCE"  )" && pwd  )"
    SOURCE="$(readlink "$SOURCE")"
    [[ $SOURCE != /*  ]] && SOURCE="$DIR/$SOURCE" # if $SOURCE was a relative symlink, we need to resolve it relative to the path where the symlink file was located
done
CURRENT_PATH="$( cd -P "$( dirname "$SOURCE"  )" && pwd  )"
ROOT_PATH=$(dirname ${CURRENT_PATH})
SDK_PROJECT_FLODER="${ROOT_PATH}/polyv-ios-vod-sdk"

cd ${CURRENT_PATH}

PODSPEC_NAME="PolyvVodSDK"

pod trunk push "${PODSPEC_NAME}.podspec" --skip-import-validation --allow-warnings

pod trunk info ${PODSPEC_NAME}